[pytest]
# Pytest configuration file
asyncio_default_fixture_loop_scope = function

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output options
addopts =
    --strict-markers
    --strict-config
    --tb=short

# Asyncio configuration
asyncio_mode = auto

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    pipeline: Pipeline related tests
    auth: Authentication related tests
    llm: LLM related tests
    chunk: Chunk processing tests
    storage: Storage related tests
    ocr: OCR related tests
    rpc: RPC related tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds) - handled by pytest-timeout plugin if installed
# timeout = 300

# Ignore paths
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    htmlcov

# Logging
log_cli = true
log_cli_level = DEBUG
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pydantic.*
