# ignore my train package
Trainer

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.*.swp

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.cache
nosetests.xml
coverage.xml

# Translations
*.mo
*.pot

# Django stuff:
*.log

# Sphinx documentation
docs/_build/
.idea
*.exe
*.pyc
.vscode
conf/setting_dev.py
cache.json5
*_dataset.py
testdata*
plugins/*/
lancedb/
setting_dev.py
ragtest/
main_of_modules.py
benchmark/
app_of_dst.py
.env

TODO
