FROM hub-mirror.wps.cn/kna/decrypt-py3.10.12:safe-openeuler-arm-v1

LABEL maintainer="<EMAIL>"
ENV DEBIAN_FRONTEND=noninteractive

USER kna:kna

RUN mkdir /home/<USER>/base_build
WORKDIR /home/<USER>/base_build

# 依赖存在安全漏洞，无安全版本，需卸载
RUN /usr/local/bin/pip3 uninstall -y py

# 工具存在安全漏洞，需升级
USER root
RUN yum update -y
RUN yum install -y gcc automake autoconf libtool make gcc-c++ postgresql-devel readline-devel
RUN yum install -y openssl-devel postgresql postgresql-devel bzip2-devel gdbm-devel xz-devel sqlite-devel
RUN yum install -y tk-devel libffi-devel vim gdb wget lsof htop mesa-libGL curl
RUN yum install -y cmake tzdata iputils telnet net-tools dos2unix
RUN yum install -y kernel-headers
RUN yum clean all && yum autoremove -y

USER kna:kna

# 安装CPU版的torch
#RUN /usr/local/bin/pip3 install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu

# pip 升级
RUN /usr/local/bin/python -m pip install --upgrade pip \
    -i https://pypi.tuna.tsinghua.edu.cn/simple \
    --no-cache-dir

COPY --chown=kna:kna requirements_pri.txt /home/<USER>/base_build/requirements.txt
RUN /usr/local/bin/pip3 install -r /home/<USER>/base_build/requirements.txt \
    -i https://mirrors.wps.cn/pypi/simple \
    --trusted-host mirrors.wps.cn --no-cache-dir

RUN /usr/local/bin/pip3 cache purge \
    && rm -rf /home/<USER>/.cache/pip \
    && rm -rf /home/<USER>/base_build
