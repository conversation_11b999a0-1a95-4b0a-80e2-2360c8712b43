FROM hub-mirror.wps.cn/kna/aidocs_dst_server-base:v1 as builder

LABEL maintainer="<EMAIL>"

ARG MODEL_BUILD_KEY

WORKDIR /app
USER root

RUN mkdir aidocs_dst_server 

COPY aidocs_dst_server-source ./aidocs_dst_server

WORKDIR /app/aidocs_dst_server
RUN sed -i "s/MODEL_BUILD_KEY/${MODEL_BUILD_KEY}/g" /app/aidocs_dst_server/commons/crypt/aes_crypt.py

# 部分系统会检测unix换行符，需转换
RUN dos2unix docker/start.sh
RUN rm /app/aidocs_dst_server/docker/Dockerfile*

###########################################################################

FROM hub-mirror.wps.cn/kna/aidocs_dst_server-base:v1

LABEL maintainer="<EMAIL>"

WORKDIR /app
USER root
RUN apt-get update && apt-get install -y tini libcairo2 imagemagick

COPY --from=builder /app/aidocs_dst_server ./aidocs_dst_server

COPY --from=builder /app/aidocs_dst_server/requirements.txt ./requirements.txt
RUN /usr/local/bin/pip3 install -r ./requirements.txt --index https://mirrors.wps.cn/pypi/simple/ --trusted-host mirrors.wps.cn

RUN rm -rf /app/requirements.txt

WORKDIR /app

EXPOSE 8080

ENTRYPOINT ["/usr/bin/tini", "--"]

CMD ["/bin/bash", "/app/aidocs_dst_server/docker/start.sh"]
