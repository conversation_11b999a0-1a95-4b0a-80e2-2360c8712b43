FROM hub-mirror.wps.cn/kna/decrypt-py3.10.12:safe-ubuntu-v1

LABEL maintainer="<EMAIL>"
ENV DEBIAN_FRONTEND=noninteractive

RUN mkdir /home/<USER>/base_build
WORKDIR /home/<USER>/base_build

# 安装CPU版的torch
#RUN /usr/local/bin/pip3 install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu
COPY --chown=kna:kna requirements.txt /home/<USER>/base_build/requirements.txt
RUN /usr/local/bin/pip3 install -r /home/<USER>/base_build/requirements.txt \
    -i https://mirrors.wps.cn/pypi/simple \
    --trusted-host mirrors.wps.cn --no-cache-dir

RUN /usr/local/bin/pip3 cache purge \
    && rm -rf /home/<USER>/.cache/pip \
    && rm -rf /home/<USER>/base_build
