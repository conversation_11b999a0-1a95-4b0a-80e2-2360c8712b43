name: ai-aidocsdst # service_name
kind: Deployment

image: {{ .Values.global.baseServices.registry.addr }}/ai-aidocsdst:{{ .Values.global.image.tag.ai_aidocsdst }}

container:
  command: ['/app/aidocs_dst_server/docker/start.sh']


lifecycle:


livenessProbe:
  tcpSocket:
    port: 8080
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 1
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  tcpSocket:
    port: 8080
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 1
  successThreshold: 1
  failureThreshold: 3

resources: # 资源配置
  limits:
    cpu: 1000m        #限制1个核心，根据实际资源评估填写
    memory: 1024Mi    #限制1G内存，根据实际资源评估填写
  requests:
    cpu: 10m          #根据实际资源评估填写
    memory: 1Mi       #根据实际资源评估填写


extraVolumeMounts: # 除日志目录挂载外的额外挂载。默认为空
extraVolumes: # 同extraVolumeMounts，请按k8s_deployment规范一一对应

#annotations:
#  prometheus.io/path: "/metrics"
#  prometheus.io/port: "80"
#  prometheus.io/scheme: "http"
#  prometheus.io/scrape: "true"

service:
  ports:
    port: 80
    protocol: TCP
    targetPort: 8080


env:
  conf_env: "private"
  IS_IPV6: '{{ default "false" .Values.global.metaData.is_ipv6 }}'
  AI_AIDOCSAI_HTTP_PORT: "8080"
  SERVICE_TIMEOUT_KEEP_ALIVE: "180"
  LOG_LEVEL: "INFO"
  LOG_MONITOR_NAME: "InsightAiMonitorError"
  KMS_USE: "1"
  KMS_SETTING_ENCRYPT: '{{ .Values.global.metaData.kms_encrypt }}'
  KMS_CONFIG_PATH: '{{ .Values.global.metaData.kms_configpath }}'
  KMS_SECRET_PATH: '{{ .Values.global.metaData.kms_secretpath }}'
  KMS_SETTING_KEYID: '{{ .Values.global.metaData.kms_keyid }}'
  AUTH_PLATFORM: "1" # 0:dmc 1:private
  # prometheus
  PROM_JOB_NAME: "wps-aidocs-dst-server"
  PROM_USERNAME: '{{ .Values.global.baseServices.kubewpsops.pgw_auth_user }}'
  PROM_PASSWORD: '{{ .Values.global.baseServices.kubewpsops.pgw_auth_passwd }}'
  # AI公网网关/私网新版网关
  ai_gateway_activated: "1"
  ai_gateway_threadnum: "10"
  ai_gateway_host: "http://ai-gateway:8088"
  ai_gateway_token: "empty"
  ai_gateway_uid: "9052"
  ai_gateway_product_name: "saas_knowledgebase_pc"
  ai_gateway_intention_code: "saas_knowledgebase_assistedcreation"
  ai_gateway_model: "abab6.5s-chat"
  ai_gateway_provider: "minimax"
  ai_gateway_version: "empty"
  # 私有化网关，9b下架
  ai_private_activated: "0"
  ai_private_host: "http://weboffice-aigatewaypri"
  ai_private_platform: "private"
  ai_private_prom_token: "0"
  ai_private_minimax_model: "abab5-chat"
  # celery开关
  CELERY_IGNORE: "1"
  # aksk
  AK: '{{ .Values.global.aksk.aidocs.ak }}'
  SK: '{{ .Values.global.aksk.aidocs.sk }}'
  aidocs_dst_sk: '{{ .Values.global.aksk.aidocs.sk }}'
  aidocs_dst_ak: '{{ .Values.global.aksk.aidocs.ak }}'
  openapi_ak: '{{ .Values.global.aksk.aidocs.ak }}'
  openapi_sk: '{{ .Values.global.aksk.aidocs.sk }}'
  # wps365配置
  wps365_host: "http://docmini-dc-main"
  WPS365_KDC_HOST: "http://docmini-dc-main"
  # redis配置
  REDIS_HOSTS: '{{ regexReplaceAll "(^\\[|\\]$|\")" .Values.global.baseServices.redis.cluster_addr ""}}'
  REDIS_PREFIX: "wps_kna_aidocs_dst_server_"
  REDIS_PASSWORD: '{{ .Values.global.baseServices.redis.password }}'
  # store配置
  store_dir: "insight-ai/doc-parse"
  back_store_dir: "insight-ai/doc-parse/back_store_private"
  # minio配置
  ECIS_MINIO_ADDR: '{{ regexReplaceAll "(^\\[|\\]$|\")" default "encs-pri-minio-proxy" .Values.global.baseServices.wps3.addr ""}}'
  ECIS_MINIO_AK: '{{ .Values.global.baseServices.wps3.ak }}'
  ECIS_MINIO_SK: '{{ .Values.global.baseServices.wps3.sk }}'
  ECIS_MINIO_BUCKET: '{{ .Values.global.baseServices.wps3.file_bucket }}'
  ECIS_MINIO_PREFIX_0: 'com_aidocs'
  # mysql配置
  db_host: '{{ regexReplaceAll "(^\\[|\\]$|\")" default "localhost" .Values.global.baseServices.db.ai_aidocsdstserver.host ""}}'
  db_port: '{{ default "3306" .Values.global.baseServices.db.ai_aidocsdstserver.port }}'
  db_user: '{{ default "wps" .Values.global.baseServices.db.ai_aidocsdstserver.user }}'
  db_pwd: '{{ .Values.global.baseServices.db.ai_aidocsdstserver.passwd }}'
  db_name: "ai_aidocsdstserver"
  db_pools: "5"
  db_env: "private"

  kafka_bootstrap_servers: '{{ regexReplaceAll "(^\\[|\\]$|\")" .Values.global.baseServices.kafka.cluster_addr ""}}'
  recall_chunk_host: "http://ai-aidocsapi"
  DST_CATALOG_FILTER_KEY: ["目录", "目録"]
  DST_CATALOG_FILTER_FLAG: ["........"]
  DST_CHECKBOX_FILTER_KEY: ["冈", "凶", "区", "图", "因", "囧", "口", "D", "O"]

  # Triton配置

  # TODO TraceRedis私网屏蔽
  # TODO ConfLLMGateway图片描述，文档总结相关sft模型屏蔽
