# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/4/25 17:33
import asyncio
import json
import time
from typing import Union, Optional, List
import logging
import re
import requests
from fastapi import BackgroundTasks
import uuid
from fastapi import Request

from commons.monitor.prom import get_mcount
from commons.trace.tracer import async_trace_span
from modules.flows.callback import set_parse_target_status, get_parse_target_status
from services.datamodel import ReqGeneralParse, ReqGeneralParseType
from modules.pipeline.context import PipelineContext, KDCInput, FileInfo, ChunkInfo
from modules.pipeline.factory import PipelineFactory

from modules.rpc.drive_rpc_factory import get_drive_rpc_client
from routers.httpcode import HTTPCODE
from modules.entity.parse_entity import DAY_7
from commons.db.redis5dao import Redis5Dao
from commons.tools.utils import error_trace
from modules.entity.parse_entity import PARSE_BACKGROUND_STATUS_KEY, GeneralParseStatus, \
    PARSE_BACKGROUND_CONTENT_PATH_KEY, RespGeneralParseData, ParseRes
from services.datamodel import ReqGeneralParseRes, RespGeneralParseRes, ParseTarget
from pydantic import BaseModel
from commons.logger.logger import PassableLogContext
from commons.db.kafkadao import KafkaProducerDao
from commons.db.storedao import StoreDao
from conf import ConfStore, ConfParseVersion
from datetime import datetime
from conf import ConfHandlerName
from commons.llm_gateway.models.chat_data import PublicGatewayHeader



# 映射：handler 名称 <-> ParseTarget，避免在代码中重复 if/elif
HANDLER_TO_TARGET = {
    ConfHandlerName.chunk_handler: ParseTarget.chunk,
    ConfHandlerName.fake_title_handler: ParseTarget.fake_title,
    ConfHandlerName.keywords_handler: ParseTarget.keywords,
    ConfHandlerName.summary_handler: ParseTarget.summary,
    ConfHandlerName.screenshot_handler: ParseTarget.screenshot,
    ConfHandlerName.image_desc_handler: ParseTarget.img_desc,
}

TARGET_TO_HANDLER = {target: handler for handler, target in HANDLER_TO_TARGET.items()}


class ParseKafkaMsg(BaseModel):
    file_url: Optional[str] = None
    body: ReqGeneralParse
    token: str
    log_context: Optional[PassableLogContext] = None
    wiki_branch: Optional[str] = None


async def parse_input_to_ks3(input_file: bytes, token: str, store_dir: str = "parse_input") -> str:
    now_time = datetime.now().strftime('%Y%m%d')
    upload_status = await StoreDao().async_upload_from_bytes(
        f"{ConfStore.back_store_dir}/{now_time}/{store_dir}_{token}", input_file)
    if not upload_status:
        raise Exception("文件bytes上传ks3失败")
    res_ks3_url = await StoreDao().async_generate_url(f"{ConfStore.back_store_dir}/{now_time}/{store_dir}_{token}",
                                                      60 * 60 * 24 * 7)
    return res_ks3_url


@async_trace_span
async def background_general_parse(r: ReqGeneralParse, file_url_or_bytes: Union[str, bytes, None], token: str, recall_header: Optional[str] = None):
    logging.info(f"parse background_index start, token: {token}")
    _t0 = time.perf_counter()
    # 构造 PipelineContext
    pipeline_context = PipelineContext(
        file_info=FileInfo(file_id=r.wps_v7_file_id or r.wps_v5_file_id or r.download_id,
                           file_name=r.file_name,
                           file_type=r.file_type),
        embed_enabled=r.embed_enabled,
        kdc_input=KDCInput(
            kdc_ks3_url=r.kdc_ks3_url,
            company_id=r.wps_company_id,
            file_url_or_bytes=file_url_or_bytes,
            convert_options=r.convert_options
        ),
        chunks_info=ChunkInfo(
            chunk_size=getattr(getattr(r.parse_target_meta, "chunk", None), "chunk_size", 1024) or 1024,
            min_chunk_size=getattr(getattr(r.parse_target_meta, "chunk", None), "chunk_size", 512) or 512,
            chunk_overlap=getattr(getattr(r.parse_target_meta, "chunk", None), "chunk_overlap", 50) or 50
        ),
        need_callback=r.need_callback,
        callback_url=r.callback_url,
        return_ks3_url=r.return_ks3_url,
        token=token,
        ocr_only=r.ocr_only,
        page_count=r.page_count,
        word_count=r.word_count,
        parse_version=ConfParseVersion.version,
        parse_target=r.parse_target,
        file_drive_id=r.wps_drive_id,
        recall_chunk_header=recall_header,

    )
    if r.parse_target and ParseTarget.chunk in r.parse_target:
        pipeline_context.need_solve_picture = True
    try:
        pipeline_context.add_business_field_into_log("token", token)
        pipeline_context.add_business_field_into_log("file_id", r.wps_v5_file_id)
        pipeline_context.add_business_field_into_log("file_name", r.file_name)

        pipeline = PipelineFactory.create_document_pipeline("base_pipeline", r)
        await pipeline.execute(pipeline_context)
        Redis5Dao().set(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.ok, ex=DAY_7)
    except ValueError as e:
        error_msg = str(e)
        if "page count" in error_msg and "exceeds limit" in error_msg:
            pipeline_context.business_log.error(f"File page count limit exceeded: {error_msg}")
            await Redis5Dao().aset(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.limit, ex=DAY_7)
        elif "word count" in error_msg and "exceeds limit" in error_msg:
            pipeline_context.business_log.error(f"File word count limit exceeded: {error_msg}")
            await Redis5Dao().aset(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.limit, ex=DAY_7)
        else:
            pipeline_context.business_log.error(
                f"background_general_parse 失败，error: {e}, file_id: {r.wps_v5_file_id}")
            await Redis5Dao().aset(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.fail, ex=DAY_7)
        error_trace()
    except Exception as e:
        pipeline_context.business_log.error(f"background_general_parse 失败，error: {e}, file_id: {r.wps_v5_file_id}")
        await Redis5Dao().aset(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.fail, ex=DAY_7)
        error_trace()
    finally:
        set_parse_target_status(token, r.parse_target)
        pipeline_context.business_log.info(f"parse background_index finish, token: {token}")
        dur = time.perf_counter() - _t0

        mc = get_mcount()
        if mc and pipeline_context.file_info:
            file_type = str(getattr(pipeline_context.file_info.file_type, "value", pipeline_context.file_info.file_type))
            mc.record_parse(file_type, "all", dur)

@async_trace_span
async def general_parse_service(r: Request, background_tasks: BackgroundTasks):
    token = str(uuid.uuid4())
    try:
        file_url_or_bytes: Union[str, bytes, None] = None
        ct = r.headers.get("Content-Type", "application/json")
        wiki_branch = r.headers.get("wiki-branch", "")
        # 加载请求携带的权益、模型信息
        public_gateway_header = PublicGatewayHeader()
        public_gateway_header.load_by_req_header(r)

        if "application/json" in ct:
            body = await r.body()
            body = ReqGeneralParse.parse_raw(body)
            logging.info(f"body ======= {body}")
            # 如果没有传入kdc_ks3_url，则调kdc对文件进行解析，获取kdc_ks3_url
            if not body.kdc_ks3_url:
                # 获取文件下载链接
                if body.file_url is not None and len(body.file_url) > 0:
                    file_url_or_bytes = body.file_url
                elif body.download_id:
                    drive_client = get_drive_rpc_client("wpsv5")
                    download_url_res = await drive_client.aget_download_url(body.download_id)
                    if not download_url_res.is_success:
                        logging.error(
                            f"Error params occurred for file id: {body.wps_v5_file_id} or download_id: {body.download_id}, error: {download_url_res.error_message}")
                        return RespGeneralParseRes(code=HTTPCODE.ERROR_PARAMS)
                    file_url_or_bytes = download_url_res.url
                elif body.wps_v5_file_id:
                    drive_client = get_drive_rpc_client("wpsv5")
                    download_url_res = await drive_client.aget_download_url(body.wps_v5_file_id)
                    if not download_url_res.is_success:
                        logging.error(
                            f"Error params occurred for file id: {body.wps_v5_file_id} or download_id: {body.download_id}, error: {download_url_res.error_message}")
                        return RespGeneralParseRes(code=HTTPCODE.ERROR_PARAMS)
                    file_url_or_bytes = download_url_res.url
                    # 临时添加，本地测试时需要用到，后面需要删除
                    file_url_or_bytes = re.sub(r'-internal', '', file_url_or_bytes, count=1)
                else:
                    return RespGeneralParseRes(code=HTTPCODE.ERROR_PARAMS)
                if file_url_or_bytes is None:
                    return RespGeneralParseRes(code=HTTPCODE.ERROR_PARAMS)
        elif "multipart/form-data" in ct:
            form = await r.form()
            parse_target_meta = form.get("parse_target_meta", None)
            if parse_target_meta is not None:
                parse_target_meta = json.loads(parse_target_meta)
            body = ReqGeneralParse(
                file_io=form["file_io"],
                wps_company_id=form.get("wps_company_id", "41000207"),
                wps_group_id=form.get("wps_group_id", None),
                wps_drive_id=form.get("wps_drive_id", None),
                wps_v7_file_id=form.get("wps_v7_file_id", None),
                wps_v5_file_id=form.get("wps_v5_file_id", None),
                return_ks3_url=form.get("return_ks3_url", False),
                use_external_link=form.get("use_external_link", False),
                use_ocr_pipeline=form.get("use_ocr_pipeline", False),
                embed_enabled=form.get("embed_enabled", True),
                kdc_ks3_url=form.get("kdc_ks3_url", None),
                need_callback=form.get("need_callback", False),
                callback_url=form.get("callback_url", None),
                file_name=form["file_name"],
                file_type=form.get("file_type", ""),
                parse_res_type=form.get("parse_res_type", "plain"),
                use_layout=form.get("use_layout", False),
                page_start=form.get("page_start", None),
                page_end=form.get("page_end", None),
                # parse_target=form.getlist("parse_target"),
                parse_target_meta=parse_target_meta,
                req_type=form.get("req_type", ReqGeneralParseType.normal),
                req_level=form.get("req_level", ReqGeneralParseType.normal),
                ocr_only=form.get("ocr_only", True),
                page_count=int(form.get("page_count")) if form.get("page_count") else None,
                word_count=int(form.get("word_count")) if form.get("word_count") else None,
            )
            body.parse_target = form.get("parse_target", "").split(",")
            if not body.kdc_ks3_url:
                if body.file_io is not None:
                    file_url_or_bytes = body.file_io.file.read()
                else:
                    return RespGeneralParseRes(code=HTTPCODE.ERROR_PARAMS)
        else:
            return RespGeneralParseRes(code=HTTPCODE.ERROR_PARAMS)
        match body.req_type:
            # 同步
            case ReqGeneralParseType.normal:
                # 构造 PipelineContext
                pipeline_context = PipelineContext(
                    public_gateway_header = public_gateway_header,
                    file_info=FileInfo(file_id=body.wps_v7_file_id or body.wps_v5_file_id or body.download_id,
                                       file_name=body.file_name,
                                       file_type=body.file_type),
                    embed_enabled=body.embed_enabled,
                    kdc_input=KDCInput(
                        kdc_ks3_url=body.kdc_ks3_url,
                        company_id=body.wps_company_id,
                        file_url_or_bytes=file_url_or_bytes,
                        convert_options=body.convert_options
                    ),
                    chunks_info=ChunkInfo(
                        chunk_size=getattr(getattr(body.parse_target_meta, "chunk", None), "chunk_size", 1024),
                        min_chunk_size=getattr(getattr(body.parse_target_meta, "chunk", None), "min_chunk_size", 512),
                        chunk_overlap=getattr(getattr(body.parse_target_meta, "chunk", None), "chunk_overlap", 50)
                    ),
                    need_callback=body.need_callback,
                    callback_url=body.callback_url,
                    return_ks3_url=body.return_ks3_url,
                    token=token,
                    ocr_only=body.ocr_only,
                    page_count=body.page_count,
                    word_count=body.word_count,
                    parse_version=ConfParseVersion.version,
                    parse_target=body.parse_target,
                    file_drive_id=body.wps_drive_id,
                    recall_chunk_header=wiki_branch
                )
                pipeline = PipelineFactory.create_document_pipeline("base_pipeline", body)
                pipeline_context.add_business_field_into_log("file_id", body.wps_v5_file_id)
                pipeline_context.add_business_field_into_log("file_name", body.file_name)
                if body.parse_target and ParseTarget.chunk in body.parse_target:
                    pipeline_context.need_solve_picture = True
                try:
                    res = await pipeline.execute(pipeline_context)
                    ##
                    if res is None:
                        logging.error(f"Pipeline execution returned None for file_id: {body.wps_v5_file_id}")
                        return RespGeneralParseRes(code=HTTPCODE.ERROR)
                    parse_result = convert_handler_results_to_resp(res.handler_results, pipeline_context, body.parse_target)
                    parse_result.fileinfo = body.fileinfo
                    # parse_result.dst_parse_version = ConfParseVersion.version
                    # print_dst_tree(res.dst)
                    return RespGeneralParseRes(code=HTTPCODE.OK, message="success", data=parse_result )
                except ValueError as e:
                    error_msg = str(e)
                    if "page count" in error_msg and "exceeds limit" in error_msg:
                        logging.warning(f"File page count limit exceeded: {error_msg}")
                        return RespGeneralParseRes(code=HTTPCODE.ERROR_PAGESIZE_LIMIT, message=error_msg)
                    elif "word count" in error_msg and "exceeds limit" in error_msg:
                        logging.warning(f"File word count limit exceeded: {error_msg}")
                        return RespGeneralParseRes(code=HTTPCODE.ERROR_PAGESIZE_LIMIT, message=error_msg)
                    else:
                        logging.error(f"ValueError in pipeline execution: {error_msg}")
                        return RespGeneralParseRes(code=HTTPCODE.ERROR, message=error_msg)
                except Exception as e:
                    logging.error(f"Unexpected error in pipeline execution: {e}")
                    error_trace()
                    return RespGeneralParseRes(code=HTTPCODE.ERROR)
            # 异步
            case ReqGeneralParseType.background:
                background_tasks.add_task(background_general_parse, body, file_url_or_bytes, token, wiki_branch)
                # 设置状态为等待
                await Redis5Dao().aset(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.wait, ex=DAY_7)
                return RespGeneralParseRes(code=HTTPCODE.OK,
                                           data=RespGeneralParseData(status=GeneralParseStatus.wait, token=token))
            # 队列
            case ReqGeneralParseType.queue:
                await Redis5Dao().aset(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.wait, ex=DAY_7)
                log_context = PassableLogContext(**PassableLogContext.dump())
                if isinstance(file_url_or_bytes, bytes):
                    file_url_or_bytes = await parse_input_to_ks3(file_url_or_bytes, token)
                    body.file_io = None
                msg = ParseKafkaMsg(file_url=file_url_or_bytes, body=body, token=token,
                                    log_context=log_context, wiki_branch=wiki_branch).model_dump()
                product_res = await KafkaProducerDao().asend_message(body.req_level, msg)
                logging.info(f"kafka发送消息成功: level={body.req_level}, msg={msg.get('body')}")
                if product_res:
                    return RespGeneralParseRes(code=HTTPCODE.OK,
                                               data=RespGeneralParseData(status=GeneralParseStatus.wait, token=token))
                else:
                    return RespGeneralParseRes(code=HTTPCODE.ERROR_KAFKA_MSG_SEND, msg="kafka消息发送失败")
            case _:
                return RespGeneralParseRes(code=HTTPCODE.ERROR_PARAMS)

    except Exception as e:
        logging.error(e)
        error_trace()
        await Redis5Dao().aset(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.fail, ex=DAY_7)
        return RespGeneralParseRes(code=HTTPCODE.ERROR)


def convert_handler_results_to_resp(handler_results, context: PipelineContext, parse_target: List[ParseTarget]):
    # Initialize ParseRes with data from handler_results
    parse_res = ParseRes(
        chunks=handler_results.get(ConfHandlerName.chunk_handler, []),
        fake_title=handler_results.get(ConfHandlerName.fake_title_handler, {}).get("fake_title"),
        fake_title_embedding=handler_results.get(ConfHandlerName.fake_title_handler, {}).get("fake_title_embedding"),
        keywords=handler_results.get(ConfHandlerName.keywords_handler, {}).get("keywords"),
        keywords_embedding=handler_results.get(ConfHandlerName.keywords_handler, {}).get("keywords_embedding"),
        summary=handler_results.get(ConfHandlerName.summary_handler, {}).get("summary"),
        summary_embedding=handler_results.get(ConfHandlerName.summary_handler, {}).get("summary_embedding"),
        img_desc=handler_results.get(ConfHandlerName.image_desc_handler, []),
        page_size=context.file_info.page_size,
        word_count=context.file_info.word_count,
        width=context.file_info.width,
        height=context.file_info.height,
        image=handler_results.get(ConfHandlerName.screenshot_handler, []),
        is_scan=context.file_info.is_scan,
        rotate_page=context.file_info.rotate_page,
        parse_version=context.parse_version,
    )

    # Create RespGeneralParseData
    resp_data = RespGeneralParseData(
        status=GeneralParseStatus.ok,  # Set status as needed
        parse_res=parse_res,
        parse_target=parse_target
        # fileinfo={
        #     "file_id": file_info.file_id,
        #     "file_name": file_info.file_name,
        #     "file_type": file_info.file_type.value
        # }
    )

    return resp_data


@async_trace_span
async def general_parse_res_service(r: ReqGeneralParseRes):
    try:
        # Default to fail if the status is not found or expired
        status = await Redis5Dao().agetstring(f"{PARSE_BACKGROUND_STATUS_KEY}_{r.token}", GeneralParseStatus.fail)
        parse_targets_to_process = r.parse_target or get_parse_target_status(r.token)
        logging.info(f"general_parse_res_service status: {status}, parse_targets_to_process: {parse_targets_to_process}")

        if status == GeneralParseStatus.ok:
            combined_parse_res = None
            found_parse_targets = []

            # 使用r中的parse_target或者初始化空列表
            # 如果没有提供parse_target，则从Redis中查找
            if not parse_targets_to_process:
                # 遍历所有handler值查找Redis中存在的数据
                handler_target_pairs = list(HANDLER_TO_TARGET.items())
                get_url_tasks = [
                    Redis5Dao().agetstring(
                        f"{PARSE_BACKGROUND_CONTENT_PATH_KEY}_{handler_name}_{r.token}", ""
                    )
                    for handler_name, _ in handler_target_pairs
                ]
                urls = await asyncio.gather(*get_url_tasks)
                target_to_url = {}
                for (handler_name, target), res_ks3_url in zip(handler_target_pairs, urls):
                    if res_ks3_url:
                        found_parse_targets.append(target)
                        target_to_url[target] = res_ks3_url

                parse_targets_to_process = found_parse_targets

                # 处理找到的parse_target
            for parse_target in parse_targets_to_process:
                # 获取对应的handler_name
                handler_name = TARGET_TO_HANDLER.get(parse_target)

                if not handler_name:
                    continue

                # 从Redis获取内容
                # 优先复用上一步已拿到的URL，避免重复请求
                res_ks3_url = (
                    target_to_url.get(parse_target)
                    if 'target_to_url' in locals() else ""
                )
                if not res_ks3_url:
                    res_ks3_url = await Redis5Dao().agetstring(
                        f"{PARSE_BACKGROUND_CONTENT_PATH_KEY}_{handler_name}_{r.token}", "")
                if res_ks3_url:
                    res = requests.get(res_ks3_url)
                    res.raise_for_status()
                    parse_res = ParseRes(**res.json())
                    if combined_parse_res is None:
                        combined_parse_res = parse_res
                    else:
                        combined_parse_res = merge_parse_res(
                            [combined_parse_res, parse_res])

            if r.return_ks3_url:
                if r.use_external_link:
                    res_ks3_url = re.sub(r'-internal', '', res_ks3_url, count=1)
                data = RespGeneralParseData(status=GeneralParseStatus.ok, res_ks3_url=res_ks3_url, fileinfo=r.fileinfo)
            else:
                data = RespGeneralParseData(
                    status=GeneralParseStatus.ok,
                    parse_res=combined_parse_res,
                    fileinfo=r.fileinfo,
                    parse_target=parse_targets_to_process if not r.parse_target else r.parse_target
                )
        elif status == GeneralParseStatus.wait:
            data = RespGeneralParseData(status=GeneralParseStatus.wait, parse_target=parse_targets_to_process)
        elif status == GeneralParseStatus.limit:
            data = RespGeneralParseData(status=GeneralParseStatus.limit, parse_target=parse_targets_to_process)
            return RespGeneralParseRes(code=HTTPCODE.ERROR_PAGESIZE_LIMIT, data=data)
        else:
            data = RespGeneralParseData(status=GeneralParseStatus.fail, parse_target=parse_targets_to_process)
        return RespGeneralParseRes(code=HTTPCODE.OK, data=data)
    except Exception as e:
        error_trace()
        return RespGeneralParseRes(code=HTTPCODE.ERROR)


def merge_parse_res(parse_res_list: list[ParseRes]) -> ParseRes:
    merged_res = ParseRes(
        chunks=[],
        fake_title=None,
        fake_title_embedding=[],
        keywords=[],
        keywords_embedding=[],
        summary=None,
        summary_embedding=[],
        page_size=0,
        word_count=0,
        width=None,
        height=None,
        image=[],
        is_scan=None,
    )

    for res in parse_res_list:
        if not res:
            continue

        if res.chunks:
            merged_res.chunks = res.chunks

        if res.fake_title:
            merged_res.fake_title = res.fake_title

        if res.fake_title_embedding:
            merged_res.fake_title_embedding = res.fake_title_embedding

        if res.keywords:
            merged_res.keywords = res.keywords

        if res.keywords_embedding:
            merged_res.keywords_embedding = res.keywords_embedding

        if res.summary:
            merged_res.summary = res.summary

        if res.summary_embedding:
            merged_res.summary_embedding = res.summary_embedding

        if res.page_size:
            merged_res.page_size = res.page_size

        if res.word_count:
            merged_res.word_count = res.word_count

        if res.width:
            merged_res.width = res.width

        if res.height:
            merged_res.height = res.height

        if res.image:
            merged_res.image = res.image

        if res.rotate_page:
            merged_res.rotate_page = res.rotate_page

        if res.parse_version:
            merged_res.parse_version = res.parse_version

        if res.is_scan is not None:
            merged_res.is_scan = res.is_scan

        if res.img_desc:
            merged_res.img_desc = res.img_desc

    return merged_res
