import json
from typing import Union, Optional, List
from pydantic import BaseModel

from conf import ConfKafka
import logging

from commons.tools.kafka_metrics import KafkaConsumerMetrics
from commons.tools.utils import error_trace
from commons.db.redis5dao import Redis5Dao
from services.datamodel import RespBaseModel
from routers.httpcode import HTTPCODE


class KafkaMsg(BaseModel):
    topic_name: str
    group_id: str

class TopicLagInfo(BaseModel):
    topic: str
    group_id: str
    total_lag: int

class ThroughputInfo(BaseModel):
    topic: str
    group_id: str
    throughput: float

class RespKafkaMsg(RespBaseModel):
    data: Optional[Union[List[TopicLagInfo], List[ThroughputInfo]]] = None

async def get_kafka_lag():
    try:
        results = []
        for c in ConfKafka.parse_config:
            topic, group_id = c["topic"], c["consumer_group_id"]
            lag = await KafkaConsumerMetrics().get_lag(topic, group_id)
            results.append(TopicLagInfo(topic=topic, group_id=group_id, total_lag=lag))
        return RespKafkaMsg(code=HTTPCODE.OK, data=results)
    except Exception as e:
        logging.error(f"get_kafka_lag error: {e}")
        error_trace()
        return RespKafkaMsg(code=HTTPCODE.ERROR)

async def get_kafka_throughput():
    try:
        throughput = await Redis5Dao().agetstring(ConfKafka.re_throughput_key, "")
        logging.info(f"get_kafka_throughput: {throughput}")
        if throughput:
            throughput = json.loads(throughput)
            results = []
            for item in throughput:
                topic = item["topic"]
                group_id = item["group_id"]
                throughput_value = item["throughput"]
                results.append(ThroughputInfo(topic=topic, group_id=group_id, throughput=throughput_value))
            return RespKafkaMsg(code=HTTPCODE.OK, data=results)
        return RespKafkaMsg(code=HTTPCODE.ERROR, message="No throughput data found in Redis")
    except Exception as e:
        logging.error(f"get_kafka_throughput error: {e}")
        error_trace()
        return RespKafkaMsg(code=HTTPCODE.ERROR)

async def fetch_and_store_throughput():
    """定时获取kafka吞吐量并写入redis"""
    try:
        acquired = Redis5Dao().set(ConfKafka.re_throughput_lock_key, "1", ex=60, nx=True)
        if not acquired:
            logging.info("Another process is already updating throughput, skipping this run.")
            return
        results = await KafkaConsumerMetrics().get_throughput(ConfKafka.parse_config)
        await Redis5Dao().aset(ConfKafka.re_throughput_key, json.dumps(results), ex=600)
        logging.info(f"Successfully update throughput record")
    except Exception as e:
        logging.error(f"Failed to update throughput: {e}")
        error_trace()