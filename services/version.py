
from commons.tools.utils import error_trace
from commons.trace.tracer import async_trace_span
from conf import ConfParseVersion
from modules.entity.version_entity import RespVersionData
from routers.httpcode import HTTPCODE
from services.datamodel import RespVersionRes


@async_trace_span
async def get_version():
    try:
        # Default to fail if the status is not found or expired
        data = RespVersionData(
            version=ConfParseVersion.version,
        )
        return RespVersionRes(code=HTTPCODE.OK, data=data)
    except Exception as e:
        error_trace()
        return RespVersionRes(code=HTTPCODE.ERROR)