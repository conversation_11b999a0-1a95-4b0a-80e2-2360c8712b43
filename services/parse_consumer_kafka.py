# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/3/25 17:33
from datetime import datetime, timedelta
import json
import logging
import signal
import time
import uuid

from commons.tools.utils import Singleton
from commons.db.kafkadao import <PERSON>f<PERSON>ConsumerDao, KafkaProducerDao
from commons.db.mysqldao import sqlsession
from commons.db.redis5dao import Redis5<PERSON>ao
from commons.hook.hook import _hooked
from typing import List
import asyncio
from services.parse import background_general_parse, ParseKafkaMsg
from commons.thread.multiprocess import MultiProcess
from commons.logger.logger import PassableLogContext
import os
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import threading
from modules.parse_record.parse_record_dao import ParseRecordDao


class KafkaConsumerProcess:
    def __init__(self, bootstrap_servers: List[str], topic: str, group_id: str, worker_num: int = 2, task_num: int = 2, worker_initializer=None, worker_initargs=()):
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.group_id = group_id
        self.consumer = None
        self.should_stop = False
        self.worker_num = worker_num
        self.task_num = task_num
        self.worker_initializer = worker_initializer
        self.worker_initargs = worker_initargs
        self.running_futures = []

    def _start_worker_process(self, hook=False):
        if hook:
            _hooked()
        # 消费者内部的进程池
        if self.worker_num > 0:
            self.process_pool: ProcessPoolExecutor = ProcessPoolExecutor(self.worker_num, initializer=self.worker_initializer, initargs=self.worker_initargs)
            self.process_pool.map(lambda: logging.info(f"KafkaConsumerProcess {self.group_id} 进程池初始化"), range(self.worker_num))
            self.thread_pool: ThreadPoolExecutor = ThreadPoolExecutor(self.worker_num)

    @classmethod
    def _consume_task(cls, msg, group_id=""):
        token = ""
        try:
            start = time.time()
            parse_msg = ParseKafkaMsg.model_validate(msg.value)
            if parse_msg.log_context is not None:
                parse_msg.log_context.export()
            else:
                PassableLogContext(ip="", req_type="", request_id="", local_id="", index=0, uri="").export()
            token = parse_msg.token
            loop = asyncio.get_event_loop()
            loop.run_until_complete(background_general_parse(parse_msg.body, parse_msg.file_url, parse_msg.token, parse_msg.wiki_branch))

            logging.info(f"ParseKafka._consumer {group_id} 消费完成, token: {token}, cost time: {time.time() - start}")
        except Exception as e:
            logging.error(f"ParseKafka._consumer {group_id} 消费异常, token: {token} error: {e}")

    async def consume(self):
        while True:
            if self.should_stop:
                break
            self.consumer = await KafkaConsumerDao(
                self.bootstrap_servers,
                [self.topic],
                self.group_id
            ).get_consumer()
            try:
                async for msg in self.consumer:
                    if self.should_stop:
                        break
                    thread_event = None
                    token = ""
                    try:
                        while len(self.running_futures) >= self.task_num:
                            logging.warning(f"ParseKafka._consumer {self.group_id} 消费者已满, running_futures num: {len(self.running_futures)}, 等待中...")
                            await asyncio.sleep(5)
                        token = msg.value["token"] if "token" in msg.value else str(uuid.uuid4())
                        # 记录任务，启动心跳
                        thread_event = await self._task_record(token, msg.value)
                        # 消费任务提交
                        future = self.process_pool.submit(self._consume_task, msg, self.group_id)

                        self.running_futures.append((token, future, thread_event))

                        # 记录当前消息的分区和偏移量
                        topic = msg.topic
                        partition = msg.partition
                        offset = msg.offset

                        await self.consumer.commit()
                        logging.info(f"Kafka提交成功: topic={topic}, partition={partition}, offset={offset}")
                        logging.info(f"ParseKafka._consumer {self.group_id} 消费新增, running_futures num: {len(self.running_futures)}")
                    except Exception as e:
                        logging.error(f"ParseKafka._consumer {self.group_id} kafka消费异常 error: {e}, msg: {msg}")
                        # 出现异常，任务视为失败, 清理任务记录和心跳
                        if thread_event is not None:
                            await self._task_clear(token, thread_event)
            except Exception as e:
                logging.error(f"ParseKafka._consumer {self.group_id} kafka消费异常 error: {e}")
                await asyncio.sleep(5)
            finally:
                if self.consumer:
                    await self.consumer.stop()
                await asyncio.sleep(5)
    async def _futrue_done_roll(self):
        while not self.should_stop:
            if len(self.running_futures) == 0:
                await asyncio.sleep(0.5)
                continue
            for i in range(len(self.running_futures) - 1, -1, -1):
                token, future, thread_event = self.running_futures[i]
                if future.done():
                    await self._task_clear(token, thread_event)
                    self.running_futures.pop(i)
            await asyncio.sleep(1)

    async def _task_record(self, token: str, msg_value: dict):
        logging.info(f"ParseKafka._task_record {self.group_id} 心跳启动，记录任务, token: {token}")
        thread_event = threading.Event()
        try:
            msg_str = json.dumps(msg_value)
            # mysql记录任务
            with sqlsession() as db:
                ParseRecordDao.add_record(db, token, msg_str)
            # 心跳任务提交
            self.thread_pool.submit(self._task_heartbeat, token, thread_event)
        except Exception as e:
            # 出现异常则不记录任务，允许丢失
            logging.error(f"KafkaConsumerProcess._task_record {self.group_id} error: {e}")

        return thread_event

    def _task_heartbeat(self, token: str, thread_event: threading.Event):
        while True:
            if thread_event.is_set():
                logging.info(f"ParseKafka._task_heartbeat {self.group_id} 心跳停止, token: {token}")
                return
            # 更新心跳
            with sqlsession() as db:
                ParseRecordDao.record_heartbeat(db, token)
            logging.debug(f"ParseKafka._task_heartbeat {self.group_id} 心跳任务执行, token: {token}")
            time.sleep(2)

    async def _task_clear(self, token, thread_event: threading.Event):
        # 停止心跳
        logging.info(f"ParseKafka._task_clear {self.group_id} 停止心跳，清理任务记录, token: {token}")
        thread_event.set()
        # mysql删除任务记录
        with sqlsession() as db:
            ParseRecordDao.del_record(db, token)

    def run(self, hook=False):
        # 设置信号处理器
        self._setup_signal_handlers()

        loop = asyncio.get_event_loop()
        # 注册停止消费者任务
        loop.create_task(self._stop_consumer())
        # 注册消费者任务完成监控
        loop.create_task(self._futrue_done_roll())

        # 运行消费者
        logging.getLogger('aiokafka').setLevel(logging.WARNING)
        logging.info(f"kafka消费进程初始化 {self.group_id} -----------------------------")

        try:
            self._start_worker_process(hook)
            loop.run_until_complete(self.consume())
        except Exception as e:
            logging.error(f"KafkaConsumerProcess.run {self.group_id} 启动异常 error: {e}")
        except asyncio.CancelledError:
            pass
        finally:
            loop.close()

    def _setup_signal_handlers(self):
        """注册信号处理器，响应主进程终止信号"""
        def stop(signum, frame):
            logging.info(f"KafkaConsumerProcess._setup_signal_handlers {self.group_id} 收到信号，停止消费者, Process {os.getpid()} received signal {signum}.")
            self.should_stop = True
        for sig in (signal.SIGINT, signal.SIGTERM):
            signal.signal(sig, stop)

    async def _stop_consumer(self):
        while not self.should_stop:
            await asyncio.sleep(1)
        if self.consumer is not None:
            await self.consumer.stop()
        if self.worker_num > 0:
            self.process_pool.shutdown()
            self.thread_pool.shutdown()
        logging.info(f"KafkaConsumerProcess._stop_consumer {self.group_id} 关闭consumer")

def process_worker(task, hook=False):
    """ProcessPoolExecutor 执行的任务包装器"""
    task.run(hook)


class ParseKafka(object):
    def __init__(self, bootstrap_servers: List[str], topic: str, group_id: str):
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.group_id = group_id
        self.initializer = None

    def consumer_by_multiprocess(self, worker_num: int = 2, task_num: int = 2,hook=False, initializer=None, initargs=()):
        """
        多进程消费kafka
        """
        if worker_num > 0:
            MultiProcess().submit(
                process_worker,
                KafkaConsumerProcess(
                    self.bootstrap_servers,
                    self.topic,
                    self.group_id,
                    worker_num=worker_num,
                    task_num=task_num,
                    worker_initializer=initializer,
                    worker_initargs=initargs
                ),
                hook
            )


class ParseReProduct(object, metaclass=Singleton):
    """
    用于解析过程中，程序意外重启后，丢失的任务重新消费
    在主进程中启动1条线程，定时检查任务记录表，将未完成的任务重新提交到kafka
    """
    def __init__(self):
        self.running = False
        self.dist_lock_key = ""

    def init(self, dist_lock_key: str):
        self.running = False
        self.dist_lock_key = dist_lock_key

    def re_product_start(self):
        threading.Thread(target=self._re_product).start()

    def _re_product(self):
        while True:
            # 通过redis的原子行操作竞争分布式锁，保证只有一个实例运行
            acquired = Redis5Dao().set(self.dist_lock_key, "1", ex=600, nx=True)
            if acquired:
                self.running = True
                logging.info("ParseReProduct.re_product 开始重新消费检测")

            if self.running:
                self.running = False
                with sqlsession() as db:
                    last_id = None
                    try:
                        finish = False
                        limit = 100
                        while True:
                            if finish:
                                break
                            rs = ParseRecordDao().scan_records(db, last_id, limit)
                            if len(rs) < limit:
                                finish = True
                            for r in rs:
                                try:
                                    token = r.token
                                    msg_value = json.loads(r.record_json)
                                    last_id = r.key_id
                                    if datetime.now() - r.mtime < timedelta(minutes=5):
                                        continue
                                    # 超过5分钟未更新心跳则认为任务丢失，需重新提交
                                    logging.info(f"ParseReProduct.re_product 任务过期，重新消费, token: {token}")
                                    # 删除任务记录
                                    ParseRecordDao().del_record(db, token)
                                    # 消费任务重新提交
                                    product_res = KafkaProducerDao().send_message("high", msg_value)
                                    if not product_res:
                                        logging.error(f"ParseReProduct.re_product KafkaProducerDao().send_message 重新消费失败, token: {token}")
                                        # 重新记录任务
                                        ParseRecordDao().add_record(db, token, r.record_json)
                                except Exception as e:
                                    logging.error(f"ParseReProduct.re_product 重新消费异常, token: {token} error: {e}")
                                    # 重新记录任务
                                    if not ParseRecordDao().exist_record(db, token):
                                        ParseRecordDao().add_record(db, token, r.record_json)
                    except Exception as e:
                        logging.error(f"ParseReProduct.re_product 重新消费异常 error: {e}")
                    finally:
                        logging.info("ParseReProduct.re_product 重新消费检测完成")
                        Redis5Dao().remove(self.dist_lock_key)
            time.sleep(300)