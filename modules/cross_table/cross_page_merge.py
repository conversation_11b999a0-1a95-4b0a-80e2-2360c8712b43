import logging
from typing import List

from modules.cross_table.merge_table_util import cross_page_merge_by_html, merge_table_by_force
from modules.entity.chunk_entity import Chunk
from modules.entity.dst_entity import DSTType


def classify_and_mark_table_indices(chunks):
    # Step 1: Classify chunks by page number
    page_chunks = {}
    for chunk in chunks:
        page_num = chunk.page_num[0]
        if page_num not in page_chunks:
            page_chunks[page_num] = []
        page_chunks[page_num].append(chunk)

    # Step 2: Mark table indices for each page
    table_indices = {}
    for page_num, chunks_on_page in page_chunks.items():
        first_table_index, last_table_index = -1, -1
        first_table_chunk_id, last_table_chunk_id = None, None
        table_index = 0
        mark_count = 0
        total_dsts = 0
        for i, chunk in enumerate(chunks_on_page):
            # Handle case where chunk.dsts is None
            if chunk.dsts is None:
                continue
            total_dsts += len(chunk.dsts)
            for dst in chunk.dsts:
                if dst.mark:
                    mark_count += 1
                    continue
                if dst.dst_type == DSTType.TABLE:  # Check if dst.type is table
                    if first_table_index == -1:
                        first_table_index = table_index
                        first_table_chunk_id = chunk.chunk_id
                    last_table_index = table_index
                    last_table_chunk_id = chunk.chunk_id
                table_index += 1

        # Calculate FromLast and handle cases with no tables
        from_last = total_dsts - last_table_index - mark_count if last_table_index != -1 else -1
        table_indices[page_num] = {
            "first_table_index": first_table_index,
            "first_chunk_id": first_table_chunk_id,
            "from_last_table_index": from_last,
            "last_chunk_id": last_table_chunk_id,
        }

    return table_indices


def process_and_merge_chunks(chunks, table_indices):
    merged_results = []  # Store merged results with chunk IDs
    # merged_chunk_ids = set()  # Track already merged chunk IDs

    # Convert table_indices to a sorted list of (page_num, indices) for sequential processing
    sorted_table_indices = sorted(table_indices.items())

    for i in range(len(sorted_table_indices) - 1):
        current_page, current_indices = sorted_table_indices[i]
        next_page, next_indices = sorted_table_indices[i + 1]

        # Check the condition
        if (
                current_indices["from_last_table_index"] != -1
                and next_indices["first_table_index"] != -1
                and current_indices["from_last_table_index"] + next_indices["first_table_index"] < 5
        ):
            # Get the corresponding chunks
            current_chunk = next(
                chunk for chunk in chunks if chunk.chunk_id == current_indices["last_chunk_id"]
            )
            before_table = current_chunk.content
            merge_table = None
            if len(merged_results) > 0 and current_chunk.chunk_id in merged_results[-1]["chunk_ids"]:
                before_table = merged_results[-1]["content"][-1]
                merge_table = merged_results[-1]
            next_chunk = next(
                chunk for chunk in chunks if chunk.chunk_id == next_indices["first_chunk_id"]
            )

            # Call cross_page_merge with their content
            merge_tag, _ = cross_page_merge_by_html(before_table, next_chunk.content)
            # If the result is a single merged table
            if merge_tag:
                if merge_table:
                    # If already merged, update the existing merged result
                    #
                    merge_table["content"].append(next_chunk.content)
                    merge_table["chunk_ids"].append(next_chunk.chunk_id)
                else:
                    # Create a new merged result
                    merged_results.append(
                        {"content": [before_table, next_chunk.content],
                         "chunk_ids": [current_chunk.chunk_id, next_chunk.chunk_id], }
                    )
    return merged_results


def merge_chunks_with_base(chunks: List[Chunk], merged_results):
    chunk_map = {chunk.chunk_id: chunk for chunk in chunks}
    merged_chunk_ids = set()

    for result in merged_results:
        base_chunk_id = result["chunk_ids"][0]
        base_chunk = chunk_map[base_chunk_id]

        for chunk_id in result["chunk_ids"][1:]:
            if chunk_id in chunk_map:
                # Handle case where dsts is None
                if base_chunk.dsts is None:
                    base_chunk.dsts = []
                if chunk_map[chunk_id].dsts is not None:
                    base_chunk.dsts.extend(chunk_map[chunk_id].dsts)
                force = merge_table_by_force(result["content"])
                logging.info(f"Force merge content: {force}")
                base_chunk.content = force
                base_chunk.page_num = list(set(base_chunk.page_num + chunk_map[chunk_id].page_num))
                base_chunk.block.extend(chunk_map[chunk_id].block)
                merged_chunk_ids.add(chunk_id)

    chunks = [chunk for chunk in chunks if chunk.chunk_id not in merged_chunk_ids]
    return chunks


def cross_table_merge(chunks: List[Chunk]) -> List[Chunk]:
    merged_results = process_and_merge_chunks(chunks, classify_and_mark_table_indices(chunks))
    return merge_chunks_with_base(chunks, merged_results)
