# encoding:utf-8
import requests
from requests.adapters import HTT<PERSON><PERSON>pter
from typing import Optional
import aiohttp
import urllib3

from commons.tools.utils import Singleton

class ConnPool(object, metaclass=Singleton):
    def __init__(self):
        self._file_req_sess: Optional[requests.Session] = None
        self._afile_req_sess: Optional[aiohttp.ClientSession] = None

    def init(self, pool_max: int = 10):
        self._file_req_sess = requests.Session()
        self._file_req_sess.mount('http://', HTTPAdapter(pool_maxsize=pool_max))
        self._file_req_sess.mount('https://', HTTPAdapter(pool_maxsize=pool_max))

        self._afile_req_sess = aiohttp.ClientSession(connector=aiohttp.TCPConnector(limit=pool_max))
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    async def close(self):
        if self._afile_req_sess:
            await self._afile_req_sess.close()
        if self._file_req_sess:
            self._file_req_sess.close()

    def get_file_req(self):
        return self._file_req_sess

    def get_afile_req(self):
        return self._afile_req_sess


if __name__ == "__main__":
    host = "xxx"
    ak = "xxx"
    sk = "xxx"
    from wpsai_insight_common.db.storedao import StoreDao, StoreType
    import uuid
    StoreDao().init(host, ak, sk, "kna-common", StoreType.Ks3)


    file_path = "D:\\data\\InstructLLM\\hetong.pdf"
    file_name = "hetong.pdf"
    StoreDao().upload_from_file(f"insight-ai/{str(uuid.uuid4())}_{file_name}", file_path)
    file_url = StoreDao().generate_url(f"insight-ai/{str(uuid.uuid4())}_{file_name}", 60*60*24)
