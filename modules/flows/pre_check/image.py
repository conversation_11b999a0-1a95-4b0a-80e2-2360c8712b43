# Author: linqi
# Date: 2025/7/10
# Time: 10:52

from modules.entity.pre_check_entity import ParserConfig, ParserName
from modules.flows.pre_check.pre_check_template import PreCheckTemplate
from commons.trace.tracer import async_trace_span
from modules.pipeline.context import PipelineContext

class ImagePreCheck(PreCheckTemplate):

    @async_trace_span
    async def precheck_process(self, context: PipelineContext):
        """
        预检查图片文件

        :param context: 上下文
        :return: 包含预检查结果的字典
        """
        return [ParserConfig(parser_name=ParserName.ImageParser, processing_pages={0}, is_all=True)]
