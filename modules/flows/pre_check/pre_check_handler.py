# Author: linqi
# Date: 2025/7/9
# Time: 14:54

import time

from commons.trace.tracer import async_trace_span
from modules.entity.pre_check_entity import Pa<PERSON><PERSON><PERSON><PERSON>, ParserConfig
from modules.pipeline.base import PipelineHandler
from modules.flows.pre_check.pdf import PDFPreCheck
from modules.flows.pre_check.image import Image<PERSON><PERSON><PERSON>heck
from modules.flows.pre_check.pre_check_template import PreCheckTemplate
from modules.pipeline.context import PipelineContext, FileType
from conf import Conf<PERSON><PERSON><PERSON><PERSON><PERSON>, MultipleParse


class PreCheckFactory:
    _precheck_processors = {
        # TODO: 增加其他类型文件的类（先不实现）
        FileType.PDF: PDFPreCheck,
        FileType.JPG: ImagePreCheck,
        FileType.JPEG: ImagePreCheck,
        FileType.PNG: ImagePreCheck,
        FileType.WEBP: ImagePreCheck,
        # todo 后面图片文件的预检就直接将context.need_scan_mode设置为 true 即可
    }

    @staticmethod
    def get_precheck_processor(file_type: FileType) -> PreCheckTemplate:
        precheck_class = PreCheckFactory._precheck_processors.get(file_type)
        if not precheck_class or not MultipleParse.enable:
            return None
        return precheck_class()


class PreCheckNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            _t0 = time.perf_counter()
            precheck_processor = PreCheckFactory.get_precheck_processor(context.file_info.file_type)
            if precheck_processor is None:
                context.business_log.error(f"No precheck processor found for file type: {context.file_info.file_type}")
                context.handler_results[ConfHandlerName.pre_check_handler] = [ParserConfig(
                    parser_name=ParserName.KdcParser,
                    is_all=True,
                    processing_pages=set(),
                )]
                return context
            res = await precheck_processor.precheck_process(context)
            context.business_log.info(f"PreCheckNode.process took {time.perf_counter() - _t0:.2f} seconds ,precheck result: {res}")
            context.handler_results[ConfHandlerName.pre_check_handler] = res
            return context
        except Exception as e:
            context.business_log.error(f"Error in PreCheckNode.process: {e}")
            raise e
