import re
import logging



def split_sentences(text):
    # 综合正则表达式：匹配句子的结尾（中文或英文标点符号），并且确保下一个字符是大写字母（英文句子）或中文字符（中文句子）
    sentence_pattern = r'(?<!\w\.\w.)(?<![A-Z]\.)(?<![A-Z][a-z]\.)(?<! [a-z]\.)(?<![A-Z][a-z][a-z]\.)(?<=[。！？!?.])(?=\s*([\d]+\.\s*)?[A-Z]|[\u4e00-\u9fff])'

    # 使用 finditer 找到所有符合模式的位置
    matches = list(re.finditer(sentence_pattern, text))

    sentences = []
    start = 0

    for match in matches:
        end = match.end()
        sentence = text[start:end].strip()
        if sentence:
            sentences.append(sentence)
        start = match.start()

    # 添加最后一句话
    last_sentence = text[start:].strip()
    if last_sentence:
        sentences.append(last_sentence)

    return sentences


def extract_sections(text, section_length=300):
    # use_idx = 0
    # for i, paragraph in enumerate(text):
    #     if "References" in paragraph or "REFERENCES" in paragraph:
    #         use_idx = i
    #         break
    # sentences = text[:use_idx]
    sentences = split_sentences(text)
    total_length = sum(len(sent) for sent in sentences)

    # 目标字数为总长度的三等分
    section_target_lengths = [total_length // 3, (total_length * 2) // 3, total_length]

    # 定义区段起始索引
    section_starts = [0]
    cumulative_length = 0

    for i, sentence in enumerate(sentences):
        cumulative_length += len(sentence)
        # 查找最接近1/3和2/3的点，记录该句子的索引
        if cumulative_length >= section_target_lengths[len(section_starts) - 1]:
            section_starts.append(i)
            if len(section_starts) == 3:  # 找到三分位置后退出
                break
    section_starts = sorted(list(set(section_starts)))
    logging.info(f"区段起始句子索引：{section_starts}, 总长度：{len(sentences)}")
    sections = []
    last_end = 0  # 记录上一段结束的位置
    for start in section_starts:
        current_length = 0
        section_sentences = []

        # 确保从上一段结束的位置开始
        start = max(start, last_end)
        # 从start位置开始，逐句添加，直到满足300字左右的条件
        for i in range(start, len(sentences)):
            current_length += len(sentences[i])
            section_sentences.append(sentences[i].strip())
            if current_length >= section_length:
                last_end = i + 1  # 更新结束位置，避免重叠
                break

        sections.append(" ".join(section_sentences))

    # 合并提取的前、中、后区段
    extracted_content = "\n".join(sections)
    # print(len(sections[0]), len(sections[1]), len(sections[2]))
    logging.debug(f'切分后的文本示例：{extracted_content[0:3]}******{extracted_content[-3:]}')
    return extracted_content