# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 16:17
from typing import Optional

from pydantic import BaseModel

from commons.tools.utils import error_trace
from commons.trace.tracer import async_trace_span
from modules.llm.chat_api import LMModel
from modules.llm.prompts import SUMMARY_PROMPT_WITHOUT_KEY
from modules.flows.summary.summary_generate import extract_sections
from modules.rpc.ocr_model import OCRModelClient
from routers.httpcode import HTTPCODE
from services.datamodel import RespBaseModel
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext
from modules.flows.callback import callback_parse_background
from modules.entity.dst_entity import print_dst_indent_tree
from modules.entity.parse_entity import ParseRes


class ReqSummary(BaseModel):
    content: str


class RespSummaryData(BaseModel):
    text_summary: str
    high_level_key: str = ""


class RespSummary(RespBaseModel):
    data: Optional[RespSummaryData] = None


async def summary_services(r: ReqSummary):
    try:
        if r.content is None or len(r.content) == 0:
            return RespSummary(code=HTTPCODE.OK, data=RespSummaryData(text_summary=""))
        extracted_content = extract_sections(r.content, section_length=300)
        status, res = await LMModel.generate_response(
            SUMMARY_PROMPT_WITHOUT_KEY.format(input_text=extracted_content[:5000]), top_k=0.99)

        text_summary = res
        # high_level_keys =  "" #此字段目前没用到，暂时为空
        return RespSummary(code=HTTPCODE.OK,
                           data=RespSummaryData(text_summary=text_summary))

    except Exception as e:
        error_trace()
        return RespSummary(code=HTTPCODE.ERROR)


class SummaryNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            # 从 context 中取出 dst_list
            dst_list = context.dst

            # 调用 print_dst_tree 函数
            plain_content = print_dst_indent_tree(dst_list)

            summary_res = await summary_services(ReqSummary(content=plain_content))
            summary_res = RespSummaryData.parse_obj(summary_res.data)
            summary_text = summary_res.text_summary
            summary_embedding = None
            if context.embed_enabled:
                summary_embedding = await OCRModelClient().request_text_embedding(summary_text)
            context.handler_results[self.name] = {
                "summary": summary_text,
                "summary_embedding": summary_embedding
            }
            # 同步接口的result后面再考虑一下怎么写， 这里的result得从上一个handler的result里面拼一下
            # context.result =
            res = ParseRes(summary=summary_text, summary_embedding=summary_embedding, page_size=context.file_info.page_size, word_count=context.file_info.word_count,
                           width=context.file_info.width, height=context.file_info.height, is_scan=context.file_info.is_scan, image=context.image, rotate_page=context.file_info.rotate_page,
                           parse_version=context.parse_version)
            await callback_parse_background(res, self.name, context.token, context.need_callback,
                                            context.return_ks3_url, context.callback_url)
            return context

        except Exception as e:
            import logging
            logging.error(f"Error in SummaryNode.process: {e}")