# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 16:30


from commons.trace.tracer import async_trace_span
from modules.pipeline.base import <PERSON><PERSON>ineHandler
from modules.flows.dst_builder.otl import OTLParse
from modules.flows.dst_builder.pdf import PdfParse
from modules.flows.dst_builder.doc import DocParse
from modules.flows.dst_builder.docx import DocxParse
from modules.flows.dst_builder.txt import TXTParse
from modules.flows.dst_builder.ppt import PptParser
from modules.flows.dst_builder.pptx import PptxParser
from modules.flows.dst_builder.xls import XlsParse
from modules.flows.dst_builder.xlsx import XlsxParse
from modules.flows.dst_builder.image import ImageParse
from modules.pipeline.context import PipelineContext, FileType
from modules.flows.dst_builder.parse_template import ParseTemplate


class ParserFactory:
    _parsers = {
        FileType.OTL: OTLParse,
        FileType.PDF: PdfParse,
        FileType.DOC: DocParse,
        FileType.DOCX: DocxPars<PERSON>,
        FileType.TXT: TXTParse,
        FileType.PPT: Ppt<PERSON>ars<PERSON>,
        FileType.PPTX: PptxParser,
        FileType.XLSX: XlsxParse,
        FileType.XLS: XlsParse,
        FileType.JPG: ImageParse,
        FileType.JPEG: ImageParse,
        FileType.PNG: ImageParse,
        FileType.WEBP: ImageParse,
    }

    @staticmethod
    def get_parser(file_type: FileType) -> ParseTemplate:
        parser_class = ParserFactory._parsers.get(file_type)
        if not parser_class:
            raise ValueError(f"No parser available for file type: {file_type}")
        return parser_class()


class DocumentParserNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            parser = ParserFactory.get_parser(context.file_info.file_type)
            kdc_data = context.kdc_data
            if not kdc_data:
                return context
            # 处理kdc的数据
            dst_dict = await parser.dst_generate(context, kdc_data)
            for page, dst_list in dst_dict.items():
                context.update_multiple_parse_dsts_unsafe(page, dst_list)
            return context

        except Exception as e:
            context.business_log.error(f"Error in DocumentParserNode.process: {e}")
            raise e
