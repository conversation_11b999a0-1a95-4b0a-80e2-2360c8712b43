# coding: utf-8
# <AUTHOR> dengfu<PERSON>
# @date    : 2025/4/22
from typing import List, Dict
import logging
from modules.entity.dst_entity import FileType, DST
from modules.entity.kdc_enttiy import WorkBook, KDC_DOC_FIELD
from modules.flows.dst_builder.xls import ParseTemplate,extract_content_for_et,row_process
from modules.common import build_root_dst
from modules.pipeline.context import PipelineContext


class XlsxParse(ParseTemplate):
    def __init__(self):
        super().__init__(FileType.XLSX)
        self.kdc_data = None
        self.dst = None

    def _kdc_validate(self) -> List[WorkBook]:
        kdc_doc_list = []
        for idx, item in enumerate(self.kdc_data):
            if item is None:
                logging.error(f"XlsxParse._kdc_validate error: kdc item is None")
                continue
            if KDC_DOC_FIELD not in item.keys() and item[KDC_DOC_FIELD] is None:
                logging.error(f"XlsxParse._kdc_validate error: doc is none or doc not in kdc item.keys():{item.keys()}")
                continue
            kdc_doc_list.append(WorkBook.model_validate(item[KDC_DOC_FIELD]))
        return kdc_doc_list

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]) -> None |  Dict[int, List[DST]]:
        self.kdc_data = kdc_data
        kdc_doc_list = self._kdc_validate()
        if len(kdc_doc_list) == 0:
            context.business_log.error(f"XlsxParse.dst_generate error: kdc_doc_list is empty")
            return None
        root = build_root_dst()
        nodes: List[DST] = [root]

        page_no = 0
        result = await extract_content_for_et(context, kdc_doc_list)

        for key, value in result.items():
            content = value["content"]
            sheet_name = value["title"]
            try:
                row_process(content, nodes, page_no, root, sheet_name)
                page_no += 1

            except ValueError as e:
                context.business_log.error(f"Failed to parse sheet {key}: {e}")

        return {0: nodes}

    def dst_reprocess(self, context: PipelineContext, dst:  List[DST]):
        pass

    def get_res(self, dst: DST):
        pass
