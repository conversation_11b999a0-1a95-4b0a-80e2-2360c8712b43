import hashlib
from typing import List, Dict, Iterator, Any

from collections import defaultdict

from modules.common import (
    build_media_map,
    build_root_dst,
    table_entity2html,
    build_dst_id,
    build_hyperlink_map,
)
from modules.entity.dst_entity import (
    FileType,
    DST,
    DSTType,
    DSTAttribute,
    BBox, PositionInfo,
)
from modules.entity.kdc_enttiy import Presentation, SlideContainerCategory, Slide, BlockType
from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.pipeline.context import PipelineContext


# ----------------------------- slide / block 迭代器 -----------------------------

def iter_slides(kdc_root: dict) -> Iterator[dict]:
    """只遍历普通幻灯片 (category == 'slides')"""
    for cont in kdc_root.get("slide_containers", []):
        if cont.get("category") == "slides":
            for slide in cont.get("slides", []):
                yield slide


async def iter_blocks(context:PipelineContext, slide: Slide, id2url: Dict[str, List[Any]], id2text: Dict[str, str]):
    """按出现顺序 yield 块信息（文本/图片/表格/公式/代码）"""
    for shape in slide.shape_tree:
        bbox = shape.bounding_box.dict() if shape.bounding_box else {}
        if shape.type == BlockType.textbox:
            text, level = "", 10
            for blk in shape.textbox.blocks or []:
                if blk.type != BlockType.para:
                    continue
                para = blk.para
                runs = "".join(r.text or "" for r in para.runs or [])
                list_str = (para.prop.list_string or "") if para.prop else ""
                level = para.prop.outline_level or 10 if para and para.prop else 10
                text += list_str + runs  # ← 不断累加
            block_type = "text"

            yield {
                "type": block_type,
                "level": level,
                "content": text,  # ← 整个 textbox 的文字
                "bbox": bbox,
            }

        # ---------- 组件 ----------
        elif shape.type == BlockType.component:
            comp = shape.component

            if comp.type.value == "image":
                yield {
                    "type": "image",
                    "level": 0,
                    "content": id2url.get(comp.media_id, ""),
                    "bbox": bbox,
                }
            elif comp.type.value == "formula":
                yield {
                    "type": "formula",
                    "level": 0,
                    "content": getattr(comp, "latex", ""),
                    "bbox": bbox,
                }
            elif comp.type.value == "code":
                yield {
                    "type": "code",
                    "level": 0,
                    "content": getattr(comp, "text", ""),
                    "bbox": bbox,
                }

        # ---------- 表格 ----------
        elif shape.type == BlockType.table:
            html = await table_entity2html(context, shape.table, id2url, id2text)
            yield {
                "type": "table",
                "level": 0,
                "content": html,
                "bbox": bbox,
            }


async def generate_presentation_dst(context: PipelineContext, kdc_data: List[dict]) -> Dict[int, List[DST]]:
    """
    通用的演示文稿DST生成函数，可被PPT和PPTX解析器复用
    
    :param context: 管道上下文
    :param kdc_data: KDC数据列表
    :return: 生成的DST字典
    """
    pres: Presentation = Presentation.model_validate(kdc_data[0]["doc"])
    id2url = await build_media_map(context, pres.medias or [])
    id2text = build_hyperlink_map(pres.hyperlinks)
    root = build_root_dst()
    nodes: List[DST] = [root]

    last_slide_parent: DST = root  # 记录最近一个 slide 的根节点
    order_counter: Dict[str, int] = defaultdict(int)
    page_no = -1

    for cont in pres.slide_containers:
        if cont.category != SlideContainerCategory.slides:
            continue
        for slide in cont.slides:
            page_no += 1
            slide_name = (slide.name or "").strip().replace("\r", "").replace("\n", "")
            current_slide_parent = None

            # 第一次循环：尝试找到匹配 slide.name 的 textbox，作为 slide 父节点
            pre_blocks = []
            async for blk in iter_blocks(context, slide, id2url, id2text):
                pre_blocks.append(blk)  # 先缓存所有 block
            for blk in pre_blocks:
                if blk["type"] not in ("title", "text"):
                    continue
                block_text = blk.get("content", "").strip().replace("\r", "").replace("\n", "")
                # 过滤没有文本的节点
                if not block_text:
                    continue

                if block_text == slide_name and block_text:
                    hash_val = hashlib.sha1(block_text.encode()).hexdigest()
                    bbox = {k: int(v) for k, v in blk["bbox"].items()}
                    parent = root
                    my_order = order_counter[parent.id]
                    order_counter[parent.id] += 1

                    current_slide_parent = DST(
                        id=build_dst_id(),
                        parent=parent.id,
                        order=my_order,
                        dst_type=DSTType("text"),
                        attributes=DSTAttribute(
                            level=1,
                            position=PositionInfo(bbox=BBox(**bbox)),
                            page=page_no,
                            hash=hash_val,
                        ),
                        content=[block_text],
                    )
                    nodes.append(current_slide_parent)
                    last_slide_parent = current_slide_parent
                    break

            # 第二次循环：挂载其他 block
            for blk in pre_blocks:
                img_pixel = None
                btype = blk["type"]
                if btype == "image":
                    content_text = blk.get("content", [""])[0] # 获取图片url
                    img_pixel = blk.get("content", [])[1]  # 获取图片像素
                else:
                    content_text = blk.get("content", "").strip()

                if not content_text and btype in ("title", "text"):
                    continue

                if current_slide_parent and content_text == slide_name:
                    continue  # 已作为 slide 的父节点，跳过

                content = [content_text]
                bbox = {k: int(v) for k, v in blk["bbox"].items()}
                hash_val = hashlib.sha1(content_text.encode()).hexdigest() if content_text else "0" * 32
                parent = current_slide_parent or last_slide_parent or root

                my_order = order_counter[parent.id]
                order_counter[parent.id] += 1

                node = DST(
                    id=build_dst_id(),
                    parent=parent.id,
                    order=my_order,
                    dst_type=DSTType(btype),
                    attributes=DSTAttribute(
                        level=10,
                        position=PositionInfo(bbox=BBox(**bbox)),
                        page=page_no,
                        hash=hash_val,
                    ),
                    content=content,
                    image_pixel=img_pixel,
                )
                nodes.append(node)

    return {0: nodes}


class PptParser(ParseTemplate):

    def __init__(self):
        super().__init__(FileType.PPT)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]) -> Dict[int, List[DST]]:
        return await generate_presentation_dst(context, kdc_data)

    # 其余接口保持空实现
    def dst_reprocess(self, context: PipelineContext, dst: List[DST]):
        pass

    def get_res(self, dst: DST):
        pass
