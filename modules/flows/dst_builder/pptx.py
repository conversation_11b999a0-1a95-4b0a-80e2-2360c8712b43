from typing import List, Dict

from modules.entity.dst_entity import FileType, DST
from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.pipeline.context import PipelineContext
from modules.flows.dst_builder.ppt import generate_presentation_dst


class PptxParser(ParseTemplate):

    def __init__(self):
        super().__init__(FileType.PPTX)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]) -> Dict[int, List[DST]]:
        return await generate_presentation_dst(context, kdc_data)

    # 其余接口保持空实现
    def dst_reprocess(self, context: PipelineContext, dst:  List[DST]):
        pass

    def get_res(self, dst: DST):
        pass
