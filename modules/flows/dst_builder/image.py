# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/8/6 14:04
from typing import List
from modules.entity.dst_entity import FileType, DST
from modules.flows.dst_builder.xls import ParseTemplate
from modules.pipeline.context import PipelineContext

class ImageParse(ParseTemplate):
    """
    KDC 图片解析，由于KDC不支持图片解析，因此此类仅作为占位符，兼容其他流程
    """
    def __init__(self):
        super().__init__(FileType.PNG)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]):
        pass

    def dst_reprocess(self, context: PipelineContext, dst:  List[DST]):
        pass

    def get_res(self, dst: DST):
        pass
