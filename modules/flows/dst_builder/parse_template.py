# coding: utf-8
# <AUTHOR> chenliang12
# @date    : 2025/4/16 16:19

from abc import ABC, abstractmethod

from typing import Optional, Union, List
import requests
import json

from commons.trace.tracer import trace_span
from modules.entity.dst_entity import FileType, DST
from modules.pipeline.context import PipelineContext
from modules.rpc.kdc_rpc import KDCRpc

from commons.logger.logger import request_id_context

def kdc_parser(
        company_id: str,
        kdc_file_url_or_bytes: Union[str, bytes],
        file_name: str,
        file_id: Optional[str] = None,
        file_type: Optional[str] = None,
        convert_options: Optional[dict] = None):
    kdc_format = "kdc"
    if not convert_options:
        convert_options = {}
    if file_type == "otl":
        convert_options["enable_upload_medias"] = True
    kdc_data = KDCRpc().get_content_by_url_or_file(
        company_id=company_id,
        file_url_or_bytes=kdc_file_url_or_bytes,
        format=kdc_format,
        filename=file_name,
        include_elements="all",
        request_id=request_id_context.get(),
        file_id=file_id,
        convert_options=convert_options
    )
    return kdc_data


class ParseTemplate(ABC):
    def __init__(self, file_type: FileType):
        self.file_type = file_type

    def run(self):
        pass

    # 暂时保留，在单元测试中使用
    def data_process(
            self,
            kdc_ks3_url: Optional[str] = None,
            company_id: Optional[str] = None,
            file_url_or_bytes: Union[str, bytes] = None,
            file_name: str = None,
            file_id: Optional[str] = None,
            file_type: Optional[str] = None,
            convert_options: Optional[dict] = None,
    ):
        if kdc_ks3_url:
            try:
                response = requests.get(kdc_ks3_url)
                response.raise_for_status()
                content_dict = response.text
                kdc_data = json.loads(content_dict)
                if not isinstance(kdc_data, list):
                    kdc_data = [kdc_data]
            except requests.exceptions.RequestException as e:
                raise ValueError(f"Failed to fetch content from {kdc_ks3_url}: {e}")
        else:
            kdc_data = kdc_parser(company_id, file_url_or_bytes, file_name, file_id, file_type, convert_options)
        return kdc_data

    @abstractmethod
    @trace_span
    async def dst_generate(self,  context: PipelineContext, kdc_data: List[dict]):
        pass

    @abstractmethod
    @trace_span
    def dst_reprocess(self, context: PipelineContext, dst:  List[DST]):
        pass

    @abstractmethod
    @trace_span
    def get_res(self, dst: DST):
        pass
