# coding: utf-8
from abc import ABC, abstractmethod
from typing import List, Dict, Optional

from commons.trace.tracer import trace_span
from modules.entity.dst_entity import DST
from modules.entity.chunk_entity import Chunk
from modules.pipeline.context import ChunkInfo, FileInfo
from modules.entity.kdc_enttiy import OutlineLevel


class ChunkTemplate(ABC):
    """
    Abstract base class for chunk processing logic.
    Each file format should implement its own chunking logic.
    """

    @abstractmethod
    def process_chunks(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        """
        Abstract method to process chunks from a list of DST objects.

        :param dst_list: List of DST objects
        :return: List of processed chunks

        Args:
            page_size:
        """
        pass

    def renumber_chunk_ids(self, chunks: List[Chunk]) -> List[Chunk]:
        """
        将输入的chunk列表中所有chunk的ID重新编号为从0开始递增的值。

        Args:
            chunks: Chunk对象列表

        Returns:
            重新编号后的Chunk对象列表
        """
        for i, chunk in enumerate(chunks):
            chunk.chunk_id = str(i)  # 假设chunk_id是字符串类型

        return chunks

    def merge_chunk(self, chunk_size: int, chunks: List[Chunk]) -> List[Chunk]:
        """
        合并chunks，根据level和content大小判断是否需要合并
        
        Args:
            chunk_size: chunk大小限制
            chunks: 待合并的chunk列表
            
        Returns:
            合并后的chunk列表
        """
        if not chunks or len(chunks) <= 1:
            return chunks
            
        merged_chunks = []
        i = 0
        
        while i < len(chunks):
            current_chunk = chunks[i]
            
            # 尝试与后续chunk合并
            while i + 1 < len(chunks):
                next_chunk = chunks[i + 1]
                
                # 检查合并条件
                if self._should_merge_chunks(current_chunk, next_chunk, chunk_size):
                    # 执行合并
                    current_chunk = self._merge_two_chunks(current_chunk, next_chunk)
                    i += 1  # 跳过已合并的chunk
                else:
                    break  # 不满足合并条件，停止与后续chunk的合并尝试
                    
            merged_chunks.append(current_chunk)
            i += 1
            
        return merged_chunks
    
    def _should_merge_chunks(self, chunk1: Chunk, chunk2: Chunk, chunk_size: int) -> bool:
        """
        判断两个chunk是否应该合并

        判断规则：
        1. 如果任一chunk是表格类型，不合并（保持表格独立性）
        2. 合并后内容不能超过chunk_size
        3. 基于文档结构层级(level)判断:
           - 如果chunk1末尾没有标题但chunk2开头有标题，不合并
           - 如果chunk1末尾有标题但chunk2开头没有标题，可以合并
           - 如果两者都没有标题，可以合并
           - 如果两者都有标题，则chunk1的标题级别必须小于等于chunk2的标题级别才能合并

        Args:
            chunk1: 第一个chunk
            chunk2: 第二个chunk
            chunk_size: chunk大小限制（字符数）

        Returns:
            bool: 是否应该合并这两个chunk
        """
        from modules.entity.chunk_entity import LabelType
        
        # 1. 检查是否包含表格类型的chunk - 表格应保持独立
        if chunk1.label == LabelType.TABLE or chunk2.label == LabelType.TABLE:
            return False
        
        # 2. 检查合并后内容大小是否超过限制
        if len(chunk1.content) + len(chunk2.content) >= chunk_size:
            return False

        # 3. 获取结构信息
        chunk1_last_level = self._get_last_non_text_level(chunk1)
        chunk2_first_level = self._get_first_non_text_level(chunk2)

        # 4. 根据结构层级判断是否可以合并

        # 情况1: chunk1没有标题，chunk2有标题 - 不合并
        # （避免将正文与下一个标题合并）
        if chunk1_last_level is None and chunk2_first_level is not None:
            return False

        # 情况2: chunk1有标题，chunk2没有标题 - 合并
        # （允许将标题与其后续正文合并）
        if chunk1_last_level is not None and chunk2_first_level is None:
            return True

        # 情况3: 两者都没有标题 - 合并
        # （两段纯正文可以合并）
        if chunk1_last_level is None and chunk2_first_level is None:
            return True

        # 情况4: 两者都有标题 - 检查层级关系
        # （只有当chunk1的标题级别小于等于chunk2的标题级别时才合并）
        return chunk1_last_level <= chunk2_first_level
    
    def _get_last_non_text_level(self, chunk: Chunk) -> Optional[int]:
        """
        获取chunk中最后一个level不为10的dst的level
        
        Args:
            chunk: 待检查的chunk
            
        Returns:
            最后一个非正文dst的level，如果没有则返回None
        """
        if not chunk.dsts:
            return None
            
        # 从后往前遍历
        for dst in reversed(chunk.dsts):
            if hasattr(dst.attributes, 'level') and dst.attributes.level != OutlineLevel.l10:
                return dst.attributes.level
                
        return None
    
    def _get_first_non_text_level(self, chunk: Chunk) -> Optional[int]:
        """
        获取chunk中第一个level不为10的dst的level
        
        Args:
            chunk: 待检查的chunk
            
        Returns:
            第一个非正文dst的level，如果没有则返回None
        """
        if not chunk.dsts:
            return None
            
        # 从前往后遍历
        for dst in chunk.dsts:
            if hasattr(dst.attributes, 'level') and dst.attributes.level != OutlineLevel.l10:
                return dst.attributes.level
                
        return None
    
    def _merge_two_chunks(self, chunk1: Chunk, chunk2: Chunk) -> Chunk:
        """
        合并两个chunk
        
        Args:
            chunk1: 第一个chunk
            chunk2: 第二个chunk
            
        Returns:
            合并后的新chunk
        """
        # 保留chunk1的完整content（包含大纲信息）
        new_content = chunk1.content
        
        # 获取chunk1中已有的dst ids
        chunk1_dst_ids = {dst.id for dst in (chunk1.dsts or [])}
        
        # 处理chunk2中的dsts
        if chunk2.dsts:
            for dst in chunk2.dsts:
                dst_content = self._get_dst_content(dst)
                
                if dst.id in chunk1_dst_ids:
                    # 重复的dst，找出在chunk2中但不在chunk1.content中的剩余内容
                    remaining_content = self._find_remaining_content(dst_content, chunk1.content)
                    if remaining_content:
                        new_content += " " + remaining_content
                else:
                    # 新的dst，直接添加完整内容
                    if dst_content.strip():
                        new_content += " " + dst_content
        
        # 合并dsts列表并去重，保持顺序
        merged_dsts = []
        seen_dst_ids = set()
        
        # 先添加chunk1的dsts
        if chunk1.dsts:
            for dst in chunk1.dsts:
                if dst.id not in seen_dst_ids:
                    merged_dsts.append(dst)
                    seen_dst_ids.add(dst.id)
        
        # 再添加chunk2的dsts，跳过重复的
        if chunk2.dsts:
            for dst in chunk2.dsts:
                if dst.id not in seen_dst_ids:
                    merged_dsts.append(dst)
                    seen_dst_ids.add(dst.id)
        
        # 合并block列表并去重，保持顺序
        merged_blocks = []
        seen_blocks = set()
        
        # 先添加chunk1的blocks
        if chunk1.block:
            for block_id in chunk1.block:
                if block_id not in seen_blocks:
                    merged_blocks.append(block_id)
                    seen_blocks.add(block_id)
        
        # 再添加chunk2的blocks，跳过重复的
        if chunk2.block:
            for block_id in chunk2.block:
                if block_id not in seen_blocks:
                    merged_blocks.append(block_id)
                    seen_blocks.add(block_id)
        
        # 合并page_num并去重排序
        merged_page_nums = list(set((chunk1.page_num or []) + (chunk2.page_num or [])))
        merged_page_nums.sort()
        
        # 创建新的合并chunk
        merged_chunk = Chunk(
            chunk_id=chunk1.chunk_id,  # 使用第一个chunk的ID
            page_size=chunk1.page_size,
            content=new_content.strip(),
            label=chunk1.label,  # 使用第一个chunk的label
            content_embedding=chunk1.content_embedding,
            page_num=merged_page_nums,
            block=merged_blocks,
            dsts=merged_dsts,
            pre_chunk=chunk1.pre_chunk,
            next_chunk=chunk2.next_chunk,  # 使用第二个chunk的next_chunk
            mark=chunk1.mark or chunk2.mark  # 优先使用第一个chunk的mark
        )
        
        return merged_chunk
    
    def _get_dst_content(self, dst: DST) -> str:
        """
        获取dst的内容
        
        Args:
            dst: DST对象
            
        Returns:
            dst的内容字符串
        """
        if hasattr(dst, 'dst_type'):
            from modules.entity.dst_entity import DSTType
            
            if dst.dst_type == DSTType.IMAGE and len(dst.content) > 1:
                # 图片类型且有描述文本，使用描述文本
                return dst.content[1]
            else:
                # 其他类型，拼接所有content
                return "".join(dst.content)
        else:
            # 没有dst_type属性，默认拼接所有content
            return "".join(dst.content)

    def _find_remaining_content(self, dst_content: str, existing_content: str) -> str:
        """
        找出dst_content中不在existing_content中的剩余内容，更高效且更准确

        Args:
            dst_content: dst的完整内容
            existing_content: 已存在的内容

        Returns:
            剩余的内容，如果没有则返回空字符串
        """
        dst_content = dst_content.strip()

        # 如果dst_content完全包含在existing_content中，返回空字符串
        if dst_content in existing_content:
            return ""

        # 尝试从existing_content的末尾查找匹配前缀
        # 这样可以确保我们找到的是连续的内容
        max_match_len = 0
        for i in range(min(len(dst_content), len(existing_content)), 0, -1):
            if existing_content.endswith(dst_content[:i]):
                max_match_len = i
                break

        # 返回剩余的内容
        if max_match_len > 0:
            return dst_content[max_match_len:].strip()
        else:
            # 如果没有找到匹配的前缀，返回完整内容
            return dst_content
