# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/8/6 15:45
from commons.trace.tracer import trace_span
from modules.entity.chunk_entity import Chunk
from modules.flows.chunk.chunk_template import ChunkTemplate

from typing import List
from modules.entity.dst_entity import DST
from modules.flows.chunk.common import build_dst_tree, build_chunk
from modules.pipeline.context import ChunkInfo


class ImageChunk(ChunkTemplate):
    @trace_span
    def process_chunks(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        """
        图片chunk采用通用的chunk处理
        :param dst_list: dst_list
        :param page_size: page_size
        :param chunks_info: chunks_info
        :return: List[Chunk]
        """
        # 根据dst的parent_id构建树结构，图片只有root->image一层
        tree = build_dst_tree(
            dst_list)
        chunks = []
        # 通过dst树构建chunk，图片只会生成1个chunk
        chunk, merge_chunk = build_chunk(tree, page_size, -1, "", "", chunks_info)
        if chunk:
            chunks.extend(chunk)
        if merge_chunk:
            chunks.append(merge_chunk)

        return chunks
