# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 16:29
import asyncio
import logging
import re
import time
from enum import Enum
from conf import ConfCoroutine
from commons.trace.tracer import async_trace_span
from modules.entity.chunk_entity import LabelType
from modules.pipeline.base import <PERSON><PERSON>ine<PERSON>andler
from modules.flows.chunk.doc import <PERSON><PERSON>hunk
from modules.flows.chunk.docx import DocxChunk
from modules.flows.chunk.pdf import PDFChunk
from modules.flows.chunk.ppt import PPTChunk
from modules.flows.chunk.pptx import PPTXChunk
from modules.flows.chunk.otl import OtlChunk
from modules.flows.chunk.txt import TxtChunk
from modules.flows.chunk.et import ETChunk
from modules.flows.chunk.image import ImageChunk
from modules.flows.chunk.chunk_template import ChunkTemplate
from modules.pipeline.context import PipelineContext, FileType
from commons.thread.multicoroutine import MultiCoroutine
from modules.flows.filters.table_processing import TableProcessorContext, TitleProcessor
from modules.rpc.ocr_model import OCRModelClient


class ChunkFactory:
    _chunk_processors = {
        FileType.OTL: OtlChunk,
        FileType.PDF: PDFChunk,
        FileType.DOC: DocChunk,
        FileType.DOCX: DocxChunk,
        FileType.TXT: TxtChunk,
        FileType.PPT: PPTChunk,
        FileType.PPTX: PPTXChunk,
        FileType.XLSX: ETChunk,
        FileType.XLS: ETChunk,
        FileType.JPG: ImageChunk,
        FileType.JPEG: ImageChunk,
        FileType.PNG: ImageChunk,
        FileType.WEBP: ImageChunk,
    }

    @staticmethod
    def get_chunk_processor(file_type: FileType) -> ChunkTemplate:
        chunk_class = ChunkFactory._chunk_processors.get(file_type)
        if not chunk_class:
            raise ValueError(f"No parser available for file type: {file_type}")
        return chunk_class()


CHUNK_TASK_SIZE = 4


class PoolKey(str, Enum):
    pool1_chunk_key = "pool1_chunk"
    pool1_fake_title_key = "pool1_fake_title"
    pool1_keywords_key = "pool1_keywords"
    pool1_summary_key = "pool1_summary"

    pool2_chunk_content_embedding_key = "pool2_chunk_content_embedding"
    pool2_image_describe_key = "pool2_image_describe"

    pool3_image_describe_embedding_key = "pool3_image_describe_embedding"


async def _chunk_content_embedding_task(chunk_list):
    client = OCRModelClient()
    cleaned_contents = []

    for chunk in chunk_list:
        if chunk.label == LabelType.TABLE:
            content = chunk.content.replace("</tr>", "\n")
            clean_text = ' '.join(re.sub(r'<.*?>', ' ', content or '').split())
        else:
            clean_text = chunk.content
        cleaned_contents.append(clean_text)

    # 并发调用单条 embedding 接口
    embedding_futures = [client.request_text_embedding(text) for text in cleaned_contents]
    content_embeddings = await asyncio.gather(*embedding_futures, return_exceptions=True)

    # 赋值回 chunk
    for index, (c, emb) in enumerate(zip(chunk_list, content_embeddings)):
        if isinstance(emb, Exception):
            # 记录错误
            logging.error(f"Embedding failed for chunk index {index}: {emb}")
            c.content_embedding = []
        else:
            c.content_embedding = emb


async def _chunk_task(context: PipelineContext):
    chunks = context.chunks
    if len(chunks) > 0:
        context.business_log.info(f"chunk len: {len(chunks)}")
        pool_chunk = MultiCoroutine()
        chunk_batch = [chunks[i:i + CHUNK_TASK_SIZE] for i in range(0, len(chunks), CHUNK_TASK_SIZE)]
        for chunk_list_index, chunk_list in enumerate(chunk_batch):
            if context.embed_enabled:
                # 分段向量任务
                pool_chunk.add_task(f"{PoolKey.pool2_chunk_content_embedding_key}_{chunk_list_index}",
                                    _chunk_content_embedding_task(chunk_list))
        pool_chunk_res = await pool_chunk.run_limit(ConfCoroutine.chunk_limit)
        for key, item in pool_chunk_res.items():
            if isinstance(item, Exception):
                context.business_log.error(f"pool_chunk task {key} failed, err: {item}")
                continue
    else:
        context.business_log.warning(f"chunk content is empty, file_id: {context.file_info.file_id}")
    return chunks


class ChunkNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            pool = MultiCoroutine()
            _t0 = time.perf_counter()
            chunk_processor = ChunkFactory.get_chunk_processor(context.file_info.file_type)
            logging.debug(f"ChunkNode.process: {len(context.dst)}, ")
            chunks = chunk_processor.process_chunks(context.dst, context.file_info.page_size,
                                                            context.chunks_info)
            chunks = chunk_processor.merge_chunk(context.chunks_info.chunk_size, chunks)
            context.chunks = chunk_processor.renumber_chunk_ids(chunks)
            context.business_log.info(f"ChunkNode.process took {time.perf_counter() - _t0:.2f} seconds")
            if context.chunks is None:
                raise ValueError("Failed to generate chunks")
            if context.file_info.file_type == FileType.OTL and context.chunks and len(context.chunks) > 0:
                context.file_info.page_size = context.chunks[0].page_size
            context.handler_results[self.name] = context.chunks
            pool.add_task(PoolKey.pool1_chunk_key, _chunk_task(context))
            pool_res = await pool.run()
            context.business_log.info(f"ChunkNode.embind took {time.perf_counter() - _t0:.2f} seconds")

            ## 添加表格描述title
            table_processor = TableProcessorContext(TitleProcessor())
            context.chunks = table_processor.process_chunks(context.chunks)

            context.handler_results[self.name] = context.chunks
            # res = ParseRes(chunks=context.chunks, page_size=context.file_info.page_size,
            #                word_count=context.file_info.word_count,
            #                width=context.file_info.width, height=context.file_info.height,
            #                is_scan=context.file_info.is_scan, image=context.image, rotate_page=context.file_info.rotate_page,
            #                parse_version=context.parse_version)
            # await callback_parse_background(res, self.name, context.token, context.need_callback,
            #                                 context.return_ks3_url, context.callback_url)
            return context
        except Exception as e:
            context.business_log.error(f"Error in ChunkNode.process: {e}")
            raise e
