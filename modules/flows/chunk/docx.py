# coding: utf-8
from commons.trace.tracer import trace_span
from modules.entity.chunk_entity import Chunk
from modules.flows.chunk.chunk_template import ChunkTemplate
from modules.pipeline.context import ChunkInfo

from typing import List
from modules.entity.dst_entity import DST
from modules.flows.chunk.common import build_dst_tree, build_chunk


class DocxChunk(ChunkTemplate):
    """
    Implementation of ChunkTemplate for DOCX files.
    Handles the logic for processing chunks specific to DOCX format.
    """

    @trace_span
    def process_chunks(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        tree = build_dst_tree(
            dst_list)
        # print("Tree structure:", tree)
        chunks = []
        chunk, merge_chunk = build_chunk(tree, page_size,-1,"", "", chunks_info)
        if chunk:
            chunks.extend(chunk)
        if merge_chunk:
            chunks.append(merge_chunk)
        return chunks
