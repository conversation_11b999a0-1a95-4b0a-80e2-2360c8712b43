import base64
import logging
import uuid
import copy
from typing import List, Union

import requests

from commons.db.storedao import StoreDao
from commons.trace.tracer import async_trace_span
from modules.entity import parse_entity
from modules.entity.parse_entity import ImageType
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import <PERSON>peline<PERSON>ontext
from PIL import Image
from io import BytesIO
import re

from services.datamodel import FileType

MAX_IMAGE_PIXELS = 10000000
URL_EXPIRATION_TIME = 1492073594

class Image2PDFNode(PipelineHandler):
    """图片转PDF 数据获取处理节点"""

    def __init__(self, name: str = "image_to_pdf_processor"):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        """
        处理流程：
        1. 图片压缩
        2. 上传图片并生成 URL
        3. 转换为 PDF 并更新上下文
        """
        try:
            file_url_or_bytes = await self._fetch_file_bytes(context.kdc_input.file_url_or_bytes)
            # img_bytes = image_compress(file_url_or_bytes)
            img_bytes = file_url_or_bytes
            img_name = f"{uuid.uuid4().hex}_{context.file_info.file_name}"
            img_url = self._upload_and_generate_url(img_name, img_bytes)
            pdf_name, pdf_url = self._to_pdf(img_bytes)

            # 更新上下文
            context.image = [parse_entity.Image(page_num=0, url=img_url, image_type=ImageType.INPUT_IMAGE)]
            context.raw_file_info = copy.deepcopy(context.file_info)
            context.raw_file_info.page_size=1
            context.kdc_input.file_url_or_bytes = pdf_url
            context.file_info.file_type = FileType.PDF
            context.file_info.file_name = pdf_name

            return context

        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to fetch file: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Image2PDFNode process failed: {str(e)}")
            raise

    async def _fetch_file_bytes(self, file_url_or_bytes):
        """获取文件的字节内容"""
        if isinstance(file_url_or_bytes, str):
            response = requests.get(file_url_or_bytes)
            response.raise_for_status()
            return response.content
        return file_url_or_bytes

    def _upload_and_generate_url(self, file_name, file_bytes):
        # todo : 后续ks3上传的文件夹需要统一设置一下
        """上传文件并生成 URL"""
        file_path = f"aidocs_dst_server/{file_name}"
        if StoreDao().upload_from_bytes(file_path, file_bytes):
            url = StoreDao().generate_url(file_path, URL_EXPIRATION_TIME)
            return re.sub(r'-internal', '', url, count=1)
        return ""

    def _to_pdf(self, image_bytes):
        """将图片转换为 PDF 并上传"""
        image = Image.open(BytesIO(image_bytes))
        pdf_bytesio = BytesIO()

        # 如果图片像素小于阈值，直接保存为 PDF；否则按比例缩放
        if image.width * image.height < MAX_IMAGE_PIXELS:
            image.save(pdf_bytesio, "PDF")
        else:
            scale = (MAX_IMAGE_PIXELS / (image.width * image.height)) ** 0.5
            image = image.resize((int(image.width * scale), int(image.height * scale)))
            image.save(pdf_bytesio, "PDF")

        file_bytes = pdf_bytesio.getvalue()
        pdf_name = f"{uuid.uuid4().hex}.pdf"
        pdf_url = self._upload_and_generate_url(f"temp/{pdf_name}", file_bytes)
        return pdf_name, pdf_url

    def images_to_pdf(self,image_list: List[Union[str, bytes]]):
        """
        Converts a list of image URLs or Base64 strings into a single PDF.

        :param image_list: List of image URLs or Base64 strings.
        :param output_pdf_path: Path to save the output PDF.
        """
        images = []
        for image_data in image_list:
            try:
                if isinstance(image_data, str):
                    if image_data.startswith("http://") or image_data.startswith("https://"):
                        # Fetch image from URL
                        response = requests.get(image_data)
                        response.raise_for_status()
                        image = Image.open(BytesIO(response.content))
                    else:
                        # Decode Base64 string
                        image_bytes = base64.b64decode(image_data)
                        image = Image.open(BytesIO(image_bytes))
                elif isinstance(image_data, bytes):
                    # Handle raw bytes
                    image = Image.open(BytesIO(image_data))
                else:
                    raise ValueError("Unsupported image data type.")

                # Convert image to RGB mode if not already
                if image.mode != "RGB":
                    image = image.convert("RGB")
                images.append(image)
            except Exception as e:
                print(f"Error processing image: {e}")

        if not images:
            raise ValueError("No valid images to convert to PDF.")
        pdf_bytesio = BytesIO()
        try:
            # Save all images as a single PDF
            images[0].save(pdf_bytesio, format="PDF", save_all=True, append_images=images[1:])
        except Exception as e:
            logging.error(f"Error saving images to PDF: {e}")
            raise

        pdf_bytes = pdf_bytesio.getvalue()
        pdf_name = f"{uuid.uuid4().hex}.pdf"
        pdf_url = self._upload_and_generate_url(f"temp/{pdf_name}", pdf_bytes)
        return pdf_name, pdf_url
