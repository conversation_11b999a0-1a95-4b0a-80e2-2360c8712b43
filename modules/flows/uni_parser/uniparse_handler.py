# Author: linqi
# Date: 2025/7/15
# Time: 19:14
import asyncio

from commons.trace.tracer import async_trace_span
from conf import ConfHand<PERSON><PERSON>ame
from modules.entity.pre_check_entity import ParserName
from modules.flows.uni_parser.kdc import KDCParser
from modules.flows.uni_parser.mupdf import M<PERSON>dfParser
from modules.flows.uni_parser.image import ImageParser
from modules.flows.uni_parser.uniparse_template import UniParseTemplate
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext


class ParserToolFactory:
    _parsers = {
        ParserName.KdcParser: KDCParser,
        ParserName.MuPdfParser: MUPdfParser,
        ParserName.ImageParser: ImageParser
    }

    @staticmethod
    def get_parser(parse_name: ParserName) -> UniParseTemplate:
        parser_class = ParserToolFactory._parsers.get(parse_name)
        if not parser_class:
            raise ValueError(f"No parser available for parser name: {parse_name}")
        return parser_class()


class UniParseNode(PipelineHandler):
    """通用文件解析节点"""

    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        per_check_result = context.handler_results[ConfHandlerName.pre_check_handler]

        # Create tasks for concurrent processing
        tasks = [
            parser.uniparse_process(context, list(parser_config.processing_pages), parser_config.is_all)
            for parser_config in per_check_result
            for parser in [ParserToolFactory.get_parser(parser_config.parser_name)]
        ]

        # Run all tasks concurrently
        await asyncio.gather(*tasks)

        return context