from typing import List, Tuple, Optional

from pydantic import BaseModel


# class MupdfBbox(BaseModel):
#     x0: float
#     y0: float
#     x1: float
#     y1: float
#
#     def __init__(self, x0: float, y0: float, x1: float, y1: float):
#         super().__init__(x0=x0, y0=y0, x1=x1, y1=y1)



class Span(BaseModel):
    size: Optional[float] = None
    flags: Optional[int] = None
    bidi: Optional[int] = None
    char_flags: Optional[int] = None
    font: Optional[str] = None
    color: Optional[int] = None
    alpha: Optional[int] = None
    ascender: Optional[float] = None
    descender: Optional[float] = None
    text: Optional[str] = ""
    origin: Optional[List[float]] = None
    bbox: Optional[List[float]] = None


class MupdfLine(BaseModel):
    spans: Optional[List[Span]] = None
    wmode: Optional[int] = None
    dir: Optional[List[float]] = None
    bbox: Optional[List[float]] = None


def is_horizontally_to_the_right(dir: Tuple[float, float], tolerance: float = 1e-6) -> bool:
    """
    Check if the direction vector is approximately horizontal to the right.

    :param dir: A tuple [x, y] representing the direction vector.
    :param tolerance: A small value to account for floating-point precision issues.
    :return: True if the direction is approximately horizontal to the right, False otherwise.
    """
    if len(dir) != 2:
        raise ValueError("Input must be a tuple of two floats.")

    return abs(dir[0] - 1.0) <= tolerance and abs(dir[1]) <= tolerance

