# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/8/5 19:26
import hashlib
from modules.flows.uni_parser.uniparse_template import UniParseTemplate
from modules.pipeline.context import PipelineContext
from typing import Union, List, Dict
from commons.trace.tracer import async_trace_span
from modules.common import build_root_dst, build_dst_id
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, PositionInfo
from modules.entity.parse_entity import Image as ParseEntityImage
from modules.entity.parse_entity import ImageType
from modules.common import upload_image
import requests
from PIL import Image
from io import BytesIO

async def parse_image_to_dst(image_bytes: bytes, context: PipelineContext) -> Dict[int, List[DST]]:
    """
    解析图片，构建DST结构
    :param image_bytes: 图片bytes
    :param context: 管道上下文
    :return: DST对象Dict
    """
    # 图片尺寸
    image = Image.open(BytesIO(image_bytes))
    image_pixel = list(image.size) # [width, height]
    image.close()

    # 图片url
    image_url = upload_image(image_bytes)
    if image_url is None or len(image_url) == 0:
        # 图片上传失败则流程终止
        context.business_log.error("Image upload failed, image_url is empty")
        raise ValueError("Image upload failed, image_url is empty")
    # 图片hash
    image_hash = hashlib.sha1(image_bytes).hexdigest()

    # 文件信息
    context.image = [ParseEntityImage(page_num=0, url=image_url, image_type=ImageType.INPUT_IMAGE)]
    context.file_info.page_size = 1  # 图片只有1页
    context.file_info.width = image_pixel[0]  # 图片宽度
    context.file_info.height = image_pixel[1]  # 图片高度

    root_dst = build_root_dst()
    dst_list = [root_dst]
    page = 0  # 图片只有1页，页码为0
    image_dst = DST(
        id=build_dst_id(),
        parent=root_dst.id,
        order=1,    # 默认排序
        dst_type=DSTType.IMAGE,
        attributes=DSTAttribute(
            position=PositionInfo(bbox=None, block_coordinate=None),    # 图片无位置信息
            level=0,    # 无大纲
            page=page,
            hash=image_hash
        ),
        content=[image_url],
        image_pixel=image_pixel
    )
    dst_list.append(image_dst)
    return {page: dst_list}


def _load_image(file_url_or_bytes: Union[str, bytes]) -> bytes:
    """
    下载图片文件

    :param file_url_or_bytes: 图片文件的URL或二进制数据
    :return: 图片bytes
    """
    res = None
    if isinstance(file_url_or_bytes, str):
        response = requests.get(file_url_or_bytes)
        response.raise_for_status()
        res = response.content
    elif isinstance(file_url_or_bytes, bytes):
        res = file_url_or_bytes
    else:
        raise ValueError("无效的文件格式，需要URL字符串或二进制数据")

    return res


class ImageParser(UniParseTemplate):

    @async_trace_span
    async def uniparse_process(self, context: PipelineContext, pages: Union[List[int], str], is_all: bool) -> PipelineContext:
        """
        解析图片文件，提取文本，构建DST结构

        :param context: 管道上下文
        :param pages: 图片只有1页
        :return: 更新后的上下文
        """
        image_bytes = _load_image(context.kdc_input.file_url_or_bytes)

        # 解析指定页面，生成DST列表
        res = await parse_image_to_dst(image_bytes, context)
        for page_num, dst_list in res.items():
            context.update_multiple_parse_dsts_unsafe(page_num, dst_list)

        return context

