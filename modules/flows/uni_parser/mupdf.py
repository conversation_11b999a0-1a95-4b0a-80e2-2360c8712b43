# Author: linqi
# Date: 2025/7/15
# Time: 19:10

import hashlib
import base64
import io
import logging
from io import BytesIO
from PIL import Image
import fitz
from typing import List, Union, Dict

from modules.entity.dst_entity import DST, DSTType, DSTAttribute, PositionInfo, BBox
from modules.common import _upload_image
from modules.common import open_pdf
from modules.flows.uni_parser.mupdf_entity import Span, is_horizontally_to_the_right
from modules.flows.uni_parser.uniparse_template import UniParseTemplate
from modules.pipeline.context import PipelineContext
from commons.trace.tracer import async_trace_span
from modules.common import build_root_dst, build_dst_id

min_space = 5


async def parse_pages_to_dst(pdf_document: fitz.Document, pages: List[int]) -> Dict:
    """
    解析PDF文件中指定页码的内容，提取文本和图片，并按照页面位置顺序构建DST结构
    使用PDF目录结构为文本块设置合适的层级

    :param pdf_document: 已打开的PDF文档对象
    :param pages: 需要处理的页码列表(从1开始计数)
    :return: DST对象列表
    """
    res = {}
    # 创建根节点
    root_dst = build_root_dst()

    # 获取并处理PDF的目录结构
    toc_dict = _build_toc_dict(pdf_document.get_toc())

    # 遍历指定的页面
    for page_num in pages:
        dst_list = [root_dst]
        pdf_page = pdf_document.load_page(page_num)

        # 处理当前页面，生成DST对象列表
        page_dst_list = await _process_single_page(
            pdf_page, page_num, root_dst, toc_dict, pdf_document
        )
        dst_list.extend(page_dst_list)
        res[page_num] = dst_list

    return res


def _build_toc_dict(toc) -> Dict:
    """
    处理目录信息，按页码组织

    :param toc: PDF目录结构
    :return: {页码: [(level, title), ...]}
    """
    toc_dict = {}
    for item in toc:
        level, title, page = item[:3]
        if page not in toc_dict:
            toc_dict[page] = []
        toc_dict[page].append((level, title))
    return toc_dict


async def _process_single_page(
        pdf_page, page_num: int, root_dst, toc_dict: Dict, pdf_document: fitz.Document
) -> List:
    """
    处理单个页面，提取文本和图片并创建DST对象

    :param pdf_page: PDF页面对象
    :param page_num: 页码
    :param root_dst: 根DST对象
    :param toc_dict: 目录字典
    :param pdf_document: PDF文档对象
    :return: DST对象列表
    """
    order_counter = 0  # 每页重置顺序计数
    dst_list = []
    try:
        # 获取当前页面的目录条目
        page_toc_entries = toc_dict.get(page_num + 1, [])

        # 1. 提取并处理文本段落
        text_elements = _extract_text_paragraphs(pdf_page, page_toc_entries)

        # 2. 提取图片信息
        image_elements = _extract_images_info(pdf_page)

        # 3. 合并所有页面元素并按位置排序
        page_elements = text_elements + image_elements
        page_elements.sort(key=lambda x: x["y_pos"])

        # 4. 按排序顺序创建DST对象
        for element in page_elements:
            if element["type"] == "text":
                text_dst = _create_text_dst(element, page_num, root_dst, order_counter)
                dst_list.append(text_dst)
            elif element["type"] == "image":
                image_dst = await _create_image_dst(element, page_num, root_dst, order_counter, pdf_document)
                dst_list.append(image_dst)
            elif element["type"] == "table":
                table_dst = _create_table_dst(element, page_num, root_dst, order_counter)
                dst_list.append(table_dst)

            elif element["type"] == "wireless_table":
                text_dst = _create_text_dst(element, page_num, root_dst, order_counter)
                text_dst.dst_type = DSTType.TABLE
                text_dst.table_mark = "wireless_table"
                dst_list.append(text_dst)
            order_counter += 1
    except Exception as e:
        logging.error(f"Error processing page {page_num + 1}: {e}")
        raise e
    return dst_list


def _extract_text_paragraphs(pdf_page, page_toc_entries) -> List[Dict]:
    """
    提取页面文本并整合为段落

    :param pdf_page: PDF页面对象
    :param page_toc_entries: 当前页面的目录条目
    :return: 文本段落元素列表
    """
    # 1. 提取原始文本行
    original_lines = _extract_original_text_lines(pdf_page)

    # 2. 合并同一行内容
    merged_lines = _merge_same_lines(original_lines)

    # 3. 将行整合为段落
    paragraphs = _group_lines_to_paragraphs(merged_lines)

    # 4. 转换为页面元素格式
    text_elements = []
    for paragraph in paragraphs:
        # 计算段落的边界框
        para_bbox = _calculate_paragraph_bbox(paragraph["lines"])

        # 确定文本的层级
        text_level = _determine_text_level(paragraph["text"], page_toc_entries)

        # 添加段落信息到页面元素列表

        text_elements.append({
            "type": paragraph["type"],  # 段落类型，默认为"text"
            "bbox": para_bbox,
            "y_pos": para_bbox[1],  # y坐标用于排序
            "text": paragraph["text"] + "\n",
            "font_size": paragraph["font_size"],
            "is_bold": paragraph["is_bold"],
            "level": text_level
        })

    return text_elements


def build_span_line(mupdf_spans_body: List[dict], line_bbox: List[float]) -> List[Dict]:
    mupdf_spans = [Span.model_validate(mupdf_span) for mupdf_span in mupdf_spans_body]

    lines = []
    current_line = {
        "text": "",
        "bbox": None,
        "font_size": -1,
        "is_bold": False
    }

    for span in mupdf_spans:
        span_bbox = span.bbox
        span_text = span.text
        span_font_size = span.size
        span_is_bold = bool(span.flags & 16)
        span_text = span_text.replace("\xa0", " ")  # 替换换行符为空格
        if len(span_text) != len(span_text.rstrip()) and len(span_text) - len(span_text.rstrip()) > min_space:
            span_bbox[2] = span_bbox[0] + (span_bbox[2] - span_bbox[0]) * (len(span_text.rstrip()) / len(span_text))
            span_text = span_text.rstrip()  # 去除末尾空格
        if len(span_text) != len(span_text.lstrip()) and len(span_text) - len(span_text.lstrip()) > min_space:
            span_bbox[0] = span_bbox[0] + (span_bbox[2] - span_bbox[0]) * (len(span_text.lstrip()) / len(span_text))
            span_text = span_text.lstrip()

        if span_text.rstrip() == "":
            continue
        if current_line["bbox"] is None:
            # Initialize the current line with the first span
            current_line["text"] = span_text
            current_line["bbox"] = span_bbox
            current_line["font_size"] = span_font_size
            current_line["is_bold"] = span_is_bold
        else:
            # Check if the current span is adjacent to the previous one
            if abs(current_line["bbox"][2] - span_bbox[0]) <= span_font_size * 0.5:  # Allow small gap
                # Merge the span into the current line
                current_line["text"] += span_text
                current_line["bbox"][2] = max(current_line["bbox"][2], span_bbox[2])  # Update right boundary
                current_line["bbox"][3] = max(current_line["bbox"][3], span_bbox[3])  # Update bottom boundary
            else:
                # Finalize the current line and start a new one
                if current_line["text"].strip() and current_line["text"] != "\xa0":
                    lines.append(current_line)
                current_line = {
                    "text": span_text,
                    "bbox": span_bbox,
                    "font_size": span_font_size,
                    "is_bold": span_is_bold
                }

    # Add the last line if it exists
    if current_line["text"].strip() and current_line["text"] != "\xa0":
        lines.append(current_line)

    for line in lines:
        line["bbox"][1] = line_bbox[1]
        line["bbox"][3] = line_bbox[3]

    return lines


def _extract_original_text_lines(pdf_page) -> List[Dict]:
    """
    收集PDF页面中的所有原始文本行信息

    :param pdf_page: PDF页面对象
    :return: 原始行信息列表
    """
    original_lines = []
    page_dict = pdf_page.get_text("dict")
    before_table = None
    before_line = []
    for block in page_dict.get("blocks", []):
        if "lines" not in block:
            continue

        block_lines = []
        line_bbox = None
        for line in block["lines"]:
            if line_bbox is None:
                line_bbox = line["bbox"]
            else:
                # 如果行的bbox与上一个行的bbox相差较大，则认为是新的一行
                if abs(line["bbox"][1] - line_bbox[1]) > line["spans"][0]["size"]:
                    line_bbox = line["bbox"]
            # 收集行中所有span的文本
            if line["dir"]:
                if not is_horizontally_to_the_right(line["dir"]):
                    continue
            temp_lines = build_span_line(line["spans"], line_bbox)

            if temp_lines:
                block_lines.extend(temp_lines)
            # 保存原始行信息

        if len(block_lines) == 0:
            continue
        block_lines = _merge_same_lines(block_lines)
        rows = get_rows(block_lines)
        # 判断是否有多行
        if len(rows) > 1:
            is_table, table_lines, had_excess_text, had_merge = _is_grid_structure_v1(block_lines, before_table)
            before_table = None
            if is_table:
                if had_merge:
                    original_lines = original_lines[:-1]  # 删除上一个表格行
                block_lines = table_lines
                if had_excess_text:
                    before_table = table_lines[-1]
        else:
            if len(before_line) > 0:
                temp_lines = before_line + block_lines
                is_table, table_lines, _, _ = _is_grid_structure_v1(temp_lines, before_table)
                before_table = None
                if is_table:
                    original_lines = original_lines[:-len(before_line)]
                    before_table = table_lines[-1]
                    block_lines = table_lines
            elif before_table:
                # 如果前一行是表格行，且当前行是起始行，则计算是否合并
                if block_lines[0]["font_size"] > before_table["font_size"] or (
                        block_lines[0]["is_bold"] and not before_table["is_bold"]):
                    before_table = None
                else:
                    is_table, table_lines, had_excess_text, had_merge = _is_grid_structure_v1(block_lines, before_table)
                    before_table = None
                    if is_table:
                        if had_merge:
                            original_lines = original_lines[:-1]
                        block_lines = table_lines
                        if had_excess_text:
                            before_table = table_lines[-1]
        if not before_table:
            before_line = block_lines
        else:
            before_line = []
        original_lines.extend(block_lines)
    return original_lines


def check_if_table(sorted_rows: List[Dict], all_columns: List[float]) -> bool:
    all_columns.sort(key=lambda x: x[0])

    # Compare each range with the next one
    for i in range(len(all_columns) - 1):
        if all_columns[i][1] > all_columns[i + 1][
            0]:  # Check if the end of one range overlaps with the start of the next
            return False

    for _, row_lines in sorted_rows:
        current_columns = [line["bbox"][0] for line in row_lines]  # Use x0 of bbox for current columns
        aligned_columns = 0

        # Check alignment with column ranges
        for col in current_columns:
            for x1, x2 in all_columns:
                # Check if the column is within 10% of the width of the range
                if (x1 - abs((x2 - x1) * 0.1)) <= col <= (x2 + abs((x2 - x1) * 0.1)):
                    aligned_columns += 1
                    break
            # if any((x1 - abs((x2 - x1) * 0.1)) <= col <= (x2 + abs((x2 - x1) * 0.1)) for x1, x2 in
            #        all_columns):
            #     aligned_columns += 1

        # Ensure at least 80% alignment
        if aligned_columns < len(current_columns) * 0.8:
            return False
    return True


def table_merge(before_table: dict, sorted_rows: List[Dict], all_columns: List[float]) -> Dict:
    # Merge the HTML content of the two tables
    merged_html_table = before_table["text"].replace("</table>", "")  # Remove the closing tag of the previous table
    merged_html_table += generate_html_table_with_missing_cells(sorted_rows, all_columns)  # Add the new table rows
    merged_html_table += "</table>"  # Re-add the closing tag

    # Update the bounding box to encompass both tables
    x1 = min(before_table["bbox"][0], sorted_rows[0][1][0]["bbox"][0])  # Minimum x1
    y1 = min(before_table["bbox"][1], sorted_rows[0][1][0]["bbox"][1])  # Minimum y1
    x2 = max(before_table["bbox"][2], sorted_rows[-1][1][-1]["bbox"][2])  # Maximum x2
    y2 = max(before_table["bbox"][3], sorted_rows[-1][1][-1]["bbox"][3])  # Maximum y2

    # Create the merged table structure
    return {
        "bbox": [x1, y1, x2, y2],
        "text": merged_html_table,
        "font_size": before_table["font_size"],  # Use the font size of the previous table
        "is_bold": before_table["is_bold"],  # Use the bold property of the previous table
        "type": "wireless_table",
        "all_columns": all_columns
    }


def get_rows(lines: List[Dict]) -> Dict:
    rows = {}

    for line in lines:
        y_center = (line["bbox"][1] + line["bbox"][3]) / 2
        row_key = round(y_center, 1)  # Group by approximate y-center
        matched_key = None

        # Check if row_key is close to any existing key in rows
        for existing_key in rows:
            if abs(row_key - existing_key) <= line["font_size"]:
                matched_key = existing_key
                break

        # Use the matched key if found, otherwise add a new key
        if matched_key is not None:
            rows[matched_key].append(line)
        else:
            rows[row_key] = [line]
    return rows


def _is_grid_structure_v1(lines: List[Dict], before_table: Dict) -> (bool, List[Dict], bool, bool):
    """
    Check if the given lines form a grid-like structure, allowing for missing cells in any row.

    :param lines: List of line information
    :return: True if the lines form a grid, False otherwise
    """
    if not lines:
        return False, lines, False, False

    rows = get_rows(lines)

    temp_table_rows, sample_rows, is_start_rows, had_excess_text = filter_rows(rows)
    is_merge = False
    #
    if before_table is not None and is_start_rows:
        # 如果前一行是表格行，且当前行是起始行，则计算是否合并
        sorted_rows = sorted(temp_table_rows.items(), key=lambda x: x[0])
        max_length = max(len(row[1]) for row in sorted_rows)
        all_columns = before_table["all_columns"]
        # 进行合并

        if max_length <= len(all_columns) and check_if_table(sorted_rows, all_columns):
            # 合并表格
            is_merge = True
            merged_table = table_merge(before_table, sorted_rows, all_columns)
            sample_rows.append(merged_table)
            return True, sample_rows, had_excess_text, is_merge

    if len(temp_table_rows) < 2:
        return False, lines, had_excess_text, is_merge
    # Sort rows by their vertical position
    sorted_rows = sorted(temp_table_rows.items(), key=lambda x: x[0])

    all_columns = []
    for _, row_lines in sorted_rows:
        for line in row_lines:
            all_columns = add_to_columns_as_ranges(all_columns, line["bbox"])

    all_columns = sorted(all_columns)  # Sort column positions

    # Check if each row aligns with the inferred columns
    if not check_if_table(sorted_rows, all_columns):
        return False, lines, had_excess_text, is_merge

    x1 = float('inf')
    y1 = float('inf')
    x2 = float('-inf')
    y2 = float('-inf')
    font_size = 0
    for _, row_lines in sorted_rows:
        for line in row_lines:
            bbox = line["bbox"]
            font_size = line["font_size"]
            x1 = min(x1, bbox[0])
            y1 = min(y1, bbox[1])
            x2 = max(x2, bbox[2])
            y2 = max(y2, bbox[3])
    html_table = generate_html_table_with_missing_cells(sorted_rows, all_columns)
    html_table = "<table>\n" + html_table + "</table>"
    line = {
        "bbox": [x1, y1, x2, y2],
        "text": html_table,
        "font_size": font_size,
        "is_bold": False,
        "type": "wireless_table",
        "all_columns": all_columns
    }

    sample_rows.append(line)

    return True, sample_rows, had_excess_text, is_merge


def generate_html_table_with_missing_cells(sorted_rows, column_ranges):
    """
    Generate an HTML table, handling missing cells by aligning rows to column ranges.

    :param sorted_rows: List of sorted rows with their elements
    :param column_ranges: List of column ranges [[x1, x2], ...]
    :return: HTML table as a string
    """
    html_table = ""

    for _, row_lines in sorted_rows:
        # Convert bbox to tuple to make it hashable
        row_dict = {tuple(line["bbox"]): line["text"] for line in row_lines}
        html_table += "  <tr>"
        for x1, x2 in column_ranges:
            # Check if any cell falls within the current column range
            cell_text = ""
            for bbox, text in row_dict.items():
                if (x1 - abs((x2 - x1) * 0.1)) <= bbox[0] <= (x2 + abs((x2 - x1) * 0.1)):
                    # if x1 <= bbox[0] < x2:  # Check if the cell's x1 falls within the column range
                    cell_text = text
                    break
            html_table += f"    <td>{cell_text}</td>" if cell_text else "    <td></td>"
        html_table += "  </tr>"

    # html_table += "</table>"
    return html_table


def add_to_columns_as_ranges(columns: List[List[float]], bbox: List[float]) -> List[List[float]]:
    """
    Add a column as a range [x1, x2] to the columns list, merging overlapping ranges.

    :param columns: List of existing column ranges [[x1, x2], ...]
    :param bbox: Bounding box of the current element [x1, y1, x2, y2]
    :return: Updated list of column ranges
    """
    x1, x2 = bbox[0], bbox[2]
    merged = False

    for col in columns:
        # Check if the new range overlaps with the existing range
        if not (x2 < col[0] or x1 > col[1]):  # Overlap condition
            col[0] = min(col[0], x1)  # Merge ranges
            col[1] = max(col[1], x2)
            merged = True
            break

    if not merged:
        columns.append([x1, x2])  # Add as a new range if no overlap

    return columns


def filter_rows(rows: Dict) -> (Dict, List[Dict], bool, bool):
    """
    Filter rows to include only those starting from the first row with more than one element
    and ending at the last row with more than one element.

    :param rows: Dictionary of rows (key: row identifier, value: list of elements in the row)
    :return: Filtered dictionary of rows
    """
    rows_list = list(rows.items())  # Convert rows to a list of (key, value) tuples
    start, end = 0, len(rows_list) - 1

    # Find the first row with more than one element
    while start < len(rows_list) and len(rows_list[start][1]) < 2:
        start += 1

    # Find the last row with more than one element
    while end >= 0 and len(rows_list[end][1]) < 2:
        end -= 1

    sample_lines = [item for _, value in rows_list[:start] + rows_list[end + 1:] for item in value]
    # Return the filtered rows as a dictionary
    table_dict = dict(rows_list[start:end + 1]) if start <= end else {}
    return table_dict, sample_lines, start == 0, end == len(rows_list) - 1


def _merge_same_lines(original_lines: List[Dict]) -> List[Dict]:
    """
    根据bbox判断并合并同一行内容

    :param original_lines: 原始行信息列表
    :return: 合并后的行信息列表
    """
    all_lines = []
    current_line = None

    # 按y坐标排序，便于识别相同行
    sorted_lines = sorted(original_lines, key=lambda x: (x["bbox"][1] + x["bbox"][3]) / 2)

    for line in sorted_lines:
        if current_line is None:
            # 第一行直接作为当前行
            current_line = line.copy()
        else:
            # 判断是否是同一行
            is_same_line = _is_same_text_line(current_line, line)

            if is_same_line:
                # 合并行内容
                current_line["text"] += " " + line["text"]
                # 更新bbox右边界
                current_line["bbox"][2] = max(current_line["bbox"][2], line["bbox"][2])
                # 更新bbox下边界
                current_line["bbox"][3] = max(current_line["bbox"][3], line["bbox"][3])
            else:
                # 不是同一行，保存当前行并开始新行
                all_lines.append(current_line)
                current_line = line.copy()

    # 添加最后一行
    if current_line:
        all_lines.append(current_line)

    return all_lines


def _is_same_text_line(current_line: Dict, line: Dict) -> bool:
    """
    判断两个文本行是否属于同一行

    :param current_line: 当前行信息
    :param line: 待判断的行信息
    :return: 是否为同一行
    """
    return (
        # 垂直位置接近(两行中点的y坐标差小于字体大小的一半)
            abs((current_line["bbox"][1] + current_line["bbox"][3]) / 2 -
                (line["bbox"][1] + line["bbox"][3]) / 2) < line["font_size"] * 0.5 and
            # # 字体大小相似
            # 水平位置合理
            abs(line["bbox"][0] - current_line["bbox"][2]) < line["font_size"] * 3
    )


def _group_lines_to_paragraphs(lines: List[Dict]) -> List[Dict]:
    """
    根据位置和格式特征将行整合为段落

    :param lines: 行信息列表
    :return: 段落信息列表
    """
    paragraphs = []
    current_paragraph = None
    max_text_width = max((line["bbox"][2] for line in lines), default=0)
    min_text_width = max((line["bbox"][0] for line in lines), default=0)
    for line in lines:
        # 判断是否应继续当前段落或开始新段落
        if "type" in line and line["type"] == "wireless_table":  # 如果是表格行，直接构造
            if current_paragraph:
                paragraphs.append(current_paragraph)
            paragraph = _create_new_paragraph(line)

            paragraphs.append(paragraph)
            current_paragraph = None
            continue
        if current_paragraph is None:
            # 开始新段落
            current_paragraph = _create_new_paragraph(line)
        elif _should_continue_paragraph(current_paragraph, line, max_text_width, min_text_width):
            # 继续当前段落
            current_paragraph["lines"].append(line)
            current_paragraph["text"] += line["text"]
        else:
            # 结束当前段落并开始新段落
            paragraphs.append(current_paragraph)
            current_paragraph = _create_new_paragraph(line)

    # 添加最后一个段落(如果存在)
    if current_paragraph:
        paragraphs.append(current_paragraph)

    return paragraphs


def _create_new_paragraph(line: Dict) -> Dict:
    """
    创建新段落对象

    :param line: 行信息
    :return: 段落对象
    """
    return {
        "lines": [line],
        "text": line["text"],
        "font_size": line["font_size"],
        "is_bold": line["is_bold"],
        "base_indent": line["bbox"][0],  # 记录基准缩进值
        "type": line["type"] if "type" in line else "text"  # 确保有type字段
    }


def _should_continue_paragraph(current_paragraph: Dict, line: Dict, max_text_width: float,
                               min_text_width: float) -> bool:
    """
    判断是否应该继续当前段落

    :param current_paragraph: 当前段落信息
    :param line: 行信息
    :return: 是否继续段落
    """
    # 检查基本格式匹配

    if not (abs(line["font_size"] - current_paragraph["font_size"]) < 0.5 and  # 字体大小相似
            line["is_bold"] == current_paragraph["is_bold"] and  # 粗体状态相同
            (line["bbox"][1] - current_paragraph["lines"][-1]["bbox"][3]) < line["font_size"] * 1.5):  # 行间距合理
        return False

    last_line = current_paragraph["lines"][-1]
    if last_line["bbox"][2] < max_text_width - line["font_size"] * 2:
        # 如果上一行的右边界超过最大文本宽度，则认为是新段落
        return False
    if line["bbox"][0] > min_text_width + line["font_size"] * 2:
        # 如果当前行的左边界超过最小文本宽度的，则认为是新段落
        return False
    # 检查对齐情况
    if len(current_paragraph["lines"]) == 1:
        # 如果是段落的第二行，允许与第一行有缩进差异
        indent_diff = line["bbox"][0] - current_paragraph["base_indent"]
        is_aligned = indent_diff <= line["font_size"] * 1 or (
                    indent_diff < 0 and abs(indent_diff) >= line["font_size"] * 2)  # 允许一定的缩进差异

        # 如果第二行没有缩进，将其设为基准缩进
        if line["bbox"][0] < current_paragraph["base_indent"]:
            current_paragraph["base_indent"] = line["bbox"][0]

        return is_aligned
    else:
        # 段落中间的行应该与第二行有相同的缩进
        second_line_indent = current_paragraph["lines"][1]["bbox"][0] if len(
            current_paragraph["lines"]) > 1 else current_paragraph["base_indent"]
        return abs(line["bbox"][0] - second_line_indent) < 5


def _calculate_paragraph_bbox(lines: List[Dict]) -> List[float]:
    """
    计算段落的边界框

    :param lines: 段落中的行列表
    :return: 边界框 [x1, y1, x2, y2]
    """
    return [
        min(line["bbox"][0] for line in lines),  # 左边界
        lines[0]["bbox"][1],  # 上边界
        max(line["bbox"][2] for line in lines),  # 右边界
        lines[-1]["bbox"][3]  # 下边界
    ]


def _determine_text_level(text: str, page_toc_entries: List) -> int:
    """
    确定文本的层级（通过匹配目录条目）

    :param text: 文本内容
    :param page_toc_entries: 当前页面的目录条目
    :return: 文本层级
    """
    text_level = 10  # 默认层级

    # 去除所有空格后的文本
    text_no_space = text.replace(" ", "")

    # 尝试匹配目录条目
    for level, title in page_toc_entries:
        # 去除标题中的所有空格
        title_no_space = title.replace(" ", "")

        # 使用无空格字符串进行匹配
        if text_no_space == title_no_space or title in text or text in title:
            text_level = level
            break

    return text_level


def _extract_images_info(pdf_page) -> List[Dict]:
    """
    收集页面中的图片信息

    :param pdf_page: PDF页面对象
    :return: 图片元素列表
    """
    image_elements = []

    for img in pdf_page.get_images(full=True):
        xref = img[0]
        image_rects = fitz.utils.get_image_rects(pdf_page, xref)
        if image_rects:
            rect = image_rects[0]
            # 添加图片信息到页面元素列表
            image_elements.append({
                "type": "image",
                "bbox": [rect.x0, rect.y0, rect.x1, rect.y1],
                "y_pos": rect.y0,  # y坐标用于排序
                "xref": xref,
                "level": 10  # 图片默认层级为10
            })

    return image_elements


def _create_table_dst(element: Dict, page_num: int, root_dst, order: int):
    table_hash = hashlib.sha1(element["text"].encode()).hexdigest()
    bbox = element["bbox"]

    return DST(
        id=build_dst_id(),
        parent=root_dst.id,
        order=order,
        dst_type=DSTType.TABLE,
        attributes=DSTAttribute(
            level=element["level"],
            position=PositionInfo(
                bbox=BBox(
                    x1=int(bbox[0] * 20),
                    y1=int(bbox[1] * 20),
                    x2=int(bbox[2] * 20),
                    y2=int(bbox[3] * 20)
                )
            ),
            page=page_num,
            hash=table_hash
        ),
        content=[element["text"]],
    )


def _create_text_dst(element: Dict, page_num: int, root_dst, order: int) -> DST:
    """
    创建文本DST对象

    :param element: 文本元素信息
    :param page_num: 页码
    :param root_dst: 根DST对象
    :param order: 顺序
    :return: 文本DST对象
    """
    line_hash = hashlib.sha1(element["text"].encode()).hexdigest()
    bbox = element["bbox"]

    return DST(
        id=build_dst_id(),
        parent=root_dst.id,
        order=order,
        dst_type=DSTType.TEXT,
        attributes=DSTAttribute(
            level=element["level"],  # 使用从目录结构中确定的层级
            position=PositionInfo(
                bbox=BBox(
                    x1=int(bbox[0] * 20),
                    y1=int(bbox[1] * 20),
                    x2=int(bbox[2] * 20),
                    y2=int(bbox[3] * 20)
                )
            ),
            page=page_num,
            hash=line_hash
        ),
        content=[element["text"]],
        font_size=element["font_size"],
        bold=element["is_bold"]
    )


async def _create_image_dst(element: Dict, page_num: int, root_dst, order: int, pdf_document: fitz.Document) -> DST:
    """
    创建图片DST对象

    :param element: 图片元素信息
    :param page_num: 页码
    :param root_dst: 根DST对象
    :param order: 顺序
    :param pdf_document: PDF文档对象
    :return: 图片DST对象
    """
    # 提取和处理图片
    xref = element["xref"]
    base_image = pdf_document.extract_image(xref)
    image_bytes = base_image["image"]

    # 处理图像
    image = Image.open(BytesIO(image_bytes))
    original_width, original_height = image.size

    # 配置参数
    scale_threshold = 800  # 超过此尺寸开始缩放
    min_size = 400  # 缩放后最小尺寸限制

    # 初始化为不缩放
    target_width, target_height = original_width, original_height
    scale = 1.0

    # 检查是否需要缩放（宽或高超过阈值）
    if original_width > scale_threshold or original_height > scale_threshold:
        # 计算缩小比例（以阈值为上限）
        scale_width = scale_threshold / original_width
        scale_height = scale_threshold / original_height
        scale = min(scale_width, scale_height)  # 取较小比例确保不超过阈值

        # 确保缩放后不小于最小尺寸
        if original_width * scale < min_size or original_height * scale < min_size:
            # 计算保证最小尺寸的比例
            min_scale_width = min_size / original_width
            min_scale_height = min_size / original_height
            scale = max(scale, min_scale_width, min_scale_height)

        # 计算目标尺寸
        target_width = int(round(original_width * scale))
        target_height = int(round(original_height * scale))

    # 执行缩放（只有当尺寸变化时才进行缩放操作）
    if (target_width, target_height) != (original_width, original_height):
        resized_image = image.resize((target_width, target_height), Image.LANCZOS)
    else:
        resized_image = image  # 尺寸不变时直接使用原图
    # 关键修复：将CMYK模式转换为RGB模式（PNG不支持CMYK）
    if resized_image.mode in ('CMYK', 'RGBA', 'P'):
        # 对于CMYK直接转换为RGB
        if resized_image.mode == 'CMYK':
            resized_image = resized_image.convert('RGB')
        # 对于带透明通道的RGBA，转换为RGB并处理透明部分
        elif resized_image.mode == 'RGBA':
            background = Image.new('RGB', resized_image.size, (255, 255, 255))
            background.paste(resized_image, mask=resized_image.split()[3])
            resized_image = background
        # 对于调色板模式，转换为RGB
        else:  # 'P'模式
            resized_image = resized_image.convert('RGB')
    # 保存处理后的图片并转为base64
    buffer = io.BytesIO()
    resized_image.save(buffer, format="PNG")
    img_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")
    buffer.close()

    # 上传图像并获取URL
    image_url = await _upload_image('', img_base64)

    bbox = element["bbox"]
    image_hash = hashlib.sha1(image_bytes).hexdigest()

    # 创建图片DST对象
    return DST(
        id=build_dst_id(),
        parent=root_dst.id,
        order=order,
        dst_type=DSTType.IMAGE,
        attributes=DSTAttribute(
            level=element["level"],  # 使用默认层级1
            position=PositionInfo(
                bbox=BBox(
                    x1=int(bbox[0] * 20),
                    y1=int(bbox[1] * 20),
                    x2=int(bbox[2] * 20),
                    y2=int(bbox[3] * 20)
                )
            ),
            page=page_num,
            hash=image_hash
        ),
        content=[image_url],
        image_pixel=[image.width, image.height]
    )


class MUPdfParser(UniParseTemplate):

    @async_trace_span
    async def uniparse_process(self, context: PipelineContext, pages: Union[List[int], str],
                               is_all: bool) -> PipelineContext:
        """
        解析PDF文件中指定页码的内容，提取文本和图片，构建DST结构

        :param context: 管道上下文
        :param pages: 需要处理的页码列表(从1开始计数)
        :return: 更新后的上下文
        """
        pdf_document = await open_pdf(context.kdc_input.file_url_or_bytes)

        try:
            # 解析指定页面，生成DST列表
            res = await parse_pages_to_dst(pdf_document, pages)
            for page_num, dst_list in res.items():
                context.update_multiple_parse_dsts_unsafe(page_num, dst_list)

        finally:
            # 确保PDF文档被关闭
            pdf_document.close()

        return context
