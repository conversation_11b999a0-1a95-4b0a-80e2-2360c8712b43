# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 16:33
import copy

import requests
import json
import logging
from pydantic import ValidationError
from typing import Union, Dict
from typing import List

from commons.thread.multicoroutine import MultiCoroutine
from conf import ConfCoroutine
from modules.flows.dst_builder.doc import is_all_images
from modules.flows.pretreat.image_handler import Image2PDFNode
from modules.pipeline.context import PipelineContext
from commons.trace.tracer import async_trace_span
from modules.rpc.kdc_rpc import KDCRpc, PAGE_SIZE
from modules.entity.kdc_enttiy import Document, RotateType, Presentation
from commons.logger.logger import request_id_context
from services.datamodel import FileType
from modules.flows.uni_parser.uniparse_template import UniParseTemplate


def custom_identify_format_from_path(sourcefile: str, format: str) -> str:
    return format


def format_page_ranges(pages: List[int]) -> List[str]:
    """将排序的页面列表转换为符合长度限制的范围字符串"""
    if not pages:
        return []

    ranges = []
    current_group = [pages[0]]

    for i in range(1, len(pages)):
        # 如果页面连续且当前分组未达到PAGE_SIZE
        if pages[i] == pages[i - 1] + 1 and len(current_group) < PAGE_SIZE:
            current_group.append(pages[i])
        else:
            # 结束当前分组
            ranges.append(f"{current_group[0] + 1}-{current_group[-1] + 1}")
            current_group = [pages[i]]

    # 添加最后一个分组
    if current_group:
        ranges.append(f"{current_group[0] + 1}-{current_group[-1] + 1}")

    return ranges


class KDCParser(UniParseTemplate):
    """KDC 数据解析器"""

    @async_trace_span
    async def uniparse_process(self, context: PipelineContext, pages: Union[List[int], str],
                               is_all: bool) -> PipelineContext:
        """
        处理流程：
        1. 优先从 kdc_ks3_url 获取数据
        2. 其次调用 kdc_parser 解析本地文件
        3. 统一处理为 list[dict] 格式
        """
        try:
            kdc_input = context.kdc_input

            # 优先处理 KS3 URL 数据源
            if kdc_input.kdc_ks3_url:
                context = await self._fetch_from_ks3(context, kdc_input.kdc_ks3_url)
            else:
                context = await self._parse_input_file(context, pages, is_all)

            self._validate_data_format(context.kdc_data)
            if context.file_info.file_type in [FileType.DOCX, FileType.DOC]:
                images = []
                is_images, images = is_all_images(context, context.kdc_data, images)
                if is_images:
                    pdf_name, pdf_url = Image2PDFNode("image_to_pdf").images_to_pdf(images)
                    context.raw_file_info = copy.deepcopy(context.file_info)
                    context.kdc_input.file_url_or_bytes = pdf_url
                    context.file_info.file_type = FileType.PDF
                    context.file_info.file_name = pdf_name
                    context = await self._parse_input_file(context, [], True)
                # 对于 Excel 文件，直接返回上下文
                return context

            return context

        except Exception as e:
            context.business_log.error(f"KDCNode process failed: {str(e)}")
            raise

    def _set_file_info(self, context: PipelineContext, kdc_file_info: dict, pages_info: Document):
        if kdc_file_info:
            if context.raw_file_info:
                context.file_info.page_size = context.raw_file_info.page_size
                context.file_info.is_scan = context.raw_file_info.is_scan
            context.file_info.page_size = kdc_file_info.get("total_page_num", 1)
            context.file_info.is_scan = kdc_file_info.get("is_scan", False)
        max_width = 0
        max_height = 0
        if context.file_info.file_type in [FileType.PPTX, FileType.PPT]:
            max_width = pages_info.prop.slide_size.width
            max_height = pages_info.prop.slide_size.height
        elif context.file_info.file_type not in [FileType.XLS, FileType.XLSX]:
            if pages_info.prop and pages_info.prop.page_props:
                # TODO: 这里的page_index是有问题的
                for page_index, page in enumerate(pages_info.prop.page_props):
                    width = 0
                    height = 0
                    # 处理页面尺寸
                    if page.size:
                        width = page.size.width
                        height = page.size.height
                    # 处理页面旋转
                    if page.rotate == RotateType.rotate90:
                        width, height = height, width
                        context.file_info.rotate_page[90] = context.file_info.rotate_page.get(90, [])
                        context.file_info.rotate_page[90].append(page_index)
                    elif page.rotate == RotateType.rotate180:
                        context.file_info.rotate_page[180] = context.file_info.rotate_page.get(180, [])
                        context.file_info.rotate_page[180].append(page_index)
                    elif page.rotate == RotateType.rotate270:
                        width, height = height, width
                        context.file_info.rotate_page[270] = context.file_info.rotate_page.get(270, [])
                        context.file_info.rotate_page[270].append(page_index)
                    # 只有在有尺寸信息时才更新最大宽高
                    if page.size:
                        max_width = max(max_width, width)
                        max_height = max(max_height, height)
        context.file_info.width = max_width
        context.file_info.height = max_height

    async def _fetch_from_ks3(self, context: PipelineContext, url: str) -> PipelineContext:
        """从 KS3 获取数据"""
        try:
            response = requests.get(url)
            response.raise_for_status()
            raw_data = json.loads(response.text)
            context.kdc_data = self._normalize_data(raw_data)
            kdc_file_info = context.kdc_data[0].get("file_info", {})
            pages_info = Document.parse_obj(context.kdc_data[0]["doc"])
            self._set_file_info(context, kdc_file_info, pages_info)
            return context
        except requests.exceptions.RequestException as e:
            context.business_log.error(f"KS3 request failed: {str(e)}")
            raise ValueError(f"KS3 request failed: {str(e)}")
        except json.JSONDecodeError as e:
            context.business_log.error(f"Invalid JSON format from KS3: {str(e)}")
            raise ValidationError(f"Invalid JSON format: {str(e)}")

    async def _parse_input_file(self, context: PipelineContext, pages: Union[List[int], str],
                                is_all: bool) -> PipelineContext:
        """解析本地文件"""
        kdc_input = context.kdc_input
        if not kdc_input.convert_options:
            kdc_input.convert_options = {}
        if context.file_info.file_type == FileType.OTL:
            kdc_input.convert_options["enable_upload_medias"] = True
        if context.file_info.file_type == FileType.PDF and "figure_dpi" not in kdc_input.convert_options:
            kdc_input.convert_options["pdf_engine"] = "scan"
        ## 构建不同的page_range
        kdc_data = []
        if is_all is False and pages:
            pool = MultiCoroutine()

            page_ranges = format_page_ranges(pages)
            for page_range in page_ranges:
                options = kdc_input.convert_options
                temp = copy.deepcopy(options)  # 确保每个任务使用独立的选项副本
                temp["page_range"] = page_range
                task = KDCRpc().aget_content_by_url_or_file(
                    company_id=kdc_input.company_id,
                    file_url_or_bytes=kdc_input.file_url_or_bytes,
                    format="kdc",
                    filename=context.file_info.file_name,
                    include_elements="all",
                    request_id=request_id_context.get(),
                    file_id=context.file_info.file_id,
                    convert_options=temp,
                    file_type=context.file_info.file_type,
                )
                pool.add_task(page_range, task)
            results = await pool.run_limit(ConfCoroutine.kdc_limit)
            sorted_results = sorted(results.items(), key=lambda x: int(x[0].split("-")[0]))
            values_list = [value for _, value in sorted_results]
            # results = await asyncio.gather(*tasks, return_exceptions=True)
            for term in values_list:
                if term and isinstance(term, list) and len(term) > 0:
                    kdc_data.extend(term)
            # for page_range in page_ranges:
            #     convert_options = kdc_input.convert_options
            #     convert_options["page_range"] = page_range
            #     term = await KDCRpc().aget_content_by_url_or_file(
            #         company_id=kdc_input.company_id,
            #         file_url_or_bytes=kdc_input.file_url_or_bytes,
            #         format="kdc",
            #         filename=context.file_info.file_name,
            #         include_elements="all",
            #         request_id=request_id_context.get(),
            #         file_id=context.file_info.file_id,
            #         convert_options=kdc_input.convert_options,
            #         file_type=context.file_info.file_type,
            #     )
            #     if term and isinstance(term, list) and len(term) > 0:
            #         kdc_data.extend(term)
        else:
            kdc_data = await KDCRpc().aget_content_by_url_or_file(
                company_id=kdc_input.company_id,
                file_url_or_bytes=kdc_input.file_url_or_bytes,
                format="kdc",
                filename=context.file_info.file_name,
                include_elements="all",
                request_id=request_id_context.get(),
                file_id=context.file_info.file_id,
                convert_options=kdc_input.convert_options,
                file_type=context.file_info.file_type,
            )
        context.kdc_data = self._normalize_data(kdc_data)
        kdc_file_info = context.kdc_data[0].get("file_info", {})
        context.business_log.debug(f"KDC data fetched kdc_file_info: {context.kdc_data[0].get('file_info', {})}")
        # todo 不同格式设置 fileinfo
        if context.file_info.file_type in [FileType.PPTX, FileType.PPT]:
            pres: Presentation = Presentation.model_validate(kdc_data[0]["doc"])
        elif context.file_info.file_type in [FileType.XLS, FileType.XLSX]:
            context.business_log.debug("Parsing XLS/XLSX file, using XlsParse")
        else:
            pages_info = Document.parse_obj(context.kdc_data[0]["doc"])
            self._set_file_info(context, kdc_file_info, pages_info)
        if context.file_info.file_type == FileType.PDF:
            if kdc_input.convert_options and kdc_input.convert_options.get("pdf_engine") == "scan":
                for kdc_doc in context.kdc_data:
                    self._update_page_index(kdc_doc["doc"])
        return context

    def _update_page_index(self, input: Dict):
        def traverse_and_update(node: Dict, index: int):
            if "blocks" in node and node["blocks"]:
                for block in node["blocks"]:
                    if "page_index" in block and block["page_index"] is not None:
                        block["page_index"] += index
            if "children" in node and node["children"]:
                for child in node["children"]:
                    traverse_and_update(child, index)

        traverse_and_update(input["tree"], input["page_start"])

    def _normalize_data(self, raw_data: Union[dict, list]) -> list:
        """统一数据格式为 list[dict]"""
        return [raw_data] if isinstance(raw_data, dict) else raw_data

    def _validate_data_format(self, data: list):
        """数据格式校验"""
        if not isinstance(data, list):
            raise ValueError("KDC data must be list type")
        if not all(isinstance(item, dict) for item in data):
            raise ValueError("All KDC items must be dictionary type")

    # Register the KDCNode with the ParserFactory
