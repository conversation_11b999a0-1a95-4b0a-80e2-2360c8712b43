# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 16:29
import time



from commons.trace.tracer import async_trace_span

from modules.flows.dst_enhance.common import CommonEnhance
from modules.flows.dst_enhance.dst_enhance_template import DstEnhanceTemplate
from modules.pipeline.base import PipelineHandler

from modules.pipeline.context import PipelineContext, FileType
from conf import ConfHandlerName


class DstEnhanceFactory:
    _dst_processors = {
        FileType.OTL: CommonEnhance,
        FileType.PDF: CommonEnhance,
        FileType.DOC: CommonEnhance,
        FileType.DOCX: CommonEnhance,
        FileType.TXT: CommonEnhance,
        FileType.PPT: CommonEnhance,
        FileType.PPTX: CommonEnhance,
        FileType.XLSX: CommonEnhance,
        FileType.XLS: CommonEnhance,
        FileType.JPG: CommonEnhance,
        FileType.JPEG: CommonEnhance,
        FileType.PNG: CommonEnhance,
        FileType.WEBP: CommonEnhance,
    }

    @staticmethod
    def get_dst_processor(file_type: FileType) -> DstEnhanceTemplate:
        dst_class = DstEnhanceFactory._dst_processors.get(file_type)
        if not dst_class:
            raise ValueError(f"No parser available for file type: {file_type}")
        return dst_class()


class DstEnhanceNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            _t0 = time.perf_counter()
            dst_processor = DstEnhanceFactory.get_dst_processor(context.file_info.file_type)
            _t0 = time.perf_counter()
            dst_list = await dst_processor.process_dst(context,context.dst, context.file_info.page_size )
            dur = time.perf_counter() - _t0
            context.business_log.debug(f"DstEnhanceNode {self.name} executed in {dur:.2f} seconds")
            context.dst = dst_list
            context.handler_results[ConfHandlerName.dst_handler] = dst_list

            return context
        except Exception as e:
            context.business_log.error(f"Error in ChunkNode.process: {e}")
            raise e
