# coding: utf-8
from abc import ABC, abstractmethod
from typing import List, Dict

from commons.trace.tracer import trace_span
from modules.entity.dst_entity import DST
from modules.entity.chunk_entity import Chunk
from modules.pipeline.context import ChunkInfo, PipelineContext


class DstEnhanceTemplate(ABC):
    """
    Abstract base class for chunk processing logic.
    Each file format should implement its own chunking logic.
    """

    @abstractmethod
    async def process_dst(self,context: PipelineContext, dst_list: List[DST], page_size: int) -> List[DST]:
        """
        Abstract method to process chunks from a list of DST objects.

        :param dst_list: List of DST objects
        :return: List of processed chunks

        Args:
            page_size:
        """
        pass
