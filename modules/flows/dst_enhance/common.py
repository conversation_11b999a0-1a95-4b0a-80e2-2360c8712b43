import asyncio
import base64
import hashlib
import io
import logging
import re
import time
from collections import defaultdict
from typing import List, Dict
import requests

from commons.prompt.ocrflux_prompt import OcrFlux, prompt_pic, prompt
from conf import Conf<PERSON><PERSON><PERSON><PERSON><PERSON>, ConfUseOcr, ConfCoroutine, MultipleParse
from modules.entity.checkbox_entity import replace_frame, replace_frame_little
from modules.entity.dst_entity import DST, DSTType
from modules.flows.crop.crop import get_checkbox_crop
from modules.flows.dst_enhance.checkbox_text_correction import correct_checkbox_marks, \
    Replacement, preprocess_hard_selected_marks
from modules.flows.dst_enhance.dst_enhance_template import DstEnhanceTemplate
from modules.pipeline.context import PipelineContext
from commons.thread.multicoroutine import MultiCoroutine
from services.datamodel import file_is_image


class CommonEnhance(DstEnhanceTemplate):
    def __init__(self):
        super().__init__()
        # ocr增强
        self.enhance_task_ocr = "ocr"
        # checkbox增强
        self.enhance_task_checkbox = "checkbox"

    def _check_dst_only_image(self, dst_list: List[DST]) -> bool:
        """
        检测dst是否只有image类型
        :param dst_list
        :return: bool
        """
        return all(dst.dst_type in (DSTType.IMAGE, DSTType.ROOT) for dst in dst_list)

    async def process_dst(self, context: PipelineContext, dst_list: List[DST], page_size: int) -> List[DST]:
        """
        Process DST objects to enhance them based on the provided page size and chunk information.

        :param dst_list: List of DST objects to be processed.
        :param page_size: Size of each page for chunking.
        :return: List of enhanced DST objects.
        """
        # Placeholder for actual processing logic
        # word_count = 0
        dst_map = {}  # key是dstid， value是dst   然后根据这个image_map去并行调用ocr模型
        for dst in dst_list:
            if dst.dst_type == DSTType.IMAGE or dst.dst_type == DSTType.FLOWCHART:
                if dst.image_pixel[0] > ConfImageFilter.width and dst.image_pixel[1] > ConfImageFilter.height:  # 过滤小图片
                    dst_map[dst.id] = dst
        pool = MultiCoroutine()
        if int(ConfUseOcr.use_ocr) and context.need_solve_picture:
            if not context.file_info.is_scan:
                # 非扫描件，对文件里的图片进行ocr增强
                pool.add_task(self.enhance_task_ocr, self.ocr_dst(context, dst_list, dst_map))
            elif (context.raw_file_info is not None
                  and file_is_image(context.raw_file_info.file_type)
                  and self._check_dst_only_image(dst_list)):
                # 源文件是图片，且解析结果只有image类型，没有text等其他类型时，进行ocr增强，解决部分图片无法解析的问题
                pool.add_task(self.enhance_task_ocr, self.ocr_dst(context, dst_list, dst_map))

        # page_num, text_chunks = classify_and_filter_dsts(dst_list)
        text_chunks = get_checkbox_crop(context,context.file_info,dst_list)
        # text_chunks = []
        if text_chunks and len(text_chunks) > 0:
            pool.add_task(self.enhance_task_checkbox, correct_checkbox_marks(context, text_chunks))
        if len(pool.keys) > 0:
            results = await pool.run()
            if self.enhance_task_ocr in results:
                dst_list = results[self.enhance_task_ocr]
            if self.enhance_task_checkbox in results:
                checkbox_replacements = results[self.enhance_task_checkbox]
                logging.debug(f"Checkbox replacements: {checkbox_replacements}")
                self.apply_replacements(dst_list,checkbox_replacements)
        return dst_list

    def apply_replacements(self, dst_list: List[DST], replacements_by_page: Dict[int, List[Replacement]]) -> List[DST]:
        # Step 1: Group replacements by dst.id
        replacements_by_dst = defaultdict(list)
        for page, replacements in replacements_by_page.items():
            for dst in dst_list:
                if dst.attributes.page != page or dst.dst_type == DSTType.IMAGE:
                    continue

                for i, content in enumerate(dst.content):
                    content = replace_frame(content)
                    # content = content.replace("□", "☐")  # Normalize checkbox symbols
                    for replacement in replacements:
                        wrong = replacement.wrong
                        start = 0
                        while (index := content.find(wrong, start)) != -1:
                            replacements_by_dst[dst.id].append((i, index, wrong, replacement.refined))
                            start = index + len(wrong)

        # Step 2: Apply replacements to dst_list
        for dst in dst_list:
            if dst.id in replacements_by_dst:
                # Sort replacements by content index and match index to avoid conflicts
                replacements = sorted(replacements_by_dst[dst.id], key=lambda x: (x[0], x[1]))
                offsets = defaultdict(int)  # Track offsets for each content index
                for content_index, match_index, wrong, refined in replacements:
                    content = dst.content[content_index]
                    adjusted_index = match_index + offsets[content_index]

                    # Perform the replacement
                    dst.content[content_index] = (
                            content[:adjusted_index] + refined + content[adjusted_index + len(wrong):]
                    )

                    # Update the offset for subsequent replacements
                    offsets[content_index] += len(refined) - len(wrong)

        self.correct_checkbox(dst_list)
        return dst_list

    def correct_checkbox(self,  dst_list):
        for dst in dst_list:
            if dst.dst_type != DSTType.IMAGE:
                dst.content = [preprocess_hard_selected_marks(replace_frame_little(content)) for content in dst.content]
                pattern = re.compile(r'[☑√□口]')
                dst.content = [
                    pattern.sub(lambda match: f" {match.group(0)}", content)
                    for content in dst.content
                ]

    async def ocr_dst(self, context, dst_list, dst_map):
        _t0 = time.perf_counter()
        try:
            res_map = await self._generate_dsts_from_image_layout(context, dst_map, ocr_only=context.ocr_only)
            dst_list = self.insert_dst_map_into_list(dst_list, res_map, ocr_only=context.ocr_only)
            context.business_log.info(
                f"DstEnhanceNode ocr_dst processed {len(dst_list)} DSTs for file type {context.file_info.file_type} , total word count: {context.file_info.word_count} ,cost time: {time.perf_counter() - _t0:.2f} seconds")
        except Exception as e:
            context.business_log.error(f"Error processing image, error: {e}")
        return dst_list

    async def _generate_dsts_from_image_layout(self, context: PipelineContext, dst_map: Dict,
                                               ocr_only: bool = True) -> Dict:
        """
        处理多个图像DST，使用MultiCoroutine限制并发数

        :param context: 流水线上下文
        :param dst_map: 包含需要处理的DST对象的字典
        :param ocr_only: 是否仅执行OCR
        :return: 包含处理结果的字典 {dst_id: dsts}
        """
        res_map = {}

        if not dst_map:
            return res_map
        try:
        # 使用MultiCoroutine限制并发
            pool = MultiCoroutine()

            # 添加所有任务到协程池
            for key, dst in dst_map.items():
                task_key = f"process_dst_{key}"
                pool.add_task(task_key, self._process_single_dst(context, dst, ocr_only))

            pool_results = await pool.run_limit(ConfCoroutine.image_ocr_limit)

            # 处理结果
            for key, result in pool_results.items():
                # 从task_key中提取dst_id
                dst_id = key.replace("process_dst_", "")

                if isinstance(result, Exception):
                    context.business_log.error(f"Error in layout recognition for image {dst_id}: {result}")
                    continue

                res_map[dst_id] = result
                for dst in result:
                    if dst.content and len(dst.content) > 1:
                        context.file_info.word_count += len(dst.content[1])
        except Exception as e:
           context.business_log.error(f"Error _generate_dsts_from_image_layout image, error: {e}")
        return res_map

    async def _process_single_dst(self, context: PipelineContext, dst: DST, ocr_only: bool = True) -> List[DST]:
        """
        处理单个DST对象的辅助方法

        :param context: 流水线上下文
        :param dst: 需要处理的DST对象
        :param ocr_only: 是否仅执行OCR
        :return: 处理结果DST列表
        """
        dsts = []

        if not dst.content:
            context.business_log.error(f"Invalid dst entry: {dst}")
            return dsts

        image_url = dst.content[0]

        try:
            # Directly perform OCR on the image URL
            # ocr_data = await OCRModelClient().request_ocr(image_url)
            _t0 = time.perf_counter()
            time_out = MultipleParse.image_ocr_time_out
            is_image_file = file_is_image(context.file_info.file_type)
            if is_image_file:
                time_out = MultipleParse.pic_input_ocr_time_out

            output_data, is_success = await OcrFlux(context.public_gateway_header).preprocess_ocrflux(image_url, prompt_pic, time_out)
            #图片类型文件才能进行retry，解析内的图片不需要retry
            if not is_success and is_image_file:
                context.business_log.warning(f"Initial OCR processing failed for {image_url}. Retrying once...")
                output_data, is_success = await OcrFlux(context.public_gateway_header).preprocess_ocrflux(image_url, prompt, time_out)

            res_str = output_data if output_data is not None else ""
            dst.content = [image_url, res_str]
            dsts.append(dst)
            context.business_log.info(f"OCR processing for {image_url} took {time.perf_counter() - _t0:.2f} seconds")

        except requests.RequestException as e:
            context.business_log.error(f"Failed to fetch image from URL: {image_url}, error: {e}")
        except Exception as e:
            context.business_log.error(f"Error processing image for dst: {dst.id}, error: {e}")

        return dsts

    def insert_dst_map_into_list(self, dst_list: List[DST], dst_map: Dict[str, List[DST]], ocr_only: bool = True) -> \
            List[DST]:
        """
        将 OCR 识别后的子节点插入到原始 DST 列表中，替换对应的图片节点。

        Args:
            dst_list: 原始的 DST 节点列表
            dst_map:  key=原始图片节点ID, value=OCR生成的子节点列表

        Returns:
            新的 DST 列表（包含 OCR 内容的子节点）
        """
        updated_dst_list = []

        for dst in dst_list:
            if dst.id in dst_map:
                if not ocr_only:
                    updated_dst_list.append(dst)  # 保留原始 dst 节点
                # Insert the corresponding value from dst_map
                updated_dst_list.extend(dst_map[dst.id])
            else:
                # Keep the original dst
                updated_dst_list.append(dst)
        for index, dst in enumerate(updated_dst_list):
            dst.order = index
        return updated_dst_list
