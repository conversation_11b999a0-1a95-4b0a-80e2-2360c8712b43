from typing import List, Protocol
from modules.entity.chunk_entity import LabelType
from modules.entity.chunk_entity import Chunk
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

# 标题文本长度常量为50
TITLE_LENGTH_LIMIT = 100
SIMILARITY_THRESHOLD = 0

def get_similarity(chunk1: Chunk, chunk2: Chunk) -> float:
    # 确保 content_embedding 不为 None，并转换为二维数组
    embedding1 = np.array(chunk1.content_embedding or []).reshape(1, -1)
    embedding2 = np.array(chunk2.content_embedding or []).reshape(1, -1)

    # 如果任一 embedding 为空，则返回相似度为 0
    if embedding1.size == 0 or embedding2.size == 0:
        return 0.0

    # 计算余弦相似度
    similarity = cosine_similarity(embedding1, embedding2)[0][0]
    return similarity

# 定义表格处理器的接口
class TableProcessor(Protocol):
    """
    采用策略模式设计表格chunk处理器
    基于word、pdf、ppt等不同类型的文件，设计不同的表格处理器
    输入输出都为 List[Chunk] 
    """
    def process(self, chunks: List[Chunk]) -> List[Chunk]:
        """处理表格 Chunk 的方法"""
        pass
    

# PDF 表格处理器
class TitleProcessor:
    def process(self, chunks: List[Chunk]) -> List[Chunk]:
        # 默认数据已处理页眉页脚
        i = 1
        while i < len(chunks) - 1:
            # 补充基于cosine_similarity 计算  chunks[i + 1].content 与 chunks[i].content  的相似度
            pre_chunk = None
            next_chunk = None

            if chunks[i].label == LabelType.TABLE:
                if chunks[i - 1].label == LabelType.TEXT and len(chunks[i - 1].content) <= TITLE_LENGTH_LIMIT:
                    pre_chunk = chunks[i - 1].chunk_id
                if chunks[i + 1].label == LabelType.TEXT and len(chunks[i + 1].content) <= TITLE_LENGTH_LIMIT:
                    next_chunk = chunks[i + 1].chunk_id

                if pre_chunk and next_chunk:
                    # 计算相似度
                    similarity_pre = get_similarity(chunks[i], chunks[i - 1])
                    similarity_next = get_similarity(chunks[i], chunks[i + 1])
                    
                    if similarity_pre > similarity_next:
                        if similarity_pre > SIMILARITY_THRESHOLD:
                            chunks[i].pre_chunk = pre_chunk
                            chunks[i-1].next_chunk = chunks[i].chunk_id
                    else:
                        if similarity_next > SIMILARITY_THRESHOLD:
                            chunks[i].next_chunk = next_chunk
                            chunks[i + 1].pre_chunk = chunks[i].chunk_id
            i += 1

        return chunks



# 表格处理器上下文
class TableProcessorContext:
    def __init__(self, processor: TableProcessor):
        self.processor = processor

    def set_processor(self, processor: TableProcessor):
        self.processor = processor

    def process_chunks(self, chunks: List[Chunk]) -> List[Chunk]:
        return self.processor.process(chunks)
    

# 示例使用
if __name__ == "__main__":
    # 输入的表格 chunks
    input_chunks = [Chunk("Table1"), Chunk("Table2")]

    # 使用 PDF 表格处理器
    context = TableProcessorContext(TitleProcessor())
    pdf_processed_chunks = context.process_chunks(input_chunks)
    print("PDF Processed Chunks:", pdf_processed_chunks)
