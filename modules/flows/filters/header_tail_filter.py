import re
import uuid
from typing import List

from modules.entity.dst_entity import DST, DSTType, MarkType
from modules.entity.chunk_entity import LabelType
from modules.entity.chunk_entity import Chunk
from modules.pipeline.context import FileInfo


def get_matching_dsts(previous_page_dsts, current_page_dsts, file_info):
    """
    Compare dsts from the previous page and current page to identify matching headers and footers.

    :param previous_page_dsts: List of dst objects from the previous page.
    :param current_page_dsts: List of dst objects from the current page.
    :return: Lists of marked dsts for headers and footers.
    """

    def compare_dsts(dst_prev, dst_curr, is_header=True):
        prev_bbox = dst_prev.attributes.position.bbox
        curr_bbox = dst_curr.attributes.position.bbox

        if dst_prev.dst_type != dst_curr.dst_type:
            return False
        if dst_prev.dst_type == DSTType.IMAGE:
            return is_equal_image(dst_prev, dst_curr, file_info)
        prev_content = re.sub(r"\d+", "《NUM》", "".join(dst_prev.content)).strip()
        curr_content = re.sub(r"\d+", "《NUM》", "".join(dst_curr.content)).strip()
        return (
                prev_content == curr_content
                and abs((prev_bbox.y2 - prev_bbox.y1) - (curr_bbox.y2 - curr_bbox.y1)) < 100
                and abs((prev_bbox.x2 - prev_bbox.x1) - (curr_bbox.x2 - curr_bbox.x1)) < 100
                and abs(prev_bbox.y2 - curr_bbox.y2) < 400
        )

    def process_dsts(dsts1, dsts2, is_header=True):
        result = {}
        is_all_text = True
        for dst_prev, dst_curr in zip(dsts1, dsts2):
            if compare_dsts(dst_prev, dst_curr, is_header):
                if dst_prev.dst_type != DSTType.TEXT:
                    is_all_text = False
                result[dst_prev.id] = dst_prev
                result[dst_curr.id] = dst_curr
            else:
                break
        return result, is_all_text

    headers, is_all_text_headers = process_dsts(previous_page_dsts, current_page_dsts)
    footers, is_all_text_footers = process_dsts(
        reversed(previous_page_dsts), reversed(current_page_dsts), is_header=False
    )
    return headers, footers, is_all_text_headers, is_all_text_footers


def is_equal_image(dst_prev, dst_curr, file_info):
    """
    Check if two dst objects are images and have matching attributes.

    :param dst_prev: Previous dst object.
    :param dst_curr: Current dst object.
    :return: Boolean indicating if they match.
    """
    prev_x1 = dst_prev.attributes.position.bbox.x1
    prev_y1 = dst_prev.attributes.position.bbox.y1
    prev_x2 = dst_prev.attributes.position.bbox.x2
    prev_y2 = dst_prev.attributes.position.bbox.y2

    curr_x1 = dst_curr.attributes.position.bbox.x1
    curr_y1 = dst_curr.attributes.position.bbox.y1
    curr_x2 = dst_curr.attributes.position.bbox.x2
    curr_y2 = dst_curr.attributes.position.bbox.y2
    image_detail_equal = True
    if len(dst_prev.content) > 1 and len(dst_curr.content) > 1:
        image_detail_equal = dst_prev.content[1] == dst_curr.content[1]
    if dst_prev.attributes.hash == dst_curr.attributes.hash or \
            ((abs(((prev_y2 - prev_y1) - (curr_y2 - curr_y1))) < 100 and abs(
                (prev_x2 - prev_x1) - (curr_x2 - curr_x1)) < 100)
             and image_detail_equal
             and (prev_y2 - prev_y1 < 0.15 * file_info.height)
             and (curr_y2 - curr_y1 < 0.15 * file_info.height)):
        return True

    return False


def merge_header_footer_into_chunk(dst_list: List[DST], page_size: int) -> Chunk:
    # Step 1: Group DSTs by page
    page_map = {}
    for dst in dst_list:
        if dst.dst_type == DSTType.ROOT:
            continue
        if dst.attributes.page not in page_map:
            page_map[dst.attributes.page] = []
        page_map[dst.attributes.page].append(dst)

    # Step 2: Find the first page with both header and footer
    for page, dsts in sorted(page_map.items()):
        headers = [dst for dst in dsts if dst.mark == MarkType.HEADER]
        footers = [dst for dst in dsts if dst.mark == MarkType.FOOTER]
        if headers or footers:
            # Step 3: Merge headers and footers into a single chunk
            need_merge_list = []
            for dst in headers + footers:
                if dst.dst_type == DSTType.IMAGE:
                    if dst.content and len(dst.content) > 1:
                        need_merge_list.append(dst.content[1])
                else:
                    need_merge_list.append("".join(dst.content))
            merged_content = " ".join(need_merge_list).strip()
            # merged_content = " ".join(
            #     "".join(dst.content) for dst in headers + footers if dst.dst_type != DSTType.IMAGE)
            merged_page_nums = {dst.attributes.page for dst in headers + footers}
            merged_dsts = headers + footers
            return Chunk(
                chunk_id=uuid.uuid4().hex,
                label=LabelType.TEXT,
                content=merged_content,
                # content_embedding="",
                page_size=page_size,
                page_num=sorted(merged_page_nums),
                dsts=merged_dsts,
                mark=None,  # No specific mark for the merged chunk
                block=[dst.id for dst in merged_dsts]
            )

    # If no page with both header and footer is found, return None
    return None


def mark_page_header_tail_text(dst_list: List[DST], page_map, sorted_pages, file_info: FileInfo) -> List[DST]:
    return mark_header_and_tail(dst_list, page_map, sorted_pages, file_info)


def mark_header_and_tail(dst_list, page_map, sorted_pages, file_info: FileInfo) -> List[DST]:
    headers = {}
    tails = {}
    header_y1 = None
    header_y2 = None
    tail_y1 = None
    tail_y2 = None
    all_text_footers = True
    all_text_headers = True
    for i in range(1, len(sorted_pages)):
        current_page = sorted_pages[i]
        previous_page = sorted_pages[i - 1]

        # Get matching dsts from the previous and current pages
        page_heads, page_tails, is_text_headers, is_text_footers = get_matching_dsts(
            page_map[previous_page], page_map[current_page], file_info
        )
        all_text_headers = all_text_headers and is_text_headers
        all_text_footers = all_text_footers and is_text_footers
        if page_heads:
            # Initialize header_y1 and header_y2 if they are None
            if header_y1 is None:
                header_y1 = min(dst.attributes.position.bbox.y1 for dst in page_heads.values())
            else:
                header_y1 = min(header_y1, min(dst.attributes.position.bbox.y1 for dst in page_heads.values()))

            if header_y2 is None:
                header_y2 = max(dst.attributes.position.bbox.y2 for dst in page_heads.values())
            else:
                header_y2 = max(header_y2, max(dst.attributes.position.bbox.y2 for dst in page_heads.values()))

        if page_tails:
            # Initialize tail_y1 and tail_y2 if they are None
            if tail_y1 is None:
                tail_y1 = min(dst.attributes.position.bbox.y1 for dst in page_tails.values())
            else:
                tail_y1 = min(tail_y1, min(dst.attributes.position.bbox.y1 for dst in page_tails.values()))

            if tail_y2 is None:
                tail_y2 = max(dst.attributes.position.bbox.y2 for dst in page_tails.values())
            else:
                tail_y2 = max(tail_y2, max(dst.attributes.position.bbox.y2 for dst in page_tails.values()))

        headers.update(page_heads)
        tails.update(page_tails)
    if tail_y2 is not None and tail_y1 is not None and tail_y2 - tail_y1 > file_info.height * 0.15:
        tail_y1 = None
        tail_y2 = None
    if header_y2 is not None and header_y1 is not None and header_y2 - header_y1 > file_info.height * 0.15:
        header_y1 = None
        header_y2 = None
    for dst in dst_list:
        y1 = dst.attributes.position.bbox.y1
        y2 = dst.attributes.position.bbox.y2
        if headers and header_y1 is not None and header_y2 is not None and (dst.id in headers or (
                (
                        header_y1 <= y1 <= header_y2 or header_y1 <= y2 <= header_y2) and dst.dst_type != DSTType.TABLE and all_text_headers is False)):
            dst.mark = MarkType.HEADER
        elif tails and tail_y1 is not None and tail_y2 is not None and (dst.id in tails or (
                (
                        tail_y1 <= y1 <= tail_y2 or tail_y1 <= y2 <= tail_y2) and dst.dst_type != DSTType.TABLE and all_text_footers is False)):
            dst.mark = MarkType.FOOTER
    return dst_list
