from typing import List

from conf import DstFilter
from modules.entity.dst_entity import DST, DSTType, MarkType

def mark_catalog(dst_list: List[DST], page_map, sorted_pages) -> List[DST]:
    catalog_pages = []
    for i in range(0, len(sorted_pages)):
        ##判断目录第一页需要满足两个条件，已经判断了一页的不需要i > DstFilter.catalog_max_page_num这个条件
        if i > DstFilter.catalog_max_page_num and len(catalog_pages) == 0:
            break
        current_page = sorted_pages[i]
        if current_page not in page_map:
            continue
        dsts = page_map[current_page]
        is_catalog = False
        match_flag = 0
        if len(catalog_pages) > 0:
        # 如果已经有目录页了,就判断标志位
            for dst in dsts:
                if dst.dst_type == DSTType.TEXT and dst.mark == None:
                    content = "".join(dst.content).strip()
                    for flag in DstFilter.catalog_filter_flag:
                        if flag in content.replace("\n", ""):
                            match_flag += 1
            if match_flag / len(dsts) > DstFilter.catalog_match_ratio:
                is_catalog = True

        else:
            for dst in dsts:
                if dst.dst_type == DSTType.TEXT and dst.mark == None:
                    content = "".join(dst.content).strip()
                    if content.replace("\n", "") in DstFilter.catalog_filter_key:
                        is_catalog = True
                        break
                    else:
                        break
        if is_catalog:
            catalog_pages.append(current_page)

    for dst in dst_list:
        if dst.attributes.page in catalog_pages:
            dst.mark = MarkType.CATALOG

    return dst_list


def get_catalog_pages(dst_list: List[DST]) -> int:
    """
    获取目录所在的最大的页码
    """
    catalog_page =-1
    for dst in dst_list:
        if dst.mark == MarkType.CATALOG:
            catalog_page = max(catalog_page, dst.attributes.page)
    return catalog_page
