from typing import List, Dict

from modules.entity.dst_entity import DST
from modules.flows.filters.catalog_filter import mark_catalog
from modules.flows.filters.header_tail_filter import mark_page_header_tail_text
from modules.pipeline.context import FileInfo


def dst_mark(dst_list: List[DST], page_map: Dict[int, List[DST]], file_info: FileInfo) -> List[DST]:
    sorted_pages = sorted(page_map.keys())
    dst_list = mark_page_header_tail_text(dst_list, page_map, sorted_pages, file_info)
    return mark_catalog(dst_list, page_map, sorted_pages)
