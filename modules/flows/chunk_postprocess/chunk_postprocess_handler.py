# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/6/10 10:05

from commons.trace.tracer import async_trace_span
from modules.entity.parse_entity import ParseRes, ImageType
from modules.flows.callback import callback_parse_background
from modules.flows.crop.crop import has_multiple_specific_chars
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext
from modules.entity.dst_entity import DSTType
from conf import ConfHandlerName


class ChunkPostProcessNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        """
        Process the context after cropping.
        This method is a placeholder for any post-crop processing logic.

        :param context: PipelineContext containing the current state of the pipeline
        :return: Updated PipelineContext
        """
        # Placeholder for any post-crop processing logic
        # Currently, it simply returns the context unchanged
        images = context.handler_results.get(ConfHandlerName.screenshot_handler, [])
        if context.table_pics:
            images.extend(context.table_pics)
        chunks = context.chunks

        if chunks and images:
            for i, image in enumerate(images):
                if image.image_type == ImageType.TABLE_IMAGE:
                    for chunk in chunks:
                        if image.dst_ids and image.dst_ids[0] in chunk.block:
                            if not image.chunk_ids:
                                image.chunk_ids = []
                            image.chunk_ids.append(chunk.chunk_id)
                if image.image_type == ImageType.CHECK_BOX_IMAGE:
                    for chunk in chunks:
                        if has_multiple_specific_chars(chunk.content) and image.page_num in chunk.page_num:
                            if not image.chunk_ids:
                                image.chunk_ids = []
                            image.chunk_ids.append(chunk.chunk_id)
        if images:
            crop_res = ParseRes(image=images, page_size=context.file_info.page_size,
                                word_count=context.file_info.word_count,
                                width=context.file_info.width, height=context.file_info.height,
                                is_scan=context.file_info.is_scan, rotate_page=context.file_info.rotate_page,
                                parse_version=context.parse_version)
            await callback_parse_background(crop_res, ConfHandlerName.screenshot_handler, context.token, context.need_callback,
                                            context.return_ks3_url, context.callback_url)
        if chunks:
            res = ParseRes(chunks=context.chunks, page_size=context.file_info.page_size,
                           word_count=context.file_info.word_count,
                           width=context.file_info.width, height=context.file_info.height,
                           is_scan=context.file_info.is_scan, image=context.image, rotate_page=context.file_info.rotate_page,
                           parse_version=context.parse_version)
            await callback_parse_background(res, ConfHandlerName.chunk_handler, context.token, context.need_callback,
                                            context.return_ks3_url, context.callback_url)

        return context
