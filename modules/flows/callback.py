import json
from typing import List, Optional

from commons.db.redis5dao import Redis5<PERSON><PERSON>
from commons.db.storedao import StoreDao
from conf import ConfStore
from modules.entity.parse_entity import GeneralParseStatus, RespGeneralParseData, PARSE_BACKGROUND_STATUS_KEY, DAY_7, \
    PARSE_BACKGROUND_CONTENT_PATH_KEY, ParseTarget, PARSE_BACKGROUND_PARSE_TARGET_KEY
from datetime import datetime
from modules.rpc.callback_func import RpcCallbackClient
from modules.rpc.common import get_host_and_uri


async def callback_parse_background(result, step_name: str, token: str, need_callback: bool, return_ks3_url: bool,
                              callback_url: str):
    # 设置状态为ok
    status = GeneralParseStatus.ok
    if result is None:
        status = GeneralParseStatus.fail
    resp = RespGeneralParseData(
        status=status,
    )
    resp.token = token
    ks3_url = upload_result_and_set_status(result, step_name, token)
    if need_callback:
        if return_ks3_url:
            # 生成ks3的url
            resp.res_ks3_url = ks3_url
        else:
            resp.parse_res = result
        # 获取host和uri
        host, uri = get_host_and_uri(callback_url)
        try:
            await RpcCallbackClient().send_callback(
                host=host,
                uri=uri,
                method="POST",
                data=resp
            )
        except Exception as e:
            raise Exception("Failed to callback result")


def upload_result_and_set_status(res, step_name, token):
    if res is not None:
        # Save the result
        res = res.dict()
        res = json.dumps(res)
        now_time = datetime.now().strftime('%Y%m%d')
        ks3_path = f"{ConfStore.back_store_dir}/{now_time}/parse_background_content_{step_name}_{token}"
        upload_status = StoreDao().upload_from_bytes(ks3_path, res.encode("utf-8"))
        if not upload_status:
            raise Exception("Failed to upload parsing result to KS3")
        url = StoreDao().generate_url(ks3_path, 1492073594)
        Redis5Dao().set(f"{PARSE_BACKGROUND_CONTENT_PATH_KEY}_{step_name}_{token}", url, ex=DAY_7)
        return url  # Return the KS3 path
    else:
        Redis5Dao().set(f"{PARSE_BACKGROUND_STATUS_KEY}_{token}", GeneralParseStatus.fail, ex=DAY_7)
        return None


def set_parse_target_status(token: str, parse_target: List[ParseTarget]):
    """
    Set the parse target status in Redis.
    """
    parse_target_json = json.dumps([target.value for target in parse_target])

    Redis5Dao().set(f"{PARSE_BACKGROUND_PARSE_TARGET_KEY}_{token}", parse_target_json, ex=DAY_7)
    return True


def get_parse_target_status(token: str) -> List[ParseTarget]:
    """
    Get the parse target status from Redis and convert it to a list of ParseTarget enums.
    """
    key = f"{PARSE_BACKGROUND_PARSE_TARGET_KEY}_{token}"
    data = Redis5Dao().get(key)
    if data:
        # Deserialize the JSON string and convert to ParseTarget enums
        return [ParseTarget(item) for item in json.loads(data)]
    return []