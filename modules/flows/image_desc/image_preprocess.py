# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/6/9 11:43

from commons.tools.utils import Singleton
from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.models.chat_data import MultiModalContent, MultiModalType, SftMultiModalText, SftMultiModalImage, SftMultiModalImageUrl
from commons.llm_gateway.models.chat_data import Message
from commons.tools.url_validator import validate_http_url
from modules.flows.image_desc.prompts import ImageDescriptionPrompt
from commons.llm_gateway.models.chat_data import SftBaseModelType

class ImagePreprocess(metaclass=Singleton):
    gateway = LLModelRpc.Gateway.Public
    selector: LLModelRpc.ModelSelector = None
    multimodal_selector: LLModelRpc.ModelSelector = None

    @classmethod
    async def get_image_desc(cls, context, image_url=None, image_base64=None):

        assert image_url is not None or image_base64 is not None

        if image_base64 is not None:
            # image_str = encode_image(image_path)
            content = [MultiModalContent(**{
                "type": MultiModalType.image,
                "content": f"data:image/png;base64,{image_base64}"}),
                       MultiModalContent(
                           **{"type": MultiModalType.text, "content": ImageDescriptionPrompt.format(context)})]
        else:
            is_valid = validate_http_url(image_url)
            if not is_valid:
                return "fail", None
            # content = [MultiModalContent(**{"type": MultiModalType.image_url, "content": image_url}),
            #            MultiModalContent(
            #                **{"type": MultiModalType.text, "content": ImageDescriptionPrompt.format(context)})]
            content = [
                SftMultiModalImage(type=MultiModalType.image_url, image_url=SftMultiModalImageUrl(url=image_url)),
                SftMultiModalText(type=MultiModalType.text, text=ImageDescriptionPrompt.format(context))
            ]

        messages = [Message(content=content, role="user")]
        status, res_text = await LLModelRpc().async_multimodal(cls.gateway, temperature=0.01, messages=messages, selector=cls.multimodal_selector,
                                                               sft_base_model=LLModelRpc.SftBaseModel(
                                                                   base_model=SftBaseModelType.qwen25_vl_32b_awq))
        return status, res_text