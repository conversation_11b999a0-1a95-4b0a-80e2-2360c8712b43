# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/6/9 11:29
from typing import List

from modules.pipeline.base import <PERSON><PERSON>ineHandler
from commons.trace.tracer import async_trace_span
from commons.llm_gateway.llm import LLMChatStatus
from conf import ConfImageFilter
from modules.pipeline.context import PipelineContext
from modules.entity.dst_entity import DST, DSTType
from modules.entity.parse_entity import ImageDesc, ParseRes
from modules.flows.callback import callback_parse_background
from modules.flows.image_desc.image_preprocess import ImagePreprocess
from commons.thread.multicoroutine import MultiCoroutine
from conf import ConfCoroutine, ConfHandlerName

class DescNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name
    async def _process_single_image_dst(self, context: PipelineContext, index: int, dst: DST,
                                        dst_list: List[DST]) -> tuple:
        """处理单个图片DST，获取上下文并调用图片描述服务"""
        try:
            # 获取前文上下文
            contents = []
            for prev_index in range(index - 1, -1, -1):
                if dst_list[prev_index].dst_type != DSTType.TEXT:
                    continue
                contents.append("".join(dst_list[prev_index].content))
                if sum(len(content) for content in contents) > 100:
                    break
            pre_context = "".join(reversed(contents))

            # 获取后文上下文
            suf_context = ""
            for next_index in range(index + 1, len(dst_list)):
                if dst_list[next_index].dst_type != DSTType.TEXT:
                    continue
                suf_context += "".join(dst_list[next_index].content)
                if len(suf_context) > 100:
                    break

            # 调用图片描述服务
            status, res_text = await ImagePreprocess().get_image_desc(
                context=pre_context + "\n" + suf_context,
                image_url=dst.content[0]
            )
            return (dst.id, status, res_text)
        except Exception as e:
            context.business_log.error(f"Error processing image description for dst {dst.id}: {e}")
            return (dst.id, LLMChatStatus.FAIL, None)

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            # 不在本节点修改 DST 内容，避免不必要的拷贝开销
            dst_list = context.dst

            # 收集需要处理描述的图片DST信息
            eligible_ids = set()
            pool = None
            for index, dst in enumerate(dst_list):
                if dst.dst_type != DSTType.IMAGE:
                    continue
                # 过滤小图
                if dst.image_pixel[0] <= ConfImageFilter.width or dst.image_pixel[1] <= ConfImageFilter.height:
                    continue
                if pool is None:
                    pool = MultiCoroutine()
                task_key = f"desc_{dst.id}"
                pool.add_task(task_key, self._process_single_image_dst(context, index, dst, dst_list))
                eligible_ids.add(dst.id)

            res = []
            # 限制并发执行所有任务
            if eligible_ids and pool is not None:
                pool_results = await pool.run_limit(ConfCoroutine.image_desc_limit)
                # 处理结果
                for _, (dst_id, status, res_text) in pool_results.items():
                    if status == LLMChatStatus.OK and dst_id in eligible_ids:
                        res.append(ImageDesc(dst_id=dst_id, desc=res_text))
                    else:
                        context.business_log.error(
                            f"获取图片描述失败, dst_id={dst_id}, 多模态模型返回状态: {status}")
                parse_res = ParseRes(img_desc=res, page_size=context.file_info.page_size,
                                     word_count=context.file_info.word_count,
                                     width=context.file_info.width, height=context.file_info.height,
                                     is_scan=context.file_info.is_scan, rotate_page=context.file_info.rotate_page,
                                     parse_version=context.parse_version)
                await callback_parse_background(parse_res, ConfHandlerName.image_desc_handler, context.token,
                                                context.need_callback,
                                                context.return_ks3_url, context.callback_url)
            context.handler_results[self.name] = res
            return context
        except Exception as e:
            context.business_log.error(f"Error in DescNode.process: {e}")
            raise e
