from dataclasses import field
from enum import Enum
from typing import Set

from pydantic import BaseModel


class ParserName(Enum):
    MuPdfParser = "MuPdfParser"
    KdcParser = "KdcParser"
    ImageParser = "ImageParser"


class ParserConfig(BaseModel):
    """
    解析器配置类
    """
    # 解析器名称
    parser_name: ParserName = ParserName.KdcParser
    # 解析页码
    processing_pages: Set[int] = field(default_factory=set)  # 需要处理的页码集合
    is_all: bool = False  # 是否全部页码都需要处理
    # is_scan: bool = False  # 是否需要扫描页码

