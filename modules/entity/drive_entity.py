# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/01/29 10:00

from typing import Optional, Union
from pydantic import BaseModel, field_validator
from enum import Enum

from commons.auth.auth_rpc import SigVerType


class RpcName(str, Enum):
    """RPC实现名称枚举"""
    WPSV5 = "wpsv5"

class DriveParams(BaseModel):
    """
    Drive RPC接口参数结构体
    """
    class_path: str  # 类路径，用于标识具体的Drive RPC实现
    enabled: bool = True  # 是否启用该实现
    host: str  # RPC服务地址
    ak_env: str  # Access Key环境变量名
    sk_env: str  # Secret Key环境变量名
    sig_type: Union[str, SigVerType]  # 签名类型，支持字符串和枚举
    download_uri: str  # 下载URL模板，格式化时需要传入file_id参数
    
    @field_validator('sig_type')
    @classmethod
    def validate_sig_type(cls, v: Union[str, SigVerType]) -> SigVerType:
        """验证并转换签名类型"""
        if isinstance(v, SigVerType):
            return v
        if isinstance(v, str):
            if v.lower() == "wps2":
                return SigVerType.wps2
            elif v.lower() == "wps4":
                return SigVerType.wps4
            else:
                raise ValueError(f"Invalid sig_type: {v}. Must be 'wps2' or 'wps4'")
        raise ValueError(f"sig_type must be string or SigVerType, got {type(v)}")
    
    def get_config_tuple(self, ak: str, sk: str) -> tuple[str, str, str, SigVerType, str]:
        """
        获取初始化RPC所需的参数元组
        
        Args:
            ak: Access Key
            sk: Secret Key
            
        Returns:
            tuple: (host, ak, sk, sig_type, download_uri)
        """
        return self.host, ak, sk, self.sig_type, self.download_uri


class DriveFileInfo(BaseModel):
    """
    Drive文件信息结构体
    """
    url: Optional[str] = None


class DriveFileResponse(BaseModel):
    """
    Drive文件响应结构体
    
    用于统一Drive RPC接口的返回结果，按照API原始格式设计
    """
    result: str  # "ok" | "error" 等
    fileinfo: Optional[DriveFileInfo] = None
    error_message: Optional[str] = None  # 当result为error时的错误描述
    status_code: Optional[int] = None    # HTTP状态码
    
    @classmethod
    def success_response(cls, url: str) -> 'DriveFileResponse':
        """创建成功响应"""
        return cls(
            result="ok",
            fileinfo=DriveFileInfo(url=url)
        )
    
    @classmethod  
    def error_response(cls, error_message: str, status_code: Optional[int] = None, result: str = "error") -> 'DriveFileResponse':
        """创建错误响应"""
        return cls(
            result=result,
            fileinfo=None,
            error_message=error_message,
            status_code=status_code
        )
    
    @property
    def is_success(self) -> bool:
        """判断是否成功"""
        return self.result == "ok"
    
    @property 
    def url(self) -> Optional[str]:
        """获取文件URL"""
        return self.fileinfo.url if self.fileinfo else None 