from dataclasses import dataclass
from typing import List
from pydantic import BaseModel


class Box(BaseModel):
    cls_id: int
    label: str
    score: float
    coordinate: List[float]


class LayOutData(BaseModel):
    boxes: List[Box]


class LayOutRep(BaseModel):
    code: int
    message: str
    data: LayOutData


class OcrData(BaseModel):
    rec_texts: List[str]


class OcrRep(BaseModel):
    code: int
    message: str
    data: OcrData


@dataclass
class SealDetectionResult:
    input_path: str
    page_index: int
    boxes: List[Box]
