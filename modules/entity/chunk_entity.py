from enum import Enum

from pydantic import BaseModel, Field
from typing import List, Optional

from modules.entity.dst_entity import DST


class DSTNode(BaseModel):
    """
    Represents a node in the DST tree.
    """
    blocks: List[DST]  # 当前节点的内容块集合
    children: List['DSTNode']  # 子节点列表


# class DSTTree(BaseModel):
#     """
#     Represents a tree structure of DST objects.
#     """
#     blocks: List[DST]  # 当前节点的内容块集合
#     children: List['DSTNode']  # 子节点列表

class LabelType(str, Enum):
    TEXT = "text"
    TABLE = "table"
    IMAGE = "image"
    CODE = "code"
    FORMULA = "formula"


class PageImage(BaseModel):
    page_num: int = Field(..., description="页码")
    image_url: str = Field(..., description="图片URL")


class Chunk(BaseModel):
    """
    Represents a chunk of content with metadata.
    """
    chunk_id: str = Field(..., description="Chunk ID")
    page_size: int = Field(..., description="Page size of the document")
    content: str = Field(..., description="Main content of the chunk, including titles and text")
    label: LabelType = Field(..., description="Label indicating the type of content (e.g., text, table)")
    content_embedding: Optional[List[float]] = []
    page_num: List[int] = Field(..., description="List of page numbers where the chunk appears")
    block: List[str] = Field(..., description="List of block IDs associated with the chunk")
    dsts: Optional[List[DST]] = None
    pre_chunk: Optional[str] = Field(None, description="Link to another chunk")
    next_chunk: Optional[str] = Field(None, description="Link to another chunk")
    mark: Optional[str] = Field(None, description="页眉页脚标记")

    # 下面的字段作为crop handler的结果字段
    table_image: Optional[str] = Field(None, description="表格图片的URL")
    scan_pdf_image: Optional[List[PageImage]] = Field(None, description="扫描PDF的图片URL")
    checkbox_image: Optional[List[PageImage]] = Field(None, description="复选框图片的URL")
