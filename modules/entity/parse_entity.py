from enum import Enum
from typing import Optional, List, Dict

from pydantic import BaseModel, Field

from modules.entity.chunk_entity import Chunk

class ImageType(str, Enum):
    INPUT_IMAGE = "input_image"
    TABLE_IMAGE = "table_image"
    SCAN_IMAGE = "scan_image"
    CHECK_BOX_IMAGE = "check_box_image"

class ParseTarget(str, Enum):
    chunk = "chunk"
    fake_title = "fake_title"
    keywords = "keywords"
    summary = "summary"
    screenshot = "screenshot"
    img_desc = "img_desc"


class Image(BaseModel):
    page_num: int  # Page number
    url: str  # Screenshot URL for the page
    chunk_ids: Optional[List[str]] = None  # List of chunk IDs
    dst_ids: Optional[List[str]] = None  # List of DST IDs
    image_type: ImageType  # Image type as an enumeration

class ImageDesc(BaseModel):
    dst_id: str                 # DST ID
    desc: Optional[str] = None  # Description of the image


class GeneralParseStatus(str, Enum):
    wait = "wait"
    ok = "ok"
    fail = "fail"
    limit = "limit"



class ParseRes(BaseModel):
    chunks: Optional[List[Chunk]] = None  # Chunks as strings or objects
    fake_title: Optional[str] = None  # Fake title
    fake_title_embedding: Optional[List[float]] = None  # Fake title vector
    keywords: Optional[List[str]] = None  # List of keywords
    keywords_embedding: Optional[List[float]] = None  # Keywords vector
    summary: Optional[str] = None  # Summary
    summary_embedding: Optional[List[float]] = None  # Summary vector
    page_size: Optional[int] = None  # Number of pages in the file
    word_count: Optional[int] = None # Word count in the file
    width: Optional[int] = None # Width of each page
    height: Optional[int] = None # Height of each page
    image: Optional[List[Image]] = None # Image object for screenshot target
    is_scan: Optional[bool] = None # Whether the file is a scanned document
    rotate_page: Optional[Dict[int, List[int]]] = Field(default_factory=dict, description="旋转度的页码列表")
    parse_version: Optional[str] = None  # Version of the parse
    img_desc: Optional[List[ImageDesc]] = None # List of image descriptions

class RespGeneralParseData(BaseModel):
    status: str = GeneralParseStatus.fail
    token: Optional[str] = None
    fileinfo: Optional[dict] = None
    res_ks3_url: Optional[str] = None
    parse_res: Optional[ParseRes] = None
    # dst_parse_version: Optional[str] = None  # Version of the DST parse
    parse_target: List[ParseTarget] = None


class RespMergeTableData(BaseModel):
    content: Optional[List[str]] = None  # Merged table content


PARSE_BACKGROUND_STATUS_KEY = "parse_background_status"
PARSE_BACKGROUND_PARSE_TARGET_KEY = "parse_background_parse_target"
PARSE_BACKGROUND_CONTENT_PATH_KEY = "parse_background_content_path"
MINUTE_20 = 1200
HOUR_2 = 7200
DAY_7 = 604800