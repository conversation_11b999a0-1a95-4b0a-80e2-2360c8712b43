from typing import List

from pydantic import BaseModel
from modules.entity.parse_entity import Image


class CheckBoxCorrect(BaseModel):
    content: str
    page: int = 0
    images: List[Image] = []


FRAME_LITTLE = "□"
FRAME_BIG = "☐"


def replace_frame(text: str):
    return text.replace(FRAME_LITTLE, FRAME_BIG)


def replace_frame_little(text: str):
    return text.replace(FRAME_BIG, FRAME_LITTLE)
