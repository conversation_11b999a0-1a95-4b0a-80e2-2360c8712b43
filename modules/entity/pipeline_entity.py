from pydantic import BaseModel, Field, validator
from typing import List, Optional
from modules.entity.dst_entity import DST
from modules.entity.chunk_entity import Chunk


# class PipeLineResult(BaseModel):
#     """
#     pipeline最终的处理结果
#     """
#     dst_res: List[DST] = Field(default_factory=list, description="DST实体结果列表")
#     chunk_res: List[Chunk] = Field(default_factory=list, description="Chunk实体结果列表")
#     dst_tree: Optional[str] = Field(default=None, description="DST大纲树")
#     is_scan: bool = Field(default=False, description="是否为扫描件")
#
#     @validator('dst_tree')
#     def dst_tree_not_empty(cls, v):
#         if v is None or v.strip() == "":
#             raise ValueError('dst_tree 不能为空')
#         return v
#
#     @validator('dst_res', 'chunk_res')
#     def list_type_check(cls, v):
#         if not isinstance(v, list):
#             raise TypeError('必须为列表类型')
#         return v