# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/22 15:03

from pydantic import BaseModel, Field
from enum import Enum
from typing import List, Optional, Union

# kdc结果中，文档内容的字段
KDC_DOC_FIELD = "doc"


class OutlineLevel(int, Enum):
    l1 = 1  # 标题
    l2 = 2
    l3 = 3
    l4 = 4
    l5 = 5
    l6 = 6
    l7 = 7
    l8 = 8
    l9 = 9
    l10 = 10  # 正文


class Highlight(BaseModel):
    """
    Highlight对象用来表示一个高亮块。
    """
    emoji: Optional[str] = None  # 可选。高亮表情
    background_color: Optional[str] = None  # 可选。背景色， RGBA 16进制描述，例如：#FF0000FF; 最后两位为透明度
    blocks: List['Block'] = None  # 可选。高亮块内的块内容


class HyperLink(BaseModel):
    """
    HyperLink对象用来表示一个超链接。
    """
    display_text: str  # 必选。超链接显示文本
    target: str  # 必选。超链接目标地址，可能是url或其他
    references: Optional[List['Reference']] = None  # 可选。关联的正文


class RotateType(int, Enum):
    rotate_0 = 0  # 0度
    rotate90 = 90  # 90度
    rotate180 = 180  # 180度
    rotate270 = 270  # 270度


class Tag(BaseModel):
    name: str  # 必选。tag名称
    value: str  # 必选。tag值


class ReferenctType(str, Enum):
    run = "run"  # 表示此id是 run ID
    block = "block"  # 表示此id是 block ID
    cell = "cell"  # 表示此id是cell ID


class Reference(BaseModel):
    """
    Reference对象用来表示一个引用范围。
    """
    id: str  # 必选。引用范围的 id，可以是 run.id, block.id。当 type 为 cell 时是一套指定单元格及显示位置的编码，格式为 'rf?rt:cf?ct?p?!'，表示区域的行起始、行终止、列起始、列终止及起始字符位置、长度，位置、长度为 -1 表示整个单元格内容
    type: ReferenctType  # 必选。id的类型


class Rectangle(BaseModel):
    """
    Rectangle对象用来表示一个矩形区域（BBox）。
    """
    x1: int  # 必选。left-top点 x 坐标
    y1: int  # 必选。left-top点 y 坐标
    x2: int  # 必选。right-bottom点 x 坐标
    y2: int  # 必选。right-bottom点 y 坐标


class Size(BaseModel):
    width: int  # 必选。宽度
    height: int  # 必选。高度


class ParaIndent(BaseModel):
    """
    ParaIndent对象用来表示段落缩进属性。
    """
    abs_left_indent: Optional[int] = None  # 可选。绝对左缩进，一般与相对左缩进不同时出现，且优先级高于相对左缩进。
    rel_left_indent: Optional[int] = None  # 可选。相对左缩进（原图识别为 rel_left_idrent，已修正）。
    abs_first_indent: Optional[int] = None  # 可选。绝对首行缩进，一般与相对首行缩进不同时出现，且优先级高于相对首行缩进。
    rel_first_indent: Optional[int] = None  # 可选。相对首行缩进


class PageProp(BaseModel):
    """
    PageProp对象用来表示一个页面的属性。
    """
    size: Optional[Size] = None  # 可选。页面大小，单位缇
    rotate: Optional[RotateType] = None  # 可选。页面和内容的旋转角度
    offset_angle: Optional[float] = None  # 可选。扫描件的旋转小角度
    dpi: Optional[int] = None  # 可选。图像场景中，KDC 结果中坐标、页大小、字号等物理单位到像素单位的 dpi
    indent: Optional[ParaIndent] = None  # 可选。段落缩进


class DocProp(BaseModel):
    """
    DocProp对象用来表示文档的属性。
    """
    page_count: Optional[int] = None  # 可选。文档总页数
    page_props: Optional[List[PageProp]] = None  # 可选。文档每一页的属性，按照页索引排序

class PPTProp(BaseModel):
    """
    PPTProp对象用来表示ppt文档的属性。
    """
    note_size: Optional[Size] = None  # 可选。文档总页数
    slide_size: Optional[Size] = None  # 可选。文档每一页的属性，按照页索引排序


class TableCell(BaseModel):
    """
    TableCell对象用来表示一个表格单元格。
    """
    blocks: Optional[List['Block']] = None  # 必选。单元格内的内容块列表
    row_span: Optional[int] = None  # 可选。垂直方向（向下）合并的单元格数量
    col_span: Optional[int] = None  # 可选。水平方向（向右）合并的单元格数量
    id: Optional[str] = None  # 可选。单元格 id
    bounding_box: Optional[Rectangle] = None  # 可选。单元格外接矩形，kdc文档写的是数组，实际不是


class TableRow(BaseModel):
    """
    TableRow对象用来表示一个表格行。
    """
    cells: Optional[List[TableCell]] = None # 必选。表格行每一行的单元格序列


class ScriptPos(str, Enum):
    subscript = "subscript"  # 下标
    superScript = "superscript"  # 上标


class RunProp(BaseModel):
    """
    RunProp对象用来表示一个句的属性。
    """
    size: Optional[float] = None  # 可选。字号大小，单位磅
    color: Optional[str] = None  # 可选。字体颜色，需为 RGBA 格式
    font_ascii: Optional[str] = None  # 可选。西文字体名称
    font_east_asia: Optional[str] = None  # 可选。中文字体名称
    bold: Optional[bool] = None  # 可选。是否加粗，默认false
    italic: Optional[bool] = None  # 可选。是否斜体，默认false
    underline: Optional[bool] = None  # 可选。是否下划线，默认false
    strike: Optional[bool] = None  # 可选。是否删除线，默认false
    high_light: Optional[str] = None  # 可选。高亮色，RGBA格式
    script_pos: Optional[ScriptPos] = None  # 可选。上下标


class ParaAlignment(str, Enum):
    left = "left"  # 左对齐
    center = "center"  # 居中对齐
    right = "right"  # 右对齐
    justify = "justify"  # 两端对齐
    distribute = "distribute"  # 分散对齐
    fill = "fill"  # 填充（表格横向布局）
    center_continuous = "center_continuous"  # 跨列居中（表格横向布局）
    general = "general"  # 常规


class ParaProp(BaseModel):
    """
    ParaProp对象用来表示一个段的属性。
    """
    def_run_prop: Optional[RunProp] = None  # 可选。段落的默认句属性
    alignment: Optional[ParaAlignment] = None  # 可选。段落对齐方式枚举
    outline_level: Optional[int] = None  # 可选。大纲级别，1 - 9为标题，10为正文，和Node对象的outline_level含义相同
    list_string: Optional[str] = None  # 可选。段落上的项目编号字符串


class FootNote(BaseModel):
    """
    FootNote对象用来表示脚注实体。文档中出现的脚注会以数组的形式组织在Document对象的footnotes字段中。
    """
    id: str  # 必选。脚注id
    blocks: Optional[List['Block']] = None  # 可选。脚注体的数据


class EndNote(BaseModel):
    """
    EndNote对象用来表示尾注实体。文档中出现的尾注会以数组的形式组织在Document对象的endnotes字段中。
    """
    id: str  # 必选。尾注id
    blocks: Optional[List['Block']] = None  # 可选。尾注体的数据


class Footer(BaseModel):
    """
    Footer 对象用来表示页脚实体。文档中出现的页脚会以数组的形式组织在 Document 对象的 footers 字段中。
    """
    id: str  # 必选。页脚id
    blocks: Optional[List['Block']] = None  # 可选。页脚体的数据


class Header(BaseModel):
    """
    Header 对象用来表示页眉实体。文档中出现的页眉会以数组的形式组织在 Document 对象的 headers 字段中。
    """
    id: str  # 必选。页眉id
    blocks: Optional[List['Block']] = None  # 可选。页眉体的数据


class Comment(BaseModel):
    """
    Comment对象用来表示批注（评论）实体。文档中出现的批注体会以数组的形式组织在Document对象的comments字段中。
    """
    references: List[Reference]  # 必选。批注引用的范围
    author: str  # 必选。批注作者
    date: str  # 必选。批注日期时间，ISO 8601日期时间格式
    blocks: Optional[List['Block']] = None  # 可选。批注体数据
    replys: Optional[List['Comment']] = None  # 可选。批注回复


class Media(BaseModel):
    """
    Media 对象用来表示图片、音频以及视频等媒体。文档中出现的媒体会以数组的形式组织在 Document 对象的 media 字段中，Component 通过 media_id 字段引用这些 Media 对象。
    """
    id: str  # 必选。media id 全局唯一
    data: Optional[str] = None  # 可选。媒体数据，base64形式，data和url二者选一
    url: Optional[str] = None  # 可选。媒体数据，url链接形式，媒体数据存放在外部，data和url二者选一
    mime_type: Optional[str] = None  # 可选。媒体数据类型，image/jpeg、image/png、image/tiff、image/wdp、video/mp4、audio/mp3
    sha1: Optional[str] = None  # 可选。媒体数据的sha1，资源校验


class ComponentType(str, Enum):
    image = "image"  # 图片
    audio = "audio"  # 音频
    video = "video"  # 视频
    chart = "chart"  # 图表
    dbsheet = "dbsheet"  # 多维表
    mindmap = "mindmap"  # 脑图
    flowchart = "flowchart"  # 流程图
    spreadsheet = "spreadsheet"  # 电子表格
    other = "other"  # 兜底类型

    @classmethod
    def _missing_(cls, value):
        """自动将未定义的类型转换为other"""
        return cls.other


class Component(BaseModel):
    """
    Component对象用来表示一个内容部件，分为媒体（图片、音频、视频）以及像日历、脑图等第三方应用的数据抽象。目前主要用来表示媒体。
    """
    type: ComponentType  # 必选。部件对象的类型
    media_id: str  # 必选。部件对象引用的媒体数据id


class TextBox(BaseModel):
    """
    TextBox对象用来表示一个文本框。
    """
    blocks: List['Block']  # 必选。文本框内的内容块列表


class Table(BaseModel):
    """
    Table对象用来表示一个表格。
    """
    rows: Optional[List[TableRow]] = None  # 可选。表格行列表


class RangeFromTo(BaseModel):
    from_: int = Field(alias="from")  # 必选。起始索引，从0开始
    to: int  # 必选。终止索引，从0开始


class Range(BaseModel):
    row_spans: RangeFromTo  # 必选。行范围
    col_spans: RangeFromTo  # 必选。列范围


class FormulaType(str, Enum):
    normal = "normal"  # 常规
    array = "array"  # 数组公式
    dynamic_array = "dynamic_array"  # 动态数组公式


class Formula(BaseModel):
    type: Optional[FormulaType] = None  # 可选。公式类型
    result_range: Optional[Range] = None  # 可选。结果所占区域，数组公式有
    text: Optional[str] = None  # 可选。公式文本


class DateTime(BaseModel):
    timestamp: Optional[int] = None  # 可选。时间戳，精度毫秒


class RunType(str, Enum):
    normal = "normal"  # 普通句，文本句。默认此类型
    datetime = "datetime"  # 日期时间
    formula = "formula"  # 公式
    inline_component = "inline_component"  # 嵌入式部件
    endnote_reference = "endnote_reference"  # 尾注引用
    footnote_reference = "footnote_reference"  # 脚注引用


class Run(BaseModel):
    """
    Run对象用来表示一个句。
    """
    id: Optional[str] = None  # 可选。句的id
    type: Optional[RunType] = None  # 可选。句的类型，若无此字段，类型为普通类型，即RunType.normal，kdc文档写的是数组，实际不是
    prop: Optional[RunProp] = None  # 可选。句属性，kdc文档写的是数组，实际不是
    text: Optional[str] = None  # 可选。文本内容
    datetime: Optional[DateTime] = None  # 可选。日期时间，kdc文档写的是数组，实际不是
    formula: Optional[Formula] = None  # 可选。公式，kdc文档写的是数组，实际不是
    inline_component: Optional[Component] = None  # 可选。嵌入式部件，kdc文档写的是数组，实际不是
    endnote_id: Optional[str] = None  # 可选。尾注引用id，表示此句是尾注占位符
    footnote_id: Optional[str] = None  # 可选。脚注引用id，表示此句是脚注占位符


class Para(BaseModel):
    """
    Para对象用来表示一个段。
    """
    prop: Optional[ParaProp] = None  # 可选。段落属性, kdc文档写的是数组，实际不是
    runs: Optional[List[Run]] = None  # 可选。段落内的句列表


class BlockType(str, Enum):
    para = "para"  # 段落
    table = "table"  # 表格
    component = "component"  # 部件（图片、音频、日历等）
    textbox = "textbox"  # 文本框
    highlight = "highlight"  # 高亮块
    header_reference = "header_reference"  # 页眉引用
    footer_reference = "footer_reference"  # 页脚引用


class Block(BaseModel):
    """
    Block 对象用来表示一个内容块，内容块分为段(Para 对象)、表(Table 对象)以及部件(Component 对象) ，部件又分为图片、音频、视频、日历等。
    """
    type: BlockType  # 必选。内容块的具体类型
    para: Optional[Para] = None  # 可选。内容块的段落内容，当BlockType为para时有效
    table: Optional[Table] = None  # 可选。内容块的表格，当BlockType为table时有效
    textbox: Optional[TextBox] = None  # 可选。内容块的文本框，当BlockType为textbox时有效
    component: Optional[Component] = None  # 可选。内容块的部件，当BlockType为component时有效
    highlight: Optional[Highlight] = None  # 可选。高亮块，当BlockType为highlight时有效
    page_index: Optional[int] = None  # 可选。文档排版后，内容块所在的页面索引
    bounding_box: Optional[Rectangle] = None  # 可选。内容块所在区域的几何坐标信息（以页左上角为参考原点）
    rotate: Optional[float] = None  # 可选。当前块的旋转角度
    index: Optional[int] = None  # 可选。当前内容块的索引（以整个KDC文档为全局）
    id: Optional[str] = None  # 可选。当前块的id
    tags: Optional[List[Tag]] = None  # 可选。当前块附加的tag集合


class Node(BaseModel):
    """
    Node对象用来表示内容树的一个节点。同一大纲级别的节点在内容树中的深度相同。
    """
    outline_level: Optional[int] = None  # 可选。大纲级别，1-9为标题，10为正文
    blocks: Optional[List[Block]] = None  # 可选。当前节点自身的内容块集合
    children: Optional[List['Node']] = None  # 可选。子节点


class Document(BaseModel):
    """
    Document对象用来表示KDC文档的根对象。文档内容可以用树状的形式组织，也可以用数组的形式组织，这取决于下游的选择。目前的实现只支持树状。
    """
    prop: Optional[Union[DocProp, PPTProp]] = None  # 可选。文档属性
    tree: Optional[Node] = None  # 可选。以大纲级别为层级，将文档内容块组织成一棵树，tree与blocks字段必须有一个存在
    blocks: Optional[List[Block]] = None  # 可选。文档内容按顺序组织成一个数组，tree与blocks字段必须有一个存在
    medias: Optional[List[Media]] = None  # 可选。文档中媒体对象组织成一个数组
    comments: Optional[List[Comment]] = None  # 可选。文档中所有批注（评论）体组织成一个数组
    hyperlinks: Optional[List[HyperLink]] = None  # 可选。文档中所有超链接对象组织成一个数组
    headers: Optional[List[Header]] = None  # 可选。文档中所有页眉对象组织成一个数组
    footers: Optional[List[Footer]] = None  # 可选。文档中所有页脚对象组织成一个数组
    footnotes: Optional[List[FootNote]] = None  # 可选。文档中所有脚注对象组织成一个数组
    endnotes: Optional[List[EndNote]] = None  # 可选。文档中所有尾注对象组织成一个数组
    page_start: Optional[int] = 0  # 可选。解析的起始页码，默认为0，表示从第一页开始解析。此字段不在KDC文档中存在


# doc
#############################################################################################################################

class OperatorType(str, Enum):
    none = "none"  # 无
    between = "between"  # ...之间
    not_between = "not_between"  # 不在...之间
    equal = "equal"  # 等于
    not_equal = "not_equal"  # 不等于
    greater = "greater"  # 大于
    less = "less"  # 小于
    greater_equal = "greater_equal"  # 大于等于
    less_equal = "less_equal"  # 小于等于


class ValidationType(str, Enum):
    date = "date"  # 日期有效性
    time = "time"  # 时间有效性
    text_length = "text_length"  # 文字长度有效性
    custom = "custom"  # 自定义有效性
    any_value = "any_value"  # 任意值
    whole = "whole"  # 整数有效性
    decimal = "decimal"  # 小数有效性
    list = "list"  # 下拉列表有效性


class DbDescriptionType(str, Enum):
    file = "file"
    text = "text"
    attachment = "attachment"


class DbDescription(BaseModel):
    id: str  # 必选。说明页面引用id
    type: DbDescriptionType  # 必选。说明页面文档引用类型
    icon: Optional[str] = None  # 可选。图标


class DataValidation(BaseModel):
    type: ValidationType  # 必选。数据有效性类型
    ref_ranges: List[Range]  # 必选。数据有效性的范围
    operator: Optional[OperatorType] = None  # 可选。有效性判断类型
    multi_support: Optional[bool] = None  # 可选。是否支持多选
    formula1: Optional[str] = None  # 可选。数据有效性的第一个限制公式
    formula2: Optional[str] = None  # 可选。数据有效性的第二个限制公式


class DBStatisticType(str, Enum):
    null = "null"  # 无统计选项
    count = "count"  # 统计内容数量
    sum = "sum"  # 统计内容综合
    filled_count = "filled_count"  # 统计已填入的内容数量
    unfilled_count = "unfilled_count"  # 统计未填入的内容数量
    filled_percentage = "filled_percentage"  # 统计已填入的内容百分比
    unfilled_percentage = "unfilled_percentage"  # 统计未填入的内容百分比
    unique_count = "unique_count"  # 统计重复的数量
    unique_percentage = "unique_percentage"  # 统计重复的百分比
    avg = "avg"  # 统计平均值
    max = "max"  # 统计最大值
    min = "min"  # 统计最小值
    checked_percentage = "checked_percentage"  # 统计选中项的百分比
    unchecked_percentage = "unchecked_percentage"  # 统计未选中项的百分比
    min_time = "min_time"  # 统计最早的时间
    max_time = "max_time"  # 统计最晚的时间
    day_range = "day_range"  # 统计日数的时间范围
    month_range = "month_range"  # 统计月数的时间范围


class DBStatisticOptions(BaseModel):
    field_id: str  # 必选。表格字段ID
    statistic_option: Optional[DBStatisticType] = None  # 可选。统计选项枚举


class DashBoardModule(BaseModel):
    id: Optional[str] = None  # 可选。当前module ID
    source_sheet_id: Optional[int] = None  # 可选。仪表盘组件关联的源表格 ID
    statistic_options: Optional[List[DBStatisticOptions]] = None  # 可选。仪表盘组件的统计方法
    chart_info: Optional[str] = None  # 可选。统计数据仪表盘信息,包括图表类型、名称等
    rich_text_content: Optional[str] = None  # 可选。文本仪表盘信息


class DBDashBoard(BaseModel):
    db_module: List[DashBoardModule]  # 必选。多维表仪表盘组件信息


class DBNodeType(str, Enum):
    folder = "folder"  # 此多维表根节点类型为"文件夹"
    sheet = "sheet"  # 此多维表根节点类型为"数据表"


class DBFolderTree(BaseModel):
    level: str  # 必选。层级编号(eg:1.3.2)
    sheet_id: Optional[int] = None  # 可选。此根节点对应的数据表id,值为-1时表示文件夹
    name: Optional[str] = None  # 可选。文件夹名称,为空时表示非文件夹


class StringInfo(BaseModel):
    style: Optional[dict] = None  # 可选。字体格式使用，kdc文档未具体提供格式
    content: Optional[str] = None  # 可选。内容


class SharedString(BaseModel):
    items: Optional[List[Run]] = None  # 可选。一个共享文本的信息，由Run数组表示


class CellAlign(BaseModel):
    horizontal: Optional[ParaAlignment] = None  # 可选。水平对齐方式
    vertical: Optional[ParaAlignment] = None  # 可选。垂直对齐方式


class CellProperty(BaseModel):
    fonts: Optional[RunProp] = None  # 可选。字体格式
    num_format: Optional[str] = None  # 可选。数字格式
    align: Optional[CellAlign] = None  # 可选。对齐方式


class CellDataType(str, Enum):
    boolean = "boolean"  # 布尔值
    date = "date"  # 日期
    number = "number"  # 数值
    error = "error"  # 错误值
    shared = "shared"  # 共享文本串
    text = "text"  # 文本串
    handle = "handle"  # 句柄(handle_tokens用)


class Cell(BaseModel):
    """
    单个单元格属性
    """
    index: int  # 必选。列索引，从0开始计数
    type: Optional[CellDataType] = None  # 可选。单元格数据类型
    value: Optional[str] = None  # 可选。单元格值，当type为shared时为索引
    style_ref: Optional[int] = None  # 可选。格式索引，从0开始计数
    image_id: Optional[str] = None  # 可选。单元格图片id
    formula: Optional[Formula] = None  # 可选。公式信息
    db_handle_id: Optional[int] = None  # 可选。多维表的handle_id


class SheetData(BaseModel):
    index: int  # 必选。行索引，从0开始计数
    height: Optional[float] = None  # 可选。行高
    spans: Optional[RangeFromTo] = None  # 可选。使用列范围
    cells: Optional[List[Cell]] = None  # 可选。单元格信息


class DBSheetWidth(BaseModel):
    field_id: str  # 必选。列对应字段id
    value: float  # 必选。宽度值


class TokenQRLabel(BaseModel):
    page_id: int  # 必选。对应的sheet id
    key: str  # 必选。二维码的key，唯一
    content: Optional[List[str]] = None  # 可选。内容数组


class TokenAddress(BaseModel):
    detail: Optional[str] = None  # 可选。详细地址
    district: List[str]  # 必选。大致地址（省、市...）


class TokenNote(BaseModel):
    file_id: str  # 必选。文件id
    content: str  # 必选。内容


class TokenAttachment(BaseModel):
    file_id: Optional[str] = None  # 可选。文件id
    type: Optional[str] = None  # 可选。文件类型
    name: Optional[str] = None  # 可选。文件名
    size: Optional[float] = None  # 可选。文件大小
    link: Optional[str] = None  # 可选。url链接
    image_size: Optional[str] = None  # 可选。图片大小(eg:60 * 60)


class TokenArray(BaseModel):
    type: CellDataType  # 必选。数据类型
    value: Optional[str] = None  # 可选。数据值


class TokenLink(BaseModel):
    sheet_id: int  # 必选。关联sheet id
    record_id: str  # 必选。关联的record id
    data_id: int  # 必选。shared_strings id


class HandleType(str, Enum):
    link = "link"  # 关联
    array = "array"  # 数组
    contact = "contact"  # 联系人
    attachment = "attachment"  # 图片与附件
    note = "note"  # 富文本
    address = "address"  # 地址
    select_items = "select_items"  # 选项
    hyperlink = "hyperlink"  # 超链接
    qrlabel = "qrlabel"  # 二维码(目前AS特有)


class DBHandleToken(BaseModel):
    type: Optional[HandleType] = None  # 可选。handle token类型
    link: Optional[List[TokenLink]] = None  # 可选。关联token数据数组
    array: Optional[List[TokenArray]] = None  # 可选。数组token数据数组
    contact_id: Optional[str] = None  # 可选。联系人token数据
    attachment: Optional[TokenAttachment] = None  # 可选。图片与附件token数据
    note: Optional[TokenNote] = None  # 可选。富文本token数据
    address: Optional[TokenAddress] = None  # 可选。地址token数据
    selected: Optional[List[int]] = None  # 可选。选项token数据数组,是SharedString的索引
    hyperlink: Optional[HyperLink] = None  # 可选。超链接token数据
    qrlabel: Optional[TokenQRLabel] = None  # 可选。二维码token数据(目前仅as有)


class FilterValueType(str, Enum):
    null = "null"  # 占位,无意义
    anything = "anything"  # 任意
    text = "text"  # 文本
    num = "num"  # 数字
    date = "date"  # 日期
    time = "time"  # 时间
    select_item = "select_item"  # 选中项目
    contact = "contact"  # 联系人
    checkbox = "checkbox"  # 复选框
    dynamic_simple = "dynamic_simple"  # 动态值(上周、上月)
    dynamic_threshold = "dynamic_threshold"  # 动态带预支(前10个、前5%)
    link = "link"  # 关联


class DBFilterValue(BaseModel):
    type: FilterValueType  # 必选。筛选值类型
    value: Optional[str] = None  # 可选。筛选值
    year: Optional[int] = None  # 可选。年
    mon: Optional[int] = None  # 可选。月
    day: Optional[int] = None  # 可选。日
    hour: Optional[int] = None  # 可选。时
    min: Optional[int] = None  # 可选。分
    sec: Optional[int] = None  # 可选。秒


class DBFilterType(str, Enum):
    null = "null"  # 条件为空
    nothing = "nothing"  # 不返回任何记录
    equals = "equals"  # 等于
    not_equal = "not_equal"  # 不等于
    empty = "empty"  # 为空
    not_empty = "not_empty"  # 不为空
    greater = "greater"  # 早于
    greater_equ = "greater_equ"  # 不晚于
    less = "less"  # 晚于
    less_equ = "less_equ"  # 不早于
    greater_equ_and_less_equ = "greater_equ_and_less_equ"  # 不早于...并不晚于...
    less_or_greater = "less_or_greater"  # 早于或晚于
    begin_with = "begin_with"  # 开头为
    not_begin_with = "not_begin_with"  # 开头不为
    end_with = "end_with"  # 结尾为
    not_end_with = "not_end_with"  # 结尾不为
    contains = "contains"  # 包含
    not_contains = "not_contains"  # 不包含
    intersected = "intersected"  # 相交
    not_intersected = "not_intersected"  # 不相交
    subset_of = "subset_of"  # 子集
    superset_of = "superset_of"  # 超集


class DBFieldFilter(BaseModel):
    id: str  # 必选。此筛选条件id
    field_id: Optional[str] = None  # 可选。筛选字段的id
    type: Optional[DBFilterType] = None  # 可选。筛选类型
    filter_value: Optional[List[DBFilterValue]]  # 可选。筛选值


class MutilFilterType(str, Enum):
    and_ = "and"  # 与
    or_ = "or"  # 或


class DBViewFilter(BaseModel):
    type: MutilFilterType  # 必选。多条件筛选之间的筛选类型
    field_filter: Optional[List[DBFieldFilter]] = None  # 可选。字段筛选条件


class UintType(str, Enum):
    Text = "Text"  # 文本
    Week = "Week"  # 周
    Month = "Month"  # 月
    Year = "Year"  # 年
    Day = "Day"  # 日
    Hour = "Hour"  # 时
    Minute = "Minute"  # 分
    Second = "Second"  # 秒


class DBViewCondition(BaseModel):
    ascending: bool = None  # 必选。是否升序
    group_uid: Optional[UintType] = None  # 可选。排序单元（目前仅分组用）
    field_id: str  # 必选。目标字段idblock


class SortType(str, Enum):
    pinyin = "pinyin"  # 拼音
    stroke = "stroke"  # 笔画


class DBViewSort(BaseModel):
    type: SortType  # 必选。按...排序
    auto_sort: Optional[bool] = None  # 可选。是否自动排序
    condition: Optional[List[DBViewCondition]] = None  # 可选。排序条件


class DBView(BaseModel):
    id: str  # 必选。视图 id
    name: str  # 必选。视图名称
    sort: Optional[DBViewSort] = None  # 可选。视序
    group: Optional[List[DBViewCondition]] = None  # 可选。各分组条件组成的数组
    filter: Optional[DBViewFilter] = None  # 可选。筛选
    visible_field: Optional[List[str]] = None  # 可选。可见字段id组成的数组
    visible_record: Optional[List[str]] = None  # 可选。可见记录id组成的数组
    order_record: Optional[List[str]] = None  # 可选。排序后记录按顺序id组成的数组


class DBFieldFormula(BaseModel):
    formula_db: Optional[str] = None  # 可选。多维表的公式
    formula_ooxml: Optional[str] = None  # 可选。ooxml形式的公式


class DBFieldItem(BaseModel):
    id: str  # 必选。选项 id
    value: Optional[str] = None  # 可选。值


class DBFieldType(str, Enum):
    date = "date"  # 日期
    time = "time"  # 时间
    number = "number"  # 数字
    currency = "currency"  # 货币
    percentage = "percentage"  # 百分比
    multiline_text = "multiline_text"  # 多行文本
    id = "id"  # 身份证
    phone = "phone"  # 电话
    email = "email"  # 邮箱
    hyperlink = "hyperlink"  # 超链接
    checkbox = "checkbox"  # 够复选框
    single_select = "single_select"  # 单选项
    multiple_select = "multiple_select"  # 多选项
    rating = "rating"  # 等级
    contact = "contact"  # 联系人
    complete = "complete"  # 进度
    cell_picture = "cell_picture"  # 单元格图片
    attachment = "attachment"  # 图片和附件
    automations = "automations"  # 自动任务
    auto_number = "auto_number"  # 编号
    created_by = "created_by"  # 创建人
    created_time = "created_time"  # 创建时间
    last_modified_by = "last_modified_by"  # 最后修改人
    last_modified_time = "last_modified_time"  # 最后修改时间
    formula = "formula"  # 公式
    link = "link"  # 双向关联
    oneway_link = "oneway_link"  # 单向关联
    note = "note"  # 富文本
    address = "address"  # 地址
    cascade = "cascade"  # 级联选项
    botton = "botton"  # 按钮


class DBRecord(BaseModel):
    id: str  # 必选。字段id
    time: Optional[float] = None  # 可选。创建时间
    creator_id: Optional[str] = None  # 可选。创建记录用户id


class DBField(BaseModel):
    type: DBFieldType  # 必选。字段类型
    id: str  # 必选。字段 id
    name: str  # 必选。字段名称
    num_format: Optional[str] = None  # 可选。数字格式
    items: Optional[List[DBFieldItem]] = None  # 可选。选项组成的数组
    formula: Optional[List[DBFieldFormula]] = None  # 可选。字段公式


class DBSheet(BaseModel):
    fields: Optional[List[DBField]] = None  # 可选。表头字段信息
    default_view: Optional[DBView] = None  # 可选。视图默认属性
    views: Optional[List[DBView]] = None  # 可选。创建的各视图属性组成的数组
    records: Optional[List[DBRecord]] = None  # 可选。行记录


class ColStyle(BaseModel):
    spans: RangeFromTo  # 必选。列范围
    width: Optional[float] = None  # 可选。列宽
    style_ref: Optional[int] = None  # 可选。样式索引，从0开始计数


class SheetType(str, Enum):
    """
    用来表示sheet类型
    """
    unknown = "unknown"  # 未知
    grid = "grid"  # 普通 sheet, ksheet 也是
    dialog = "dialog"  # 对话框 sheet
    chart = "chart"  # 图表 sheet
    macro = "macro"  # 宏 sheet
    module = "module"  # 模块 sheet
    griddb = "griddb"  # 数据库表
    dashboard = "dashboard"  # 仪表盘
    flexboard = "flexboard"  # 轻文档
    dashboard_db = "dashboard_db"  # DB 仪表盘
    application = "application"  # 应用 app
    workbench = "workbench"  # 表单工作台


class Sheet(BaseModel):
    """
    单个 sheet 属性。
    """
    name: str  # 必选。sheet 名称
    id: int  # 必选。sheet id，从 0 开始计数
    used_range: Optional[Range] = None  # 可选。使用的区域
    type: Optional[SheetType] = None  # 可选。sheet 类型
    default_size: Optional[Size] = None  # 可选。单元格默认宽高
    data: Optional[List[SheetData]] = None  # 可选。单元格数据数组
    merge_cells: Optional[List[Range]] = None  # 可选。合并单元格的数组
    cols_style: Optional[List[ColStyle]] = None  # 可选。列格式数组
    db_sheet: Optional[DBSheet] = None  # 可选。dbsheet 扩展属性
    flex_paper: Optional[DbDescription] = None  # 可选。多维表说明页面
    hyperlinks: Optional[List[HyperLink]] = None  # 可选。工作表超链接
    db_dash_board: Optional[DBDashBoard] = None  # 可选。多维表仪表盘信息
    data_validations: Optional[List[DataValidation]] = None  # 可选。区域的数据有效性设置


class WorkBook(BaseModel):
    """
    包含表格文档数据所有数据，为 KDC 抽取树状结构的根节点。
    """
    sheets: List[Sheet]  # 必选。各类型 sheet 数组
    cell_images: Optional[List[Media]] = None  # 可选。单元格图片组成的数组
    shared_strings: Optional[List[SharedString]] = None  # 可选。共享文本组成的数组
    styles: Optional[List[CellProperty]] = None  # 可选。属性(数字格式、字体、对齐)集合
    handle_tokens: Optional[List[DBHandleToken]] = None  # 可选。多维表独存的数据数组
    db_folder_tree: Optional[List[DBFolderTree]] = None  # 可选。多维表侧边栏文件结构


# et
#############################################################################################################################


class LayoutType(str, Enum):
    title = "title"
    text = "text"
    two_column_text = "two_column_text"
    table = "table"
    text_and_chart = "text_and_chart"
    chart_and_text = "chart_and_text"
    org_chart = "org_chart"
    chart = "chart"
    text_and_clipart = "text_and_clipart"
    clipart_and_text = "clipart_and_text"
    title_only = "title_only"
    blank = "blank"
    text_and_object = "text_and_object"
    object_and_text = "object_and_text"
    large_object = "large_object"
    object = "object"
    text_and_media_clip = "text_and_media_clip"
    media_clip_and_text = "media_clip_and_text"
    object_over_text = "object_over_text"
    text_over_object = "text_over_object"
    text_and_two_objects = "text_and_two_objects"
    two_objects_and_text = "two_objects_and_text"
    two_objects_over_text = "two_objects_over_text"
    four_objects = "four_objects"
    vertical_text = "vertical_text"
    clip_art_and_vertical_text = "clip_art_and_vertical_text"
    vertical_title_and_text = "vertical_title_and_text"
    vertical_title_and_text_over_chart = "vertical_title_and_text_over_chart"
    two_objects = "two_objects"
    object_and_two_objects = "object_and_two_objects"
    two_objects_and_object = "two_objects_and_object"
    custom = "custom"
    section_header = "section_header"
    comparison = "comparison"
    content_with_caption = "content_with_caption"
    picture_with_caption = "picture_with_caption"
    diagram = "diagram"
    object_only = "object_only"
    picture_text = "picture_text"
    two_text_and_two_objects = "two_text_and_two_objects"
    text_clip_art = "text_clip_art"


class SlideLayout(BaseModel):
    layout_type: LayoutType


class Slide(BaseModel):
    """
    Slide对象用来标识KDC文档里普通幻灯片数据数据结构及属性信息
    """
    id: int  # 必选。幻灯片ID
    name: Optional[str] = None  # 必选。幻灯片名称
    shape_tree: List[Block]  # 必选。幻灯片对象树
    note_page: Optional[List[Block]] = None  # 可选。备注页对象
    tags: Optional[List[Tag]] = None
    comments: Optional[List[Comment]] = None  # 可选。幻灯片评论对象


class SlideContainerCategory(str, Enum):
    """
    用来表示SlideContainer枚举类型
    """
    slide_masters = "slide_masters"  # 母版
    notes_masters = "notes_masters"  # 备注母版
    slide_layouts = "slide_layouts"  # 版式
    slides = "slides"  # 幻灯片
    notepages = "notepages"  # 备注页


class SlideContainer(BaseModel):
    """
    slideContainer 对象用来标识 KDC 文档的幻灯片（普通幻灯片、版式幻灯片、母版幻灯片、备注幻灯片、备注母版幻灯片等）幻灯片数据存放的数组容器结构
    """
    category: SlideContainerCategory  # 必须。幻灯片类型属性。
    slides: List[Slide]  # 必须。幻灯片数组


class PresProp(BaseModel):
    """
    sllideContainer对象用来标识KDC文档的幻灯片(普通幻灯片、版式幻灯片、母版幻灯片、备注幻灯片、备注母版幻灯片等)幻灯片数据存放的数组容器结构
    """
    slide_size: Size  # 必选。幻灯片画布大小
    note_size: Size  # 必选。幻灯片备注画布大小


class Presentation(BaseModel):
    """
    Presentation 对象用来表示 KDC 文档的根对象。演示文档结构为树状结构组成。
    """
    prop: PresProp  # 必选。备注画布大小属性
    slide_containers: List[SlideContainer]  # 必选。幻灯片、母版、板式容器对象
    medias: Optional[List[Media]] = None  # 可选。媒体资源文件，图片、音视频等
    hyperlinks: Optional[List[HyperLink]] = None  # 可选。超链接数据

# ppt
#############################################################################################################################
