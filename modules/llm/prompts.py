# Description: This file contains the prompts for the API
# Author：Hua Yanwei
# Date: 2024-11-20

SUMMARY_PROMPT_WITHOUT_KEY = """
-目标-
给定一个可能与本活动相关的文本以及可能的实体类型，为这段文本生成一段二百字以内的摘要

######################
举例：
文本：证券代码:603209证券简称:兴通股份兴通海运股份有限公司 2023年第三季度报告本公司董事会及全体董事保证本公告内容不存在任何虚假记载、误导性陈述或者重大遗漏,并对其内容的真实性、准确性和完整性承担法律责任。
重要内容提示:公司董事会、监事会及董事、监事、高级管理人员保证季度报告内容的真实、准确、完整,不存在虚假记载、误导性陈述或重大遗漏,并承担个别和连带的法律责任。
公司负责人、主管会计工作负责人及会计机构负责人(会计主管人员)保证季度报告中财务信息的真实、准确、完整。第三季度财务报表是否经审计□是√否一、主要财务数据(一)主要会计数据和财务指标单位:元币种:人民币注:“本报告期”指本季度初至本季度末 3个月期间,下同。
财务报表合并资产负债表2023年 9月 30日编制单位:兴通海运股份有限公司单位:元币种:人民币审计类型:未经审计公司负责人:陈兴明主管会计工作负责人:黄木生会计机构负责人:黄木生
合并利润表2023年 1—9月编制单位:兴通海运股份有限公司单位:元币种:人民币审计类型:未经审计本期发生同一控制下企业合并的,被合并方在合并前实现的净利润为:0元,上期被合并方实现的净利润为:0元。

输出：
兴通海运股份有限公司发布了2023年第三季度报告，董事会、监事会及高管保证报告内容的真实性和完整性。公司负责人及财务负责人确认财务信息的准确性。报告包含主要财务数据和未经审计的合并资产负债表及利润表，披露了2023年1至9月的经营情况。

######################
-实际数据-
######################
文本: {input_text}
######################

直接输出摘要内容，不要做出任何解释，不要包含任何其他内容，使用中文作为最终的输出。
"""


PROMPT_CONTENT_GEN_TITLE_SFT = """请作为一个专业的文档分析助手,对以下文档进行梳理总结。

文档内容：
{}

规则:
- 用一句简洁的话总结文档的核心主题
- 字数控制在15-100字之间
- 需要包含文档的主要目的或核心观点
- 使用陈述句式,确保语义完整
- 无意义的文档直接输出空字符串
- 不要做出任何的解释,使用与用户输入相同的语言"""

PROMPT_CONTENT_GEN_TAGS_SFT = """请作为一个专业的文档分析助手,对以下文档进行多维度深度分析：

文档内容：
{}

分析维度和要求：

1. 文档类型判断 (占比40%)：
- 从以下维度综合判断文档类型：
  * 目标受众(技术/业务/管理层/客户等)
  * 写作目的(汇报/计划/分析/说明/解决方案等)
  * 文档结构特征(格式化程度/章节安排等)
  * 时效性特征(实时/周期/长期等)
- 最多输出3个最相关的文档类型

2. 核心关键词提取 (占比60%)：
- 提取最多5个核心关键词,优先级由高到低排序
- 关键词选取标准：
  * 业务重要性：对业务目标的关键程度
  * 技术专业性：技术概念的专业水平
  * 问题针对性：对具体问题的指向性
  * 独特识别性：区分该文档的独特标识

基于给定信息动态调整文档类型和核心关键词的数量，最少为1个

文档类型和关键词之间不可出现重复词语

请将分析结果以如下JSON格式输出,不要包含任何其他内容：

{{"types": [], "keywords": []}}

请使用与输入文档相同的语言输出结果。"""
