# encoding:utf-8
import logging
import asyncio

from commons.llm_gateway.llm import LLModelRpc, LLMChatStatus
from commons.llm_gateway.models.public_model_gateway import PublicModelGateway
from commons.llm_gateway.models.sft_model_gateway import SftModelGateway
from commons.llm_gateway.models.chat_data import SftBaseModelType
from commons.llm_gateway.models.chat_data import Message
from typing import List

from commons.trace.tracer import async_trace_span

GET_TAGS_LORA = "get_tags"
GET_TITLE_LORA = "get_title"


class LMModel(object):
    gateway = LLModelRpc.Gateway.Public
    selector: LLModelRpc.ModelSelector = None
    sft_base_model: LLModelRpc.SftBaseModel = SftBaseModelType.qwen25_14b_aidocs
    kas_sft_base_model: LLModelRpc.SftBaseModel = SftBaseModelType.kas_qsearch_qwen2_5_14b

    @classmethod
    @async_trace_span
    async def generate_response(
            cls,
            llm_prompt: str = None,
            messages: List[Message] = None,
            top_k: float = None,
            temperature: float = 0.99,
            selector: LLModelRpc.ModelSelector = None,
            guided_json: dict = None,
    ) -> (LLMChatStatus, str):
        if cls.gateway == LLModelRpc.Gateway.Sft:
            sft_base_model = cls.sft_base_model
            final_sft_base_model = LLModelRpc.SftBaseModel(base_model=sft_base_model)
        else:
            final_sft_base_model = None
        logging.info(f"LMModel.generate_response final_sft_base_model: {final_sft_base_model}")
        if selector is not None:
            final_selector = selector
        else:
            final_selector = cls.selector
        if messages is None:
            generator = await LLModelRpc().async_chat_text_stream(cls.gateway, llm_prompt,
                                                                  top_k=top_k,
                                                                  temperature=temperature,
                                                                  selector=final_selector,
                                                                  sft_base_model=final_sft_base_model,
                                                                  guided_json=guided_json,
                                                                  )
        else:
            generator = await LLModelRpc().async_chat_text_stream(cls.gateway, messages=messages,
                                                                  top_k=top_k,
                                                                  temperature=temperature,
                                                                  selector=final_selector,
                                                                  sft_base_model=final_sft_base_model,
                                                                  guided_json=guided_json,
                                                                  )
        status = LLMChatStatus.OK
        text = ""
        async for content_stream in generator:
            if content_stream.status != LLMChatStatus.OK:
                status = content_stream.status
                text = ""
                break
            text += content_stream.text

        return status, text


if __name__ == '__main__':
    # 依赖初始化
    pub_conf = PublicModelGateway.Conf(
        host="http://aigc-gateway-test.ksord.com",
        token="YOUR_TOKEN",
        uid="9047",
        product_name="wps-kanmail-qa",
        intention_code="aigctest",
        provider="zhipu-zone",
        model="chatglm3-130b-wps",
        version="",
        multimodal_provider="ali",
        multimodal_model="qwen-vl-max-0809",
        multimodal_version=None,
        sec_from="AI_DRIVE_KNOWLEDGE",
    )
    sft_conf = SftModelGateway.Conf(
        host="http://privatization-model.kna.wps.cn",
        multimodal_host="http://privatization-model.kna.wps.cn/vl_7b",
        token="YOUR_TOKEN"
    )
    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)


    async def _main():
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = LLModelRpc.ModelSelector(model="chatglm3-130b-wps", provider="zhipu-zone", version="")

        status, text = await LMModel.generate_response("你好")
        print(status)
        print(text)


    loop = asyncio.get_event_loop()
    loop.run_until_complete(_main())
