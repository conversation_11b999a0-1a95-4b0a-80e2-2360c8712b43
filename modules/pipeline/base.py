import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set
from enum import Enum
import asyncio

from commons.monitor.prom import get_mcount
from modules.pipeline.context import PipelineContext



class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class PipelineHandler(ABC):
    """流水线处理节点基类"""

    def __init__(self, name: str):
        self.name = name
        self.parallel_handlers: Set[PipelineHandler] = set()  # 并行执行的处理器集合
        self.parallel_pipelines: List[Pipeline] = []  # List of sub-pipelines to execute in parallel
        self.next_handler: Optional[PipelineHandler] = None  # 下一个处理器
        self.status = TaskStatus.PENDING
        self.error: Optional[Exception] = None

    def add_parallel_pipeline(self, pipeline: 'Pipeline') -> 'PipelineHandler':
        """Add a sub-pipeline to be executed in parallel"""
        self.parallel_pipelines.append(pipeline)
        return self

    def add_parallel(self, handler: 'PipelineHandler') -> 'PipelineHandler':
        """添加并行执行的处理器"""
        self.parallel_handlers.add(handler)
        return handler

    def set_next(self, handler: 'PipelineHandler') -> 'PipelineHandler':
        """设置下一个处理器"""
        self.next_handler = handler
        return handler

    @abstractmethod
    async def process(self, context: PipelineContext) -> PipelineContext:
        """处理逻辑"""
        pass

    async def execute(self, context: PipelineContext) -> PipelineContext:
        """执行处理流程"""
        try:
            self.status = TaskStatus.RUNNING

            _t0 = time.perf_counter()
            result = await self.process(context)
            dur = time.perf_counter() - _t0

            mc = get_mcount()
            if mc and context.file_info and len(self.parallel_pipelines) == 0:
                context.business_log.debug(f"PipelineHandler {self.name} executed in {dur:.2f} seconds")
                file_type = str(getattr(context.file_info.file_type, "value", context.file_info.file_type))
                mc.record_parse(file_type, self.name, dur)
            ## Execute all parallel sub-pipelines
            if self.parallel_pipelines:
                tasks = [pipeline.execute(context) for pipeline in self.parallel_pipelines]
                results = await asyncio.gather(*tasks, return_exceptions=True)

                for r in results:
                    if isinstance(r, Exception):
                        raise r
                dur = time.perf_counter() - _t0

                mc = get_mcount()
                if mc and context.file_info:
                    file_type = str(getattr(context.file_info.file_type, "value", context.file_info.file_type))
                    context.business_log.debug(f"PipelineHandler {self.name} executed in {dur:.2f} seconds")

                    mc.record_parse(file_type, self.name, dur)

            # 并发执行所有并行处理器
            if self.parallel_handlers:
                tasks = [handler.execute(context) for handler in self.parallel_handlers]
                results = await asyncio.gather(*tasks, return_exceptions=True)

                for r in results:
                    if isinstance(r, Exception):
                        raise r

                    # 并行任务里handler结果的存储放到具体的handler实现里面
                    # context.handler_results[handler.name] = r

                    # pipeline里面传递的context是一个引用，parallel_handler的修改已经在context上面了
                    # 只有在多个parallel_handler处理同一个字段时才需要合并字段
                    # if isinstance(r, PipelineContext):
                    #     context.merge(r)
                # result = context
            self.status = TaskStatus.SUCCESS

            # 传递给下一个处理器
            if self.next_handler:
                return await self.next_handler.execute(result)
            return result

        except Exception as e:
            self.status = TaskStatus.FAILED
            self.error = e
            raise


# 有并行任务的handler可以创建下面这个类去执行并行任务
class ParallelGroupHandler(PipelineHandler):
    """并行任务需要使用的handler类"""

    async def process(self, context):
        return context


class Pipeline:
    """流水线基类"""

    def __init__(self, name: str):
        self.name = name
        self.head: Optional[PipelineHandler] = None
        self.tail: Optional[PipelineHandler] = None
        self.handlers: List[PipelineHandler] = []

    def add_handler(self, handler: PipelineHandler) -> 'Pipeline':
        """添加处理节点"""
        if not self.head:
            self.head = handler
            self.tail = handler
        else:
            self.tail.set_next(handler)
            self.tail = handler
        self.handlers.append(handler)
        return self

    async def execute(self, context: PipelineContext) -> PipelineContext:
        """执行流水线"""
        if not self.head:
            raise ValueError("Pipeline is empty")
        return await self.head.execute(context)

    def get_handler_status(self) -> Dict[str, TaskStatus]:
        """获取所有处理节点状态"""
        return {handler.name: handler.status for handler in self.handlers}

    def visualize(self) -> str:
        """可视化流水线结构"""
        graph = ["digraph G {"]
        for handler in self.handlers:
            # 添加并行处理器
            for parallel in handler.parallel_handlers:
                graph.append(f'    "{handler.name}" -> "{parallel.name}" [style=dotted];')
            # 添加下一个处理器
            if handler.next_handler:
                graph.append(f'    "{handler.name}" -> "{handler.next_handler.name}";')
        graph.append("}")
        return "\n".join(graph)
