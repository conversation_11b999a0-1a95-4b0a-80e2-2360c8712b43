from typing import Any, Dict, Optional, List, Union
from pydantic import BaseModel, Field

from commons.llm_gateway.models.chat_data import PublicGatewayHeader
from commons.logger.business_log import BusinessLogger
from modules.entity.dst_entity import DST
from modules.entity.chunk_entity import Chunk
from modules.entity.parse_entity import RespGeneralParseData, Image

from services.datamodel import FileType, ParseTarget


class KDCInput(BaseModel):
    """KDC输入参数"""
    kdc_ks3_url: Optional[str] = None
    company_id: Optional[str] = None
    file_url_or_bytes: Optional[Union[str, bytes]] = None
    convert_options: Optional[dict] = None


class FileInfo(BaseModel):
    """文件信息"""
    file_id: Optional[str] = None
    file_name: Optional[str] = None
    file_type: FileType = Field(..., description="文件类型")
    page_size: int = 1
    word_count: Optional[int] = None
    width: Optional[int] = None
    height: Optional[int] = None
    is_scan: bool = False
    rotate_page: Optional[Dict[int, List[int]]] = Field(default_factory=dict, description="旋转度的页码列表")


class OcData(BaseModel):
    page_num: Optional[int] = None
    dsts: Optional[List[DST]] = None
    full_page: Optional[bool] = False
    need_multi_modal: Optional[bool] = None


class ChunkInfo(BaseModel):
    chunk_size: Optional[int] = 1024
    min_chunk_size: Optional[int] = 512
    chunk_overlap: Optional[int] = 50


class PipelineContext(BaseModel):
    """流水线上下文"""
    public_gateway_header: Optional[PublicGatewayHeader] = None
    file_info: Optional[FileInfo] = Field(default_factory=lambda: FileInfo())
    raw_file_info: Optional[FileInfo] = None
    image: Optional[List[Image]] = None
    kdc_input: Optional[KDCInput] = None
    kdc_data: List[dict] = None
    embed_enabled: bool = True
    dst: Optional[List[DST]] = None
    chunks: Optional[List[Chunk]] = None
    chunks_info: Optional[ChunkInfo] = None
    need_callback: bool = False
    callback_url: Optional[str] = None
    return_ks3_url: bool = False
    token: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    handler_results: Dict[str, Any] = Field(default_factory=dict)
    result: RespGeneralParseData = Field(default=None, description="pipeline最终的处理结果")
    business_log: BusinessLogger = Field(default_factory=lambda: BusinessLogger(""))
    need_solve_picture: bool = False
    ocr_only: bool = True
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    parse_version: Optional[str] = None
    parse_target: List[ParseTarget] = None
    file_drive_id: Optional[str] = None
    recall_chunk_header: str = ""
    need_scan_mode: bool = False
    table_pics: Optional[List[Image]] = None
    layout: Optional[str] = None
    precheck_pages: Optional[List[int]] = None
    multiple_parse_dsts: Optional[Dict[int, List[DST]]] = Field(default_factory=dict)
    pdf_table_page_elements: Optional[List[Dict]] = None

    def add_business_field_into_log(self, key: str, value: str):
        """
        添加业务日志中的文件信息

        :param key: 日志的key
        :param value: 日志的value
        """
        if self.business_log:
            self.business_log.add_business_field(key, value)
        else:
            self.business_log = BusinessLogger("")
            self.business_log.add_business_field(key, value)

    def update_multiple_parse_dsts_unsafe(self, key: int, dsts: List[DST]):
        """
        更新多路解析的DSTs

        :param key: page起始页码
        :param dsts: DST列表
        非线程安全函数，请在调用前确保线程安全
        """
        if key not in self.multiple_parse_dsts:
            self.multiple_parse_dsts[key] = []
        self.multiple_parse_dsts[key].extend(dsts)

    def sort_multiple_parse_dsts(self) -> List[DST]:
        """
        整理多路解析的DST节点，并处理根节点关系

        处理逻辑:
        1. 保留键为0的列表中的第一个节点作为全局根节点
        2. 对于其他键(i>0)的列表，跳过第一个节点(本地根节点)不保留
        3. 将指向被跳过的本地根节点的引用修改为指向全局根节点

        :return: 处理后合并的DST列表
        """
        result = []
        sorted_keys = sorted(self.multiple_parse_dsts.keys())

        # 如果没有数据，直接返回空列表
        if not sorted_keys:
            return []

        # 步骤1: 处理键为0的情况，获取全局根节点ID
        root_id = ""
        if 0 in self.multiple_parse_dsts and self.multiple_parse_dsts[0]:
            root_id = self.multiple_parse_dsts[0][0].id
            # 添加键为0的所有DST节点（包含根节点）
            result.extend(self.multiple_parse_dsts[0])

        # 步骤2: 处理其他键，跳过每个键的第一个节点
        for key in sorted_keys:
            if key == 0 or not self.multiple_parse_dsts[key]:
                continue

            local_root_id = self.multiple_parse_dsts[key][0].id  # 记录本地根节点ID

            # 处理当前键中除第一个节点外的所有节点
            for dst in self.multiple_parse_dsts[key][1:]:
                # 如果节点引用了本地根节点，则修改为引用全局根节点
                if dst.parent == local_root_id and root_id:
                    dst.parent = root_id
                result.append(dst)

        return result

    class Config:
        arbitrary_types_allowed = True

    # def merge(self, other: 'PipelineContext') -> 'PipelineContext':
    #     """Merge another PipelineContext into this one."""
    #     for field_name, field_value in other.dict().items():
    #         if field_value is None:
    #             continue
    #         current_value = getattr(self, field_name, None)
    #         if isinstance(current_value, dict) and isinstance(field_value, dict):
    #             current_value.update(field_value)
    #         elif isinstance(current_value, list) and isinstance(field_value, list):
    #             current_value.extend(field_value)
    #         else:
    #             setattr(self, field_name, field_value)
    #     return self
