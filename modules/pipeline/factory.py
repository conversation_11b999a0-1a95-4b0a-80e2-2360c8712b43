from .base import Pipeline, ParallelGroupHandler
from modules.flows.uni_parser.uniparse_handler import UniParseNode
from modules.flows.dst_builder.dst_handler import DocumentParserNode
from modules.flows.dst_builder.dst_merge import DSTMergeNode
from modules.flows.chunk.chunk_handler import ChunkNode
from modules.flows.crop.crop import CropNode
from modules.flows.summary.summary import SummaryNode
from modules.flows.fake_title.fake_title import FakeTitleNode
from modules.flows.keywords.keywords import KeywordsNode
from services.datamodel import ParseTarget
from services.datamodel import ReqGeneralParse, FileType
from ..flows.chunk_postprocess.chunk_postprocess_handler import ChunkPostProcessNode
from ..flows.dst_enhance.dst_enhance_handler import DstEnhanceNode
from ..flows.image_desc.desc_handler import DescNode
from ..flows.chunk_recall.chunk_recall_handler import ChunkRecallNode
from ..flows.pre_check.pre_check_handler import PreCheckNode
from conf import ConfHandlerName

class PipelineFactory:
    """流水线工厂类"""

    @staticmethod
    def create_document_pipeline(name: str, request: ReqGeneralParse) -> Pipeline:
        """创建文档处理流水线"""
        pipeline = Pipeline(name)
        # 主流程节点   后续可以将node的名字设置成环境变量注入
        # handler的名称最好通过环境变量注入
        has_chunk_target = False
        for target in request.parse_target:
            if target == ParseTarget.chunk:
                has_chunk_target = True
        if not has_chunk_target:
            pipeline.add_handler(ChunkRecallNode(ConfHandlerName.chunk_recall_handler))
            first_parallel_node = ParallelGroupHandler("desc_crop_parallel")
            if ParseTarget.img_desc in request.parse_target:
                first_parallel_node.add_parallel(DescNode(ConfHandlerName.image_desc_handler))
            if ParseTarget.screenshot in request.parse_target:
                first_parallel_node.add_parallel(CropNode(ConfHandlerName.screenshot_handler))
            pipeline.add_handler(first_parallel_node)
            # 添加ChunkPostProcessNode
            pipeline.add_handler(ChunkPostProcessNode(ConfHandlerName.chunk_post_process_handler))
            second_parallel_node = ParallelGroupHandler("parallel_group")
            # 根据目标添加相应的处理节点
            for target in request.parse_target:
                if target == ParseTarget.summary:
                    second_parallel_node.add_parallel(SummaryNode(ConfHandlerName.summary_handler))
                elif target == ParseTarget.fake_title:
                    second_parallel_node.add_parallel(FakeTitleNode(ConfHandlerName.fake_title_handler))
                elif target == ParseTarget.keywords:
                    second_parallel_node.add_parallel(KeywordsNode(ConfHandlerName.keywords_handler))
            pipeline.add_handler(second_parallel_node)

        else:
            pipeline.add_handler(PreCheckNode(ConfHandlerName.pre_check_handler))
            # if request.file_type in [FileType.JPG, FileType.JPEG, FileType.WEBP, FileType.PNG]:
                # pipeline.add_handler(Image2PDFNode(ConfHandlerName.image_to_pdf_handler))

            pipeline.add_handler(UniParseNode(ConfHandlerName.uniparser_handler)).add_handler(DocumentParserNode(ConfHandlerName.dst_handler))
            pipeline.add_handler(DSTMergeNode(ConfHandlerName.dst_merge_handler))
            if request.parse_target:
                parallel_node = ParallelGroupHandler("parallel_group")
                if ParseTarget.screenshot in request.parse_target and ParseTarget.chunk in request.parse_target:
                    sub_pipeline_chunk = Pipeline("SubPipeline1")
                    sub_pipeline_chunk.add_handler(DstEnhanceNode(ConfHandlerName.dst_enhance_handler)).add_handler(ChunkNode(ConfHandlerName.chunk_handler))
                    sub_pipeline_shot = Pipeline("SubPipeline2")
                    sub_pipeline_shot.add_handler(CropNode(ConfHandlerName.screenshot_handler))
                    sub_pipeline_handler = ParallelGroupHandler("sub_pipeline_handler")
                    sub_pipeline_handler.add_parallel_pipeline(sub_pipeline_chunk)
                    sub_pipeline_handler.add_parallel_pipeline(sub_pipeline_shot)
                    if ParseTarget.img_desc in request.parse_target:
                        sub_pipeline_img_desc = Pipeline("SubPipeline3")
                        sub_pipeline_img_desc.add_handler(DescNode(ConfHandlerName.image_desc_handler))
                        sub_pipeline_handler.add_parallel_pipeline(sub_pipeline_img_desc)
                    pipeline.add_handler(sub_pipeline_handler)
                    ## 后置处理截图挂载chunkid
                    pipeline.add_handler(ChunkPostProcessNode(ConfHandlerName.chunk_post_process_handler))
                    for target in request.parse_target:
                        if target == ParseTarget.chunk:
                            continue
                        elif target == ParseTarget.screenshot:
                            continue
                        elif target == ParseTarget.summary:
                            parallel_node.add_parallel(SummaryNode(ConfHandlerName.summary_handler))
                        elif target == ParseTarget.fake_title:
                            parallel_node.add_parallel(FakeTitleNode(ConfHandlerName.fake_title_handler))
                        elif target == ParseTarget.keywords:
                            parallel_node.add_parallel(KeywordsNode(ConfHandlerName.keywords_handler))
                    pipeline.add_handler(parallel_node)

                else:
                    dst_postprocess_node = ParallelGroupHandler("dst_postprocess_group")
                    dst_postprocess_node.add_parallel(DstEnhanceNode(ConfHandlerName.dst_enhance_handler))
                    if ParseTarget.img_desc in request.parse_target:
                        dst_postprocess_node.add_parallel(DescNode(ConfHandlerName.image_desc_handler))
                    pipeline.add_handler(dst_postprocess_node)
                    for target in request.parse_target:
                        if target == ParseTarget.chunk:
                            pipeline.add_handler(ChunkNode(ConfHandlerName.chunk_handler))
                        elif target == ParseTarget.screenshot:
                            pipeline.add_handler(CropNode(ConfHandlerName.screenshot_handler))
                        elif target == ParseTarget.summary:
                            parallel_node.add_parallel(SummaryNode(ConfHandlerName.summary_handler))
                        elif target == ParseTarget.fake_title:
                            parallel_node.add_parallel(FakeTitleNode(ConfHandlerName.fake_title_handler))
                        elif target == ParseTarget.keywords:
                            parallel_node.add_parallel(KeywordsNode(ConfHandlerName.keywords_handler))
                    pipeline.add_handler(ChunkPostProcessNode(ConfHandlerName.chunk_post_process_handler))
                    pipeline.add_handler(parallel_node)
        return pipeline
