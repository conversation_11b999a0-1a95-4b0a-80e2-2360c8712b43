# Author: linqi
# Date: 2025/6/24
# Time: 18:21
import logging
from typing import List, Optional, Dict
from pydantic import BaseModel, Field
import json
from commons.logger.business_log import logger

from commons.tools.utils import Singleton
from commons.trace.tracer import async_trace_span
from commons.auth.auth_rpc import AuthRequest, SigVerType
from modules.pipeline.context import FileInfo
from modules.entity.chunk_entity import Chunk


class RecallChunkInput(BaseModel):
    """解析结果请求参数"""

    drive_id: Optional[str] = None  # 库id
    file_ids: Optional[List[str]] = None  # 文件id列表
    from_: Optional[int] = Field(default=None, alias="from")  # 分页的起始
    size: Optional[int] = None  # 分页的大小
    chunk_ids: Optional[List[str]] = None  # chunk id列表（回溯场景下使用）
    with_content: bool = False  # 是否返回chunk content
    with_embedding: bool = False  # 是否返回chunk content embedding
    with_fileinfo: bool = False  # 是否返回文档信息


class ChunkInfo(BaseModel):
    file_id: Optional[str] = None  # 文件ID
    chunk: Optional[Chunk] = None


class RecallChunkOutput(BaseModel):
    drive_id: Optional[str] = None
    chunks: Optional[List[ChunkInfo]] = None
    file_infos: Optional[List[FileInfo]] = None
    dst_parse_version: Optional[str] = None


class RecallChunkClient(metaclass=Singleton):
    def __init__(self):
        self._req: Optional[AuthRequest] = None

    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType = SigVerType.wps2):
        self._req = AuthRequest(host, ak, sk, sig_type)

    @async_trace_span
    async def request_parse_result(self, r: RecallChunkInput, recall_header: str) -> RecallChunkOutput:
        uri = "/openapi/dev/api/v1/recall/chunk"
        body = r.model_dump(by_alias=True) if hasattr(r, "model_dump") else r.dict(by_alias=True)
        status, parse_res = await self._req.async_call("POST", uri, body=body, header={"wiki-branch": recall_header})
        res = None
        if status == 200:
            res_dict = json.loads(parse_res)
            if res_dict["code"] == 0 and "data" in res_dict and res_dict["data"] is not None:
                res = RecallChunkOutput.model_validate(res_dict['data'])
            else:
                logger.error(f"request_parse_result fail, uri: {uri}, status: {status}, res: {parse_res}")
        else:
            logger.error(f"request_parse_result fail, uri: {uri}, status: {status}, res: {parse_res}")
        return res
