# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import copy
import logging
from typing import List, Optional, Union
from pydantic import BaseModel
from requests_toolbelt import MultipartEncoder
import json
import time
import asyncio

from conf import ConfCoroutine
from commons.tools.utils import Singleton
from commons.tools.wps365api import Wps365api
from commons.trace.tracer import async_trace_span, trace_span
from commons.thread.multicoroutine import MultiCoroutine
from services.datamodel import FileType

PAGE_SIZE = 8


async def create_task_with_name(uri, post_body, post_form_data, company_id, use_async_func, task_name):
    # Create and execute the task
    result = await Wps365api().apost(
        uri=uri,
        body=post_body,
        form_data=post_form_data,
        resp_model=None,
        company_id=company_id,
        use_async_func=use_async_func
    )
    # Return the result and task name as a tuple
    return result, task_name


class KDCRpc(metaclass=Singleton):
    """
    依赖公共工具：Wps365api，公共工具在工程中会自动初始化，main测试需要手动初始化
    """

    @classmethod
    @async_trace_span
    async def aget_content(
            cls,
            company_id: str,
            drive_id: str,
            v7_file_id: str,
            format: str = None,
            include_elements: List[str] = None,
            disable_pdf_detection: bool = None,
            page_range: str = None,
            enable_multi_threading: bool = None,
            task_duration: str = None,
            enable_upload_medias: bool = None,
    ) -> Optional[dict]:
        """
        kdc v7接口，文件内容提取，http://kbe.kingsoft.net/apis/all.html#tag/%E3%80%90%E6%96%87%E4%BB%B6%E3%80%91%E5%86%85%E5%AE%B9/operation/get_file_content
        :param company_id: 企业ID
        :param drive_id: drive ID
        :param v7_file_id: 文件ID
        :param format: 文档内容目标格式，枚举："kdc" "plain" "markdown"
        :param include_elements: 指定抽取元素。默认元素为para，且一定会被导出；其余附加元素根据请求参数选择性导出。枚举："para" "table" "component" "textbox" "all"
        :param disable_pdf_detection: 禁用图章、二维码、公式和流程图识别模型开关，禁用后无效果；默认为false（不禁用）
        :param page_range: 解析的页码范围，例如：1-9。暂时只支持单组“A-B”。A必须小于等于B，否则解析全部页
        :param enable_multi_threading: 公有云适用，开启多线程解析。支持值：[true, false]，默认：关闭（false）欲查询支持多线程的组件，请参考：https://kdocs.cn/l/clRuNI0K9agH?linkname=LYRxQTAADw
        :param task_duration: 公有云适用，强制退出并返回最小解析页数的结果的等待时长。默认：不强制退出。说明：1. 单位：sec; 2. 大于网关超时限制则以网关设置为准。更多请参考：https://kdocs.cn/l/clRuNI0K9agH?linkname=X554aaMp7X
        :param enable_upload_medias: 公有云适用，是否将文档中的多媒体附件上传对象存储。默认false（关闭）。如果true，则上传并返回有效期内可下载的url（注意：开启该开关可能对性能有较大影响，请评估后使用）
        :return None
        """
        uri = f"/v7/drives/{drive_id}/files/{v7_file_id}/content"
        params = {}
        if format:
            params["format"] = format
        if include_elements:
            params["include_elements"] = include_elements
        if disable_pdf_detection:
            params["disable_pdf_detection"] = disable_pdf_detection
        if page_range:
            params["page_range"] = page_range
        if enable_multi_threading:
            params["enable_multi_threading"] = enable_multi_threading
        if task_duration:
            params["task_duration"] = task_duration
        if enable_upload_medias:
            params["enable_upload_medias"] = enable_upload_medias

        res = await Wps365api().aget(uri=uri, params=params, resp_model=None, company_id=company_id)
        return res

    @classmethod
    @trace_span
    def get_content(
            cls,
            company_id: str,
            drive_id: str,
            v7_file_id: str,
            format: str = None,
            include_elements: List[str] = None,
            disable_pdf_detection: bool = None,
            page_range: str = None,
            enable_multi_threading: bool = None,
            task_duration: str = None,
            enable_upload_medias: bool = None,
    ) -> Optional[dict]:
        """
        kdc v7接口，文件内容提取，http://kbe.kingsoft.net/apis/all.html#tag/%E3%80%90%E6%96%87%E4%BB%B6%E3%80%91%E5%86%85%E5%AE%B9/operation/get_file_content
        :param company_id: 企业ID
        :param drive_id: drive ID
        :param v7_file_id: 文件ID
        :param format: 文档内容目标格式，枚举："kdc" "plain" "markdown"
        :param include_elements: 指定抽取元素。默认元素为para，且一定会被导出；其余附加元素根据请求参数选择性导出。枚举："para" "table" "component" "textbox" "all"
        :param disable_pdf_detection: 禁用图章、二维码、公式和流程图识别模型开关，禁用后无效果；默认为false（不禁用）
        :param page_range: 解析的页码范围，例如：1-9。暂时只支持单组“A-B”。A必须小于等于B，否则解析全部页
        :param enable_multi_threading: 公有云适用，开启多线程解析。支持值：[true, false]，默认：关闭（false）欲查询支持多线程的组件，请参考：https://kdocs.cn/l/clRuNI0K9agH?linkname=LYRxQTAADw
        :param task_duration: 公有云适用，强制退出并返回最小解析页数的结果的等待时长。默认：不强制退出。说明：1. 单位：sec; 2. 大于网关超时限制则以网关设置为准。更多请参考：https://kdocs.cn/l/clRuNI0K9agH?linkname=X554aaMp7X
        :param enable_upload_medias: 公有云适用，是否将文档中的多媒体附件上传对象存储。默认false（关闭）。如果true，则上传并返回有效期内可下载的url（注意：开启该开关可能对性能有较大影响，请评估后使用）
        :return None
        """
        uri = f"/v7/drives/{drive_id}/files/{v7_file_id}/content"
        params = {}
        if format:
            params["format"] = format
        if include_elements:
            params["include_elements"] = include_elements
        if disable_pdf_detection:
            params["disable_pdf_detection"] = disable_pdf_detection
        if page_range:
            params["page_range"] = page_range
        if enable_multi_threading:
            params["enable_multi_threading"] = enable_multi_threading
        if task_duration:
            params["task_duration"] = task_duration
        if enable_upload_medias:
            params["enable_upload_medias"] = enable_upload_medias

        res = Wps365api().get(uri=uri, params=params, resp_model=None, company_id=company_id)
        return res

    class OpenArgs(BaseModel):
        password: str

    @classmethod
    @async_trace_span
    async def aget_content_by_url_or_file(
            cls,
            company_id: str,
            file_url_or_bytes: Union[str, bytes],
            format: str = None,
            filename: str = None,
            pdf_edet_opt: str = None,
            include_elements: str = None,
            open_args: OpenArgs = None,
            convert_options: dict = None,
            task_duration: str = None,
            enable_html_format_table: bool = None,
            file_id: str = None,
            request_id: str = None,
            file_type: FileType = None,
    ) -> Optional[List[dict]]:
        """
        kdc v7接口，通过url或文件进行内容提取，https://365.kdocs.cn/l/clRuNI0K9agH
        :param company_id: 企业ID
        :param file_url_or_bytes: 文件url或二进制流
        :param format: 文档内容目标格式，枚举："kdc" "plain" "markdown"
        :param filename: 文件名, 注意文件名必需保证后缀正确, 文件名可以脱敏(比如采用随机字符串)，仅在json格式提交时生效
        :param pdf_edet_opt: 控制是否解析二维码等元素以提升PDF标准件解析速度（参数值：默认模式-0；关闭解析-1；开启解析-2；自动模式-3）
        :param include_elements: 文档的元素分为默认元素和附加元素，默认元素一定会被导出，附加元素根据请求参数，选择性导出。默认元素： para（不包括table、component、和textbox嵌套的para）
            附加元素：table、component、textbox。可选参数为：table、component、textbox，all。
        :param open_args: 打开文档的参数，例如打开加密文档时需要传入密码
        :param convert_options: 转换选项，E.G.{"disable_pdf_detection": true,"page_range":"1-3","fixed_chunk_size": true},
            • disable_pdf_detection：关闭二维码等元素，效果同pdf_edet_opt参数；
            • page_range：解析的页码范围，只支持单组"A-B"且A必须大于B，否则解析全部页
            • fixed_chunk_size：多线程解析时分块大小是否固定
        :param task_duration: 到达时间强制提前退出并返回最小解析页数的结果的等待时长。默认：不强制退出。说明：
            1. 必须为整数，单位：sec;
            2. 大于网关超时限制则以网关设置为准；
            3. 传入early_stop_at则默认开启多线程解析；
            4. 不宜设置过短。建议不低于15sec
        :param enable_html_format_table: 输出markdown时表格是否使用html格式
        :param parse_pages: 需要解析的指定页码列表，如[1,3,4,6,7,8]
        :return: List[dict] 每个dict增加了page_start字段，表示解析结果的起始页码
        """
        uri = f"/v7/longtask/exporter/export_file_content"
        if request_id:
            uri += f"?request_id={request_id}"
        body = {}
        if format:
            body["format"] = format
        if filename:
            body["filename"] = filename
        if pdf_edet_opt:
            body["pdf_edet_opt"] = pdf_edet_opt
        if include_elements:
            body["include_elements"] = include_elements
        if open_args:
            body["open_args"] = open_args.dict()
        multiple_parse = False
        use_async_func = False
        if convert_options:
            body["convert_options"] = convert_options
        if file_type == FileType.PDF:
            # 解析全部，走分页解析
            if 'convert_options' in body and body['convert_options'] is not None:
                if "page_range" in body["convert_options"]:
                    # 存在page_range，则不解析全部页数
                    multiple_parse = False
                else:
                    # 不存在page_range，默认解析全部页数
                    multiple_parse = True
                    body["convert_options"]["page_range"] = f"1-{PAGE_SIZE}"
                    convert_options = body["convert_options"]
            else:
                # 不存在convert_options，默认解析全部页数
                multiple_parse = True
                convert_options = {"page_range": f"1-{PAGE_SIZE}"}
                body["convert_options"] = convert_options
        body["file_id"] = file_id
        if task_duration:
            body["task_duration"] = task_duration
        if enable_html_format_table:
            body["enable_html_format_table"] = enable_html_format_table

        def body_change(file_url_or_bytes, body):
            # Create a deep copy of the body to avoid overwriting during concurrency
            body = copy.deepcopy(body)

            if isinstance(file_url_or_bytes, str):
                body["url"] = file_url_or_bytes
                post_body = body
                post_form_data = None
            else:
                body["form_file"] = (filename, file_url_or_bytes)
                if "convert_options" in body and body["convert_options"] is not None:
                    body["convert_options"] = json.dumps(body["convert_options"])
                post_form_data = MultipartEncoder(fields={**body})
                post_body = None

            return post_body, post_form_data

        post_body, post_form_data = body_change(file_url_or_bytes, body)

        res = await Wps365api().apost(uri=uri, body=post_body, form_data=post_form_data, resp_model=None,
                                      company_id=company_id)
        if res is not None:
            if body.get("convert_options") and body["convert_options"].get("page_range"):
                res["doc"]["page_start"] = int(body["convert_options"]["page_range"].split("-")[0]) - 1
            # res["doc"]["page_start"] = int(body["convert_options"]["page_range"].split("-")[0]) - 1

        res_list = [res]
        if res is not None and "file_info" in res and res["file_info"] is not None and "total_page_num" in res["file_info"]:
            page_count = res["file_info"]["total_page_num"]
            if multiple_parse and page_count > PAGE_SIZE:
                start_index = PAGE_SIZE + 1
                end_index = start_index + PAGE_SIZE - 1
                pool = MultiCoroutine()
                while start_index <= page_count:
                    task_convert_options = copy.deepcopy(convert_options)
                    task_convert_options["page_range"] = f"{start_index}-{end_index}"
                    body["convert_options"] = task_convert_options
                    post_body, post_form_data = body_change(file_url_or_bytes, body)
                    # Create a task for each page range
                    task_name = f"page_{start_index}_{end_index}"
                    pool.add_task(f"kdc_task_{task_name}", create_task_with_name(
                                  uri=uri,
                                  post_body=post_body,
                                  post_form_data=post_form_data,
                                  company_id=company_id,
                                  use_async_func=use_async_func,
                                  task_name=task_name))

                    start_index = end_index + 1
                    end_index = start_index + PAGE_SIZE - 1

                # 限制并发运行
                results_dict = await pool.run_limit(ConfCoroutine.kdc_limit)

                # Sort results by task name (page range)
                sorted_results = sorted(
                    results_dict.values(),
                    key=lambda x: int(x[1].split("_")[1])  # Extract and convert start_index to int
                )

                # Append results to res_list in order
                for multi_res, key in sorted_results:
                    multi_res["doc"]["page_start"] = int(key.split("_")[1]) - 1
                    res_list.append(multi_res)

        return res_list

    # 轮询kdc异步结果接口
    @classmethod
    async def poll_task_status(cls, task_id: str, company_id: str, use_async_func: bool = False) -> dict:
        """
        基于 task_id 轮询任务状态。
        :param task_id: 任务 ID。
        :param company_id: 企业 ID。
        :return: 任务完成时的最终响应。
        :raises: TimeoutError 如果轮询时间超过 3600 秒。
        """
        uri = f"/v7/coop/asyn_export/{task_id}/query_job"
        max_polling_time = 3600  # 最大轮询时间（秒）
        polling_interval = 2  # 轮询间隔（秒）
        start_time = time.time()

        while True:
            # 检查是否超过最大轮询时间
            elapsed_time = time.time() - start_time
            if elapsed_time > max_polling_time:
                raise TimeoutError(f"轮询时间超过最大限制 {max_polling_time} 秒。")

            # 调用 GET 接口查询任务状态
            res = await Wps365api().aget(uri=uri, params=None, resp_model=None, company_id=company_id,
                                         use_async_func=use_async_func)
            status = res.get("status")
            logging.info(f"当前异步获取kdc结果的状态：{status}")
            # 检查任务状态
            if res and res.get("status") == "finished":
                return res["data"]  # 返回任务完成的最终结果
            elif res and res.get("status") == "failed":
                raise ValueError(f"任务 {task_id} 失败，响应内容: {res}")

            # 等待轮询间隔
            await asyncio.sleep(polling_interval)

    # kdc解析异步调用方法 + 根据task_id轮询异步结果接口
    @classmethod
    @async_trace_span
    async def async_aget_content_by_url_or_file(
            cls,
            company_id: str,
            file_url_or_bytes: Union[str, bytes],
            format: str = None,
            filename: str = None,
            pdf_edet_opt: str = None,
            include_elements: str = None,
            open_args: OpenArgs = None,
            convert_options: dict = None,
            task_duration: str = None,
            enable_html_format_table: bool = None,
            file_id: str = None,
            request_id: str = None,
    ) -> Optional[List[dict]]:
        """
        kdc v7接口，通过url或文件进行内容提取，https://365.kdocs.cn/l/clRuNI0K9agH
        :param company_id: 企业ID
        :param file_url_or_bytes: 文件url或二进制流
        :param format: 文档内容目标格式，枚举："kdc" "plain" "markdown"
        :param filename: 文件名, 注意文件名必需保证后缀正确, 文件名可以脱敏(比如采用随机字符串)，仅在json格式提交时生效
        :param pdf_edet_opt: 控制是否解析二维码等元素以提升PDF标准件解析速度（参数值：默认模式-0；关闭解析-1；开启解析-2；自动模式-3）
        :param include_elements: 文档的元素分为默认元素和附加元素，默认元素一定会被导出，附加元素根据请求参数，选择性导出。默认元素： para（不包括table、component、和textbox嵌套的para）
            附加元素：table、component、textbox。可选参数为：table、component、textbox，all。
        :param open_args: 打开文档的参数，例如打开加密文档时需要传入密码
        :param convert_options: 转换选项，E.G.{"disable_pdf_detection": true,"page_range":"1-3","fixed_chunk_size": true},
            • disable_pdf_detection：关闭二维码等元素，效果同pdf_edet_opt参数；
            • page_range：解析的页码范围，只支持单组“A-B”且A必须大于B，否则解析全部页
            • fixed_chunk_size：多线程解析时分块大小是否固定
        :param task_duration: 到达时间强制提前退出并返回最小解析页数的结果的等待时长。默认：不强制退出。说明：
            1. 必须为整数，单位：sec;
            2. 大于网关超时限制则以网关设置为准；
            3. 传入early_stop_at则默认开启多线程解析；
            4. 不宜设置过短。建议不低于15sec
        :param enable_html_format_table: 输出markdown时表格是否使用html格式
        """
        uri = f"/v7/coop/asyn_export/create_job"
        if request_id:
            uri += f"?request_id={request_id}"
        body = {}
        if format:
            body["format"] = format
        if filename:
            body["filename"] = filename
        if pdf_edet_opt:
            body["pdf_edet_opt"] = pdf_edet_opt
        if include_elements:
            body["include_elements"] = include_elements
        if open_args:
            body["open_args"] = open_args.dict()
        if convert_options:
            body["convert_options"] = convert_options

        use_async_func = True

        body["file_id"] = file_id
        if task_duration:
            body["task_duration"] = task_duration
        if enable_html_format_table:
            body["enable_html_format_table"] = enable_html_format_table

        def body_change(file_url_or_bytes, body):
            if isinstance(file_url_or_bytes, str):
                body["url"] = file_url_or_bytes
                post_body = body
                post_form_data = None
            else:
                body["form_file"] = (filename, file_url_or_bytes)
                if "convert_options" in body and body["convert_options"] is not None:
                    body["convert_options"] = json.dumps(body["convert_options"])
                post_form_data = MultipartEncoder(fields={**body})
                post_body = None
            return post_body, post_form_data

        post_body, post_form_data = body_change(file_url_or_bytes, body)

        res = await Wps365api().apost(uri=uri, body=post_body, form_data=post_form_data, resp_model=None,
                                      company_id=company_id, use_async_func=use_async_func)
        if res and "task_id" in res:
            task_id = res["task_id"]
            try:
                final_result = await cls.poll_task_status(task_id, company_id, use_async_func)
                return [final_result]
            except Exception as e:
                raise RuntimeError(f"轮询任务状态时出错: {e}")
        else:
            raise ValueError("未能从响应中获取 task_id。")

    @classmethod
    @trace_span
    def get_content_by_url_or_file(
            cls,
            company_id: str,
            file_url_or_bytes: Union[str, bytes],
            format: str = None,
            filename: str = None,
            pdf_edet_opt: str = None,
            include_elements: str = None,
            open_args: OpenArgs = None,
            convert_options: dict = None,
            task_duration: str = None,
            enable_html_format_table: bool = None,
            file_id: str = None,
            request_id: str = None,
    ) -> Optional[List[dict]]:
        """
        kdc v7接口，通过url或文件进行内容提取，https://365.kdocs.cn/l/clRuNI0K9agH
        :param company_id: 企业ID
        :param file_url_or_bytes: 文件url或二进制流
        :param format: 文档内容目标格式，枚举："kdc" "plain" "markdown"
        :param filename: 文件名, 注意文件名必需保证后缀正确, 文件名可以脱敏(比如采用随机字符串)，仅在json格式提交时生效
        :param pdf_edet_opt: 控制是否解析二维码等元素以提升PDF标准件解析速度（参数值：默认模式-0；关闭解析-1；开启解析-2；自动模式-3）
        :param include_elements: 文档的元素分为默认元素和附加元素，默认元素一定会被导出，附加元素根据请求参数，选择性导出。默认元素： para（不包括table、component、和textbox嵌套的para）
            附加元素：table、component、textbox。可选参数为：table、component、textbox，all。
        :param open_args: 打开文档的参数，例如打开加密文档时需要传入密码
        :param convert_options: 转换选项，E.G.{"disable_pdf_detection": true,"page_range":"1-3","fixed_chunk_size": true},
            • disable_pdf_detection：关闭二维码等元素，效果同pdf_edet_opt参数；
            • page_range：解析的页码范围，只支持单组“A-B”且A必须大于B，否则解析全部页
            • fixed_chunk_size：多线程解析时分块大小是否固定
        :param task_duration: 到达时间强制提前退出并返回最小解析页数的结果的等待时长。默认：不强制退出。说明：
            1. 必须为整数，单位：sec;
            2. 大于网关超时限制则以网关设置为准；
            3. 传入early_stop_at则默认开启多线程解析；
            4. 不宜设置过短。建议不低于15sec
        :param enable_html_format_table: 输出markdown时表格是否使用html格式
        """
        uri = f"/v7/longtask/exporter/export_file_content"
        if request_id:
            uri += f"?request_id={request_id}"
        body = {}
        if format:
            body["format"] = format
        if filename:
            body["filename"] = filename
        if pdf_edet_opt:
            body["pdf_edet_opt"] = pdf_edet_opt
        if include_elements:
            body["include_elements"] = include_elements
        if open_args:
            body["open_args"] = open_args.dict()
        multiple_parse = False
        if convert_options:
            body["convert_options"] = convert_options
        else:
            if "pdf" in filename:
                # 解析全部，走分页解析
                multiple_parse = True
                if 'convert_options' in body and body['convert_options'] is not None:
                    body["convert_options"]["page_range"] = f"1-{PAGE_SIZE}"
                    convert_options = body["convert_options"]
                else:
                    convert_options = {"page_range": f"1-{PAGE_SIZE}"}
                    body["convert_options"] = convert_options
        body["file_id"] = file_id
        if task_duration:
            body["task_duration"] = task_duration
        if enable_html_format_table:
            body["enable_html_format_table"] = enable_html_format_table

        def body_change(file_url_or_bytes, body):
            if isinstance(file_url_or_bytes, str):
                body["url"] = file_url_or_bytes
                post_body = body
                post_form_data = None
            else:
                body["form_file"] = (filename, file_url_or_bytes)
                if "convert_options" in body and body["convert_options"] is not None:
                    body["convert_options"] = json.dumps(body["convert_options"])
                post_form_data = MultipartEncoder(fields={**body})
                post_body = None
            return post_body, post_form_data

        post_body, post_form_data = body_change(file_url_or_bytes, body)

        res = Wps365api().post(uri=uri, body=post_body, form_data=post_form_data, resp_model=None,
                               company_id=company_id)
        res_list = [res]
        if res is not None and "file_info" in res and res["file_info"] is not None and "total_page_num" in res[
            "file_info"]:
            page_count = res["file_info"]["total_page_num"]
            if multiple_parse and page_count > PAGE_SIZE:
                start_index = PAGE_SIZE + 1
                end_index = start_index + PAGE_SIZE - 1
                while start_index <= page_count:
                    convert_options["page_range"] = f"{start_index}-{end_index}"
                    body["convert_options"] = convert_options
                    post_body, post_form_data = body_change(file_url_or_bytes, body)
                    multi_res = Wps365api().post(uri=uri, body=post_body, form_data=post_form_data, resp_model=None,
                                                 company_id=company_id)
                    res_list.append(multi_res)

                    start_index = end_index + 1
                    end_index = start_index + PAGE_SIZE - 1

        return res_list


if __name__ == '__main__':
    import os
    import asyncio
    from commons.logger.logger import init_logger

    # 依赖初始化
    init_logger("", "DEBUG", "err")


    # Wps365api().init("https://api.wps.cn", 10, "https://openapi.wps.cn", "",
    #                  "", os.environ.get("woa_custom_ak", ""))

    async def _async():
        Wps365api().init("https://api.wps.cn", 10, "https://openapi.wps.cn", "",
                         "", os.environ.get("woa_custom_ak", ""))
        company_id = "41000207"
        drive_id = "1218736720"
        # v7文件id（在公网表现为link_id）
        v7_file_id = "cvrXCbPYQv4p"

        drive_id = "2265797916"
        v7_file_id = "clGbblj3vTQN"
        format = "kdc"
        include_elements = ["all"]
        disable_pdf_detection = False
        page_range = "1-9"
        enable_multi_threading = False
        task_duration = None
        enable_upload_medias = False
        # res = await KDCRpc().aget_content(
        #     company_id,
        #     drive_id,
        #     v7_file_id,
        #     format,
        #     include_elements,
        #     disable_pdf_detection,
        #     page_range,
        #     enable_multi_threading,
        #     task_duration,
        #     enable_upload_medias
        # )
        #
        # print(res)
        #
        # res = KDCRpc().get_content(
        #     company_id,
        #     drive_id,
        #     v7_file_id,
        #     format,
        #     include_elements,
        #     disable_pdf_detection,
        #     page_range,
        #     enable_multi_threading,
        #     task_duration,
        #     enable_upload_medias
        # )
        #
        # print(res)

        company_id = "41000207"
        file_url_or_bytes = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/%E3%80%8A%E5%B7%AE%E6%97%85%E7%AE%A1%E7%90%86%E5%88%B6%E5%BA%A6%E3%80%8B.pdf?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1793994983&Signature=vrTgG%2B27PanzQc5xBS7p1ijvAgE%3D"
        format = "kdc"
        filename = "parse_test.pdf"
        pdf_edet_opt = None
        include_elements = None
        open_args = None
        convert_options = None
        task_duration = None
        enable_html_format_table = None
        # res = KDCRpc().get_content_by_url_or_file(
        #     company_id,
        #     file_url_or_bytes,
        #     format,
        #     filename,
        #     pdf_edet_opt,
        #     include_elements,
        #     open_args,
        #     convert_options,
        #     task_duration,
        #     enable_html_format_table
        # )
        # print(res)
        res = await KDCRpc().aget_content_by_url_or_file(
            company_id,
            file_url_or_bytes,
            format,
            filename,
            pdf_edet_opt,
            include_elements,
            open_args,
            convert_options,
            task_duration,
            enable_html_format_table
        )
        print(res)
        # with open(r"D:\file_test\parse_test.docx", "rb") as f:
        #     fb = f.read()
        #     res = KDCRpc().get_content_by_url_or_file(
        #         company_id,
        #         fb,
        #         format,
        #         filename,
        #         pdf_edet_opt,
        #         include_elements,
        #         open_args,
        #         convert_options,
        #         task_duration,
        #         enable_html_format_table
        #     )
        #     print(res)

        # res = await KDCRpc().aget_content_by_url_or_file(
        #     company_id,
        #     fb,
        #     format,
        #     filename,
        #     pdf_edet_opt,
        #     include_elements,
        #     open_args,
        #     convert_options,
        #     task_duration,
        #     enable_html_format_table
        # )
        # print(res)
        # 依赖关闭
        await Wps365api().close()


    asyncio.run(_async())
    # loop = asyncio.get_event_loop()
    # loop.run_until_complete(_async())
