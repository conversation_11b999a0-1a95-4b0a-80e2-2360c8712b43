# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/07/29 09:20
from abc import ABC, abstractmethod
from typing import Optional

from commons.auth.auth_rpc import SigVerType
from modules.entity.drive_entity import DriveFileResponse


class DriveRpcInterface(ABC):
    """
    Drive RPC服务的抽象接口
    
    使用策略模式定义统一的接口，便于后续扩展不同类型的RPC服务
    """
    
    @abstractmethod
    async def aget_download_url(self, file_id: str) -> DriveFileResponse:
        """
        异步获取文件下载链接
        
        Args:
            file_id: 文件ID
            
        Returns:
            DriveFileResponse: 包含result、fileinfo等字段的统一响应结构体
        """
        pass
    
    @abstractmethod
    def get_download_url(self, file_id: str) -> DriveFileResponse:
        """
        同步获取文件下载链接
        
        Args:
            file_id: 文件ID
            
        Returns:
            DriveFileResponse: 包含result、fileinfo等字段的统一响应结构体
        """
        pass
    
    @abstractmethod
    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType, download_uri: str, **kwargs):
        """
        初始化RPC客户端
        
        Args:
            host: 服务地址
            ak: Access Key
            sk: Secret Key
            sig_type: 签名验证类型
            download_uri: 获取下载链接的URI
            **kwargs: 其他可选参数
        """
        pass 