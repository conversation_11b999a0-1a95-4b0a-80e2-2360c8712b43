# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/07/29 09:20
import json
import logging
import os
from enum import Enum
from typing import Optional, Dict, Type
from commons.tools.utils import Singleton
from modules.rpc.drive_rpc_interface import DriveRpcInterface
from modules.entity.drive_entity import DriveFileResponse
from commons.auth.auth_rpc import SigVerType


class DriveRpcFactory(metaclass=Singleton):
    """
    Drive RPC工厂类
    
    使用工厂模式管理不同类型的Drive RPC实例
    支持策略切换和扩展新类型
    """
    
    def __init__(self):
        # 注册不同类型的RPC实现类 - 使用字符串作为key以支持动态注册
        self._rpc_classes: Dict[str, Type[DriveRpcInterface]] = {}
        self._rpc_instances: Dict[str, DriveRpcInterface] = {}

        # 注册已有的实现
        self._register_default_implementations()
    
    def _register_default_implementations(self):
        """注册默认的RPC实现"""
        # 尝试从配置文件注册
        success = self._register_from_config_file()
        
        # 如果配置文件注册失败，则使用默认的V5实现
        if not success or not self._rpc_classes:
            logging.info("Config file registration failed or empty, falling back to default V5 implementation")
            try:
                from modules.rpc.drive_v5_rpc import DriveV5Rpc
                self.register_rpc_class("wpsv5", DriveV5Rpc)
            except ImportError as e:
                logging.warning(f"Failed to import DriveV5Rpc: {e}")
    
    def _register_from_config_file(self) -> bool:
        """从环境变量配置注册RPC实现
        
        Returns:
            bool: 是否注册成功
        """
        try:
            from conf import ConfDriveRpcConfig
            implementations = ConfDriveRpcConfig.rpc_config.get("implementations", {})
            
            registered_count = 0
            for rpc_type_str, impl_config in implementations.items():
                if not impl_config.get('enabled', True):
                    logging.info(f"RPC implementation {rpc_type_str} is disabled in config")
                    continue
                
                try:
                    # 动态导入实现类
                    class_path = impl_config['class_path']
                    module_name, class_name = class_path.rsplit('.', 1)
                    module = __import__(module_name, fromlist=[class_name])
                    rpc_class = getattr(module, class_name)
                    
                    # 注册实现类 - 直接使用字符串类型
                    self.register_rpc_class(rpc_type_str, rpc_class)
                    registered_count += 1
                    
                    logging.info(f"Registered RPC from environment config: {rpc_type_str} -> {class_path}")
                    
                except Exception as e:
                    logging.error(f"Failed to register RPC implementation {rpc_type_str}: {e}")

                
            return registered_count > 0
            
        except Exception as e:
            logging.error(f"Failed to load RPC config from environment: {e}")
            return False
    
    def register_rpc_class(self, rpc_type: str, rpc_class: Type[DriveRpcInterface]):
        """
        注册新的RPC实现类
        
        Args:
            rpc_type: RPC类型字符串
            rpc_class: RPC实现类
        """
        if not issubclass(rpc_class, DriveRpcInterface):
            raise ValueError(f"RPC class must implement DriveRpcInterface")
        
        self._rpc_classes[rpc_type] = rpc_class
        logging.info(f"Registered Drive RPC {rpc_type}: {rpc_class.__name__}")
    
    def get_rpc_instance(self, rpc_type: Optional[str] = None) -> DriveRpcInterface:
        """
        获取RPC实例（单例模式）
        
        Args:
            rpc_type: 指定类型字符串，不指定则使用默认类型
            
        Returns:
            DriveRpcInterface: RPC实例
            
        Raises:
            ValueError: 当指定类型未注册时
        """
        
        # 检查是否已注册
        if rpc_type not in self._rpc_classes:
            raise ValueError(f"Drive RPC type {rpc_type} is not registered")
        
        # 如果实例不存在，创建新实例
        if rpc_type not in self._rpc_instances:
            rpc_class = self._rpc_classes[rpc_type]
            self._rpc_instances[rpc_type] = rpc_class()
            logging.info(f"Created new Drive RPC instance for type {rpc_type}")
        
        return self._rpc_instances[rpc_type]
    
    def get_available_types(self) -> list[str]:
        """
        获取所有已注册的类型
        
        Returns:
            list[str]: 已注册rpc类型字符串列表
        """
        return list(self._rpc_classes.keys())


class DriveRpcClient:
    """
    Drive RPC客户端封装
    
    提供简单的接口，隐藏工厂的复杂性
    """
    
    def __init__(self, rpc_type: Optional[str] = None):
        """
        初始化客户端
        
        Args:
            rpc_type: 指定RPC类型字符串，不指定则使用默认类型
        """
        self._factory = DriveRpcFactory()
        self._rpc = self._factory.get_rpc_instance(rpc_type)
    
    async def aget_download_url(self, file_id: str) -> DriveFileResponse:
        """异步获取下载链接，返回统一格式响应"""
        return await self._rpc.aget_download_url(file_id)
    
    def get_download_url(self, file_id: str) -> DriveFileResponse:
        """同步获取下载链接，返回统一格式响应"""
        return self._rpc.get_download_url(file_id)
    
    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType, **kwargs):
        """初始化RPC连接"""
        return self._rpc.init(host, ak, sk, sig_type, **kwargs)


# 提供全局访问点
def get_drive_rpc_client(rpc_type: Optional[str] = None) -> DriveRpcClient:
    """
    获取Drive RPC客户端实例
    
    Args:
        rpc_type: 指定类型字符串，不指定则使用默认类型
        
    Returns:
        DriveRpcClient: RPC客户端实例
    """
    return DriveRpcClient(rpc_type)