import json
import time
from typing import Optional, Union

import aiohttp
import logging
from fastapi import status
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from commons.tools.utils import Singleton
from commons.auth.auth_route import AuthPlatform
from commons.auth.auth_rpc import sig_wps4, sig_wps2
from commons.logger.logger import request_id_context
from modules.entity.parse_entity import RespGeneralParseData


class RpcCallbackClient(metaclass=Singleton):
    """统一的回调服务客户端"""
    def __init__(self):
        self._ak = ""
        self._sk = ""
        self._auth_platform = ""

    def init(self, ak: str, sk: str, auth_platform: AuthPlatform):
        self._ak = ak
        self._sk = sk
        self._auth_platform = auth_platform

    def _sig(self, method: str, uri: str, body: dict = None, content_type: str = "") -> dict:
        if body:
            body = json.dumps(body).encode("utf-8")
        date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
        if self._auth_platform == AuthPlatform.dmc:
            return sig_wps2(uri, body, self._ak, self._sk, date, content_type)
        elif self._auth_platform == AuthPlatform.private:
            return sig_wps4(method, uri, body, self._ak, self._sk, date, content_type)

    @retry(
        stop=stop_after_attempt(3),  # 最大重试次数
        wait=wait_exponential(multiplier=1, min=2, max=10),  # 指数退避策略
        retry=retry_if_exception_type(Exception),  # 重试条件：捕获所有异常
    )
    async def _acall(self, host: str, method: str, uri: str, data: dict = None, headers: dict = None,
                     timeout_second: int = 180):
        new_headers = self._sig(method, uri, data)
        if headers:
            new_headers.update(headers)
        new_headers["X-Request-Id"] = request_id_context.get()
        url = f"{host}{uri}"
        async with aiohttp.ClientSession() as sess:
            async with sess.request(method=method, url=url, json=data, headers=new_headers,
                                    timeout=timeout_second) as r:
                if r.status != status.HTTP_200_OK:
                    res_text = await r.text(encoding="utf-8")
                    err_text = f"rpc callback request fail, uri: {uri}, status_code: {r.status}, res_text: {res_text}"
                    logging.error(err_text)
                    raise Exception(err_text)

    async def send_callback(
            self,
            host: str,
            uri: str,
            method: str = "POST",
            data: Optional[Union[dict,RespGeneralParseData ]] = None,
            headers: dict = None,
            timeout_second: int = 180
    ):
        """
        统一的回调 API 调用接口

        参数:
            uri (str): 请求的目标URI路径（如：/notify/graph_dst_result）
            method (str): HTTP方法（默认POST）
            data (dict): 请求体数据（自动转为JSON）
            headers (dict): 额外HTTP头
            timeout_second (int): 超时时间（默认180秒）
        """
        if data is not None and not isinstance(data, dict):
            data = data.dict()
        await self._acall(
            host=host,
            method=method,
            uri=uri,
            data=data,
            headers=headers,
            timeout_second=timeout_second
        )