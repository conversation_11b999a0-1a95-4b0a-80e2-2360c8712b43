import json
import logging
import async<PERSON>
from typing import List, Optional
from pydantic import BaseModel

from commons.tools.utils import Singleton
from commons.auth.auth_rpc import AuthRequest, SigVerType
from commons.trace.tracer import async_trace_span, trace_span
from conf import ConfHttpTriton
from modules.entity.crop_entity import LayOutData, Box, OcrData, LayOutRep, OcrRep


class OCRModelClient(metaclass=Singleton):
    def __init__(self):
        self._req: Optional[AuthRequest] = None

    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType = SigVerType.wps2):
        self._req = AuthRequest(host, ak, sk, sig_type)

    @async_trace_span
    async def request_layout_model(self, pic_url: str) -> LayOutData:
        uri = ConfHttpTriton.layout_uri
        body = {
            "image_url": pic_url
        }
        status, text = await self._req.async_call("POST", uri, body=body)
        res = None
        if status == 200:
            res_dict = json.loads(text)
            if res_dict["code"] == 0 and "data" in res_dict and res_dict["data"] is not None:
                res = LayOutData.model_validate(res_dict['data'])
            else:
                logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        else:
            logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        return res

    @async_trace_span
    async def request_ocr(self, pic_url: str) -> Optional[OcrData]:
        try:
            uri = ConfHttpTriton.orc_uri
            body = {
                "image_url": pic_url
            }
            status, text = await self._req.async_call("POST", uri, body=body, timeout=30.0)
            res = None
            if status == 200:
                res_dict = json.loads(text)
                if res_dict["code"] == 0 and "data" in res_dict and res_dict["data"] is not None:
                    res = OcrData.model_validate(res_dict["data"])
                else:
                    logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
            else:
                logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
            return res
        except asyncio.TimeoutError:
            logging.error(f"OCR请求超时: pic_url={pic_url}")
            return None
        except Exception as e:
            logging.error(f"OCR处理请求出错: pic_url={pic_url}, error={str(e)}")
            return None

    @async_trace_span
    async def request_table_cls(self, pic_url: str) -> str:
        uri = ConfHttpTriton.table_cls_uri
        body = {
            "image_url": pic_url
        }
        status, text = await self._req.async_call("POST", uri, body=body)
        res = None
        if status == 200:
            res_dict = json.loads(text)
            if res_dict["code"] == 0 and "data" in res_dict and res_dict["data"] is not None:
                res = res_dict["data"].get("table_type", "")
            else:
                logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        else:
            logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        return res

    @async_trace_span
    async def request_wired_table(self, pic_url: str) -> str:
        uri = ConfHttpTriton.wired_table_ocr_uri
        body = {
            "image_url": pic_url
        }
        status, text = await self._req.async_call("POST", uri, body=body)
        res = None
        if status == 200:
            res_dict = json.loads(text)
            if res_dict["code"] == 0 and "data" in res_dict and res_dict["data"] is not None:
                res = res_dict["data"].get("table_html", "")
            else:
                logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        else:
            logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        return res

    @async_trace_span
    async def request_wireless_table(self, pic_url: str) -> str:
        uri = ConfHttpTriton.wireless_table_ocr_uri
        body = {
            "image_url": pic_url
        }
        status, text = await self._req.async_call("POST", uri, body=body)
        res = None
        if status == 200:
            res_dict = json.loads(text)
            if res_dict["code"] == 0 and "data" in res_dict and res_dict["data"] is not None:
                res = res_dict["data"].get("table_html", "")
            else:
                logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        else:
            logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        return res

    @async_trace_span
    async def request_text_embedding(self, texts: str) -> Optional[List[float]]:
        uri = ConfHttpTriton.text_embedding_uri
        body = {
            "text": texts
        }
        status, text = await self._req.async_call("POST", uri, body=body)
        res = None
        if status == 200:
            res_dict = json.loads(text)
            if res_dict["code"] == 0 and "data" in res_dict and res_dict["data"] is not None:
                res = res_dict["data"].get("embedding")
            else:
                logging.error(f"text embedding rpc fail, uri: {uri}, status: {status}, text: {text}")
        else:
            logging.error(f"text embedding rpc fail, uri: {uri}, status: {status}, text: {text}")
        return res