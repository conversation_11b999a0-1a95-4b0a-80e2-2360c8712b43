# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import json
import logging
from typing import List, Optional
from pydantic import BaseModel

from commons.tools.utils import SingletonABCMeta
from commons.tools.wps365api import Wps365api
from commons.auth.auth_rpc import AuthRequest, SigVerType
from modules.rpc.drive_rpc_interface import DriveRpcInterface
from modules.entity.drive_entity import DriveFileResponse


class DriveV5Rpc(DriveRpcInterface, metaclass=SingletonABCMeta):
    # https://cloud-doc.wps.cn/qing_v5/developer.html?h=%2Fapi%2Fv5%2Fdeveloper%2Ffiles
    def __init__(self):
        self._req: Optional[AuthRequest] = None
        self._download_uri_template: Optional[str] = None

    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType, download_uri: str, **kwargs):
        self._req = AuthRequest(host, ak, sk, sig_type)
        self._download_uri_template = download_uri

    async def aget_download_url(self, file_id: str) -> DriveFileResponse:
        uri = self._download_uri_template.format(file_id=file_id)
        try:
            status, text = await self._req.async_call("GET", uri)
            if status == 200:
                res = json.loads(text)
                if res["result"] == "ok" and "fileinfo" in res and res["fileinfo"] is not None:
                    return DriveFileResponse.success_response(res["fileinfo"]["url"])
                else:
                    error_msg = f"API response error: {res.get('result', 'unknown error')}"
                    logging.error(f"download rpc fail, uri: {uri}, status: {status}, text: {text}")
                    return DriveFileResponse.error_response(error_msg, status)
            else:
                error_msg = f"HTTP error: status {status}"
                logging.error(f"download rpc fail, uri: {uri}, status: {status}, text: {text}")
                return DriveFileResponse.error_response(error_msg, status)
        except Exception as e:
            error_msg = f"Request failed: {str(e)}"
            logging.error(f"download rpc exception, uri: {uri}, error: {error_msg}")
            return DriveFileResponse.error_response(error_msg)

    def get_download_url(self, file_id: str) -> DriveFileResponse:
        uri = self._download_uri_template.format(file_id=file_id)
        try:
            status, text = self._req.call("GET", uri)
            if status == 200:
                res = json.loads(text)
                if res["result"] == "ok" and "fileinfo" in res and res["fileinfo"] is not None:
                    return DriveFileResponse.success_response(res["fileinfo"]["url"])
                else:
                    error_msg = f"API response error: {res.get('result', 'unknown error')}"
                    logging.error(f"download rpc fail, uri: {uri}, status: {status}, text: {text}")
                    return DriveFileResponse.error_response(error_msg, status)
            else:
                error_msg = f"HTTP error: status {status}"
                logging.error(f"download rpc fail, uri: {uri}, status: {status}, text: {text}")
                return DriveFileResponse.error_response(error_msg, status)
        except Exception as e:
            error_msg = f"Request failed: {str(e)}"
            logging.error(f"download rpc exception, uri: {uri}, error: {error_msg}")
            return DriveFileResponse.error_response(error_msg)


if __name__ == '__main__':
    import os
    import asyncio
    from wpsai_insight_common.logger.logger import init_logger

    # 依赖初始化
    init_logger("", "DEBUG", "err")
    DriveV5Rpc().init("http://drive.wps.cn", "AK20240110KTHTIQ", "84d4a3110edad06a20372da70c31889b")


    async def _async():
        # v5文件id
        v5_file_id = "1001624877391"
        res = await DriveV5Rpc().aget_download_url(v5_file_id)
        if res.is_success:
            print(f"Success: {res.url}")
        else:
            print(f"Error: {res.error_message} (status: {res.status_code})")


    loop = asyncio.get_event_loop()
    loop.run_until_complete(_async())
