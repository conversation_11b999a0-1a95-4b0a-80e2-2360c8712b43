# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/07/29 09:21
import json
import logging
import os
from typing import Optional, Dict, Any, Tuple

from modules.rpc.drive_rpc_factory import DriveRpcFactory
from commons.auth.auth_rpc import SigVerType
from conf import ConfDriveRpcConfig
from modules.entity.drive_entity import DriveParams


class RpcConfigManager:
    """
    RPC配置管理器
    
    负责读取和管理RPC配置文件，提供配置信息给其他组件使用
    """
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._load_config()
    
    def _load_config(self):
        """从环境变量加载RPC配置"""
        try:
            # 直接使用配置类的rpc_config
            self._config = ConfDriveRpcConfig.rpc_config
            
            # 验证配置的有效性
            validated_impls = ConfDriveRpcConfig.get_validated_implementations()
            logging.info(f"RPC config loaded successfully. Found {len(validated_impls)} implementations: {list(validated_impls.keys())}")
                
        except Exception as e:
            logging.error(f"Failed to load or validate RPC config: {e}")
            raise ValueError(f"Invalid RPC configuration: {e}") from e

    def get_implementations(self) -> Dict[str, Dict[str, Any]]:
        """获取所有RPC实现配置"""
        return self._config.get("implementations", {})
    
    def get_enabled_implementations(self) -> Dict[str, DriveParams]:
        """获取启用的RPC实现配置"""
        try:
            validated_impls = ConfDriveRpcConfig.get_validated_implementations()
            return {
                name: params 
                for name, params in validated_impls.items() 
                if params.enabled
            }
        except Exception as e:
            logging.error(f"Failed to get enabled implementations: {e}")
            return {}
    
    def is_implementation_enabled(self, rpc_type: str) -> bool:
        """检查指定RPC实现是否启用"""
        try:
            drive_params = ConfDriveRpcConfig.get_implementation_config(rpc_type)
            return drive_params.enabled
        except ValueError:
            # RPC类型不存在
            return False
        except Exception as e:
            logging.error(f"Failed to check if implementation '{rpc_type}' is enabled: {e}")
            return False
    
    def get_rpc_config(self, rpc_type: str) -> Tuple[str, str, str, SigVerType, str]:
        """
        获取指定RPC类型的配置参数
        
        Args:
            rpc_type: RPC类型字符串
            
        Returns:
            Tuple[host, ak, sk, sig_type, download_uri]
            
        Raises:
            ValueError: 当RPC类型未配置或环境变量缺失时
        """
        try:
            # 获取验证后的配置
            drive_params = ConfDriveRpcConfig.get_implementation_config(rpc_type)
            
            # 从环境变量获取凭据
            ak = os.environ.get(drive_params.ak_env)
            sk = os.environ.get(drive_params.sk_env)
            
            # 检查必要参数
            if not ak:
                raise ValueError(f"Missing Access Key environment variable: {drive_params.ak_env}")
            if not sk:
                raise ValueError(f"Missing Secret Key environment variable: {drive_params.sk_env}")
            
            # 使用DriveParams的便捷方法获取配置
            return drive_params.get_config_tuple(ak, sk)
            
        except Exception as e:
            logging.error(f"Failed to get RPC config for type '{rpc_type}': {e}")
            raise
    
    def reload_config(self):
        """重新加载配置（从环境变量）"""
        self._config = None
        self._load_config()
        logging.info("RPC configuration reloaded from environment variables")
    
    def get_available_types(self) -> list[str]:
        """获取所有可用的RPC类型"""
        return list(self.get_enabled_implementations().keys())
    
    def get_implementation_info(self, rpc_type: str) -> Optional[DriveParams]:
        """获取指定RPC实现的详细信息"""
        try:
            return ConfDriveRpcConfig.get_implementation_config(rpc_type)
        except ValueError:
            return None
        except Exception as e:
            logging.error(f"Failed to get implementation info for '{rpc_type}': {e}")
            return None


# 单例实例
_rpc_config_manager = RpcConfigManager()


class DriveRpcManager:
    """
    Drive RPC管理器
    
    负责RPC实例的初始化和管理
    """
    
    @classmethod
    def initialize_drive_rpc(cls, 
                           type: str,
                           host: str,
                           ak: str,
                           sk: str,
                           sig_type: SigVerType,
                           download_uri: str) -> None:
        """
        初始化Drive RPC配置
        
        Args:
            type: RPC类型，默认使用wpsv5
            host: 服务地址
            ak: Access Key
            sk: Secret Key
            sig_type: 签名验证类型
            download_uri: 下载URI
        """
        try:
            # 获取工厂实例
            factory = DriveRpcFactory()
 
            # 获取RPC实例并初始化
            rpc_instance = factory.get_rpc_instance(type)

            # 初始化RPC连接
            rpc_instance.init(host, ak, sk, sig_type, download_uri)
            logging.info(f"Successfully initialized Drive RPC with host: {host}")
            
        except Exception as e:
            logging.error(f"Failed to initialize Drive RPC: {e}")
            raise

    @classmethod 
    def auto_initialize(cls) -> None:
        """根据配置文件自动初始化所有启用的Drive RPC实现"""
        try:
            # 获取所有启用的RPC类型
            enabled_types = cls.get_available_types()
            if not enabled_types:
                raise ValueError("No enabled RPC implementations found in config")
            
            logging.info(f"Found enabled RPC types: {enabled_types}")
            
            # 初始化所有启用的类型
            initialized_count = 0
            failed_types = []
            
            for rpc_type in enabled_types:
                try:
                    cls.initialize_by_type(rpc_type)
                    initialized_count += 1
                    logging.info(f"Successfully initialized RPC type: {rpc_type}")
                except Exception as e:
                    failed_types.append(rpc_type)
                    logging.warning(f"Failed to initialize RPC type {rpc_type}: {e}")
            
            if initialized_count == 0:
                raise ValueError(f"Failed to initialize any RPC implementations. Failed types: {failed_types}")
            
            logging.info(f"Auto-initialized {initialized_count} Drive RPC implementations")
            if failed_types:
                logging.warning(f"Failed to initialize types: {failed_types}")
            
        except Exception as e:
            logging.error(f"Failed to auto-initialize Drive RPC: {e}")
            raise e
    
    @classmethod
    def initialize_by_type(cls, rpc_type_str: str) -> None:
        """
        根据指定的RPC类型初始化
        
        Args:
            rpc_type_str: RPC类型字符串，如 "wpsv5"
        """
        try:
            # 检查类型是否启用
            if not _rpc_config_manager.is_implementation_enabled(rpc_type_str):
                raise ValueError(f"RPC type {rpc_type_str} is not enabled in configuration")
            
            # 从配置管理器获取参数
            host, ak, sk, sig_type, download_uri = _rpc_config_manager.get_rpc_config(rpc_type_str)
            
            # 初始化RPC
            cls.initialize_drive_rpc(
                type=rpc_type_str,
                host=host,
                ak=ak,
                sk=sk,
                sig_type=sig_type,
                download_uri=download_uri
            )
            
            logging.info(f"Successfully initialized Drive RPC with type: {rpc_type_str}")
            
        except Exception as e:
            logging.error(f"Failed to initialize Drive RPC with type {rpc_type_str}: {e}")
            raise
    
    @classmethod
    def get_available_types(cls) -> list[str]:
        """
        获取所有可用的RPC类型
        
        Returns:
            list[str]: 类型名称列表
        """
        # 从配置文件获取可用类型
        config_types = _rpc_config_manager.get_available_types()
        
        # 从工厂获取已注册的类型
        factory = DriveRpcFactory()
        factory_types = factory.get_available_types()
        
        # 返回两者的交集
        return list(set(config_types) & set(factory_types))
    
    @classmethod
    def get_config_info(cls) -> dict:
        """
        获取当前RPC配置信息
        
        Returns:
            dict: 配置信息
        """
        try:
            enabled_impls = _rpc_config_manager.get_enabled_implementations()
            return {
                "available_types": list(enabled_impls.keys()),
                "enabled_implementations": {
                    name: {
                        "class_path": params.class_path,
                        "host": params.host,
                        "sig_type": params.sig_type.value,
                        "enabled": params.enabled
                    }
                    for name, params in enabled_impls.items()
                }
            }
        except Exception as e:
            logging.error(f"Failed to get config info: {e}")
            return {
                "available_types": [],
                "enabled_implementations": {},
                "error": str(e)
            }
    
    @classmethod
    def reload_config(cls):
        """重新加载配置（从环境变量）"""
        _rpc_config_manager.reload_config()
        logging.info("Drive RPC configuration reloaded from environment variables")


