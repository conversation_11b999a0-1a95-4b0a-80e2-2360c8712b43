import logging
import re
from collections import defaultdict
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>

from commons.logger.business_log import logger
from modules.entity.dst_entity import DST


class RomanNumeral:
    ROMAN_REGEX = r'^M{0,4}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})$'
    ROMAN_MAPPING = {'I': 1, 'V': 5, 'X': 10, 'L': 50, 'C': 100, 'D': 500, 'M': 1000}

    @staticmethod
    def is_roman(s: str) -> bool:
        return re.match(RomanNumeral.ROMAN_REGEX, s, re.IGNORECASE) is not None

    @staticmethod
    def to_int(roman: str) -> int:
        roman = roman.upper()
        total = 0
        prev_value = 0
        for char in reversed(roman):
            value = RomanNumeral.ROMAN_MAPPING[char]
            if value < prev_value:
                total -= value
            else:
                total += value
            prev_value = value
        return total


class StringFormatValidator:
    """支持严格前缀匹配的字符串格式验证工具"""

    # 中文数字列表，用于精确匹配
    CHINESE_DIGITS = {'一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '百', '千', '万'}
    # 特殊前缀符号
    SPECIAL_PREFIX_SYMBOLS = {'（', '）', '第', '节', '、'}
    FILTERED_PREFIXES = {'O-', 'C'}
    EN_PREFIXES = {'E','A]+['}

    @staticmethod
    def analyze_format(mapping: Dict[Hashable, str]) -> (Dict[str, Dict[Hashable, str]], bool):
        """
        分析映射中的字符串值，基于前缀模式分组，增强中文前缀识别
        """
        base_format_groups = StringFormatValidator._initial_format_analysis(mapping)
        prefix_groups, is_all_filter_patten = StringFormatValidator._group_by_prefix_pattern(base_format_groups)
        return prefix_groups, is_all_filter_patten

    @staticmethod
    def _initial_format_analysis(mapping: Dict[Hashable, str]) -> Dict[Pattern, Dict[Hashable, str]]:
        """执行初始格式分析"""
        format_groups: Dict[Pattern, Dict[Hashable, str]] = {}
        for key, value in mapping.items():
            matched = False
            for pattern in format_groups:
                if pattern.fullmatch(value):
                    format_groups[pattern][key] = value
                    matched = True
                    break
            if not matched:
                pattern = StringFormatValidator._generate_pattern([value])
                if pattern:
                    format_groups[pattern] = {key: value}
        return format_groups

    @staticmethod
    def _generate_pattern(strings: List[str]) -> Optional[Pattern]:
        """为一组字符串生成格式正则表达式"""
        if not strings:
            return None


        def tokenize(s: str) -> List[Tuple[str, str]]:
            """
            将字符串按照字符类型分割成标记序列。

            该函数遍历输入字符串的每个字符，根据字符类型将连续相同类型的字符分组，
            形成一系列标记。每个标记由类型标识符和实际内容组成。

            字符类型分为：
            - 'N': 数字字符
            - 'R': 罗马数字字符
            - 'C': 中文字符
            - 'A': 字母字符
            - 'S': 特殊字符/符号

            Args:
                s: 需要分词的输入字符串

            Returns:
                List[Tuple[str, str]]: 标记列表，每个标记是一个元组(类型, 内容)
            示例："AB123 测试" → [('A', 'AB'), ('N', '123'), ('S', ' '), ('C', '测试')]
            """
            tokens = []
            current_type = None
            current_start = 0
            for i, char in enumerate(s):
                char_type = None
                if char.isdigit():
                    char_type = 'N'
                elif RomanNumeral.is_roman(char):
                    char_type = 'R'
                elif '\u4e00' <= char <= '\u9fff':
                    char_type = 'C'
                elif char.isalpha():
                    char_type = 'A'
                else:
                    char_type = 'S'

                if current_type is None:
                    current_type = char_type
                elif char_type != current_type:
                    tokens.append((current_type, s[current_start:i]))
                    current_type = char_type
                    current_start = i
            if current_type is not None:
                tokens.append((current_type, s[current_start:]))
            return tokens

        #验证所有字符串是否具有相同的标记结构
        token_lists = [tokenize(s) for s in strings]
        first_tokens = token_lists[0]
        for tokens in token_lists[1:]:
            if len(tokens) != len(first_tokens):
                return None
            for i, (type1, _) in enumerate(tokens):
                type2, _ = first_tokens[i]
                if type1 != type2:
                    return None

        #根据上面第一个字符串的标记构建正则
        regex_pattern = ''
        for token_type, value in first_tokens:
            if token_type == 'N':
                regex_pattern += r'\d+'
            elif token_type == 'R':
                regex_pattern += r'[IVXLCDM]+'
            elif token_type == 'C':
                regex_pattern += r'[\u4e00-\u9fff]+'
            elif token_type == 'A':
                regex_pattern += r'[a-zA-Z]+'
            else:
                regex_pattern += re.escape(value)

        try:
            return re.compile(f'^{regex_pattern}$', re.IGNORECASE)
        except re.error:
            return None

    @staticmethod
    def _extract_prefix_pattern(pattern: Pattern) -> str:
        pattern_str = pattern.pattern
        # 优先匹配罗马数字前缀
        match = re.search(r'([IVXLCDM]+)([^a-zA-Z0-9\u4e00-\u9fff]+)', pattern_str, re.IGNORECASE)
        if not match:
            # 再匹配其他类型前缀
            match = re.search(r'([\d一二三四五六七八九十百千万a-zA-Z]+)([^a-zA-Z0-9\u4e00-\u9fff]+)', pattern_str)
        if match:
            prefix_content = match.group(1)
            connector = match.group(2)

            if RomanNumeral.is_roman(prefix_content):
                prefix_type = 'R'
            elif prefix_content.isdigit():
                prefix_type = 'N'
            elif all('\u4e00' <= char <= '\u9fff' for char in prefix_content):
                prefix_type = 'C'
            elif prefix_content.isalpha():
                prefix_type = 'A'
            else:
                prefix_type = 'O'

            return f"{prefix_type}{connector}"

        return ''

    @staticmethod
    def refine_prefix_pattern(pattern: str, sample_string: str) -> str:
        if not sample_string:
            return pattern
        if re.match(r'^[IVXLCDM]+\.\s*', sample_string, re.IGNORECASE):
            return 'R.'  # 归类为罗马数字加圆点前缀
        hierarchy_match = re.match(r'^([A-Z])\.(\d+(\.\d+)*)\s*', sample_string)
        if hierarchy_match:
            hierarchy = hierarchy_match.group(2)
            depth = hierarchy.count('.') + 1  # Calculate depth based on the number of dots
            return f"{hierarchy_match.group(1)}-Level-{depth}"  # Group by depth level
        num_prefix_match = re.match(r'^\d+(?:\.\d+)*\.*\s*', sample_string)
        if num_prefix_match:
            # 步骤1：提取原始前缀并清洗
            raw_prefix = num_prefix_match.group(0)
            # 移除末尾所有点（处理格式变体：如"1."→"1"，"*******.."→"*******"）
            raw_prefix = raw_prefix.strip()
            cleaned_prefix = re.sub(r'\.*$', '', raw_prefix)
            # 移除前缀中的空格（避免"1 "和"1. "因空格差异影响判断）
            cleaned_prefix = cleaned_prefix.replace(' ', '')

            # 步骤2：特殊处理一级格式变体（核心通用化逻辑）
            # 若清洗后是纯数字（无点）→ 视为0个点（一级）
            # 若清洗后是"数字.数字"（如"1.2"）→ 正常统计点数量
            if re.fullmatch(r'^\d+$', cleaned_prefix):
                # 一级格式："1 产品"→清洗后"1"→0个点
                dot_count = 0
            else:
                # 多级格式：按实际点数量统计（如"2.4.1"→2个点）
                dot_count = cleaned_prefix.count('.')

            # 步骤3：动态生成层级标识（不限制层级数量）
            return f'N-Level-{dot_count}'
        # Other existing conditions remain unchanged
        if re.match(r'^[A-Z]\.\s*', sample_string):
            return 'A.'  # Assign to English letter with dot prefix
        bracket_match = re.match(r'^[(（]([A-Za-z])[\)）]\s*', sample_string, re.IGNORECASE)
        if bracket_match:
            return 'Bracket-A'  # Assig
        # Handle purely English text

        # 处理全角括号内中文数字前缀
        full_bracket_match = re.match(r'^（([一二三四五六七八九十百千万\s]+）)\s*', sample_string)
        if full_bracket_match:
            # return 'C（）'  # 归类为全角括号内中文数字前缀
            return 'C()'  # 归类为半角括号内中文数字前缀
        # 处理半角括号内中文数字前缀
        half_bracket_match = re.match(r'^([(]([一二三四五六七八九十百千万\s]+)[)])\s*', sample_string)
        if half_bracket_match:
            return 'C()'  # 归类为半角括号内中文数字前缀

        # 处理全角括号内阿拉伯数字前缀
        full_num_bracket_match = re.match(r'^（([\d\s]+）)\s*', sample_string)
        if full_num_bracket_match:
            return 'N（）'  # 归类为全角括号内阿拉伯数字前缀

        # 处理半角括号内阿拉伯数字前缀
        half_num_bracket_match = re.match(r'^([(]([\d\s]+)[)])\s*', sample_string)
        if half_num_bracket_match:
            return 'N()'  # 归类为半角括号内阿拉伯数字前缀

        if re.match(r'^\d+\s*、\s*', sample_string):
            return 'N、'  # 归类为数字顿号前缀

        symbol_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳])\s*', sample_string)
        if symbol_match:
            return 'S'  # 归类为特殊符号前缀
            # 处理括号内的中文数字前缀
        if re.match(r'^（[一二三四五六七八九十百千万\s]+）\s*', sample_string):
            return 'C（）'  # 归类为中文括号前缀

            # 处理"第X节"形式的前缀（支持多位中文数字）
        if re.match(r'^第\s*[一二三四五六七八九十百千万]+\s*(?:[十百千万]\s*)?节\s*', sample_string):
            return 'C节'  # 归类为中文节前缀

        # 处理中文数字+顿号的前缀（允许空格）
        # if re.match(r'^[一二三四五六七八九十百千万\s]+\s*、\s*', sample_string):
        #     return 'C、'  # 归类为中文顿号前缀‘
        if re.match(r'^(?:\s*[一二三四五六七八九十百千万]+\s*[十百千万]?\s*)+\s*、\s*', sample_string):
            return 'C、'  # 归类为中文顿号前缀

        # 处理普通中文前缀
        if all('\u4e00' <= char <= '\u9fff' for char in sample_string):
            return 'C'  # 归类为纯中文前缀

        if sample_string[0].isalpha() and all(char.isalpha() or char.isspace() for char in sample_string):
            return 'E'  # Assign to pure English text prefix
        return pattern

    def _group_by_prefix_pattern(format_groups: Dict[Pattern, Dict[Hashable, str]]) -> (
    Dict[str, Dict[Hashable, str]], bool):
        """按优化后的前缀模式分组，使用前缀作为键"""
        prefix_groups: Dict[str, Dict[Hashable, str]] = defaultdict(dict)
        prefix_filter_groups: Dict[str, Dict[Hashable, str]] = defaultdict(dict)
        is_all_filter_patten = True
        for pattern, key_values in format_groups.items():
            if not key_values:
                continue

            sample_value = next(iter(key_values.values()))
            prefix_pattern = StringFormatValidator._extract_prefix_pattern(pattern)
            refined_prefix = StringFormatValidator.refine_prefix_pattern(prefix_pattern, sample_value)
            if refined_prefix.startswith('O') or refined_prefix in StringFormatValidator.FILTERED_PREFIXES:
                prefix_filter_groups[refined_prefix].update(key_values)
                continue
            # 直接使用前缀作为键，合并相同前缀的组
            prefix_groups[refined_prefix].update(key_values)
            is_all_filter_patten = False

        if is_all_filter_patten:
            return prefix_filter_groups, is_all_filter_patten
        return prefix_groups, is_all_filter_patten

    @staticmethod
    def validate_against_groups(
            groups: Dict[str, Dict[Hashable, str]],
            test_string: str,
            match_prefix: bool = False
    ) -> List[Tuple[str, Dict[Hashable, str]]]:
        """验证字符串，支持前缀匹配或完整匹配，或两者同时检查"""
        matched_groups = []
        test_string = test_string.replace(" ","")
        # 生成测试字符串的模式和前缀
        test_pattern = StringFormatValidator._generate_pattern([test_string])
        if not test_pattern:
            return []

        test_prefix = StringFormatValidator._extract_prefix_pattern(test_pattern)
        refined_test_prefix = StringFormatValidator.refine_prefix_pattern(test_prefix, test_string)

        # 遍历所有组，检查是否匹配
        for prefix, key_values in groups.items():
            # 前缀匹配检查
            prefix_match = match_prefix and (refined_test_prefix == prefix)

            # 完整匹配检查：测试模式是否能匹配组内任一字符串
            # full_match = False
            # if not prefix_match or match_prefix is False:  # 避免不必要的检查
            #     for value in key_values.values():
            #         if test_pattern.fullmatch(value):
            #             full_match = True
            #             break

            # 只要满足前缀匹配或完整匹配即视为匹配成功
            if prefix_match:
                matched_groups.append((prefix, key_values))

        return matched_groups

    @staticmethod
    def add_to_group(
            groups: Dict[str, Dict[Hashable, str]],
            test_key: Hashable,
            test_string: str,
            prefix: str
    ) -> None:
        """将测试项添加到指定前缀的组"""
        if prefix in groups:
            groups[prefix][test_key] = test_string
            logger.debug(f"  已添加 '{test_key}: {test_string}' 到组 '{prefix}'")
        else:
            groups[prefix] = {test_key: test_string}
            logger.debug(f"  新建组 '{prefix}' 并添加 '{test_key}: {test_string}'")


# 使用示例


def assign_levels(is_en, format_groups: Dict[str, Dict[Hashable, str]],
                  dst_list: List[DST]):
    # Create a mapping of dst_id to dst object for quick lookup
    dst_id_map = {dst.id: dst for dst in dst_list}

    # Collect patterns and their corresponding dsts
    pattern_dst_mapping = []
    for (pattern), group in format_groups.items():
        dsts = [dst_id_map[dst_id] for dst_id in group.keys() if dst_id in dst_id_map]
        logger.debug(f"Pattern: {pattern}, group: {group.values()}")
        if dsts:
            # Find the minimum page number for the pattern

            min_page = min(dst.attributes.page for dst in dsts)
            # Find the dst with the smallest x1 value on the min_page
            min_y1_dst = min(
                (dst for dst in dsts if dst.attributes.page == min_page),
                key=lambda d: d.attributes.position.bbox.y1,
            )
            pattern_dst_mapping.append((pattern, dsts, min_page, min_y1_dst))

    # Sort patterns by their minimum page number and x1 value
    pattern_dst_mapping.sort(key=lambda x: (x[2], x[3].attributes.position.bbox.y1))

    level = 1
    for i, (pattern, dsts, _, latest_dst) in enumerate(pattern_dst_mapping):
        if i < 10:
            # Assign levels for the first 10 patterns
            sorted_dsts = sorted(dsts, key=lambda d: (d.attributes.page, d.attributes.position.bbox.y1))
            for dst in sorted_dsts:
                dst.attributes.level = level

            if is_en and pattern == 'E':
                continue
            level += 1
        else:
            # Process remaining patterns
            current_page = latest_dst.attributes.page
            current_y1 = latest_dst.attributes.position.bbox.y1

            # Get all dsts on the same page for other patterns
            other_dsts_on_page = [
                dst for _, other_dsts, _, _ in pattern_dst_mapping[:i]
                for dst in other_dsts if dst.attributes.page == current_page
            ]
            other_dsts_on_page = sorted(other_dsts_on_page, key=lambda d: d.attributes.position.bbox.y1)

            # Find the first dst on the page with y1 greater than the latest_dst
            for other_dst in other_dsts_on_page:
                if other_dst.attributes.position.bbox.y1 > current_y1:
                    # Assign level to all dsts of the current pattern
                    new_level = other_dst.attributes.level - 1
                    for dst in dsts:
                        dst.attributes.level = new_level
                    break
    return dst_list


def add_parent(dst_list: List[DST]):
    for i in range(len(dst_list) - 1, -1, -1):
        current_dst = dst_list[i]
        parent_id = None
        for j in range(i - 1, -1, -1):
            if dst_list[j].attributes.level < current_dst.attributes.level:
                parent_id = dst_list[j].id
                break
        if parent_id is not None:
            current_dst.parent = parent_id
    return dst_list


def is_main_text(dst: DST, main_text_size: float) -> bool:
    return ( dst.bold is False or (
            dst.font_size is not None and dst.font_size < main_text_size))


# 使用示例
if __name__ == "__main__":

    mixed_examples = {
        # "id1": "1.1自然生成",  # 数字前缀
        # "id2": "A.1测试生成",  # 字母前缀
        # "id3": "III. METHODOLOGY",  # 罗马数字前缀
        # "id4": "IV. RESULTS",  # 罗马数字前缀
        # "id5": "2.1责任模式",  # 数字前缀
        # "id6": "B.2其他情况",  # 字母前缀
        # "id7": "1.123特殊情况",  # 数字前缀
        # "id8": "1-1其他格式",  # 数字前缀
        # "id9": "2.2 责任/模式",  # 数字前缀
        # "id10": "二、责任分配",  # 中文顿号前缀
        # "id11": "2.2 责任、模式",  # 数字前缀
        # "id12": "2.2 责任;模式",  # 数字前缀
        # "id13": "3.3 测试项",  # 数字前缀
        # "id14": "IV-4 特殊案例",  # 罗马数字前缀
        # "id15": "第二节 主要内容",  # 中文节前缀
        # "id16": "第三节 主要内容",  # 中文节前缀
        # "id16": "① 基本要求",  # 符号前缀
        # "id17": "① 要求",  # 符号前缀
        # "id18": "43、 1 年内到期的非流动负债",  # 符号前缀
        # "id29": "44、 年内到期的非流动负债",  # 符号前缀
        # "id19": "修订信息",  # 纯中文
        # "id20": "（A） 市场风险",  # 中文括号前缀
        # "id21": "（B） 审批程序及其他说明",  # 中文括号前缀
        # "id24": "(一) 与日常经营相关的关联交易",  # 中文括号前缀
        # "id23": "二、细节、说明",  # 中文顿号前缀
        # "id27": "四、总结",  # 中文顿号前缀
        # "id28": "（1）划分为持有待售确认标准",  # 中文顿号前缀
        # "id29": "（5）低值易耗品的摊销方法",  # 中文顿号前缀
        # "id30": "（1） 敏感性分析：",  # 中文顿号前缀
        # "id31": "① WPS 365",  # 中文顿号前缀
        # "id32": "② AI驱动办公软件升级",  # 中文顿号前缀
        # "id33": "③ WPS AI 办公助手",  # 中文顿号前缀
        # "id33": "Abstract",  # 中文顿号前缀
        # "id34": "Randomized experiments are increasingly",  # 中文顿号前缀
        # "id35": "1 Introduction",  # 中文顿号前缀
        # "id36": "2 Introduction",  # 中文顿号前缀
        # "id37": "3.3 Testing Total effects",  # 中文顿号前缀
        # "id38": "3.4 Testing Total effects",  # 中文顿号前缀
        # "id39": "A Futher Details",  # 中文顿号前缀
        # "id40": "B Futher Details",  # 中文顿号前缀
        # "id41": "A.1 Details for Remark 3.2",  # 中文顿号前缀
        # "id42": "A.2 Details for Remark 3.2",  # 中文顿号前缀
        # "id43": "A.2.1 Details for Remark 3.2",  # 中文顿号前缀
        "id43": "1 产品",  # 中文顿号前缀
        "id49": "1. 产品",  # 中文顿号前缀
        "id44": "2.3 产品组成",  # 中文顿号前缀
        "id45": "2.2 产品定位",  # 中文顿号前缀
        "id46": "2.4.1 强大便捷的文档存储管理服务",  # 中文顿号前缀
        "id47": "*******. 搭建文档数据资产管理中心",  # 中文顿号前缀
        "id48": "2.4.1.2 避免终端文件损坏造成的损失",  # 中文顿号前缀
    }

    # 分析格式并分组
    prefix_groups, is_all = StringFormatValidator.analyze_format(mixed_examples)

    print(f"从 {len(mixed_examples)} 个样本中识别出 {len(prefix_groups)} 个前缀模式组:")
    for i, (prefix_pattern, key_values) in enumerate(prefix_groups.items(), 1):
        print(f"\nGroup {i} - Prefix Pattern: '{prefix_pattern}'")
        print(f"  Items: {list(key_values.keys())}")
        print(f"  Actual Content: {[mixed_examples[key] for key in key_values.keys()]}")

    # 验证功能测试
    test_cases = {
        # "id22": "3.4 新增测试",  # 应匹配"N."前缀组
        # "id24": "V. DISCUSSION",  # 应匹配"R."前缀组
        # "id25": "IV-4 特殊案例",  # 应匹配"R-"前缀组
        # "id26": "C.3 补充说明",  # 应匹配"A."前缀组
        "id29": "2.4.2.1. 提升对文档的集中管控能力",  # 应匹配纯中文组
        # "id29": "(一) 与日常经营相关的关联交易",  # 应匹配纯中文组
        # "id30": "十二、 因国家秘密、商业秘密等原因的信息暂缓、豁免情况说明",  # 应匹配"第二节 主要内容"所在组
        # "id32": "十 一、采用公允价值计量的项目",  # 应匹配"（三） 市场风险"所在组
        # "id33": "一、本公司董事会、监事会及董事、监事、高级管理人员保证年度报告内容的真实性、准确性、完整性，不存在虚假记载、误导性陈述或重大遗漏，并承担个别和连带的法律责任。"
        # ） 审批程序及其他说明"所在组应匹配"（四
    }

    print("\n验证测试:")
    for test_key, test_str in test_cases.items():
        print(f"\n测试项: '{test_key}: {test_str}'")
        matched_groups = StringFormatValidator.validate_against_groups(prefix_groups, test_str, match_prefix=True)
        if matched_groups:
            prefix, _ = matched_groups[0]
            StringFormatValidator.add_to_group(prefix_groups, test_key, test_str, prefix)
        else:
            print("  未匹配到任何组，将作为新组添加")
            test_pattern = StringFormatValidator._generate_pattern([test_str])
            if test_pattern:
                test_prefix = StringFormatValidator._extract_prefix_pattern(test_pattern)
                refined_prefix = StringFormatValidator.refine_prefix_pattern(test_prefix, test_str)
                prefix_groups[refined_prefix] = {test_key: test_str}
                print(f"  ⚠️ 新建组 '{refined_prefix}' 并添加 '{test_key}: {test_str}'")
            else:
                print("  未匹配任何组")

    # 打印最终的分组结果
    print("\n最终分组结果:")
    for i, ((prefix_pattern), key_values) in enumerate(prefix_groups.items(), 1):
        print(f"\n组 {i} - 前缀模式: '{prefix_pattern}'")
        print(f"  包含项: {list(key_values.keys())}")
        print(f"  实际内容: {list(key_values.values())}")
