import re


def contains_chinese(content: str) -> bool:
    """
    Check if the content list contains any Chinese characters.

    :param content: List of strings to check.
    :return: True if any string contains Chinese characters, False otherwise.
    """
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    # for text in content:
    if chinese_pattern.search(content):
        return True
    return False
