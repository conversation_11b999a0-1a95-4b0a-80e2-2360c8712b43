# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/2 14:55


from commons.db.mysqldao import <PERSON>sqlDao, SQLBase, Session
from sqlalchemy import Column, BigInteger, DateTime, String, TEXT, Integer
from typing import Optional, List


class ParseRecordBase(SQLBase):
    __tablename__ = "parse_record"
    key_id = Column("id", BigInteger, primary_key=True, autoincrement=True)
    token = Column(String(100))
    heartbeat = Column(Integer, default=0)
    env = Column(String(20))
    record_json = Column(TEXT)


class ParseRecord(ParseRecordBase):
    ctime = Column("CREATE_TIME", DateTime, default=None)
    mtime = Column("UPDATE_TIME", DateTime, default=None)


class ParseRecordDao(object):
    @staticmethod
    def add_record(db: Session, token: str, record_json: str, commit: bool = True) -> int:
        record = ParseRecordBase(token=token, record_json=record_json, env=MysqlDao().env)
        db.add(record)
        if commit:
            db.commit()
        return record.key_id

    @staticmethod
    def get_record(db: Session, token: str) -> Optional[ParseRecord]:
        return db.query(ParseRecord).filter(ParseRecordBase.token == token).first()

    @staticmethod
    def exist_record(db: Session, token: str) -> bool:
        return len(db.query(ParseRecord).filter(ParseRecordBase.token == token).all()) > 0

    @staticmethod
    def record_heartbeat(db: Session, token: str, commit: bool = True):
        record = db.query(ParseRecord).filter(ParseRecordBase.token == token).first()
        if record is not None:
            record.heartbeat += 1
            if commit:
                db.commit()

    @staticmethod
    def del_record(db: Session, token: str, commit: bool = True):
        db.query(ParseRecord).filter(ParseRecordBase.token == token).delete()
        if commit:
            db.commit()

    @staticmethod
    def scan_records(db: Session, last_id: int = None, limit: int = 100) -> List[ParseRecord]:
        q = db.query(ParseRecord).filter(ParseRecord.env == MysqlDao().env).order_by(ParseRecord.key_id.asc())
        if last_id is not None:
            q = q.filter(ParseRecord.key_id > last_id)
        rs = q.limit(limit).all()
        return rs
