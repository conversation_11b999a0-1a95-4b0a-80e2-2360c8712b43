["tests/integration/test_router_integration.py::TestKafkaIntegration::test_kafka_lag_success", "tests/integration/test_router_integration.py::TestKafkaIntegration::test_kafka_monitoring_consistency", "tests/integration/test_router_integration.py::TestKafkaIntegration::test_kafka_throughput_success", "tests/integration/test_router_integration.py::TestParseIntegration::test_general_parse_res_invalid_token", "tests/integration/test_router_integration.py::TestParseIntegration::test_general_parse_res_success", "tests/integration/test_router_integration.py::TestParseIntegration::test_parse_pipeline_background_mode", "tests/integration/test_router_integration.py::TestParseIntegration::test_parse_pipeline_success", "tests/integration/test_router_integration.py::TestParseIntegration::test_parse_pipeline_with_url", "tests/integration/test_router_integration.py::TestTableMergeIntegration::test_merge_table_empty_content", "tests/integration/test_router_integration.py::TestTableMergeIntegration::test_merge_table_large_content", "tests/integration/test_router_integration.py::TestTableMergeIntegration::test_merge_table_success", "tests/integration/test_router_integration.py::TestVersionIntegration::test_parse_version_multiple_calls", "tests/integration/test_router_integration.py::TestVersionIntegration::test_parse_version_success"]