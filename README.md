# aidocs_dst_server

构建新的文档解析流程以及DST和chunking

## 发版改动
### 🔧 v1.1.20250731 (2025-07-31)
<details>
<summary>缺陷修复</summary>

- 修复checkbox勾选识别错误
</details>

---

### 🔧 v1.1.20250807 (2025-08-07)
<details open>
<summary>核心功能升级</summary>

| 模块          | 改进方向      | 具体修改                                                                                                              |
|-------------|-----------|-------------------------------------------------------------------------------------------------------------------|
| **解析引擎**    | 多路解析优化    | - PDF双引擎路由：<br>  `KDC`（复杂版式）/`MuPDF`（常规页面）<br> - kdc路由条件：有线表格/全页图片/表格图层走kdc <br> - mupdf路由条件：剩下的页面走mupdf          |
| **内容处理**    | Chunk模块升级 | - 双模式切分：<br>  `大纲模式`（结构层级）/`语义模式`（上下文关联）                                                                          |
| **文件服务**    | 下载链路重构    | - 环境变量注入式配置<br> - 工厂实例动态初始化                                                                                       |
| **图片文件解析**  | 图片文件解析优化  | - 图片文件不会转换为pdf文件再走kdc解析，而是直接走ocr flux进行解析                                                                         |
| **dst解析增强** | 无线表和图片增强  | - 对于已经标记为table的dst，走一次版式判断，发现内部有无无线表，有的话走一次ocr flux进行增强 <br> - 对于解析出的图片元素也会走ocr flux进行增强 <br> - 上面两项可以通过环境变量开关进行控制 | 

</details>


### 🔧 v1.1.20250807 (2025-08-14)
<details>
<summary>返回值优化</summary>

- 解析结果返回值添加parse_target
</details>
<details>
<summary>图片描述改造</summary>

- 图片描述请求参数从need_image_desc 改为parse_target
</details>