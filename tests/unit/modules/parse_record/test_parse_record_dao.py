"""
modules.parse_record.parse_record_dao 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime
from typing import List

from modules.parse_record.parse_record_dao import (
    ParseRecordBase, ParseRecord, ParseRecordDao
)


class TestParseRecordBase:
    """ParseRecordBase 模型的测试用例"""

    def test_parse_record_base_table_name(self):
        """测试 ParseRecordBase 表名"""
        assert ParseRecordBase.__tablename__ == "parse_record"

    def test_parse_record_base_columns(self):
        """测试 ParseRecordBase 具有必需的列"""
        # 检查列是否存在
        assert hasattr(ParseRecordBase, 'key_id')
        assert hasattr(ParseRecordBase, 'token')
        assert hasattr(ParseRecordBase, 'heartbeat')
        assert hasattr(ParseRecordBase, 'env')
        assert hasattr(ParseRecordBase, 'record_json')

    def test_parse_record_base_creation(self):
        """测试 ParseRecordBase 实例创建"""
        record = ParseRecordBase(
            token="test_token",
            record_json='{"test": "data"}',
            env="test"
        )
        
        assert record.token == "test_token"
        assert record.record_json == '{"test": "data"}'
        assert record.env == "test"
        # assert record.heartbeat == 0  # 默认值


class TestParseRecord:
    """ParseRecord 模型的测试用例"""

    def test_parse_record_inherits_from_base(self):
        """测试 ParseRecord 继承自 ParseRecordBase"""
        assert issubclass(ParseRecord, ParseRecordBase)

    def test_parse_record_additional_columns(self):
        """测试 ParseRecord 具有额外的时间戳列"""
        assert hasattr(ParseRecord, 'ctime')
        assert hasattr(ParseRecord, 'mtime')

    def test_parse_record_creation(self):
        """测试 ParseRecord 实例创建"""
        now = datetime.now()
        record = ParseRecord(
            token="test_token",
            record_json='{"test": "data"}',
            env="test",
            ctime=now,
            mtime=now
        )
        
        assert record.token == "test_token"
        assert record.record_json == '{"test": "data"}'
        assert record.env == "test"
        assert record.ctime == now
        assert record.mtime == now


class TestParseRecordDao:
    """ParseRecordDao 类的测试用例"""

    @patch('modules.parse_record.parse_record_dao.MysqlDao')
    def test_add_record_success(self, mock_mysql_dao):
        """测试成功添加记录"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_mysql_dao.return_value.env = "test"
        
        # Call method
        result = ParseRecordDao.add_record(
            db=mock_db,
            token="test_token",
            record_json='{"test": "data"}',
            commit=True
        )
        
        # Verify database operations
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        
        # Verify the record was created with correct data
        added_record = mock_db.add.call_args[0][0]
        assert added_record.token == "test_token"
        assert added_record.record_json == '{"test": "data"}'
        assert added_record.env == "test"

    @patch('modules.parse_record.parse_record_dao.MysqlDao')
    def test_add_record_no_commit(self, mock_mysql_dao):
        """测试不提交的记录添加"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_mysql_dao.return_value.env = "test"
        
        # Call method
        ParseRecordDao.add_record(
            db=mock_db,
            token="test_token",
            record_json='{"test": "data"}',
            commit=False
        )
        
        # Verify database operations
        mock_db.add.assert_called_once()
        mock_db.commit.assert_not_called()

    def test_get_record_success(self):
        """测试成功检索记录"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_record = MagicMock(spec=ParseRecord)
        mock_db.query.return_value.filter.return_value.first.return_value = mock_record
        
        # Call method
        result = ParseRecordDao.get_record(mock_db, "test_token")
        
        # Verify result
        assert result is mock_record
        mock_db.query.assert_called_once_with(ParseRecord)

    def test_get_record_not_found(self):
        """测试记录未找到时的记录检索"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Call method
        result = ParseRecordDao.get_record(mock_db, "nonexistent_token")
        
        # Verify result
        assert result is None

    def test_exist_record_true(self):
        """测试记录存在时的 exist_record"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_db.query.return_value.filter.return_value.all.return_value = [MagicMock()]
        
        # Call method
        result = ParseRecordDao.exist_record(mock_db, "existing_token")
        
        # Verify result
        assert result is True

    def test_exist_record_false(self):
        """测试记录不存在时的 exist_record"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_db.query.return_value.filter.return_value.all.return_value = []
        
        # Call method
        result = ParseRecordDao.exist_record(mock_db, "nonexistent_token")
        
        # Verify result
        assert result is False

    def test_record_heartbeat_success(self):
        """测试成功记录心跳"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_record = MagicMock()
        mock_record.heartbeat = 5
        mock_db.query.return_value.filter.return_value.first.return_value = mock_record
        
        # Call method
        ParseRecordDao.record_heartbeat(mock_db, "test_token", commit=True)
        
        # Verify heartbeat was incremented
        assert mock_record.heartbeat == 6
        mock_db.commit.assert_called_once()

    def test_record_heartbeat_no_commit(self):
        """测试不提交的心跳记录"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_record = MagicMock()
        mock_record.heartbeat = 3
        mock_db.query.return_value.filter.return_value.first.return_value = mock_record
        
        # Call method
        ParseRecordDao.record_heartbeat(mock_db, "test_token", commit=False)
        
        # Verify heartbeat was incremented but not committed
        assert mock_record.heartbeat == 4
        mock_db.commit.assert_not_called()

    def test_record_heartbeat_record_not_found(self):
        """测试记录未找到时的心跳记录"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Call method (should not raise exception)
        ParseRecordDao.record_heartbeat(mock_db, "nonexistent_token")
        
        # Verify no commit was called
        mock_db.commit.assert_not_called()

    def test_del_record_success(self):
        """测试成功删除记录"""
        # 设置模拟对象
        mock_db = MagicMock()
        
        # Call method
        ParseRecordDao.del_record(mock_db, "test_token", commit=True)
        
        # Verify database operations
        mock_db.query.assert_called_once_with(ParseRecord)
        mock_db.commit.assert_called_once()

    def test_del_record_no_commit(self):
        """测试不提交的记录删除"""
        # 设置模拟对象
        mock_db = MagicMock()
        
        # Call method
        ParseRecordDao.del_record(mock_db, "test_token", commit=False)
        
        # Verify database operations
        mock_db.query.assert_called_once_with(ParseRecord)
        mock_db.commit.assert_not_called()

    @patch('modules.parse_record.parse_record_dao.MysqlDao')
    def test_scan_records_success(self, mock_mysql_dao):
        """测试成功的记录扫描"""
        # Setup mocks
        mock_db = MagicMock()
        mock_mysql_dao.return_value.env = "test"
        mock_records = [MagicMock(spec=ParseRecord) for _ in range(3)]
        mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = mock_records
        
        # Call method
        result = ParseRecordDao.scan_records(mock_db, last_id=None, limit=100)
        
        # Verify result
        assert result == mock_records
        assert len(result) == 3

    @patch('modules.parse_record.parse_record_dao.MysqlDao')
    def test_scan_records_with_last_id(self, mock_mysql_dao):
        """测试使用last_id参数的记录扫描"""
        # 设置模拟对象
        mock_db = MagicMock()
        mock_mysql_dao.return_value.env = "test"
        mock_query = mock_db.query.return_value
        mock_filter1 = mock_query.filter.return_value
        mock_order = mock_filter1.order_by.return_value
        mock_filter2 = mock_order.filter.return_value
        mock_limit = mock_filter2.limit.return_value
        mock_limit.all.return_value = []

        # Call method
        ParseRecordDao.scan_records(mock_db, last_id=10, limit=50)

        # Verify that filter was called twice: once on query, once on order result
        mock_query.filter.assert_called_once()
        mock_order.filter.assert_called_once()

    @patch('modules.parse_record.parse_record_dao.MysqlDao')
    def test_scan_records_custom_limit(self, mock_mysql_dao):
        """测试使用自定义限制的记录扫描"""
        # Setup mocks
        mock_db = MagicMock()
        mock_mysql_dao.return_value.env = "test"
        mock_query = mock_db.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_order = mock_filter.order_by.return_value
        mock_limit = mock_order.limit.return_value
        mock_limit.all.return_value = []
        
        # Call method
        ParseRecordDao.scan_records(mock_db, last_id=None, limit=25)
        
        # Verify limit was applied
        mock_order.limit.assert_called_once_with(25)

    def test_parse_record_dao_static_methods(self):
        """测试所有DAO方法都是静态的"""
        # 验证方法是静态的（可以在没有实例的情况下调用）
        assert hasattr(ParseRecordDao, 'add_record')
        assert hasattr(ParseRecordDao, 'get_record')
        assert hasattr(ParseRecordDao, 'exist_record')
        assert hasattr(ParseRecordDao, 'record_heartbeat')
        assert hasattr(ParseRecordDao, 'del_record')
        assert hasattr(ParseRecordDao, 'scan_records')
        
        # All methods should be callable without instantiation
        methods = [
            ParseRecordDao.add_record,
            ParseRecordDao.get_record,
            ParseRecordDao.exist_record,
            ParseRecordDao.record_heartbeat,
            ParseRecordDao.del_record,
            ParseRecordDao.scan_records
        ]
        
        for method in methods:
            assert callable(method)
