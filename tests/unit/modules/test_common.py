"""
Unit tests for modules.common module
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from io import BytesIO
from PIL import Image
import base64
import uuid
import aiohttp

from modules.common import (
    table2html, build_dst_id, upload_image, async_get_file,
    build_hyperlink_map, bytes_or_base64_image,
    table_entity2html, build_media_map, build_root_dst,
    calculate_original_bbox, _upload_image, _get_pdf_data, open_pdf,
    _sort_textbox, _process_cell_content, _process_textbox,
    _process_paragraph, _process_component
)
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from modules.entity.kdc_enttiy import Media, BlockType, ComponentType, HyperLink, Block


class TestCommonUtilities:
    """通用工具函数的测试用例"""

    @pytest.mark.asyncio
    async def test_table_entity2html_empty_table(self):
        """测试table_entity2html处理空表格"""
        result = await table_entity2html(None, {}, {})
        assert result == "<table></table>"

    @pytest.mark.asyncio
    async def test_table_entity2html_no_rows(self):
        """测试table_entity2html处理没有行的表格"""
        mock_table = MagicMock()
        mock_table.rows = []
        result = await table_entity2html(mock_table, {}, {})
        assert result == "<table></table>"

    @pytest.mark.asyncio
    async def test_table_entity2html_basic_table(self):
        """测试table_entity2html处理基本表格结构"""
        # Create mock table structure
        mock_cell = MagicMock()
        mock_cell.row_span = 1
        mock_cell.col_span = 1
        mock_cell.blocks = []

        mock_row = MagicMock()
        mock_row.cells = [mock_cell]

        mock_table = MagicMock()
        mock_table.rows = [mock_row]

        with patch('modules.common._process_cell_content', return_value="test content"):
            result = await table_entity2html(mock_table, {}, {})
            assert result.startswith("<table>")
            assert result.endswith("</table>")
            assert "test content" in result

    @pytest.mark.asyncio
    async def test_table_entity2html_with_spans(self):
        """Test table_entity2html with row and column spans"""
        mock_cell = MagicMock()
        mock_cell.row_span = 2
        mock_cell.col_span = 3
        mock_cell.blocks = []

        mock_row = MagicMock()
        mock_row.cells = [mock_cell]

        mock_table = MagicMock()
        mock_table.rows = [mock_row]

        with patch('modules.common._process_cell_content', return_value="spanned content"):
            result = await table_entity2html(mock_table, {}, {})
            assert 'rowspan="2"' in result
            assert 'colspan="3"' in result
            assert "spanned content" in result

    def test_table2html_success(self):
        """测试成功的表格到HTML转换"""
        table_data = [
            [{'content': ['Header 1'], 'row_span': 1, 'col_span': 1},
             {'content': ['Header 2'], 'row_span': 1, 'col_span': 1}],
            [{'content': ['Row 1 Col 1'], 'row_span': 1, 'col_span': 1},
             {'content': ['Row 1 Col 2'], 'row_span': 1, 'col_span': 1}]
        ]

        result = table2html(table_data)

        assert result.startswith("<table>")
        assert result.endswith("</table>")
        assert "Header 1" in result
        assert "Header 2" in result
        assert "Row 1 Col 1" in result
        assert "Row 1 Col 2" in result

    def test_table2html_empty(self):
        """Test table2html with empty table"""
        result = table2html([])
        assert result == ""

    def test_table2html_none(self):
        """Test table2html with None input"""
        result = table2html(None)
        assert result == ""

    def test_build_dst_id(self):
        """Test DST ID generation"""
        id1 = build_dst_id()
        id2 = build_dst_id()

        assert isinstance(id1, str)
        assert isinstance(id2, str)
        assert len(id1) == 32  # UUID hex string length
        assert len(id2) == 32
        assert id1 != id2  # Should be unique

    @pytest.mark.asyncio
    async def test_async_get_file_success(self):
        """Test successful async file retrieval"""
        mock_url = "https://example.com/test.jpg"
        mock_image_data = b"fake image data"

        with patch('modules.common.aiohttp.ClientSession') as mock_session_class:
            # Create a proper async context manager mock
            mock_response = AsyncMock()
            mock_response.read = AsyncMock(return_value=mock_image_data)
            mock_response.raise_for_status = MagicMock()

            mock_get_context = AsyncMock()
            mock_get_context.__aenter__ = AsyncMock(return_value=mock_response)
            mock_get_context.__aexit__ = AsyncMock(return_value=None)

            mock_session_instance = AsyncMock()
            mock_session_instance.get = MagicMock(return_value=mock_get_context)

            mock_session_context = AsyncMock()
            mock_session_context.__aenter__ = AsyncMock(return_value=mock_session_instance)
            mock_session_context.__aexit__ = AsyncMock(return_value=None)

            mock_session_class.return_value = mock_session_context

            with patch('modules.common.Image.open') as mock_image_open:
                mock_image = MagicMock()
                mock_image_open.return_value = mock_image

                result = await async_get_file(mock_url)

                assert result == mock_image
                mock_response.raise_for_status.assert_called_once()
                mock_response.read.assert_called_once()

    def test_upload_image_success(self):
        """Test successful image upload"""
        mock_base64_str = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

        with patch('modules.common.StoreDao') as mock_store:
            mock_instance = mock_store.return_value
            # Mock the upload_from_bytes to return True (success)
            mock_instance.upload_from_bytes = MagicMock(return_value=True)
            # Mock generate_url to return a URL that contains '-internal'
            mock_instance.generate_url = MagicMock(return_value="https://ks3-internal.example.com/image.png")

            result = upload_image(mock_base64_str)

            # The function should remove '-internal' from the URL
            assert result == "https://ks3.example.com/image.png"
            mock_instance.upload_from_bytes.assert_called_once()
            mock_instance.generate_url.assert_called_once()

    def test_build_hyperlink_map(self):
        """Test building hyperlink map"""
        from modules.entity.kdc_enttiy import HyperLink

        mock_hyperlinks = [
            MagicMock(spec=HyperLink),
            MagicMock(spec=HyperLink)
        ]

        # Configure first hyperlink
        mock_hyperlinks[0].display_text = "Link 1"
        mock_hyperlinks[0].references = [MagicMock(id="ref1")]

        # Configure second hyperlink
        mock_hyperlinks[1].display_text = "Link 2"
        mock_hyperlinks[1].references = [MagicMock(id="ref2")]

        result = build_hyperlink_map(mock_hyperlinks)

        assert isinstance(result, dict)
        assert "ref1" in result
        assert "ref2" in result

    def test_build_hyperlink_map_empty(self):
        """Test building hyperlink map with empty list"""
        result = build_hyperlink_map([])
        assert result == {}

    def test_build_hyperlink_map_none(self):
        """Test building hyperlink map with None"""
        result = build_hyperlink_map(None)
        assert result == {}

    def test_bytes_or_base64_image_with_bytes(self):
        """Test bytes_or_base64_image with bytes input"""
        # Create a simple 1x1 pixel PNG image in bytes
        mock_image_bytes = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x12IDATx\x9cc```bPPP\x00\x02\xd2\x00\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'

        with patch('modules.common.Image.open') as mock_image_open:
            mock_image = MagicMock()
            mock_image_open.return_value = mock_image

            result = bytes_or_base64_image(mock_image_bytes)

            assert result == mock_image
            mock_image_open.assert_called_once()

    def test_bytes_or_base64_image_with_base64(self):
        """Test bytes_or_base64_image with base64 string input"""
        mock_base64_str = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

        with patch('modules.common.base64.b64decode') as mock_b64decode:
            with patch('modules.common.Image.open') as mock_image_open:
                mock_decoded_bytes = b"decoded image data"
                mock_b64decode.return_value = mock_decoded_bytes
                mock_image = MagicMock()
                mock_image_open.return_value = mock_image

                result = bytes_or_base64_image(mock_base64_str)

                assert result == mock_image
                mock_b64decode.assert_called_once_with(mock_base64_str)
                mock_image_open.assert_called_once()

    def test_build_root_dst(self):
        """Test building root DST node"""
        result = build_root_dst()

        assert isinstance(result, DST)
        assert result.dst_type == DSTType.ROOT
        assert result.parent == str(-1)
        assert result.content == ['根节点']
        assert result.order == 0
        assert result.attributes.level == 0
        assert result.attributes.page == 0
        assert isinstance(result.id, str)
        assert len(result.id) == 32  # UUID hex string length

    def test_calculate_original_bbox_no_rotation(self):
        """Test calculate_original_bbox with no rotation"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40, rotate=0)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        assert result == (10, 20, 30, 40)

    def test_calculate_original_bbox_90_degrees(self):
        """Test calculate_original_bbox with 90 degree rotation"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40, rotate=90)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        # For 90 degrees: original_x1 = width-y2, original_y1 = x1, etc.
        expected = (100-40, 10, 100-20, 30)  # (60, 10, 80, 30)
        assert result == expected

    def test_calculate_original_bbox_180_degrees(self):
        """Test calculate_original_bbox with 180 degree rotation"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40, rotate=180)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        # For 180 degrees: original_x1 = width - x2, original_y1 = height - y2, etc.
        expected = (100-30, 200-40, 100-10, 200-20)  # (70, 160, 90, 180)
        assert result == expected

    def test_calculate_original_bbox_270_degrees(self):
        """Test calculate_original_bbox with 270 degree rotation"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40, rotate=270)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        # For 270 degrees: original_x1 = y1, original_y1 = height - x2, etc.
        expected = (20, 200-30, 40, 200-10)  # (20, 170, 40, 190)
        assert result == expected

    @pytest.mark.asyncio
    async def test_upload_image_async_success(self):
        """Test async image upload success"""
        mock_base64_str = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        idx = "test_idx"

        with patch('modules.common.StoreDao') as mock_store_class:
            mock_store = mock_store_class.return_value
            mock_store.async_upload_from_bytes = AsyncMock(return_value=True)
            mock_store.async_generate_url = AsyncMock(return_value="https://ks3-internal.example.com/image.png")

            result = await _upload_image(idx, mock_base64_str, public=True)

            assert result == "https://ks3.example.com/image.png"
            mock_store.async_upload_from_bytes.assert_called_once()
            mock_store.async_generate_url.assert_called_once()

    @pytest.mark.asyncio
    async def test_upload_image_async_failure(self):
        """Test async image upload failure"""
        mock_base64_str = "invalid_base64"
        idx = "test_idx"

        with patch('modules.common.StoreDao') as mock_store_class:
            mock_store = mock_store_class.return_value
            mock_store.async_upload_from_bytes = AsyncMock(side_effect=Exception("Upload failed"))

            result = await _upload_image(idx, mock_base64_str)

            assert result == ""

    @pytest.mark.asyncio
    async def test_build_media_map_empty(self):
        """Test build_media_map with empty media list"""
        from modules.pipeline.context import PipelineContext

        context = MagicMock(spec=PipelineContext)
        context.need_solve_picture = True

        result = await build_media_map(context, [])

        assert result == {}

    @pytest.mark.asyncio
    async def test_build_media_map_disabled(self):
        """Test build_media_map when picture solving is disabled"""
        from modules.pipeline.context import PipelineContext

        context = MagicMock(spec=PipelineContext)
        context.need_solve_picture = False

        mock_media = MagicMock(spec=Media)
        result = await build_media_map(context, [mock_media])

        assert result == {}

    @pytest.mark.asyncio
    async def test_build_media_map_with_url(self):
        """Test build_media_map with media having URL"""
        from modules.pipeline.context import PipelineContext

        context = MagicMock(spec=PipelineContext)
        context.need_solve_picture = True

        mock_media = MagicMock(spec=Media)
        mock_media.id = "media_1"
        mock_media.url = "https://ks3-internal.example.com/test.png"
        mock_media.data = None

        with patch('modules.common.async_get_file') as mock_get_file:
            with patch('modules.common._upload_image') as mock_upload:
                mock_image = MagicMock()
                mock_image.size = (100, 200)
                mock_image.save = MagicMock()
                mock_get_file.return_value = mock_image
                mock_upload.return_value = "https://ks3.example.com/uploaded.png"

                result = await build_media_map(context, [mock_media])

                assert "media_1" in result
                assert result["media_1"] == ["https://ks3.example.com/uploaded.png", [100, 200]]
                mock_get_file.assert_called_once_with("https://ks3.example.com/test.png")
                mock_upload.assert_called_once()

    @pytest.mark.asyncio
    async def test_build_media_map_with_data(self):
        """Test build_media_map with media having base64 data"""
        from modules.pipeline.context import PipelineContext

        context = MagicMock(spec=PipelineContext)
        context.need_solve_picture = True

        mock_media = MagicMock(spec=Media)
        mock_media.id = "media_2"
        mock_media.url = None
        mock_media.data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

        with patch('modules.common.Image.open') as mock_image_open:
            with patch('modules.common._upload_image') as mock_upload:
                mock_image = MagicMock()
                mock_image.size = (150, 300)
                mock_image_open.return_value = mock_image
                mock_upload.return_value = "https://ks3.example.com/uploaded2.png"

                result = await build_media_map(context, [mock_media])

                assert "media_2" in result
                assert result["media_2"] == ["https://ks3.example.com/uploaded2.png", [150, 300]]
                mock_upload.assert_called_once_with("media_2", mock_media.data, public=True)

    @pytest.mark.asyncio
    async def test_get_pdf_data_with_url(self):
        """Test _get_pdf_data with URL"""
        mock_url = "https://example.com/test.pdf"
        mock_pdf_content = b"fake pdf content"

        with patch('modules.common.aiohttp.ClientSession') as mock_session_class:
            mock_response = AsyncMock()
            mock_response.read = AsyncMock(return_value=mock_pdf_content)
            mock_response.raise_for_status = MagicMock()

            mock_get_context = AsyncMock()
            mock_get_context.__aenter__ = AsyncMock(return_value=mock_response)
            mock_get_context.__aexit__ = AsyncMock(return_value=None)

            mock_session_instance = AsyncMock()
            mock_session_instance.get = MagicMock(return_value=mock_get_context)

            mock_session_context = AsyncMock()
            mock_session_context.__aenter__ = AsyncMock(return_value=mock_session_instance)
            mock_session_context.__aexit__ = AsyncMock(return_value=None)

            mock_session_class.return_value = mock_session_context

            result = await _get_pdf_data(mock_url)

            assert isinstance(result, BytesIO)
            assert result.getvalue() == mock_pdf_content

    @pytest.mark.asyncio
    async def test_get_pdf_data_with_bytes(self):
        """Test _get_pdf_data with bytes"""
        mock_pdf_content = b"fake pdf content"

        result = await _get_pdf_data(mock_pdf_content)

        assert isinstance(result, BytesIO)
        assert result.getvalue() == mock_pdf_content

    @pytest.mark.asyncio
    async def test_get_pdf_data_invalid_input(self):
        """Test _get_pdf_data with invalid input"""
        with pytest.raises(ValueError, match="无效的文件格式"):
            await _get_pdf_data(123)  # Invalid type

    @pytest.mark.asyncio
    async def test_open_pdf_success(self):
        """Test open_pdf success"""
        mock_pdf_content = b"fake pdf content"

        with patch('modules.common._get_pdf_data') as mock_get_pdf_data:
            with patch('modules.common.fitz.open') as mock_fitz_open:
                mock_get_pdf_data.return_value = BytesIO(mock_pdf_content)
                mock_doc = MagicMock()
                mock_fitz_open.return_value = mock_doc

                result = await open_pdf(mock_pdf_content)

                assert result == mock_doc
                mock_get_pdf_data.assert_called_once_with(mock_pdf_content)
                mock_fitz_open.assert_called_once()

    @pytest.mark.asyncio
    async def test_open_pdf_client_error(self):
        """Test open_pdf with client error"""
        mock_url = "https://example.com/test.pdf"

        with patch('modules.common._get_pdf_data') as mock_get_pdf_data:
            mock_get_pdf_data.side_effect = aiohttp.ClientError("Network error")

            with pytest.raises(aiohttp.ClientError):
                await open_pdf(mock_url)

    def test_sort_textbox_empty(self):
        """Test _sort_textbox with empty list"""
        result = _sort_textbox([])
        assert result == []

    def test_sort_textbox_none_bounding_box(self):
        """Test _sort_textbox with blocks having None bounding_box"""
        mock_block = MagicMock()
        mock_block.bounding_box = None

        result = _sort_textbox([mock_block])
        assert result == []

    def test_sort_textbox_single_block(self):
        """Test _sort_textbox with single block"""
        mock_bbox = MagicMock()
        mock_bbox.y1 = 10
        mock_bbox.y2 = 20
        mock_bbox.x1 = 5

        mock_block = MagicMock()
        mock_block.bounding_box = mock_bbox

        result = _sort_textbox([mock_block])
        assert len(result) == 1
        assert len(result[0]) == 1
        assert result[0][0] == mock_block

    def test_sort_textbox_multiple_blocks_same_row(self):
        """Test _sort_textbox with multiple blocks in same row"""
        # Create blocks with overlapping y coordinates (same row)
        mock_bbox1 = MagicMock()
        mock_bbox1.y1 = 10
        mock_bbox1.y2 = 20
        mock_bbox1.x1 = 5

        mock_bbox2 = MagicMock()
        mock_bbox2.y1 = 15  # Overlaps with first block
        mock_bbox2.y2 = 25
        mock_bbox2.x1 = 25  # To the right of first block

        mock_block1 = MagicMock()
        mock_block1.bounding_box = mock_bbox1

        mock_block2 = MagicMock()
        mock_block2.bounding_box = mock_bbox2

        result = _sort_textbox([mock_block1, mock_block2])
        assert len(result) == 1  # Should be in same row
        assert len(result[0]) == 2
        # Should be sorted by x1 coordinate
        assert result[0][0] == mock_block1  # x1=5
        assert result[0][1] == mock_block2  # x1=25

    def test_sort_textbox_multiple_blocks_different_rows(self):
        """Test _sort_textbox with blocks in different rows"""
        # Create blocks with non-overlapping y coordinates (different rows)
        mock_bbox1 = MagicMock()
        mock_bbox1.y1 = 10
        mock_bbox1.y2 = 20
        mock_bbox1.x1 = 5

        mock_bbox2 = MagicMock()
        mock_bbox2.y1 = 30  # Below first block
        mock_bbox2.y2 = 40
        mock_bbox2.x1 = 15

        mock_block1 = MagicMock()
        mock_block1.bounding_box = mock_bbox1

        mock_block2 = MagicMock()
        mock_block2.bounding_box = mock_bbox2

        result = _sort_textbox([mock_block1, mock_block2])
        assert len(result) == 2  # Should be in different rows
        assert len(result[0]) == 1
        assert len(result[1]) == 1
        assert result[0][0] == mock_block1  # First row
        assert result[1][0] == mock_block2  # Second row

    @pytest.mark.asyncio
    async def test_process_cell_content_empty(self):
        """Test _process_cell_content with empty cell"""
        mock_cell = MagicMock()
        mock_cell.blocks = []

        result = await _process_cell_content(mock_cell, {}, {}, 0)
        assert result == ""

    @pytest.mark.asyncio
    async def test_process_cell_content_no_blocks_attr(self):
        """Test _process_cell_content with cell having no blocks attribute"""
        mock_cell = MagicMock()
        del mock_cell.blocks  # Remove blocks attribute

        result = await _process_cell_content(mock_cell, {}, {}, 0)
        assert result == ""

    @pytest.mark.asyncio
    async def test_process_cell_content_with_textbox(self):
        """Test _process_cell_content with textbox block"""
        mock_textbox = MagicMock()
        mock_textbox.blocks = []

        mock_block = MagicMock()
        mock_block.type = BlockType.textbox
        mock_block.textbox = mock_textbox
        mock_block.bounding_box = MagicMock()
        mock_block.bounding_box.y1 = 10
        mock_block.bounding_box.y2 = 20
        mock_block.bounding_box.x1 = 5

        mock_cell = MagicMock()
        mock_cell.blocks = [mock_block]

        with patch('modules.common._process_textbox', return_value="textbox content"):
            result = await _process_cell_content(mock_cell, {}, {}, 0)
            assert result == "textbox content"

    @pytest.mark.asyncio
    async def test_process_cell_content_with_paragraph(self):
        """Test _process_cell_content with paragraph block"""
        mock_para = MagicMock()

        mock_block = MagicMock()
        mock_block.type = BlockType.para
        mock_block.para = mock_para
        mock_block.bounding_box = MagicMock()
        mock_block.bounding_box.y1 = 10
        mock_block.bounding_box.y2 = 20
        mock_block.bounding_box.x1 = 5

        mock_cell = MagicMock()
        mock_cell.blocks = [mock_block]

        with patch('modules.common._process_paragraph', return_value="paragraph content"):
            result = await _process_cell_content(mock_cell, {}, {}, 0)
            assert result == "paragraph content"

    @pytest.mark.asyncio
    async def test_process_cell_content_with_component(self):
        """Test _process_cell_content with component block"""
        mock_component = MagicMock()

        mock_block = MagicMock()
        mock_block.type = BlockType.component
        mock_block.component = mock_component
        mock_block.bounding_box = MagicMock()
        mock_block.bounding_box.y1 = 10
        mock_block.bounding_box.y2 = 20
        mock_block.bounding_box.x1 = 5

        mock_cell = MagicMock()
        mock_cell.blocks = [mock_block]

        with patch('modules.common._process_component', return_value="component content"):
            result = await _process_cell_content(mock_cell, {}, {}, 0)
            assert result == "component content"

    def test_process_textbox_empty(self):
        """Test _process_textbox with empty textbox"""
        mock_textbox = MagicMock()
        mock_textbox.blocks = []

        result = _process_textbox(mock_textbox, {})
        assert result == ""

    def test_process_textbox_with_paragraphs(self):
        """Test _process_textbox with paragraph blocks"""
        mock_para = MagicMock()

        mock_block = MagicMock()
        mock_block.type = BlockType.para
        mock_block.para = mock_para

        mock_textbox = MagicMock()
        mock_textbox.blocks = [mock_block]

        with patch('modules.common._process_paragraph', return_value="para text"):
            result = _process_textbox(mock_textbox, {})
            assert result == "para text"

    def test_process_paragraph_empty(self):
        """Test _process_paragraph with empty paragraph"""
        mock_para = MagicMock()
        mock_para.prop = None
        mock_para.runs = []

        result = _process_paragraph(mock_para, {})
        assert result == ""

    def test_process_paragraph_with_list_string(self):
        """Test _process_paragraph with list string"""
        mock_prop = MagicMock()
        mock_prop.list_string = "• "

        mock_para = MagicMock()
        mock_para.prop = mock_prop
        mock_para.runs = []

        result = _process_paragraph(mock_para, {})
        assert result == "• "

    def test_process_paragraph_with_runs(self):
        """Test _process_paragraph with text runs"""
        mock_run1 = MagicMock()
        mock_run1.text = "Hello "
        mock_run1.id = "run1"

        mock_run2 = MagicMock()
        mock_run2.text = "World"
        mock_run2.id = "run2"

        mock_para = MagicMock()
        mock_para.prop = None
        mock_para.runs = [mock_run1, mock_run2]

        result = _process_paragraph(mock_para, {})
        assert result == "Hello World"

    def test_process_paragraph_with_id2text_filter(self):
        """Test _process_paragraph with id2text filtering"""
        mock_run1 = MagicMock()
        mock_run1.text = "Visible text"
        mock_run1.id = "run1"

        mock_run2 = MagicMock()
        mock_run2.text = "Hidden text"
        mock_run2.id = "run2"

        mock_para = MagicMock()
        mock_para.prop = None
        mock_para.runs = [mock_run1, mock_run2]

        id2text = {"run2": "Different text"}  # run2 is in id2text but with different text

        result = _process_paragraph(mock_para, id2text)
        assert result == "Visible text"  # Only run1 should be included

    @pytest.mark.asyncio
    async def test_process_component_missing_media_id(self):
        """Test _process_component with missing media_id"""
        mock_component = MagicMock()
        mock_component.media_id = "missing_id"

        result = await _process_component(mock_component, {})
        assert result == ""

    @pytest.mark.asyncio
    async def test_process_component_image_type(self):
        """Test _process_component with image component"""
        mock_component = MagicMock()
        mock_component.media_id = "image_id"
        mock_component.type = ComponentType.image

        id2url = {"image_id": ["https://example.com/image.png"]}

        with patch('modules.common.preprocess_ocrflux', return_value=("OCR result", True)):
            result = await _process_component(mock_component, id2url)
            assert result == "OCR result"

    @pytest.mark.asyncio
    async def test_process_component_non_image_type(self):
        """Test _process_component with non-image component"""
        mock_component = MagicMock()
        mock_component.media_id = "other_id"
        mock_component.type = "other_type"  # Not ComponentType.image

        id2url = {"other_id": ["https://example.com/other.pdf"]}

        result = await _process_component(mock_component, id2url)
        assert result == ""
