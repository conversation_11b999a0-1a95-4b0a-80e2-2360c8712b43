"""
modules.cross_table.merge_table_util 模块的单元测试
"""
import pytest
import pandas as pd
from unittest.mock import patch, MagicMock

from modules.cross_table.merge_table_util import (
    table_html2df, split_sub_table_by_html, generate_new_html_table,
    extract_trs, cross_page_merge_by_html, merge_table_by_force
)


class TestTableHtml2Df:
    """table_html2df 函数的测试用例"""

    def test_table_html2df_valid_table(self):
        """测试 table_html2df 处理有效 HTML 表格的情况"""
        html = """
        <table>
            <tr><td>A</td><td>B</td></tr>
            <tr><td>1</td><td>2</td></tr>
        </table>
        """
        result = table_html2df(html)
        
        assert isinstance(result, pd.DataFrame)
        assert not result.empty
        assert result.shape == (2, 2)

    def test_table_html2df_empty_string(self):
        """测试 table_html2df 处理空字符串的情况"""
        result = table_html2df("")
        assert isinstance(result, pd.DataFrame)
        assert result.empty

    def test_table_html2df_none_input(self):
        """测试 table_html2df 处理 None 输入的情况"""
        result = table_html2df(None)
        assert isinstance(result, pd.DataFrame)
        assert result.empty

    def test_table_html2df_non_string_input(self):
        """测试 table_html2df 处理非字符串输入的情况"""
        result = table_html2df(123)
        assert isinstance(result, pd.DataFrame)
        assert result.empty

    def test_table_html2df_no_table_tag(self):
        """测试 table_html2df 处理没有表格标签的 HTML 的情况"""
        html = "<div>Not a table</div>"
        result = table_html2df(html)
        assert isinstance(result, pd.DataFrame)
        assert result.empty

    def test_table_html2df_whitespace_only(self):
        """测试 table_html2df 处理仅有空白字符的情况"""
        result = table_html2df("   \n\t   ")
        assert isinstance(result, pd.DataFrame)
        assert result.empty

    @patch('modules.cross_table.merge_table_util.pd.read_html')
    def test_table_html2df_exception_handling(self, mock_read_html):
        """测试 table_html2df 异常处理"""
        mock_read_html.side_effect = Exception("Parse error")
        
        html = "<table><tr><td>Test</td></tr></table>"
        result = table_html2df(html)
        
        assert isinstance(result, pd.DataFrame)
        assert result.empty


class TestSplitSubTableByHtml:
    """split_sub_table_by_html 函数的测试用例"""

    def test_split_sub_table_by_html_no_table(self):
        """测试 split_sub_table_by_html 处理没有表格的情况"""
        html = "<div>No table here</div>"
        result = split_sub_table_by_html(html)
        assert result == []

    def test_split_sub_table_by_html_empty_table(self):
        """测试 split_sub_table_by_html 处理空表格的情况"""
        html = "<table></table>"
        result = split_sub_table_by_html(html)
        assert result == []

    def test_split_sub_table_by_html_simple_table(self):
        """测试 split_sub_table_by_html 处理简单表格的情况"""
        html = """
        <table>
            <tr><td colspan="2">Header</td></tr>
            <tr><td>A</td><td>B</td></tr>
        </table>
        """
        result = split_sub_table_by_html(html)
        assert isinstance(result, list)
        assert len(result) > 0

    def test_split_sub_table_by_html_with_rowspan_colspan(self):
        """测试 split_sub_table_by_html 处理带有 rowspan 和 colspan 的情况"""
        html = """
        <table>
            <tr><td colspan="3">Full width header</td></tr>
            <tr><td>A</td><td>B</td><td>C</td></tr>
            <tr><td colspan="3">Another full width</td></tr>
            <tr><td>D</td><td>E</td><td>F</td></tr>
        </table>
        """
        result = split_sub_table_by_html(html)
        assert isinstance(result, list)
        # 应该基于全宽行分割成多个子表格


class TestGenerateNewHtmlTable:
    """generate_new_html_table 函数的测试用例"""

    def test_generate_new_html_table_empty_rows(self):
        """测试 generate_new_html_table 处理空行的情况"""
        result = generate_new_html_table([])
        assert "<table>" in result
        assert "</table>" in result

    def test_generate_new_html_table_with_string_rows(self):
        """测试 generate_new_html_table 处理字符串行的情况"""
        rows = ["<tr><td>A</td><td>B</td></tr>", "<tr><td>C</td><td>D</td></tr>"]
        result = generate_new_html_table(rows)
        
        assert "<table>" in result
        assert "</table>" in result
        assert "<tr>" in result
        assert "</tr>" in result
        assert "A" in result
        assert "B" in result
        assert "C" in result
        assert "D" in result

    def test_generate_new_html_table_with_nested_lists(self):
        """测试 generate_new_html_table 处理嵌套列表的情况"""
        rows = [["<tr><td>A</td></tr>", "<tr><td>B</td></tr>"]]
        result = generate_new_html_table(rows)
        
        assert "<table>" in result
        assert "</table>" in result
        assert "A" in result
        assert "B" in result

    def test_generate_new_html_table_with_invalid_rows(self):
        """测试 generate_new_html_table 处理无效行的情况"""
        rows = ["invalid html", None, 123]
        result = generate_new_html_table(rows)
        
        # 仍应生成表格结构
        assert "<table>" in result
        assert "</table>" in result


class TestExtractTrs:
    """extract_trs 函数的测试用例"""

    def test_extract_trs_valid_table(self):
        """测试 extract_trs 处理有效表格的情况"""
        html = """
        <table>
            <tr><td>A</td><td>B</td></tr>
            <tr><td>C</td><td>D</td></tr>
        </table>
        """
        result = extract_trs(html)
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert all("<tr>" in row and "</tr>" in row for row in result)
        assert any("A" in row for row in result)
        assert any("C" in row for row in result)

    def test_extract_trs_no_table(self):
        """测试 extract_trs 处理没有表格的情况"""
        html = "<div>No table</div>"
        result = extract_trs(html)
        assert result == []

    def test_extract_trs_empty_table(self):
        """测试 extract_trs 处理空表格的情况"""
        html = "<table></table>"
        result = extract_trs(html)
        assert result == []

    def test_extract_trs_with_tbody(self):
        """测试 extract_trs 处理带有 tbody 的情况"""
        html = """
        <table>
            <tbody>
                <tr><td>A</td></tr>
                <tr><td>B</td></tr>
            </tbody>
        </table>
        """
        result = extract_trs(html)
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert all("<tr>" in row and "</tr>" in row for row in result)


class TestCrossPageMergeByHtml:
    """cross_page_merge_by_html 函数的测试用例"""

    def test_cross_page_merge_by_html_simple_tables(self):
        """测试 cross_page_merge_by_html 处理简单表格的情况"""
        pre_table = """
        <table>
            <tr><td>A</td><td>B</td></tr>
            <tr><td>1</td><td>2</td></tr>
        </table>
        """
        next_table = """
        <table>
            <tr><td>C</td><td>D</td></tr>
            <tr><td>3</td><td>4</td></tr>
        </table>
        """
        
        merge_flag, final_table = cross_page_merge_by_html(pre_table, next_table)
        
        assert isinstance(merge_flag, bool)
        assert isinstance(final_table, list)
        assert len(final_table) >= 2  # 至少有原始的两个表格

    def test_cross_page_merge_by_html_empty_tables(self):
        """测试 cross_page_merge_by_html 处理空表格的情况"""
        pre_table = "<table></table>"
        next_table = "<table></table>"
        
        merge_flag, final_table = cross_page_merge_by_html(pre_table, next_table)
        
        assert isinstance(merge_flag, bool)
        assert isinstance(final_table, list)
        assert final_table == [pre_table, next_table]

    def test_cross_page_merge_by_html_no_merge_conditions(self):
        """测试 cross_page_merge_by_html 不满足合并条件时的情况"""
        pre_table = """
        <table>
            <tr><td>A</td><td>B</td><td>C</td></tr>
            <tr><td>1</td><td>2</td><td>3</td></tr>
        </table>
        """
        next_table = """
        <table>
            <tr><td>X</td><td>Y</td></tr>
            <tr><td>7</td><td>8</td></tr>
        </table>
        """
        
        merge_flag, final_table = cross_page_merge_by_html(pre_table, next_table)
        
        # 由于列数不同，不应该合并
        assert merge_flag is False
        assert final_table == [pre_table, next_table]


class TestMergeTableByForce:
    """merge_table_by_force 函数的测试用例"""

    def test_merge_table_by_force_multiple_tables(self):
        """测试 merge_table_by_force 处理多个表格的情况"""
        table_list = [
            "<table><tr><td>A</td><td>B</td></tr></table>",
            "<table><tr><td>C</td><td>D</td></tr></table>",
            "<table><tr><td>E</td><td>F</td></tr></table>"
        ]
        
        result = merge_table_by_force(table_list)
        
        assert "<table>" in result
        assert "</table>" in result
        assert "A" in result
        assert "C" in result
        assert "E" in result

    def test_merge_table_by_force_empty_list(self):
        """测试 merge_table_by_force 处理空列表的情况"""
        result = merge_table_by_force([])
        
        assert "<table>" in result
        assert "</table>" in result

    def test_merge_table_by_force_single_table(self):
        """测试 merge_table_by_force 处理单个表格的情况"""
        table_list = ["<table><tr><td>Single</td></tr></table>"]
        
        result = merge_table_by_force(table_list)
        
        assert "<table>" in result
        assert "</table>" in result
        assert "Single" in result

    def test_merge_table_by_force_invalid_tables(self):
        """测试 merge_table_by_force 处理无效表格的情况"""
        table_list = ["<div>Not a table</div>", ""]

        result = merge_table_by_force(table_list)

        # 仍应生成表格结构
        assert "<table>" in result
        assert "</table>" in result

    def test_merge_table_by_force_mixed_valid_invalid(self):
        """测试 merge_table_by_force 处理有效和无效表格混合的情况"""
        table_list = [
            "<table><tr><td>Valid</td></tr></table>",
            "<div>Invalid</div>",
            "<table><tr><td>Also Valid</td></tr></table>"
        ]
        
        result = merge_table_by_force(table_list)
        
        assert "<table>" in result
        assert "</table>" in result
        assert "Valid" in result
        assert "Also Valid" in result
        # 无效内容不应出现
        assert "Invalid" not in result
