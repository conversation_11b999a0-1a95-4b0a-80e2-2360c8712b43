"""
modules.cross_table.cross_page_merge 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock

from modules.cross_table.cross_page_merge import (
    classify_and_mark_table_indices, process_and_merge_chunks,
    merge_chunks_with_base, cross_table_merge
)
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DSTType, DST, DSTAttribute, BBox, PositionInfo


class TestClassifyAndMarkTableIndices:
    """classify_and_mark_table_indices 函数的测试用例"""

    def create_test_chunk(self, chunk_id, content, page_num, label_type=LabelType.TABLE):
        """创建具有必需字段的测试块的辅助方法"""
        return Chunk(
            chunk_id=chunk_id,
            page_size=100,
            content=content,
            label=label_type,
            page_num=page_num,
            block=[f"block_{chunk_id}"]
        )

    def test_classify_empty_chunks(self):
        """测试 classify_and_mark_table_indices 处理空块列表的情况"""
        result = classify_and_mark_table_indices([])
        
        assert result == {}

    def test_classify_single_chunk_single_page(self):
        """测试 classify_and_mark_table_indices 处理单页面单块的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])

        result = classify_and_mark_table_indices([chunk])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]
        assert "first_chunk_id" in result[1]
        assert "from_last_table_index" in result[1]
        assert "last_chunk_id" in result[1]

    def test_classify_multiple_chunks_same_page(self):
        """测试 classify_and_mark_table_indices 处理同一页面多个块的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Table 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Table 2</td></tr></table>", [1])

        result = classify_and_mark_table_indices([chunk1, chunk2])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]
        assert "first_chunk_id" in result[1]
        assert "from_last_table_index" in result[1]
        assert "last_chunk_id" in result[1]

    def test_classify_chunks_different_pages(self):
        """测试 classify_and_mark_table_indices 处理不同页面块的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1 Table</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2 Table</td></tr></table>", [2])

        result = classify_and_mark_table_indices([chunk1, chunk2])

        assert 1 in result
        assert 2 in result
        assert isinstance(result[1], dict)
        assert isinstance(result[2], dict)
        assert "first_table_index" in result[1]
        assert "first_table_index" in result[2]

    def test_classify_chunks_multiple_pages_per_chunk(self):
        """测试 classify_and_mark_table_indices 处理跨多页面块的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Multi-page Table</td></tr></table>", [1, 2, 3])

        result = classify_and_mark_table_indices([chunk])

        # 应该按第一个页码进行分类
        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]

    def test_classify_mixed_label_types(self):
        """测试 classify_and_mark_table_indices 处理混合标签类型的情况"""
        table_chunk = self.create_test_chunk("table_chunk", "<table><tr><td>Table</td></tr></table>", [1], LabelType.TABLE)
        text_chunk = self.create_test_chunk("text_chunk", "Regular text content", [1], LabelType.TEXT)

        result = classify_and_mark_table_indices([table_chunk, text_chunk])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]

    def test_classify_chunks_zero_page(self):
        """测试 classify_and_mark_table_indices 处理零页码的情况"""
        chunk = self.create_test_chunk("chunk_0", "<table><tr><td>Zero Page</td></tr></table>", [0])

        result = classify_and_mark_table_indices([chunk])

        assert 0 in result
        assert isinstance(result[0], dict)
        assert "first_table_index" in result[0]

    def test_classify_chunks_negative_page(self):
        """测试 classify_and_mark_table_indices 处理负页码的情况"""
        chunk = self.create_test_chunk("chunk_neg", "<table><tr><td>Negative Page</td></tr></table>", [-1])

        result = classify_and_mark_table_indices([chunk])

        assert -1 in result
        assert isinstance(result[-1], dict)
        assert "first_table_index" in result[-1]

    def test_classify_chunks_with_dst_data(self):
        """测试 classify_and_mark_table_indices 处理包含 DST 数据的块的情况"""
        # 创建包含所有必需字段的 DST 对象
        bbox = BBox(x1=10, y1=20, x2=100, y2=200)
        pos = PositionInfo(bbox=bbox)
        attr = DSTAttribute(level=0, position=pos, page=0, hash="a" * 32)

        dst1 = DST(
            id="dst1", parent="-1", order=0, dst_type=DSTType.TABLE,
            attributes=attr, content=["<table><tr><td>Table 1</td></tr></table>"], mark=None
        )
        dst2 = DST(
            id="dst2", parent="-1", order=1, dst_type=DSTType.TEXT,
            attributes=attr, content=["Some text"], mark="header"  # 已标记的 DST
        )
        dst3 = DST(
            id="dst3", parent="-1", order=2, dst_type=DSTType.TABLE,
            attributes=attr, content=["<table><tr><td>Table 2</td></tr></table>"], mark=None
        )

        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])
        chunk.dsts = [dst1, dst2, dst3]

        result = classify_and_mark_table_indices([chunk])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert result[1]["first_table_index"] == 0  # 第一个表格 DST
        assert result[1]["last_chunk_id"] == "chunk_1"
        assert result[1]["from_last_table_index"] == 1  # 应该考虑已标记的 DST


class TestProcessAndMergeChunks:
    """process_and_merge_chunks 函数的测试用例"""

    def create_test_chunk(self, chunk_id, content, page_num, label_type=LabelType.TABLE):
        """创建具有必需字段的测试块的辅助方法"""
        return Chunk(
            chunk_id=chunk_id,
            page_size=100,
            content=content,
            label=label_type,
            page_num=page_num,
            block=[f"block_{chunk_id}"]
        )

    def test_process_and_merge_empty_chunks(self):
        """测试 process_and_merge_chunks 处理空块和空页面块的情况"""
        result = process_and_merge_chunks([], {})

        assert result == []

    def test_process_and_merge_empty_page_chunks(self):
        """测试 process_and_merge_chunks 处理有块但页面块为空的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])

        result = process_and_merge_chunks([chunk], {})

        assert result == []

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_single_page(self, mock_merge):
        """测试 process_and_merge_chunks 处理单页面的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])
        table_indices = {1: {
            "first_table_index": 0,
            "first_chunk_id": "chunk_1",
            "from_last_table_index": 0,
            "last_chunk_id": "chunk_1",
        }}

        mock_merge.return_value = (True, ["<table><tr><td>Merged</td></tr></table>"])

        result = process_and_merge_chunks([chunk], table_indices)

        assert len(result) == 0  # 单页面不会有合并
        # mock_merge.assert_not_called()  # 单页面不会触发合并

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_multiple_pages(self, mock_merge):
        """测试 process_and_merge_chunks 处理多页面的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2</td></tr></table>", [2])
        table_indices = {
            1: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_1",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_1",
            },
            2: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_2",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_2",
            }
        }

        mock_merge.return_value = (True, [
            "<table><tr><td>Merged 1</td></tr></table>",
            "<table><tr><td>Merged 2</td></tr></table>"
        ])

        result = process_and_merge_chunks([chunk1, chunk2], table_indices)

        assert len(result) == 1  # 两页面之间的一个合并结果
        assert "chunk_ids" in result[0]
        assert "content" in result[0]
        assert result[0]["chunk_ids"] == ["chunk_1", "chunk_2"]
        mock_merge.assert_called_once()

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_no_merge_condition(self, mock_merge):
        """测试 process_and_merge_chunks 不满足合并条件时的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2</td></tr></table>", [2])
        table_indices = {
            1: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_1",
                "from_last_table_index": 10,  # 大值以防止合并
                "last_chunk_id": "chunk_1",
            },
            2: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_2",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_2",
            }
        }

        result = process_and_merge_chunks([chunk1, chunk2], table_indices)

        assert len(result) == 0  # 不应该发生合并
        mock_merge.assert_not_called()

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_consecutive_merges(self, mock_merge):
        """测试 process_and_merge_chunks 处理连续合并的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2</td></tr></table>", [2])
        chunk3 = self.create_test_chunk("chunk_3", "<table><tr><td>Page 3</td></tr></table>", [3])
        table_indices = {
            1: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_1",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_1",
            },
            2: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_2",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_2",
            },
            3: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_3",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_3",
            }
        }

        # 第一次调用返回 True（合并），第二次调用返回 True（连续合并）
        mock_merge.side_effect = [(True, ["merged_content"]), (True, ["merged_content_2"])]

        result = process_and_merge_chunks([chunk1, chunk2, chunk3], table_indices)

        assert len(result) == 1  # 一个合并结果
        assert len(result[0]["chunk_ids"]) == 3  # 所有三个块都被合并
        assert mock_merge.call_count == 2


class TestMergeChunksWithBase:
    """merge_chunks_with_base 函数的测试用例"""

    def test_merge_chunks_with_base_empty(self):
        """测试 merge_chunks_with_base 处理空输入的情况"""
        result = merge_chunks_with_base([], [])
        
        assert result == []

    def test_merge_chunks_with_base_no_merged_results(self):
        """测试 merge_chunks_with_base 没有合并结果的情况"""
        chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Original</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        
        result = merge_chunks_with_base([chunk], [])
        
        assert len(result) == 1
        assert result[0] == chunk

    def test_merge_chunks_with_base_with_merged_results(self):
        """测试 merge_chunks_with_base 有合并结果的情况"""
        chunk1 = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Original 1</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        chunk2 = Chunk(
            chunk_id="chunk_2",
            page_size=100,
            content="<table><tr><td>Original 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        
        merged_results = [{
            "content": ["<table><tr><td>Original 1</td></tr></table>", "<table><tr><td>Original 2</td></tr></table>"],
            "chunk_ids": ["chunk_1", "chunk_2"]
        }]

        result = merge_chunks_with_base([chunk1, chunk2], merged_results)

        assert len(result) == 1  # chunk2 被合并到 chunk1，所以只剩下 chunk1
        assert result[0].chunk_id == "chunk_1"
        # 检查内容是否已合并
        assert "<table>" in result[0].content

    def test_merge_chunks_with_base_multiple_merged_results(self):
        """测试 merge_chunks_with_base 处理多个合并结果的情况"""
        chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Original</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        
        merged_results = []  # 此测试的空合并结果

        result = merge_chunks_with_base([chunk], merged_results)

        assert len(result) == 1  # 只剩下原始块
        assert result[0] == chunk


class TestCrossTableMerge:
    """cross_table_merge 函数的测试用例"""

    def test_cross_table_merge_empty(self):
        """测试 cross_table_merge 处理空块的情况"""
        result = cross_table_merge([])
        
        assert result == []

    @patch('modules.cross_table.cross_page_merge.process_and_merge_chunks')
    @patch('modules.cross_table.cross_page_merge.merge_chunks_with_base')
    @patch('modules.cross_table.cross_page_merge.classify_and_mark_table_indices')
    def test_cross_table_merge_integration(self, mock_classify, mock_merge_base, mock_process):
        """测试 cross_table_merge 集成功能"""
        chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Test</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        
        mock_classify.return_value = {1: [chunk]}
        mock_process.return_value = ["<table><tr><td>Merged</td></tr></table>"]
        mock_merge_base.return_value = [chunk]
        
        result = cross_table_merge([chunk])
        
        assert result == [chunk]
        mock_classify.assert_called_once_with([chunk])
        mock_process.assert_called_once_with([chunk], {1: [chunk]})
        mock_merge_base.assert_called_once_with([chunk], ["<table><tr><td>Merged</td></tr></table>"])

    @patch('modules.cross_table.cross_page_merge.process_and_merge_chunks')
    @patch('modules.cross_table.cross_page_merge.merge_chunks_with_base')
    @patch('modules.cross_table.cross_page_merge.classify_and_mark_table_indices')
    def test_cross_table_merge_multiple_chunks(self, mock_classify, mock_merge_base, mock_process):
        """测试 cross_table_merge 处理多个块的情况"""
        chunk1 = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Table 1</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        chunk2 = Chunk(
            chunk_id="chunk_2",
            page_size=100,
            content="<table><tr><td>Table 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        chunks = [chunk1, chunk2]
        
        mock_classify.return_value = {1: [chunk1], 2: [chunk2]}
        mock_process.return_value = [
            "<table><tr><td>Merged 1</td></tr></table>",
            "<table><tr><td>Merged 2</td></tr></table>"
        ]
        mock_merge_base.return_value = chunks
        
        result = cross_table_merge(chunks)
        
        assert result == chunks
        mock_classify.assert_called_once_with(chunks)
        mock_process.assert_called_once_with(chunks, {1: [chunk1], 2: [chunk2]})
        mock_merge_base.assert_called_once()

    def test_cross_table_merge_real_scenario(self):
        """测试 cross_table_merge 更真实的场景"""
        # 创建可能代表跨页面分割表格的块
        chunk1 = Chunk(
            chunk_id="table_part_1",
            page_size=100,
            content="<table><tr><td>Header 1</td><td>Header 2</td></tr><tr><td>Row 1 Col 1</td><td>Row 1 Col 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        chunk2 = Chunk(
            chunk_id="table_part_2",
            page_size=100,
            content="<table><tr><td>Row 2 Col 1</td><td>Row 2 Col 2</td></tr><tr><td>Row 3 Col 1</td><td>Row 3 Col 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        
        chunks = [chunk1, chunk2]
        
        # 这应该与实际实现一起工作
        result = cross_table_merge(chunks)
        
        # 结果应该包含原始块加上任何合并结果
        assert isinstance(result, list)
        assert len(result) >= len(chunks)  # 应该至少有原始块
