"""
modules.pipeline.base 模块的测试（处理器和管道）
"""
import pytest
import asyncio
import sys
import os
import time
from unittest.mock import Mock, patch, AsyncMock
from typing import Any
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 在导入之前模拟环境变量和模块
os.environ.setdefault('SERVICE_ID', 'test_service_id')
os.environ.setdefault('SERVICE_SECRET', 'test_service_secret')
os.environ.setdefault('SERVICE_KEY', 'test_service_key')
os.environ.setdefault('LOG_LEVEL', 'INFO')
os.environ.setdefault('aidocs_dst_ak', 'test_ak')
os.environ.setdefault('aidocs_dst_sk', 'test_sk')
os.environ.setdefault('conf_env', 'dev')

# 模拟复杂依赖
with patch.dict('sys.modules', {
    'conf': <PERSON><PERSON>(),
    'conf.setting_dev': <PERSON><PERSON>(),
    'conf.setting': Mock(),
    'commons.auth.auth_route': Mock(),
    'commons.auth.dmc_checkauth': Mock(),
    'commons.auth.auth_rpc': Mock(),
    'commons.tools.cams_decrypt': Mock(),
    'commons.logger.business_log': Mock(),
    'commons.monitor.prom': Mock(),
    'prometheus_client': Mock()
}):
    from modules.pipeline.base import (
        PipelineHandler, ParallelGroupHandler, Pipeline, TaskStatus
    )
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType


class MockHandler(PipelineHandler):
    """用于测试的模拟处理器"""
    
    def __init__(self, name: str, should_fail: bool = False, delay: float = 0):
        super().__init__(name)
        self.should_fail = should_fail
        self.delay = delay
        self.process_called = False
    
    async def process(self, context: PipelineContext) -> PipelineContext:
        """模拟处理实现"""
        self.process_called = True
        if self.delay > 0:
            await asyncio.sleep(self.delay)
        
        if self.should_fail:
            raise ValueError(f"Handler {self.name} failed")
        
        # Add some test data to context
        context.handler_results[self.name] = f"processed_by_{self.name}"
        return context


class TestTaskStatus:
    """TaskStatus 枚举的测试"""

    def test_task_status_values(self):
        """测试 TaskStatus 枚举值"""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.RUNNING.value == "running"
        assert TaskStatus.SUCCESS.value == "success"
        assert TaskStatus.FAILED.value == "failed"
        assert TaskStatus.SKIPPED.value == "skipped"


class TestPipelineHandler:
    """PipelineHandler 基类的测试"""

    def test_handler_initialization(self):
        """测试处理器初始化"""
        handler = MockHandler("test_handler")
        assert handler.name == "test_handler"
        assert len(handler.parallel_handlers) == 0
        assert len(handler.parallel_pipelines) == 0
        assert handler.next_handler is None
        assert handler.status == TaskStatus.PENDING
        assert handler.error is None
    
    def test_add_parallel_handler(self):
        """测试添加并行处理器"""
        handler1 = MockHandler("handler1")
        handler2 = MockHandler("handler2")
        handler3 = MockHandler("handler3")
        
        result = handler1.add_parallel(handler2)
        assert result == handler2
        assert handler2 in handler1.parallel_handlers
        
        handler1.add_parallel(handler3)
        assert len(handler1.parallel_handlers) == 2
        assert handler2 in handler1.parallel_handlers
        assert handler3 in handler1.parallel_handlers
    
    def test_add_parallel_pipeline(self):
        """测试添加并行管道"""
        handler = MockHandler("handler")
        pipeline = Pipeline("test_pipeline")
        
        result = handler.add_parallel_pipeline(pipeline)
        assert result == handler
        assert pipeline in handler.parallel_pipelines
    
    def test_set_next_handler(self):
        """测试设置下一个处理器"""
        handler1 = MockHandler("handler1")
        handler2 = MockHandler("handler2")
        
        result = handler1.set_next(handler2)
        assert result == handler2
        assert handler1.next_handler == handler2
    
    @pytest.mark.asyncio
    async def test_execute_success(self):
        """测试成功的处理器执行"""
        handler = MockHandler("test_handler")
        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)

        result = await handler.execute(context)
        
        assert handler.status == TaskStatus.SUCCESS
        assert handler.error is None
        assert handler.process_called is True
        assert result.handler_results["test_handler"] == "processed_by_test_handler"
    
    @pytest.mark.asyncio
    async def test_execute_failure(self):
        """测试处理器执行失败"""
        handler = MockHandler("test_handler", should_fail=True)
        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)

        with pytest.raises(ValueError, match="Handler test_handler failed"):
            await handler.execute(context)
        
        assert handler.status == TaskStatus.FAILED
        assert isinstance(handler.error, ValueError)
        assert handler.process_called is True
    
    @pytest.mark.asyncio
    async def test_execute_with_next_handler(self):
        """测试与下一个处理器的执行"""
        handler1 = MockHandler("handler1")
        handler2 = MockHandler("handler2")
        handler1.set_next(handler2)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)
        result = await handler1.execute(context)
        
        assert handler1.status == TaskStatus.SUCCESS
        assert handler2.status == TaskStatus.SUCCESS
        assert handler1.process_called is True
        assert handler2.process_called is True
        assert result.handler_results["handler1"] == "processed_by_handler1"
        assert result.handler_results["handler2"] == "processed_by_handler2"
    
    @pytest.mark.asyncio
    async def test_execute_with_parallel_handlers(self):
        """测试与并行处理器的执行"""
        main_handler = MockHandler("main")
        parallel1 = MockHandler("parallel1")
        parallel2 = MockHandler("parallel2")
        
        main_handler.add_parallel(parallel1)
        main_handler.add_parallel(parallel2)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)
        result = await main_handler.execute(context)
        
        assert main_handler.status == TaskStatus.SUCCESS
        assert parallel1.status == TaskStatus.SUCCESS
        assert parallel2.status == TaskStatus.SUCCESS
        assert main_handler.process_called is True
        assert parallel1.process_called is True
        assert parallel2.process_called is True
    
    @pytest.mark.asyncio
    async def test_execute_with_parallel_pipeline(self):
        """测试与并行管道的执行"""
        main_handler = MockHandler("main")
        
        # Create a parallel pipeline
        parallel_pipeline = Pipeline("parallel_pipeline")
        parallel_handler = MockHandler("parallel_handler")
        parallel_pipeline.add_handler(parallel_handler)
        
        main_handler.add_parallel_pipeline(parallel_pipeline)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)
        result = await main_handler.execute(context)
        
        assert main_handler.status == TaskStatus.SUCCESS
        assert parallel_handler.status == TaskStatus.SUCCESS
        assert main_handler.process_called is True
        assert parallel_handler.process_called is True
    
    @pytest.mark.asyncio
    async def test_execute_parallel_handler_failure(self):
        """测试并行处理器失败时的执行"""
        main_handler = MockHandler("main")
        parallel_handler = MockHandler("parallel", should_fail=True)
        
        main_handler.add_parallel(parallel_handler)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)

        with pytest.raises(ValueError, match="Handler parallel failed"):
            await main_handler.execute(context)
        
        assert main_handler.status == TaskStatus.FAILED
        assert isinstance(main_handler.error, ValueError)
    



class TestParallelGroupHandler:
    """ParallelGroupHandler 类的测试"""
    
    @pytest.mark.asyncio
    async def test_parallel_group_handler_process(self):
        """测试 ParallelGroupHandler 处理方法"""
        handler = ParallelGroupHandler("parallel_group")
        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)

        result = await handler.process(context)
        
        assert result == context
        assert handler.status == TaskStatus.PENDING  # Status changes in execute, not process


class TestPipeline:
    """Pipeline 类的测试"""

    def test_pipeline_initialization(self):
        """测试管道初始化"""
        pipeline = Pipeline("test_pipeline")
        assert pipeline.name == "test_pipeline"
        assert pipeline.head is None
        assert pipeline.tail is None
        assert len(pipeline.handlers) == 0
    
    def test_add_single_handler(self):
        """测试向管道添加单个处理器"""
        pipeline = Pipeline("test_pipeline")
        handler = MockHandler("handler1")
        
        result = pipeline.add_handler(handler)
        
        assert result == pipeline
        assert pipeline.head == handler
        assert pipeline.tail == handler
        assert len(pipeline.handlers) == 1
        assert handler in pipeline.handlers
    
    def test_add_multiple_handlers(self):
        """测试向管道添加多个处理器"""
        pipeline = Pipeline("test_pipeline")
        handler1 = MockHandler("handler1")
        handler2 = MockHandler("handler2")
        handler3 = MockHandler("handler3")
        
        pipeline.add_handler(handler1).add_handler(handler2).add_handler(handler3)
        
        assert pipeline.head == handler1
        assert pipeline.tail == handler3
        assert len(pipeline.handlers) == 3
        assert handler1.next_handler == handler2
        assert handler2.next_handler == handler3
        assert handler3.next_handler is None
    
    @pytest.mark.asyncio
    async def test_execute_empty_pipeline(self):
        """测试执行空管道"""
        pipeline = Pipeline("empty_pipeline")
        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)

        with pytest.raises(ValueError, match="Pipeline is empty"):
            await pipeline.execute(context)
    
    @pytest.mark.asyncio
    async def test_execute_single_handler_pipeline(self):
        """测试执行带有单个处理器的管道"""
        pipeline = Pipeline("test_pipeline")
        handler = MockHandler("handler1")
        pipeline.add_handler(handler)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)
        result = await pipeline.execute(context)
        
        assert handler.process_called is True
        assert handler.status == TaskStatus.SUCCESS
        assert result.handler_results["handler1"] == "processed_by_handler1"
    
    @pytest.mark.asyncio
    async def test_execute_multiple_handlers_pipeline(self):
        """测试执行带有多个处理器的管道"""
        pipeline = Pipeline("test_pipeline")
        handler1 = MockHandler("handler1")
        handler2 = MockHandler("handler2")
        handler3 = MockHandler("handler3")
        
        pipeline.add_handler(handler1).add_handler(handler2).add_handler(handler3)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)
        result = await pipeline.execute(context)
        
        assert handler1.process_called is True
        assert handler2.process_called is True
        assert handler3.process_called is True
        assert all(h.status == TaskStatus.SUCCESS for h in [handler1, handler2, handler3])
        assert "handler1" in result.handler_results
        assert "handler2" in result.handler_results
        assert "handler3" in result.handler_results
    
    def test_get_handler_status(self):
        """测试获取处理器状态"""
        pipeline = Pipeline("test_pipeline")
        handler1 = MockHandler("handler1")
        handler2 = MockHandler("handler2")
        
        pipeline.add_handler(handler1).add_handler(handler2)
        
        status_dict = pipeline.get_handler_status()
        
        assert status_dict["handler1"] == TaskStatus.PENDING
        assert status_dict["handler2"] == TaskStatus.PENDING
    
    def test_visualize_empty_pipeline(self):
        """测试可视化空管道"""
        pipeline = Pipeline("empty_pipeline")
        result = pipeline.visualize()
        
        expected = "digraph G {\n}"
        assert result == expected
    
    def test_visualize_pipeline_with_handlers(self):
        """测试可视化带有处理器的管道"""
        pipeline = Pipeline("test_pipeline")
        handler1 = MockHandler("handler1")
        handler2 = MockHandler("handler2")
        handler3 = MockHandler("handler3")
        
        # Add parallel handler to test visualization
        parallel_handler = MockHandler("parallel")
        handler1.add_parallel(parallel_handler)
        
        pipeline.add_handler(handler1).add_handler(handler2).add_handler(handler3)
        
        result = pipeline.visualize()
        
        assert "digraph G {" in result
        assert "}" in result
        assert '"handler1" -> "parallel"' in result
        assert '"handler1" -> "handler2"' in result
        assert '"handler2" -> "handler3"' in result

    @pytest.mark.asyncio
    async def test_execute_with_timeout_simulation(self):
        """测试模拟延迟的执行"""
        handler = MockHandler("slow_handler", delay=0.1)
        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)

        start_time = time.time()
        result = await handler.execute(context)
        end_time = time.time()

        assert end_time - start_time >= 0.1
        assert handler.status == TaskStatus.SUCCESS
        assert result.handler_results["slow_handler"] == "processed_by_slow_handler"

    @pytest.mark.asyncio
    async def test_execute_parallel_pipeline_failure(self):
        """测试并行管道失败时的执行"""
        main_handler = MockHandler("main")

        # Create a failing parallel pipeline
        failing_pipeline = Pipeline("failing_pipeline")
        failing_handler = MockHandler("failing_handler", should_fail=True)
        failing_pipeline.add_handler(failing_handler)

        main_handler.add_parallel_pipeline(failing_pipeline)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)

        with pytest.raises(ValueError, match="Handler failing_handler failed"):
            await main_handler.execute(context)

        assert main_handler.status == TaskStatus.FAILED

    @pytest.mark.asyncio
    async def test_execute_mixed_parallel_operations(self):
        """测试同时使用并行处理器和管道的执行"""
        main_handler = MockHandler("main")

        # Add parallel handler
        parallel_handler = MockHandler("parallel_handler")
        main_handler.add_parallel(parallel_handler)

        # Add parallel pipeline
        parallel_pipeline = Pipeline("parallel_pipeline")
        pipeline_handler = MockHandler("pipeline_handler")
        parallel_pipeline.add_handler(pipeline_handler)
        main_handler.add_parallel_pipeline(parallel_pipeline)

        file_info = FileInfo(file_type=FileType.PDF)
        context = PipelineContext(file_info=file_info)
        result = await main_handler.execute(context)

        assert main_handler.status == TaskStatus.SUCCESS
        assert parallel_handler.status == TaskStatus.SUCCESS
        assert pipeline_handler.status == TaskStatus.SUCCESS
        assert main_handler.process_called is True
        assert parallel_handler.process_called is True
        assert pipeline_handler.process_called is True
