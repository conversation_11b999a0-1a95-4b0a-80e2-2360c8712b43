"""
modules.layout.common 模块的单元测试
"""
import pytest

from modules.layout.common import contains_chinese


class TestContainsChinese:
    """contains_chinese函数的测试用例"""

    def test_contains_chinese_with_chinese_text(self):
        """测试contains_chinese处理包含中文字符的文本"""
        assert contains_chinese("你好世界") is True
        assert contains_chinese("Hello 世界") is True
        assert contains_chinese("测试") is True
        assert contains_chinese("中文字符") is True

    def test_contains_chinese_without_chinese_text(self):
        """测试contains_chinese处理不包含中文字符的文本"""
        assert contains_chinese("Hello World") is False
        assert contains_chinese("123456") is False
        assert contains_chinese("!@#$%^&*()") is False
        assert contains_chinese("English text only") is False

    def test_contains_chinese_with_empty_string(self):
        """测试contains_chinese处理空字符串"""
        assert contains_chinese("") is False

    def test_contains_chinese_with_mixed_content(self):
        """测试contains_chinese处理混合内容"""
        assert contains_chinese("Hello 你好 World") is True
        assert contains_chinese("123 中文 456") is True
        assert contains_chinese("English + 中文 + Numbers 123") is True

    def test_contains_chinese_with_japanese_characters(self):
        """测试contains_chinese处理日文字符"""
        # 日文平假名和片假名在中文 Unicode 范围 [\u4e00-\u9fff] 之外
        assert contains_chinese("こんにちは") is False  # 平假名
        assert contains_chinese("カタカナ") is False    # 片假名

    def test_contains_chinese_with_korean_characters(self):
        """测试contains_chinese处理韩文字符"""
        # 韩文字符在中文 Unicode 范围之外
        assert contains_chinese("안녕하세요") is False

    def test_contains_chinese_with_special_unicode(self):
        """测试contains_chinese处理各种Unicode字符"""
        assert contains_chinese("αβγδε") is False  # 希腊文
        assert contains_chinese("абвгд") is False  # 西里尔文
        assert contains_chinese("العربية") is False  # 阿拉伯文

    def test_contains_chinese_with_whitespace_and_punctuation(self):
        """测试contains_chinese处理空白字符和标点符号"""
        assert contains_chinese("   ") is False
        assert contains_chinese(".,;:!?") is False
        assert contains_chinese("   中文   ") is True
        # 中文标点符号在 [\u4e00-\u9fff] 范围之外
        assert contains_chinese("，。；：！？") is False  # 中文标点符号

    def test_contains_chinese_with_numbers_and_chinese(self):
        """测试contains_chinese处理数字和中文字符"""
        assert contains_chinese("2024年") is True
        assert contains_chinese("第1章") is True
        assert contains_chinese("100个") is True

    def test_contains_chinese_edge_cases(self):
        """测试contains_chinese处理边界情况"""
        # 测试中文 Unicode 范围边界的字符
        assert contains_chinese("\u4e00") is True   # 范围内第一个字符
        assert contains_chinese("\u9fff") is True   # 范围内最后一个字符
        assert contains_chinese("\u4dff") is False  # 范围之前
        assert contains_chinese("\ua000") is False  # 范围之后

    def test_contains_chinese_with_traditional_chinese(self):
        """测试contains_chinese处理繁体中文字符"""
        assert contains_chinese("繁體中文") is True
        assert contains_chinese("臺灣") is True
        assert contains_chinese("學習") is True

    def test_contains_chinese_with_simplified_chinese(self):
        """测试contains_chinese处理简体中文字符"""
        assert contains_chinese("简体中文") is True
        assert contains_chinese("台湾") is True
        assert contains_chinese("学习") is True

    def test_contains_chinese_performance_with_long_strings(self):
        """测试contains_chinese处理长字符串的性能"""
        long_english = "a" * 10000
        long_chinese = "中" * 10000
        mixed_long = "a" * 5000 + "中" + "b" * 4999
        
        assert contains_chinese(long_english) is False
        assert contains_chinese(long_chinese) is True
        assert contains_chinese(mixed_long) is True

    def test_contains_chinese_with_newlines_and_tabs(self):
        """测试contains_chinese处理换行符和制表符"""
        assert contains_chinese("Hello\nWorld") is False
        assert contains_chinese("Hello\tWorld") is False
        assert contains_chinese("你好\n世界") is True
        assert contains_chinese("中文\t测试") is True

    def test_contains_chinese_case_sensitivity(self):
        """测试contains_chinese不受大小写影响（中文没有大小写）"""
        assert contains_chinese("HELLO WORLD") is False
        assert contains_chinese("hello world") is False
        assert contains_chinese("Hello 中文 WORLD") is True
        assert contains_chinese("HELLO 中文 world") is True
