"""
modules.layout.typesetting 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock, patch

from modules.layout.typesetting import (
    determine_layout_with_middle_line, typesetting_correct,
    typesetting_left_right, typesetting_top_bottom, reorder_dst_by_page
)
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo


class TestConstants:
    """排版常量的测试用例"""

    def test_typesetting_constants(self):
        """测试排版常量值"""
        assert typesetting_left_right == "left-right"
        assert typesetting_top_bottom == "top-bottom"


class TestDetermineLayoutWithMiddleLine:
    """determine_layout_with_middle_line 函数的测试用例"""

    def create_mock_dst(self, x1, y1, x2, y2):
        """创建带有边界框的模拟DST的辅助方法"""
        mock_dst = MagicMock(spec=DST)
        mock_dst.attributes = MagicMock(spec=DSTAttribute)
        mock_dst.attributes.position = MagicMock(spec=PositionInfo)
        mock_dst.attributes.position.bbox = MagicMock(spec=BBox)
        mock_dst.attributes.position.bbox.x1 = x1
        mock_dst.attributes.position.bbox.y1 = y1
        mock_dst.attributes.position.bbox.x2 = x2
        mock_dst.attributes.position.bbox.y2 = y2
        return mock_dst

    def test_determine_layout_empty_page_map(self):
        """测试 determine_layout_with_middle_line 处理空页面映射的情况"""
        result = determine_layout_with_middle_line({})
        
        # 没有数据时应该返回 top-bottom
        assert result == typesetting_top_bottom

    def test_determine_layout_no_valid_dsts(self):
        """测试 determine_layout_with_middle_line 处理没有有效 DST 的情况"""
        # 创建没有属性的 DST
        mock_dst = MagicMock(spec=DST)
        mock_dst.attributes = None
        
        page_dst_map = {1: [mock_dst]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        assert result == typesetting_top_bottom

    def test_determine_layout_horizontal_layout(self):
        """测试 determine_layout_with_middle_line 检测水平布局"""
        # 创建跨越页面大部分宽度的 DST（水平布局）
        dst1 = self.create_mock_dst(0, 0, 90, 20)    # Wide element
        dst2 = self.create_mock_dst(0, 25, 85, 45)   # Wide element
        dst3 = self.create_mock_dst(0, 50, 95, 70)   # Wide element
        
        page_dst_map = {1: [dst1, dst2, dst3]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        # Wide elements should indicate top-bottom layout
        assert result == typesetting_top_bottom

    def test_determine_layout_vertical_layout(self):
        """测试 determine_layout_with_middle_line 检测垂直布局"""
        # 创建较窄的 DST（垂直/列布局）
        dst1 = self.create_mock_dst(0, 0, 30, 50)    # Left column
        dst2 = self.create_mock_dst(35, 0, 65, 50)   # Right column
        dst3 = self.create_mock_dst(70, 0, 100, 50)  # Far right column
        
        page_dst_map = {1: [dst1, dst2, dst3]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        # Narrow columns should indicate left-right layout
        assert result == typesetting_left_right

    def test_determine_layout_mixed_elements(self):
        """测试 determine_layout_with_middle_line 处理混合元素大小的情况"""
        # 宽窄元素的混合
        wide_dst = self.create_mock_dst(0, 0, 80, 20)     # Wide element (area = 1600)
        narrow_dst1 = self.create_mock_dst(0, 25, 25, 45) # Narrow element (area = 500)
        narrow_dst2 = self.create_mock_dst(30, 25, 55, 45) # Narrow element (area = 500)
        
        page_dst_map = {1: [wide_dst, narrow_dst1, narrow_dst2]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        # Should depend on which type has more total area
        # Wide element area (1600) vs narrow elements area (1000)
        assert result == typesetting_top_bottom

    def test_determine_layout_multiple_pages(self):
        """测试 determine_layout_with_middle_line 处理多页面的情况"""
        # 第1页：水平布局
        dst1_p1 = self.create_mock_dst(0, 0, 90, 20)
        dst2_p1 = self.create_mock_dst(0, 25, 85, 45)
        
        # Page 2: vertical layout
        dst1_p2 = self.create_mock_dst(0, 0, 30, 50)
        dst2_p2 = self.create_mock_dst(35, 0, 65, 50)
        
        page_dst_map = {
            1: [dst1_p1, dst2_p1],
            2: [dst1_p2, dst2_p2]
        }
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        # Should aggregate across all pages
        assert result in [typesetting_left_right, typesetting_top_bottom]

    def test_determine_layout_equal_densities(self):
        """测试 determine_layout_with_middle_line 处理混合密度的情况"""
        # 创建一个宽元素和一个窄元素来测试逻辑
        # Page width will be 100 (from x1=0 to x2=100)
        dst1 = self.create_mock_dst(0, 0, 60, 50)    # Wide element: width=60 > 0.5*100=50
        dst2 = self.create_mock_dst(70, 0, 90, 50)   # Narrow element: width=20 < 0.5*100=50

        page_dst_map = {1: [dst1, dst2]}

        result = determine_layout_with_middle_line(page_dst_map)

        # dst1: width=60 > 50, so contributes to horizontal_density (60*50=3000)
        # dst2: width=20 < 50, so contributes to vertical_density (20*50=1000)
        # horizontal_density=3000 > vertical_density=1000, so returns top-bottom
        # But the function logic is: if horizontal_density < vertical_density: return left-right
        # Since 3000 > 1000, it goes to else case and returns top-bottom
        assert result == typesetting_top_bottom

    def test_determine_layout_single_element(self):
        """测试 determine_layout_with_middle_line 处理单个元素的情况"""
        dst = self.create_mock_dst(10, 10, 90, 30)
        
        page_dst_map = {1: [dst]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        # Single wide element should indicate top-bottom
        assert result == typesetting_top_bottom

    def test_determine_layout_zero_area_elements(self):
        """测试 determine_layout_with_middle_line 处理零面积元素的情况"""
        # 宽度或高度为零的元素
        dst1 = self.create_mock_dst(10, 10, 10, 20)  # Zero width
        dst2 = self.create_mock_dst(20, 15, 30, 15)  # Zero height
        
        page_dst_map = {1: [dst1, dst2]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        # Zero area elements shouldn't contribute to density
        assert result == typesetting_top_bottom

    def test_determine_layout_dst_without_position(self):
        """测试 determine_layout_with_middle_line 处理没有位置的 DST 的情况"""
        mock_dst = MagicMock(spec=DST)
        mock_dst.attributes = MagicMock(spec=DSTAttribute)
        mock_dst.attributes.position = None
        
        page_dst_map = {1: [mock_dst]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        assert result == typesetting_top_bottom

    def test_determine_layout_dst_without_bbox(self):
        """测试 determine_layout_with_middle_line 处理没有边界框的 DST 的情况"""
        mock_dst = MagicMock(spec=DST)
        mock_dst.attributes = MagicMock(spec=DSTAttribute)
        mock_dst.attributes.position = MagicMock(spec=PositionInfo)
        mock_dst.attributes.position.bbox = None
        
        page_dst_map = {1: [mock_dst]}
        
        result = determine_layout_with_middle_line(page_dst_map)
        
        assert result == typesetting_top_bottom


class TestTypesettingCorrect:
    """typesetting_correct 函数的测试用例"""

    def create_mock_dst(self, x1, y1, x2, y2):
        """创建带有边界框的模拟DST的辅助方法"""
        mock_dst = MagicMock(spec=DST)
        mock_dst.attributes = MagicMock(spec=DSTAttribute)
        mock_dst.attributes.position = MagicMock(spec=PositionInfo)
        mock_dst.attributes.position.bbox = MagicMock(spec=BBox)
        mock_dst.attributes.position.bbox.x1 = x1
        mock_dst.attributes.position.bbox.y1 = y1
        mock_dst.attributes.position.bbox.x2 = x2
        mock_dst.attributes.position.bbox.y2 = y2
        return mock_dst

    @patch('modules.layout.typesetting.determine_layout_with_middle_line')
    @patch('modules.layout.typesetting.reorder_dst_by_page')
    def test_typesetting_correct_left_right_layout(self, mock_reorder, mock_determine):
        """测试 typesetting_correct 处理左右布局的情况"""
        mock_root_dst = MagicMock(spec=DST)
        page_dst_map = {1: [self.create_mock_dst(0, 0, 50, 50)]}
        
        mock_determine.return_value = typesetting_left_right
        mock_reorder.return_value = "reordered_dst"
        
        result_dst, layout = typesetting_correct(mock_root_dst, page_dst_map)
        
        assert result_dst == "reordered_dst"
        assert layout == typesetting_left_right
        mock_determine.assert_called_once_with(page_dst_map)
        mock_reorder.assert_called_once_with(mock_root_dst, page_dst_map)

    @patch('modules.layout.typesetting.determine_layout_with_middle_line')
    def test_typesetting_correct_top_bottom_layout(self, mock_determine):
        """测试 typesetting_correct 处理上下布局的情况"""
        mock_root_dst = MagicMock(spec=DST)
        page_dst_map = {1: [self.create_mock_dst(0, 0, 90, 20)]}
        
        mock_determine.return_value = typesetting_top_bottom
        
        result_dst, layout = typesetting_correct(mock_root_dst, page_dst_map)
        
        assert result_dst is None
        assert layout == typesetting_top_bottom
        mock_determine.assert_called_once_with(page_dst_map)

    @patch('modules.layout.typesetting.determine_layout_with_middle_line')
    def test_typesetting_correct_unknown_layout(self, mock_determine):
        """测试 typesetting_correct 处理未知布局的情况"""
        mock_root_dst = MagicMock(spec=DST)
        page_dst_map = {1: [self.create_mock_dst(0, 0, 50, 50)]}
        
        mock_determine.return_value = "unknown-layout"
        
        result_dst, layout = typesetting_correct(mock_root_dst, page_dst_map)
        
        assert result_dst is None
        assert layout == "unknown-layout"
        mock_determine.assert_called_once_with(page_dst_map)

    @patch('modules.layout.typesetting.determine_layout_with_middle_line')
    @patch('modules.layout.typesetting.reorder_dst_by_page')
    def test_typesetting_correct_empty_page_map(self, mock_reorder, mock_determine):
        """测试 typesetting_correct 处理空页面映射的情况"""
        mock_root_dst = MagicMock(spec=DST)
        page_dst_map = {}
        
        mock_determine.return_value = typesetting_top_bottom
        
        result_dst, layout = typesetting_correct(mock_root_dst, page_dst_map)
        
        assert result_dst is None
        assert layout == typesetting_top_bottom
        mock_determine.assert_called_once_with(page_dst_map)
        mock_reorder.assert_not_called()

    @patch('modules.layout.typesetting.determine_layout_with_middle_line')
    @patch('modules.layout.typesetting.reorder_dst_by_page')
    def test_typesetting_correct_multiple_pages(self, mock_reorder, mock_determine):
        """测试 typesetting_correct 处理多页面的情况"""
        mock_root_dst = MagicMock(spec=DST)
        page_dst_map = {
            1: [self.create_mock_dst(0, 0, 30, 50)],
            2: [self.create_mock_dst(35, 0, 65, 50)],
            3: [self.create_mock_dst(70, 0, 100, 50)]
        }
        
        mock_determine.return_value = typesetting_left_right
        mock_reorder.return_value = "reordered_multi_page_dst"
        
        result_dst, layout = typesetting_correct(mock_root_dst, page_dst_map)
        
        assert result_dst == "reordered_multi_page_dst"
        assert layout == typesetting_left_right
        mock_determine.assert_called_once_with(page_dst_map)
        mock_reorder.assert_called_once_with(mock_root_dst, page_dst_map)

    @patch('modules.layout.typesetting.determine_layout_with_middle_line')
    @patch('modules.layout.typesetting.reorder_dst_by_page')
    def test_typesetting_correct_none_root_dst(self, mock_reorder, mock_determine):
        """测试 typesetting_correct 处理 None 根 DST 的情况"""
        page_dst_map = {1: [self.create_mock_dst(0, 0, 50, 50)]}
        
        mock_determine.return_value = typesetting_left_right
        mock_reorder.return_value = "reordered_dst"
        
        result_dst, layout = typesetting_correct(None, page_dst_map)
        
        assert result_dst == "reordered_dst"
        assert layout == typesetting_left_right
        mock_determine.assert_called_once_with(page_dst_map)
        mock_reorder.assert_called_once_with(None, page_dst_map)


class TestReorderDstByPage:
    """reorder_dst_by_page 函数的测试用例"""

    def create_test_dst(self, dst_id, x1, y1, x2, y2):
        """创建测试DST对象的辅助方法"""
        bbox = BBox(x1=x1, y1=y1, x2=x2, y2=y2)
        pos = PositionInfo(bbox=bbox)
        attr = DSTAttribute(level=1, position=pos, page=1, hash="a" * 32)

        return DST(
            id=dst_id,
            parent="-1",
            order=0,
            dst_type=DSTType.TEXT,
            attributes=attr,
            content=[f"Content {dst_id}"]
        )

    def test_reorder_dst_by_page_basic(self):
        """测试 reorder_dst_by_page 处理基本左右布局的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        # Create DSTs for left and right columns
        left_dst = self.create_test_dst("left", 10, 50, 40, 100)    # Left side
        right_dst = self.create_test_dst("right", 60, 50, 90, 100)  # Right side

        page_dst_map = {1: [left_dst, right_dst]}

        result = reorder_dst_by_page(root_dst, page_dst_map)

        # Should return root + left + right
        assert len(result) == 3
        assert result[0] == root_dst
        assert result[1] == left_dst
        assert result[2] == right_dst

    def test_reorder_dst_by_page_cross_domain_content(self):
        """测试 reorder_dst_by_page 处理跨越中间阈值内容的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        # Create DSTs where one crosses the middle threshold
        left_dst = self.create_test_dst("left", 10, 50, 40, 100)      # Left side
        cross_dst = self.create_test_dst("cross", 30, 120, 70, 150)   # Crosses middle (threshold = 50)
        right_dst = self.create_test_dst("right", 60, 50, 90, 100)    # Right side

        page_dst_map = {1: [left_dst, cross_dst, right_dst]}

        result = reorder_dst_by_page(root_dst, page_dst_map)

        # Should return root + left_side (including cross_dst) + right_side
        assert len(result) == 4
        assert result[0] == root_dst
        # cross_dst should be in left_side because it crosses the threshold
        assert cross_dst in result[1:3]  # Should be in left_side
        assert right_dst in result

    def test_reorder_dst_by_page_threshold_calculation(self):
        """测试 reorder_dst_by_page 阈值计算"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        # Create DSTs with specific positions to test threshold
        # Page range: x1=10 to x2=90, so threshold = (90-10)/2 + 10 = 50
        dst1 = self.create_test_dst("dst1", 10, 50, 30, 100)   # x2=30 <= 50 (left)
        dst2 = self.create_test_dst("dst2", 20, 50, 50, 100)   # x2=50 <= 50 (left)
        dst3 = self.create_test_dst("dst3", 51, 50, 70, 100)   # x1=51 > 50 (right)
        dst4 = self.create_test_dst("dst4", 70, 50, 90, 100)   # x1=70 > 50 (right)

        page_dst_map = {1: [dst1, dst2, dst3, dst4]}

        result = reorder_dst_by_page(root_dst, page_dst_map)

        # Should return root + left_side + right_side
        assert len(result) == 5
        assert result[0] == root_dst
        # dst1 and dst2 should be in left_side (positions 1-2)
        assert dst1 in result[1:3]
        assert dst2 in result[1:3]
        # dst3 and dst4 should be in right_side (positions 3-4)
        assert dst3 in result[3:5]
        assert dst4 in result[3:5]

    def test_reorder_dst_by_page_multiple_pages(self):
        """测试 reorder_dst_by_page 处理多页面的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        # Page 1 DSTs
        page1_left = self.create_test_dst("p1_left", 10, 50, 40, 100)
        page1_right = self.create_test_dst("p1_right", 60, 50, 90, 100)

        # Page 2 DSTs
        page2_left = self.create_test_dst("p2_left", 15, 50, 35, 100)
        page2_right = self.create_test_dst("p2_right", 65, 50, 85, 100)

        page_dst_map = {
            1: [page1_left, page1_right],
            2: [page2_left, page2_right]
        }

        result = reorder_dst_by_page(root_dst, page_dst_map)

        # Should return root + all DSTs from all pages
        assert len(result) == 5
        assert result[0] == root_dst
        # All page DSTs should be included
        page_dsts = [page1_left, page1_right, page2_left, page2_right]
        for dst in page_dsts:
            assert dst in result[1:]

    def test_reorder_dst_by_page_empty_page_map(self):
        """测试 reorder_dst_by_page 处理空页面映射的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        result = reorder_dst_by_page(root_dst, {})

        # Should return only root DST
        assert len(result) == 1
        assert result[0] == root_dst

    def test_reorder_dst_by_page_single_dst_per_page(self):
        """测试 reorder_dst_by_page 处理每页单个 DST 的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        single_dst = self.create_test_dst("single", 25, 50, 75, 100)

        page_dst_map = {1: [single_dst]}

        result = reorder_dst_by_page(root_dst, page_dst_map)

        # Should return root + single DST
        assert len(result) == 2
        assert result[0] == root_dst
        assert result[1] == single_dst

    def test_reorder_dst_by_page_exact_threshold_boundary(self):
        """测试 reorder_dst_by_page 处理恰好在阈值边界的 DST 的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        # Threshold will be (90-10)/2 + 10 = 50
        boundary_dst1 = self.create_test_dst("boundary1", 40, 50, 50, 100)  # x2=50 <= 50 (left)
        boundary_dst2 = self.create_test_dst("boundary2", 50, 50, 60, 100)  # x1=50 < 50 is False, x2=60 > 50 (right)

        page_dst_map = {1: [boundary_dst1, boundary_dst2]}

        result = reorder_dst_by_page(root_dst, page_dst_map)

        assert len(result) == 3
        assert result[0] == root_dst
        # boundary_dst1 should be in left_side (x2 <= threshold)
        # boundary_dst2 should be in right_side (x1 >= threshold)
        assert boundary_dst1 in result[1:2]  # Left side
        assert boundary_dst2 in result[2:3]  # Right side

    def test_reorder_dst_by_page_all_left_side(self):
        """测试 reorder_dst_by_page 处理所有 DST 都在左侧的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        # All DSTs on left side (threshold = 50)
        left_dst1 = self.create_test_dst("left1", 10, 50, 30, 100)
        left_dst2 = self.create_test_dst("left2", 15, 120, 35, 150)
        left_dst3 = self.create_test_dst("left3", 20, 170, 40, 200)

        page_dst_map = {1: [left_dst1, left_dst2, left_dst3]}

        result = reorder_dst_by_page(root_dst, page_dst_map)

        # Should return root + all left DSTs + empty right side
        assert len(result) == 4
        assert result[0] == root_dst
        # All DSTs should be in left_side
        for dst in [left_dst1, left_dst2, left_dst3]:
            assert dst in result[1:]

    def test_reorder_dst_by_page_all_right_side(self):
        """测试 reorder_dst_by_page 处理所有 DST 都在右侧的情况"""
        root_dst = self.create_test_dst("root", 0, 0, 100, 20)

        # All DSTs on right side (threshold = 50)
        right_dst1 = self.create_test_dst("right1", 60, 50, 80, 100)
        right_dst2 = self.create_test_dst("right2", 65, 120, 85, 150)
        right_dst3 = self.create_test_dst("right3", 70, 170, 90, 200)

        page_dst_map = {1: [right_dst1, right_dst2, right_dst3]}

        result = reorder_dst_by_page(root_dst, page_dst_map)

        # Should return root + empty left side + all right DSTs
        assert len(result) == 4
        assert result[0] == root_dst
        # All DSTs should be in right_side
        for dst in [right_dst1, right_dst2, right_dst3]:
            assert dst in result[1:]
