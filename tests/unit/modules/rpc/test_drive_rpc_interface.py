"""
modules.rpc.drive_rpc_interface 模块的单元测试
"""
import pytest
from abc import ABC
from unittest.mock import MagicMock

from modules.rpc.drive_rpc_interface import DriveRpcInterface
from modules.entity.drive_entity import DriveFileResponse
from commons.auth.auth_rpc import SigVerType


class TestDriveRpcInterface:
    """DriveRpcInterface抽象类的测试用例"""

    def test_drive_rpc_interface_is_abstract(self):
        """测试DriveRpcInterface是抽象类"""
        assert issubclass(DriveRpcInterface, ABC)
        
        # 不应该能够直接实例化
        with pytest.raises(TypeError):
            DriveRpcInterface()

    def test_drive_rpc_interface_abstract_methods(self):
        """测试DriveRpcInterface具有所需的抽象方法"""
        # 检查抽象方法是否存在
        assert hasattr(DriveRpcInterface, 'aget_download_url')
        assert hasattr(DriveRpcInterface, 'get_download_url')
        assert hasattr(DriveRpcInterface, 'init')
        
        # 检查它们是否为抽象方法
        assert getattr(DriveRpcInterface.aget_download_url, '__isabstractmethod__', False)
        assert getattr(DriveRpcInterface.get_download_url, '__isabstractmethod__', False)
        assert getattr(DriveRpcInterface.init, '__isabstractmethod__', False)


class ConcreteDriveRpc(DriveRpcInterface):
    """用于测试的具体实现"""
    
    def __init__(self):
        self.initialized = False
        self.host = None
        self.ak = None
        self.sk = None
        self.sig_type = None
        self.download_uri = None

    async def aget_download_url(self, file_id: str) -> DriveFileResponse:
        """测试异步获取下载URL的实现"""
        if not self.initialized:
            return DriveFileResponse.error_response("Not initialized")
        
        if file_id == "valid_file_id":
            return DriveFileResponse.success_response("https://example.com/download/valid_file_id")
        else:
            return DriveFileResponse.error_response("File not found", 404)

    def get_download_url(self, file_id: str) -> DriveFileResponse:
        """测试同步获取下载URL的实现"""
        if not self.initialized:
            return DriveFileResponse.error_response("Not initialized")
        
        if file_id == "valid_file_id":
            return DriveFileResponse.success_response("https://example.com/download/valid_file_id")
        else:
            return DriveFileResponse.error_response("File not found", 404)

    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType, download_uri: str, **kwargs):
        """测试初始化的实现"""
        self.host = host
        self.ak = ak
        self.sk = sk
        self.sig_type = sig_type
        self.download_uri = download_uri
        self.initialized = True


class TestConcreteDriveRpc:
    """具体实现的测试用例"""

    def test_concrete_implementation_can_be_instantiated(self):
        """测试具体实现可以被实例化"""
        rpc = ConcreteDriveRpc()
        assert isinstance(rpc, DriveRpcInterface)
        assert isinstance(rpc, ConcreteDriveRpc)

    def test_init_method(self):
        """测试初始化方法的实现"""
        rpc = ConcreteDriveRpc()
        
        host = "https://example.com"
        ak = "test_ak"
        sk = "test_sk"
        sig_type = SigVerType.wps2
        download_uri = "/api/download"
        
        rpc.init(host, ak, sk, sig_type, download_uri)
        
        assert rpc.initialized is True
        assert rpc.host == host
        assert rpc.ak == ak
        assert rpc.sk == sk
        assert rpc.sig_type == sig_type
        assert rpc.download_uri == download_uri

    def test_init_method_with_kwargs(self):
        """测试带有额外关键字参数的初始化方法"""
        rpc = ConcreteDriveRpc()
        
        rpc.init(
            host="https://example.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps4,
            download_uri="/api/download",
            timeout=30,
            retries=3
        )
        
        assert rpc.initialized is True

    @pytest.mark.asyncio
    async def test_aget_download_url_success(self):
        """测试成功异步获取下载URL"""
        rpc = ConcreteDriveRpc()
        rpc.init("https://example.com", "ak", "sk", SigVerType.wps2, "/download")
        
        result = await rpc.aget_download_url("valid_file_id")
        
        assert result.result == "ok"
        assert result.url == "https://example.com/download/valid_file_id"
        assert result.is_success

    @pytest.mark.asyncio
    async def test_aget_download_url_not_initialized(self):
        """测试未初始化时异步获取下载URL"""
        rpc = ConcreteDriveRpc()
        
        result = await rpc.aget_download_url("valid_file_id")
        
        assert result.result == "error"
        assert "Not initialized" in result.error_message
        assert not result.is_success

    @pytest.mark.asyncio
    async def test_aget_download_url_file_not_found(self):
        """测试使用无效文件ID异步获取下载URL"""
        rpc = ConcreteDriveRpc()
        rpc.init("https://example.com", "ak", "sk", SigVerType.wps2, "/download")
        
        result = await rpc.aget_download_url("invalid_file_id")
        
        assert result.result == "error"
        assert result.status_code == 404
        assert "File not found" in result.error_message
        assert not result.is_success

    def test_get_download_url_success(self):
        """测试成功同步获取下载URL"""
        rpc = ConcreteDriveRpc()
        rpc.init("https://example.com", "ak", "sk", SigVerType.wps2, "/download")
        
        result = rpc.get_download_url("valid_file_id")
        
        assert result.result == "ok"
        assert result.url == "https://example.com/download/valid_file_id"
        assert result.is_success

    def test_get_download_url_not_initialized(self):
        """测试未初始化时同步获取下载URL"""
        rpc = ConcreteDriveRpc()
        
        result = rpc.get_download_url("valid_file_id")
        
        assert result.result == "error"
        assert "Not initialized" in result.error_message
        assert not result.is_success

    def test_get_download_url_file_not_found(self):
        """测试使用无效文件ID同步获取下载URL"""
        rpc = ConcreteDriveRpc()
        rpc.init("https://example.com", "ak", "sk", SigVerType.wps2, "/download")
        
        result = rpc.get_download_url("invalid_file_id")
        
        assert result.result == "error"
        assert result.status_code == 404
        assert "File not found" in result.error_message
        assert not result.is_success

    def test_interface_method_signatures(self):
        """测试接口方法签名是否正确"""
        import inspect
        
        # 测试aget_download_url签名
        sig = inspect.signature(DriveRpcInterface.aget_download_url)
        params = list(sig.parameters.keys())
        assert 'self' in params
        assert 'file_id' in params
        
        # 测试get_download_url签名
        sig = inspect.signature(DriveRpcInterface.get_download_url)
        params = list(sig.parameters.keys())
        assert 'self' in params
        assert 'file_id' in params
        
        # 测试init签名
        sig = inspect.signature(DriveRpcInterface.init)
        params = list(sig.parameters.keys())
        assert 'self' in params
        assert 'host' in params
        assert 'ak' in params
        assert 'sk' in params
        assert 'sig_type' in params
        assert 'download_uri' in params
        assert 'kwargs' in params
