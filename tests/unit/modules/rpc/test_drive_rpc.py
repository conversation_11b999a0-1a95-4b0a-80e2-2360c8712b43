"""
modules.rpc.callback_func 模块的单元测试
"""
import pytest
import json
import time
from unittest.mock import patch, MagicMock, AsyncMock
import aiohttp
from fastapi import status

from modules.rpc.callback_func import RpcCallbackClient
from commons.auth.auth_route import AuthPlatform
from modules.entity.parse_entity import RespGeneralParseData


class TestRpcCallbackClient:
    """RpcCallbackClient类的测试用例"""

    def test_rpc_callback_client_singleton(self):
        """测试RpcCallbackClient是单例模式"""
        client1 = RpcCallbackClient()
        client2 = RpcCallbackClient()
        assert client1 is client2

    def test_init_method(self):
        """测试RpcCallbackClient的初始化"""
        client = RpcCallbackClient()
        ak = "test_ak"
        sk = "test_sk"
        auth_platform = AuthPlatform.dmc

        client.init(ak, sk, auth_platform)

        assert client._ak == ak
        assert client._sk == sk
        assert client._auth_platform == auth_platform

    @patch('modules.rpc.callback_func.sig_wps2')
    @patch('modules.rpc.callback_func.time.strftime')
    @patch('modules.rpc.callback_func.time.gmtime')
    def test_sig_method_dmc_platform(self, mock_gmtime, mock_strftime, mock_sig_wps2):
        """测试DMC平台的_sig方法"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)

        mock_gmtime.return_value = time.struct_time((2023, 1, 1, 0, 0, 0, 0, 0, 0))
        mock_strftime.return_value = "Sun, 01 Jan 2023 00:00:00 GMT"
        mock_sig_wps2.return_value = {"Authorization": "test_auth"}

        method = "POST"
        uri = "/test/uri"
        body = {"key": "value"}

        result = client._sig(method, uri, body)

        mock_sig_wps2.assert_called_once()
        assert result == {"Authorization": "test_auth"}

    @patch('modules.rpc.callback_func.sig_wps4')
    @patch('modules.rpc.callback_func.time.strftime')
    @patch('modules.rpc.callback_func.time.gmtime')
    def test_sig_method_cams_platform(self, mock_gmtime, mock_strftime, mock_sig_wps4):
        """测试CAMS平台的_sig方法"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.private)

        mock_gmtime.return_value = time.struct_time((2023, 1, 1, 0, 0, 0, 0, 0, 0))
        mock_strftime.return_value = "Sun, 01 Jan 2023 00:00:00 GMT"
        mock_sig_wps4.return_value = {"Authorization": "test_auth"}

        method = "POST"
        uri = "/test/uri"
        body = {"key": "value"}

        result = client._sig(method, uri, body)

        mock_sig_wps4.assert_called_once()
        assert result == {"Authorization": "test_auth"}

    def test_sig_method_with_none_body(self):
        """测试_sig方法处理None body的情况"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)

        with patch('modules.rpc.callback_func.sig_wps2') as mock_sig_wps2:
            mock_sig_wps2.return_value = {"Authorization": "test_auth"}

            result = client._sig("GET", "/test/uri", None)

            mock_sig_wps2.assert_called_once()
            assert result == {"Authorization": "test_auth"}

    @pytest.mark.asyncio
    async def test_acall_success(self):
        """测试成功的_acall方法"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)

        # 直接模拟_acall方法以避免复杂的aiohttp模拟
        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            mock_acall.return_value = None  # 成功调用返回None

            await client._acall(
                host="http://test.com",
                method="POST",
                uri="/test/uri",
                data={"key": "value"}
            )

            mock_acall.assert_called_once_with(
                host="http://test.com",
                method="POST",
                uri="/test/uri",
                data={"key": "value"}
            )

    @pytest.mark.asyncio
    async def test_acall_failure(self):
        """测试_acall方法处理HTTP错误的情况"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)

        # 模拟_acall方法抛出异常
        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            mock_acall.side_effect = Exception("rpc callback request fail")

            with pytest.raises(Exception) as exc_info:
                await client._acall(
                    host="http://test.com",
                    method="POST",
                    uri="/test/uri",
                    data={"key": "value"}
                )

            assert "rpc callback request fail" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_acall_http_error_response(self):
        """测试_acall方法处理HTTP错误响应的情况"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)

        # 由于_acall方法有retry装饰器，我们需要测试RetryError
        with patch('modules.rpc.callback_func.aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 404
            mock_response.text = AsyncMock(return_value="Not Found")

            mock_context_manager = AsyncMock()
            mock_context_manager.__aenter__ = AsyncMock(return_value=mock_response)
            mock_context_manager.__aexit__ = AsyncMock(return_value=None)

            mock_session_instance = AsyncMock()
            mock_session_instance.request.return_value = mock_context_manager
            mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
            mock_session_instance.__aexit__ = AsyncMock(return_value=None)

            mock_session.return_value = mock_session_instance

            # 由于retry装饰器，异常会被包装在RetryError中
            from tenacity import RetryError
            with pytest.raises(RetryError):
                await client._acall(
                    host="http://test.com",
                    method="POST",
                    uri="/test/uri",
                    data={"key": "value"}
                )

    @pytest.mark.asyncio
    async def test_send_callback_with_dict_data(self):
        """测试send_callback方法处理字典数据"""
        client = RpcCallbackClient()

        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            await client.send_callback(
                host="http://test.com",
                uri="/test/uri",
                method="POST",
                data={"key": "value"}
            )

            mock_acall.assert_called_once_with(
                host="http://test.com",
                method="POST",
                uri="/test/uri",
                data={"key": "value"},
                headers=None,
                timeout_second=180
            )

    @pytest.mark.asyncio
    async def test_send_callback_with_pydantic_data(self):
        """测试send_callback方法处理Pydantic模型数据"""
        client = RpcCallbackClient()

        # 创建一个模拟的Pydantic模型
        mock_data = MagicMock()
        mock_data.dict.return_value = {"converted": "data"}

        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            await client.send_callback(
                host="http://test.com",
                uri="/test/uri",
                data=mock_data
            )

            mock_acall.assert_called_once_with(
                host="http://test.com",
                method="POST",
                uri="/test/uri",
                data={"converted": "data"},
                headers=None,
                timeout_second=180
            )

    @pytest.mark.asyncio
    async def test_send_callback_with_none_data(self):
        """测试send_callback方法处理None数据"""
        client = RpcCallbackClient()

        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            await client.send_callback(
                host="http://test.com",
                uri="/test/uri"
            )

            mock_acall.assert_called_once_with(
                host="http://test.com",
                method="POST",
                uri="/test/uri",
                data=None,
                headers=None,
                timeout_second=180
            )

    @pytest.mark.asyncio
    async def test_send_callback_with_custom_parameters(self):
        """测试send_callback方法处理自定义参数"""
        client = RpcCallbackClient()

        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            await client.send_callback(
                host="http://test.com",
                uri="/test/uri",
                method="PUT",
                data={"key": "value"},
                headers={"Custom-Header": "value"},
                timeout_second=300
            )

            mock_acall.assert_called_once_with(
                host="http://test.com",
                method="PUT",
                uri="/test/uri",
                data={"key": "value"},
                headers={"Custom-Header": "value"},
                timeout_second=300
            )