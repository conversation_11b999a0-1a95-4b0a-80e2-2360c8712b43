"""
modules.rpc.drive_rpc_factory 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock
from typing import Dict, Type

from modules.rpc.drive_rpc_factory import DriveRpcFactory, DriveRpcClient, get_drive_rpc_client
from modules.rpc.drive_rpc_interface import DriveRpcInterface
from modules.entity.drive_entity import DriveFileResponse
from commons.auth.auth_rpc import SigVerType


class MockDriveRpc(DriveRpcInterface):
    """用于测试的模拟实现"""
    
    def __init__(self):
        self.initialized = False
        self.init_called_with = None

    async def aget_download_url(self, file_id: str) -> DriveFileResponse:
        return DriveFileResponse.success_response(f"async_url_{file_id}")

    def get_download_url(self, file_id: str) -> DriveFileResponse:
        return DriveFileResponse.success_response(f"sync_url_{file_id}")

    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType, download_uri: str, **kwargs):
        self.initialized = True
        self.init_called_with = {
            'host': host, 'ak': ak, 'sk': sk, 
            'sig_type': sig_type, 'download_uri': download_uri, 
            'kwargs': kwargs
        }


class TestDriveRpcFactory:
    """DriveRpcFactory类的测试用例"""

    @patch('modules.rpc.drive_rpc_factory.DriveRpcFactory._register_default_implementations')
    def test_factory_is_singleton(self, mock_register):
        """测试DriveRpcFactory是单例模式"""
        mock_register.return_value = None
        factory1 = DriveRpcFactory()
        factory2 = DriveRpcFactory()
        assert factory1 is factory2

    @patch('modules.rpc.drive_rpc_factory.DriveRpcFactory._register_default_implementations')
    def test_factory_initialization(self, mock_register):
        """测试工厂初始化"""
        mock_register.return_value = None
        factory = DriveRpcFactory()
        assert hasattr(factory, '_rpc_classes')
        assert hasattr(factory, '_rpc_instances')
        assert isinstance(factory._rpc_classes, dict)
        assert isinstance(factory._rpc_instances, dict)

    @patch('modules.rpc.drive_rpc_factory.DriveRpcFactory._register_from_config_file')
    @patch('modules.rpc.drive_rpc_factory.DriveRpcFactory.__init__', lambda x: None)
    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_default_implementations_config_success(self, mock_logging, mock_register_config):
        """测试使用成功配置注册默认实现"""
        mock_register_config.return_value = True

        factory = DriveRpcFactory()
        factory._rpc_classes = {"test": MockDriveRpc}  # Simulate config registration
        factory._rpc_instances = {}

        factory._register_default_implementations()

        mock_register_config.assert_called_once()
        assert "test" in factory._rpc_classes

    @patch('modules.rpc.drive_rpc_factory.DriveRpcFactory._register_from_config_file')
    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_default_implementations_config_failure(self, mock_logging, mock_register_config):
        """测试配置失败时的默认实现注册"""
        mock_register_config.return_value = False

        # 模拟导入DriveV5Rpc
        with patch('builtins.__import__') as mock_import:
            def side_effect(name, *args, **kwargs):
                if name == 'modules.rpc.drive_v5_rpc':
                    mock_module = MagicMock()
                    mock_module.DriveV5Rpc = MockDriveRpc
                    return mock_module
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            factory = DriveRpcFactory()
            factory._rpc_classes.clear()  # 清除任何现有的注册

            factory._register_default_implementations()

            assert "wpsv5" in factory._rpc_classes
            assert factory._rpc_classes["wpsv5"] == MockDriveRpc

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_from_config_file_success(self, mock_logging):
        """测试从配置文件成功注册"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        # 模拟导入conf模块和ConfDriveRpcConfig
        with patch('builtins.__import__') as mock_import:
            def side_effect(name, *args, **kwargs):
                if name == 'conf':
                    mock_conf_module = MagicMock()
                    mock_conf_module.ConfDriveRpcConfig.rpc_config = {
                        "implementations": {
                            "test_rpc": {
                                "class_path": "tests.unit.modules.rpc.test_drive_rpc_factory.MockDriveRpc",
                                "enabled": True
                            }
                        }
                    }
                    return mock_conf_module
                elif name == 'tests.unit.modules.rpc.test_drive_rpc_factory':
                    mock_module = MagicMock()
                    mock_module.MockDriveRpc = MockDriveRpc
                    return mock_module
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            result = factory._register_from_config_file()

            assert result is True
            assert "test_rpc" in factory._rpc_classes
            mock_logging.info.assert_any_call("Registered RPC from environment config: test_rpc -> tests.unit.modules.rpc.test_drive_rpc_factory.MockDriveRpc")

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_from_config_file_disabled_implementation(self, mock_logging):
        """测试禁用实现的注册"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        # 模拟导入conf模块和ConfDriveRpcConfig
        with patch('builtins.__import__') as mock_import:
            def side_effect(name, *args, **kwargs):
                if name == 'conf':
                    mock_conf_module = MagicMock()
                    mock_conf_module.ConfDriveRpcConfig.rpc_config = {
                        "implementations": {
                            "disabled_rpc": {
                                "class_path": "test.path.DisabledRpc",
                                "enabled": False
                            }
                        }
                    }
                    return mock_conf_module
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            result = factory._register_from_config_file()

            assert result is False
            assert "disabled_rpc" not in factory._rpc_classes
            mock_logging.info.assert_called_with("RPC implementation disabled_rpc is disabled in config")

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_from_config_file_import_error(self, mock_logging):
        """测试导入错误时的注册"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        # 模拟导入conf模块和ConfDriveRpcConfig
        with patch('builtins.__import__') as mock_import:
            def side_effect(name, *args, **kwargs):
                if name == 'conf':
                    mock_conf_module = MagicMock()
                    mock_conf_module.ConfDriveRpcConfig.rpc_config = {
                        "implementations": {
                            "invalid_rpc": {
                                "class_path": "nonexistent.module.InvalidRpc",
                                "enabled": True
                            }
                        }
                    }
                    return mock_conf_module
                elif name == 'nonexistent.module':
                    raise ImportError("No module named 'nonexistent'")
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            result = factory._register_from_config_file()

            assert result is False
            assert "invalid_rpc" not in factory._rpc_classes
            mock_logging.error.assert_any_call("Failed to register RPC implementation invalid_rpc: No module named 'nonexistent'")

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_from_config_file_mixed_results(self, mock_logging):
        """测试混合结果（部分成功，部分失败）"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        # 模拟导入conf模块和ConfDriveRpcConfig
        with patch('builtins.__import__') as mock_import:
            def side_effect(name, *args, **kwargs):
                if name == 'conf':
                    mock_conf_module = MagicMock()
                    mock_conf_module.ConfDriveRpcConfig.rpc_config = {
                        "implementations": {
                            "valid_rpc": {
                                "class_path": "tests.unit.modules.rpc.test_drive_rpc_factory.MockDriveRpc",
                                "enabled": True
                            },
                            "disabled_rpc": {
                                "class_path": "test.path.DisabledRpc",
                                "enabled": False
                            },
                            "invalid_rpc": {
                                "class_path": "nonexistent.module.InvalidRpc",
                                "enabled": True
                            }
                        }
                    }
                    return mock_conf_module
                elif name == 'tests.unit.modules.rpc.test_drive_rpc_factory':
                    mock_module = MagicMock()
                    mock_module.MockDriveRpc = MockDriveRpc
                    return mock_module
                elif name == 'nonexistent.module':
                    raise ImportError("No module named 'nonexistent'")
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            result = factory._register_from_config_file()

            # 应该返回True，因为至少有一个成功注册
            assert result is True
            assert "valid_rpc" in factory._rpc_classes
            assert "disabled_rpc" not in factory._rpc_classes
            assert "invalid_rpc" not in factory._rpc_classes

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_from_config_file_config_load_error(self, mock_logging):
        """测试配置加载错误"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        # 模拟导入ConfDriveRpcConfig时出错
        with patch('builtins.__import__', side_effect=ImportError("Config import failed")):
            result = factory._register_from_config_file()

            assert result is False
            mock_logging.error.assert_called_with("Failed to load RPC config from environment: Config import failed")

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_from_config_file_empty_config(self, mock_logging):
        """测试空配置"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        # 模拟导入conf模块和ConfDriveRpcConfig
        with patch('builtins.__import__') as mock_import:
            def side_effect(name, *args, **kwargs):
                if name == 'conf':
                    mock_conf_module = MagicMock()
                    mock_conf_module.ConfDriveRpcConfig.rpc_config = {"implementations": {}}
                    return mock_conf_module
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            result = factory._register_from_config_file()

            assert result is False

    def test_register_rpc_class(self):
        """测试手动RPC类注册"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        factory.register_rpc_class("test_type", MockDriveRpc)

        assert "test_type" in factory._rpc_classes
        assert factory._rpc_classes["test_type"] == MockDriveRpc

    def test_register_rpc_class_invalid_interface(self):
        """测试注册不实现DriveRpcInterface的类"""
        factory = DriveRpcFactory()

        class InvalidRpc:
            pass

        with pytest.raises(ValueError) as exc_info:
            factory.register_rpc_class("invalid", InvalidRpc)

        assert "RPC class must implement DriveRpcInterface" in str(exc_info.value)

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_register_default_implementations_import_error(self, mock_logging):
        """测试默认实现导入错误"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()

        with patch.object(factory, '_register_from_config_file', return_value=False):
            with patch('builtins.__import__', side_effect=ImportError("Import failed")):
                factory._register_default_implementations()

                mock_logging.warning.assert_called_with("Failed to import DriveV5Rpc: Import failed")

    @patch('modules.rpc.drive_rpc_factory.logging')
    def test_get_rpc_instance_logging(self, mock_logging):
        """测试获取RPC实例时的日志记录"""
        factory = DriveRpcFactory()
        factory.register_rpc_class("test_type", MockDriveRpc)

        # 第一次获取实例应该记录日志
        instance = factory.get_rpc_instance("test_type")

        mock_logging.info.assert_called_with("Created new Drive RPC instance for type test_type")

        # 第二次获取相同实例不应该再记录日志
        mock_logging.reset_mock()
        instance2 = factory.get_rpc_instance("test_type")

        mock_logging.info.assert_not_called()
        assert instance is instance2

    def test_get_rpc_instance_new_instance(self):
        """测试获取新的RPC实例"""
        factory = DriveRpcFactory()
        factory.register_rpc_class("test_type", MockDriveRpc)
        
        instance = factory.get_rpc_instance("test_type")
        
        assert isinstance(instance, MockDriveRpc)
        assert "test_type" in factory._rpc_instances
        assert factory._rpc_instances["test_type"] is instance

    def test_get_rpc_instance_existing_instance(self):
        """测试获取现有RPC实例（单例行为）"""
        factory = DriveRpcFactory()
        factory.register_rpc_class("test_type", MockDriveRpc)
        
        instance1 = factory.get_rpc_instance("test_type")
        instance2 = factory.get_rpc_instance("test_type")
        
        assert instance1 is instance2

    def test_get_rpc_instance_unregistered_type(self):
        """测试获取未注册类型的RPC实例"""
        factory = DriveRpcFactory()
        
        with pytest.raises(ValueError) as exc_info:
            factory.get_rpc_instance("unregistered_type")
        
        assert "is not registered" in str(exc_info.value)

    def test_get_available_types(self):
        """测试获取可用的RPC类型"""
        factory = DriveRpcFactory()
        factory._rpc_classes.clear()
        factory.register_rpc_class("type1", MockDriveRpc)
        factory.register_rpc_class("type2", MockDriveRpc)
        
        types = factory.get_available_types()
        
        assert isinstance(types, list)
        assert "type1" in types
        assert "type2" in types
        assert len(types) == 2


class TestDriveRpcClient:
    """DriveRpcClient类的测试用例"""

    def test_client_initialization(self):
        """测试DriveRpcClient初始化"""
        with patch('modules.rpc.drive_rpc_factory.DriveRpcFactory') as mock_factory_class:
            mock_factory = MagicMock()
            mock_rpc = MockDriveRpc()
            mock_factory.get_rpc_instance.return_value = mock_rpc
            mock_factory_class.return_value = mock_factory
            
            client = DriveRpcClient("test_type")
            
            mock_factory.get_rpc_instance.assert_called_once_with("test_type")
            assert client._rpc is mock_rpc

    def test_client_initialization_default_type(self):
        """测试DriveRpcClient使用默认类型初始化"""
        with patch('modules.rpc.drive_rpc_factory.DriveRpcFactory') as mock_factory_class:
            mock_factory = MagicMock()
            mock_rpc = MockDriveRpc()
            mock_factory.get_rpc_instance.return_value = mock_rpc
            mock_factory_class.return_value = mock_factory
            
            client = DriveRpcClient(None)
            
            mock_factory.get_rpc_instance.assert_called_once_with(None)

    @pytest.mark.asyncio
    async def test_client_aget_download_url(self):
        """测试客户端异步获取下载URL"""
        with patch('modules.rpc.drive_rpc_factory.DriveRpcFactory') as mock_factory_class:
            mock_factory = MagicMock()
            mock_rpc = MockDriveRpc()
            mock_factory.get_rpc_instance.return_value = mock_rpc
            mock_factory_class.return_value = mock_factory
            
            client = DriveRpcClient("test_type")
            result = await client.aget_download_url("test_file_id")
            
            assert result.result == "ok"
            assert result.url == "async_url_test_file_id"

    def test_client_get_download_url(self):
        """测试客户端同步获取下载URL"""
        with patch('modules.rpc.drive_rpc_factory.DriveRpcFactory') as mock_factory_class:
            mock_factory = MagicMock()
            mock_rpc = MockDriveRpc()
            mock_factory.get_rpc_instance.return_value = mock_rpc
            mock_factory_class.return_value = mock_factory
            
            client = DriveRpcClient("test_type")
            result = client.get_download_url("test_file_id")
            
            assert result.result == "ok"
            assert result.url == "sync_url_test_file_id"

    def test_client_init(self):
        """测试客户端初始化方法"""
        with patch('modules.rpc.drive_rpc_factory.DriveRpcFactory') as mock_factory_class:
            mock_factory = MagicMock()
            mock_rpc = MockDriveRpc()
            mock_factory.get_rpc_instance.return_value = mock_rpc
            mock_factory_class.return_value = mock_factory
            
            client = DriveRpcClient("test_type")
            client.init("host", "ak", "sk", SigVerType.wps2, download_uri="/download")
            
            assert mock_rpc.initialized is True
            assert mock_rpc.init_called_with['host'] == "host"


class TestGlobalFunctions:
    """全局工具函数的测试用例"""

    def test_get_drive_rpc_client_with_type(self):
        """测试使用特定类型的get_drive_rpc_client"""
        with patch('modules.rpc.drive_rpc_factory.DriveRpcClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            
            result = get_drive_rpc_client("test_type")
            
            mock_client_class.assert_called_once_with("test_type")
            assert result is mock_client

    def test_get_drive_rpc_client_default_type(self):
        """测试使用默认类型的get_drive_rpc_client"""
        with patch('modules.rpc.drive_rpc_factory.DriveRpcClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            
            result = get_drive_rpc_client(None)
            
            mock_client_class.assert_called_once_with(None)
            assert result is mock_client
