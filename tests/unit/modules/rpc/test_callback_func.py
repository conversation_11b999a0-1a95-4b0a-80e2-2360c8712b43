"""
modules.rpc.callback_func 模块的单元测试
"""
import pytest
import json
import time
from unittest.mock import patch, MagicMock, AsyncMock
from aiohttp import ClientSession, ClientResponse
from fastapi import status

from modules.rpc.callback_func import Rpc<PERSON><PERSON>backClient
from commons.auth.auth_route import AuthPlatform
from modules.entity.parse_entity import RespGeneralParseData


class TestRpcCallbackClient:
    """RpcCallbackClient类的测试用例"""

    def test_singleton_pattern(self):
        """测试单例模式"""
        instance1 = RpcCallbackClient()
        instance2 = RpcCallbackClient()
        assert instance1 is instance2

    def test_init_method(self):
        """测试初始化方法"""
        client = RpcCallbackClient()
        
        # 测试初始状态
        assert client._ak == ""
        assert client._sk == ""
        assert client._auth_platform == ""
        
        # 测试初始化
        ak = "test_access_key"
        sk = "test_secret_key"
        auth_platform = AuthPlatform.dmc
        
        client.init(ak, sk, auth_platform)
        
        assert client._ak == ak
        assert client._sk == sk
        assert client._auth_platform == auth_platform

    @patch('modules.rpc.callback_func.time')
    @patch('modules.rpc.callback_func.sig_wps2')
    def test_sig_method_dmc_platform(self, mock_sig_wps2, mock_time):
        """测试DMC平台的签名方法"""
        # 设置模拟
        mock_time.strftime.return_value = "Mon, 01 Jan 2024 00:00:00 GMT"
        mock_time.gmtime.return_value = time.struct_time((2024, 1, 1, 0, 0, 0, 0, 1, 0))
        mock_sig_wps2.return_value = {"Authorization": "test_auth"}
        
        # 初始化客户端
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)
        
        # 测试参数
        method = "POST"
        uri = "/test/uri"
        body = {"test": "data"}
        content_type = "application/json"
        
        # 调用方法
        result = client._sig(method, uri, body, content_type)
        
        # 验证结果
        assert result == {"Authorization": "test_auth"}
        
        # 验证sig_wps2被正确调用
        expected_body = json.dumps(body).encode("utf-8")
        mock_sig_wps2.assert_called_once_with(
            uri, expected_body, "test_ak", "test_sk", 
            "Mon, 01 Jan 2024 00:00:00 GMT", content_type
        )

    @patch('modules.rpc.callback_func.time')
    @patch('modules.rpc.callback_func.sig_wps4')
    def test_sig_method_cams_platform(self, mock_sig_wps4, mock_time):
        """测试CAMS平台的签名方法"""
        # 设置模拟
        mock_time.strftime.return_value = "Mon, 01 Jan 2024 00:00:00 GMT"
        mock_time.gmtime.return_value = time.struct_time((2024, 1, 1, 0, 0, 0, 0, 1, 0))
        mock_sig_wps4.return_value = {"Authorization": "test_auth_cams"}
        
        # 初始化客户端
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.private)
        
        # 测试参数
        method = "POST"
        uri = "/test/uri"
        body = {"test": "data"}
        content_type = "application/json"
        
        # 调用方法
        result = client._sig(method, uri, body, content_type)
        
        # 验证结果
        assert result == {"Authorization": "test_auth_cams"}
        
        # 验证sig_wps4被正确调用
        expected_body = json.dumps(body).encode("utf-8")
        mock_sig_wps4.assert_called_once_with(
            method, uri, expected_body, "test_ak", "test_sk", 
            "Mon, 01 Jan 2024 00:00:00 GMT", content_type
        )

    @patch('modules.rpc.callback_func.time')
    @patch('modules.rpc.callback_func.sig_wps2')
    def test_sig_method_with_none_body(self, mock_sig_wps2, mock_time):
        """测试签名方法处理None body的情况"""
        # 设置模拟
        mock_time.strftime.return_value = "Mon, 01 Jan 2024 00:00:00 GMT"
        mock_sig_wps2.return_value = {"Authorization": "test_auth"}
        
        # 初始化客户端
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)
        
        # 调用方法（body为None）
        result = client._sig("GET", "/test/uri", None)
        
        # 验证sig_wps2被调用时body为None
        mock_sig_wps2.assert_called_once_with(
            "/test/uri", None, "test_ak", "test_sk", 
            "Mon, 01 Jan 2024 00:00:00 GMT", ""
        )

    @pytest.mark.asyncio
    @patch('modules.rpc.callback_func.request_id_context')
    @patch('modules.rpc.callback_func.aiohttp.ClientSession')
    async def test_acall_success(self, mock_client_session, mock_request_id_context):
        """测试_acall方法成功调用"""
        # 设置模拟
        mock_request_id_context.get.return_value = "test_request_id"
        
        # 模拟HTTP响应
        mock_response = MagicMock()
        mock_response.status = status.HTTP_200_OK
        mock_response.__aenter__ = AsyncMock(return_value=mock_response)
        mock_response.__aexit__ = AsyncMock(return_value=None)
        
        # 模拟会话
        mock_session = MagicMock()
        mock_session.request.return_value = mock_response
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)
        
        mock_client_session.return_value = mock_session
        
        # 初始化客户端并模拟签名
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)
        
        with patch.object(client, '_sig', return_value={"Authorization": "test_auth"}):
            # 调用方法
            await client._acall(
                host="https://example.com",
                method="POST",
                uri="/test/uri",
                data={"test": "data"},
                headers={"Custom": "header"},
                timeout_second=60
            )
        
        # 验证会话请求被调用
        mock_session.request.assert_called_once()
        call_args = mock_session.request.call_args
        
        assert call_args[1]["method"] == "POST"
        assert call_args[1]["url"] == "https://example.com/test/uri"
        assert call_args[1]["json"] == {"test": "data"}
        assert call_args[1]["timeout"] == 60
        
        # 验证headers包含签名和自定义头
        headers = call_args[1]["headers"]
        assert headers["Authorization"] == "test_auth"
        assert headers["Custom"] == "header"
        assert headers["X-Request-Id"] == "test_request_id"

    @pytest.mark.asyncio
    @patch('modules.rpc.callback_func.request_id_context')
    @patch('modules.rpc.callback_func.aiohttp.ClientSession')
    @patch('modules.rpc.callback_func.logging')
    async def test_acall_http_error(self, mock_logging, mock_client_session, mock_request_id_context):
        """测试_acall方法HTTP错误处理"""
        from tenacity import RetryError

        # 设置模拟
        mock_request_id_context.get.return_value = "test_request_id"

        # 模拟HTTP错误响应
        mock_response = MagicMock()
        mock_response.status = status.HTTP_400_BAD_REQUEST
        mock_response.text = AsyncMock(return_value="Bad Request Error")
        mock_response.__aenter__ = AsyncMock(return_value=mock_response)
        mock_response.__aexit__ = AsyncMock(return_value=None)

        # 模拟会话
        mock_session = MagicMock()
        mock_session.request.return_value = mock_response
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)

        mock_client_session.return_value = mock_session

        # 初始化客户端
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)

        with patch.object(client, '_sig', return_value={"Authorization": "test_auth"}):
            # 调用方法并期望RetryError异常（因为使用了tenacity重试装饰器）
            with pytest.raises(RetryError) as exc_info:
                await client._acall(
                    host="https://example.com",
                    method="POST",
                    uri="/test/uri",
                    data={"test": "data"}
                )

        # 验证是RetryError异常
        assert isinstance(exc_info.value, RetryError)

        # 验证错误日志被调用（重试3次，每次都会记录错误）
        assert mock_logging.error.call_count >= 1

    @pytest.mark.asyncio
    async def test_send_callback_with_dict_data(self):
        """测试send_callback方法处理字典数据"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)
        
        # 模拟_acall方法
        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            # 测试参数
            host = "https://example.com"
            uri = "/callback/test"
            method = "POST"
            data = {"result": "success"}
            headers = {"Content-Type": "application/json"}
            timeout_second = 120
            
            # 调用方法
            await client.send_callback(
                host=host,
                uri=uri,
                method=method,
                data=data,
                headers=headers,
                timeout_second=timeout_second
            )
            
            # 验证_acall被正确调用
            mock_acall.assert_called_once_with(
                host=host,
                method=method,
                uri=uri,
                data=data,
                headers=headers,
                timeout_second=timeout_second
            )

    @pytest.mark.asyncio
    async def test_send_callback_with_pydantic_data(self):
        """测试send_callback方法处理Pydantic数据"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)
        
        # 创建Pydantic数据对象
        pydantic_data = RespGeneralParseData()
        pydantic_data.token = "test_token"
        
        # 模拟_acall方法
        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            # 调用方法
            await client.send_callback(
                host="https://example.com",
                uri="/callback/test",
                data=pydantic_data
            )
            
            # 验证_acall被调用时data被转换为字典
            mock_acall.assert_called_once()
            call_args = mock_acall.call_args
            assert isinstance(call_args[1]["data"], dict)

    @pytest.mark.asyncio
    async def test_send_callback_with_none_data(self):
        """测试send_callback方法处理None数据"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)
        
        # 模拟_acall方法
        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            # 调用方法
            await client.send_callback(
                host="https://example.com",
                uri="/callback/test",
                data=None
            )
            
            # 验证_acall被调用时data为None
            mock_acall.assert_called_once()
            call_args = mock_acall.call_args
            assert call_args[1]["data"] is None

    @pytest.mark.asyncio
    async def test_send_callback_default_parameters(self):
        """测试send_callback方法的默认参数"""
        client = RpcCallbackClient()
        client.init("test_ak", "test_sk", AuthPlatform.dmc)
        
        # 模拟_acall方法
        with patch.object(client, '_acall', new_callable=AsyncMock) as mock_acall:
            # 调用方法（只传必需参数）
            await client.send_callback(
                host="https://example.com",
                uri="/callback/test"
            )
            
            # 验证默认参数
            mock_acall.assert_called_once()
            call_args = mock_acall.call_args
            assert call_args[1]["method"] == "POST"
            assert call_args[1]["data"] is None
            assert call_args[1]["headers"] is None
            assert call_args[1]["timeout_second"] == 180
