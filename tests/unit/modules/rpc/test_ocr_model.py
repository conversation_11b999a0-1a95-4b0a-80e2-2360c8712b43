"""
modules.rpc.kdc_rpc 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import json
import asyncio

from modules.rpc.kdc_rpc import KDCRpc, PAGE_SIZE
from services.datamodel import FileType


class TestKdcRpcConstants:
    """KDC RPC 常量的测试用例"""

    def test_page_size_constant(self):
        """测试 PAGE_SIZE 常量"""
        assert PAGE_SIZE == 8
        assert isinstance(PAGE_SIZE, int)


class TestKDCRpc:
    """KDCRpc 类的测试用例"""

    def test_kdc_rpc_singleton(self):
        """测试 KDCRpc 是单例模式"""
        rpc1 = KDCRpc()
        rpc2 = KDCRpc()
        assert rpc1 is rpc2

    def test_kdc_rpc_initialization(self):
        """测试 KDCRpc 初始化"""
        rpc = KDCRpc()
        # KDCRpc 没有 init 方法，使用类方法
        assert hasattr(rpc, 'aget_content')
        assert hasattr(rpc, 'get_content')

    def test_kdc_rpc_class_methods(self):
        """测试 KDCRpc 类方法存在"""
        assert hasattr(KDCRpc, 'aget_content')
        assert hasattr(KDCRpc, 'get_content')
        assert callable(KDCRpc.aget_content)
        assert callable(KDCRpc.get_content)

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_success(self, mock_wps365api):
        """测试异步获取内容成功"""
        # 模拟成功响应
        mock_response = {
            "code": 0,
            "data": {
                "pages": [
                    {
                        "page_id": 1,
                        "blocks": [
                            {
                                "type": "text",
                                "content": "Sample text"
                            }
                        ]
                    }
                ]
            }
        }

        # 正确设置模拟 - aget_content 使用的是 aget 方法
        mock_api_instance = AsyncMock()
        mock_api_instance.aget.return_value = mock_response
        mock_wps365api.return_value = mock_api_instance

        result = await KDCRpc.aget_content(
            company_id="test_company",
            drive_id="test_drive",
            v7_file_id="test_file_id"
        )

        assert result is not None
        assert result["code"] == 0
        mock_api_instance.aget.assert_called_once()

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_api_error(self, mock_wps365api):
        """测试异步获取内容API错误"""
        # 模拟API错误响应
        mock_api_instance = AsyncMock()
        mock_api_instance.aget.side_effect = Exception("API error")
        mock_wps365api.return_value = mock_api_instance

        with pytest.raises(Exception):
            await KDCRpc.aget_content(
                company_id="test_company",
                drive_id="test_drive",
                v7_file_id="test_file_id"
            )

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_success(self, mock_wps365api):
        """测试同步获取内容成功"""
        # 模拟成功响应
        mock_response = {
            "code": 0,
            "data": {
                "pages": [
                    {
                        "page_id": 1,
                        "blocks": []
                    }
                ]
            }
        }

        mock_api_instance = MagicMock()
        mock_api_instance.get.return_value = mock_response
        mock_wps365api.return_value = mock_api_instance

        result = KDCRpc.get_content(
            company_id="test_company",
            drive_id="test_drive",
            v7_file_id="test_file_id"
        )

        assert result is not None
        assert result["code"] == 0

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_error(self, mock_wps365api):
        """测试同步获取内容错误"""
        # 模拟错误响应
        mock_api_instance = MagicMock()
        mock_api_instance.get.side_effect = Exception("API error")
        mock_wps365api.return_value = mock_api_instance

        with pytest.raises(Exception):
            KDCRpc.get_content(
                company_id="test_company",
                drive_id="test_drive",
                v7_file_id="test_file_id"
            )

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_success(self, mock_wps365api):
        """测试通过URL或文件异步获取内容成功"""
        mock_response = {"doc": {"pages": []}}

        mock_api_instance = AsyncMock()
        mock_api_instance.apost.return_value = mock_response
        mock_wps365api.return_value = mock_api_instance

        result = await KDCRpc.aget_content_by_url_or_file(
            company_id="test_company",
            file_url_or_bytes="https://example.com/file.pdf"
        )

        assert result is not None
        assert len(result) == 1  # 返回的是列表
        assert result[0]["doc"]["pages"] == []

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_by_url_or_file_success(self, mock_wps365api):
        """测试通过URL或文件同步获取内容成功"""
        mock_response = {"doc": {"pages": []}}

        mock_api_instance = MagicMock()
        mock_api_instance.post.return_value = mock_response
        mock_wps365api.return_value = mock_api_instance

        result = KDCRpc.get_content_by_url_or_file(
            company_id="test_company",
            file_url_or_bytes="https://example.com/file.pdf",
            filename="test.pdf"  # 提供 filename 参数避免 NoneType 错误
        )

        assert result is not None
        assert len(result) == 1  # 返回的是列表
        assert result[0]["doc"]["pages"] == []

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_poll_task_status_success(self, mock_wps365api):
        """测试轮询任务状态成功"""
        mock_response = {"status": "finished", "data": {"code": 0, "pages": []}}

        mock_api_instance = AsyncMock()
        mock_api_instance.aget.return_value = mock_response
        mock_wps365api.return_value = mock_api_instance

        result = await KDCRpc.poll_task_status(
            task_id="test_task_id",
            company_id="test_company"
        )

        assert result is not None
        assert result["code"] == 0

    def test_create_task_with_name_function(self):
        """测试create_task_with_name函数存在"""
        from modules.rpc.kdc_rpc import create_task_with_name
        assert callable(create_task_with_name)

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    @patch.object(KDCRpc, 'poll_task_status')
    async def test_async_aget_content_by_url_or_file_success(self, mock_poll_task_status, mock_wps365api):
        """测试通过URL或文件异步获取内容成功（异步版本）"""
        # 模拟创建任务的响应
        mock_create_response = {"task_id": "test_task_123"}
        # 模拟轮询任务状态的响应
        mock_poll_response = {"doc": {"pages": []}}

        mock_api_instance = AsyncMock()
        mock_api_instance.apost.return_value = mock_create_response
        mock_wps365api.return_value = mock_api_instance
        mock_poll_task_status.return_value = mock_poll_response

        result = await KDCRpc.async_aget_content_by_url_or_file(
            company_id="test_company",
            file_url_or_bytes="https://example.com/file.pdf"
        )

        assert result is not None
        assert len(result) == 1  # 返回的是列表
        assert result[0]["doc"]["pages"] == []
        mock_poll_task_status.assert_called_once_with("test_task_123", "test_company", True)