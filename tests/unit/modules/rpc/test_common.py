"""
modules.rpc.common 模块的单元测试
"""
import pytest
from urllib.parse import urlparse

from modules.rpc.common import get_host_and_uri


class TestCommonFunctions:
    """通用工具函数的测试用例"""

    def test_get_host_and_uri_with_full_url(self):
        """测试get_host_and_uri处理完整URL"""
        url = "https://example.com/api/v1/callback"
        host, uri = get_host_and_uri(url)
        
        assert host == "example.com"
        assert uri == "/api/v1/callback"

    def test_get_host_and_uri_with_port(self):
        """测试get_host_and_uri处理包含端口的URL"""
        url = "http://localhost:8080/api/callback"
        host, uri = get_host_and_uri(url)
        
        assert host == "localhost:8080"
        assert uri == "/api/callback"

    def test_get_host_and_uri_with_query_params(self):
        """测试get_host_and_uri处理查询参数"""
        url = "https://api.example.com/callback?param1=value1&param2=value2"
        host, uri = get_host_and_uri(url)
        
        assert host == "api.example.com"
        assert uri == "/callback"

    def test_get_host_and_uri_with_fragment(self):
        """测试get_host_and_uri处理URL片段"""
        url = "https://example.com/api/callback#section1"
        host, uri = get_host_and_uri(url)
        
        assert host == "example.com"
        assert uri == "/api/callback"

    def test_get_host_and_uri_with_root_path(self):
        """测试get_host_and_uri处理根路径"""
        url = "https://example.com/"
        host, uri = get_host_and_uri(url)
        
        assert host == "example.com"
        assert uri == "/"

    def test_get_host_and_uri_with_no_path(self):
        """测试get_host_and_uri处理无路径"""
        url = "https://example.com"
        host, uri = get_host_and_uri(url)
        
        assert host == "example.com"
        assert uri == ""

    def test_get_host_and_uri_with_subdomain(self):
        """测试get_host_and_uri处理子域名"""
        url = "https://api.subdomain.example.com/v1/callback"
        host, uri = get_host_and_uri(url)
        
        assert host == "api.subdomain.example.com"
        assert uri == "/v1/callback"

    def test_get_host_and_uri_with_https(self):
        """测试get_host_and_uri处理HTTPS协议"""
        url = "https://secure.example.com/secure/callback"
        host, uri = get_host_and_uri(url)
        
        assert host == "secure.example.com"
        assert uri == "/secure/callback"

    def test_get_host_and_uri_with_http(self):
        """测试get_host_and_uri处理HTTP协议"""
        url = "http://insecure.example.com/insecure/callback"
        host, uri = get_host_and_uri(url)
        
        assert host == "insecure.example.com"
        assert uri == "/insecure/callback"

    def test_get_host_and_uri_with_complex_path(self):
        """测试get_host_and_uri处理复杂路径"""
        url = "https://example.com/api/v1/users/123/callbacks/456"
        host, uri = get_host_and_uri(url)
        
        assert host == "example.com"
        assert uri == "/api/v1/users/123/callbacks/456"

    def test_get_host_and_uri_with_ip_address(self):
        """测试get_host_and_uri处理IP地址"""
        url = "http://*************:3000/api/callback"
        host, uri = get_host_and_uri(url)
        
        assert host == "*************:3000"
        assert uri == "/api/callback"

    def test_get_host_and_uri_with_localhost(self):
        """测试get_host_and_uri处理localhost"""
        url = "http://localhost/callback"
        host, uri = get_host_and_uri(url)
        
        assert host == "localhost"
        assert uri == "/callback"

    def test_get_host_and_uri_edge_cases(self):
        """测试get_host_and_uri处理边界情况"""
        # 测试空字符串 - urlparse能够优雅地处理这种情况
        host, uri = get_host_and_uri("")
        assert host == ""
        assert uri == ""

        # 测试无效URL - urlparse能够优雅地处理这种情况
        host, uri = get_host_and_uri("not-a-url")
        assert host == ""
        assert uri == "not-a-url"

    def test_get_host_and_uri_return_types(self):
        """测试get_host_and_uri返回正确的类型"""
        url = "https://example.com/api/callback"
        host, uri = get_host_and_uri(url)
        
        assert isinstance(host, str)
        assert isinstance(uri, str)

    def test_get_host_and_uri_with_special_characters(self):
        """测试get_host_and_uri处理路径中的特殊字符"""
        url = "https://example.com/api/callback%20with%20spaces"
        host, uri = get_host_and_uri(url)
        
        assert host == "example.com"
        assert uri == "/api/callback%20with%20spaces"
