"""
modules.rpc.ocr_model 模块的单元测试
"""
import pytest
from unittest.mock import patch, AsyncMock, MagicMock
import json
import asyncio

from modules.rpc.ocr_model import OCRModelClient
from modules.entity.crop_entity import LayOutData, OcrData
from commons.auth.auth_rpc import SigVerType


class TestOCRModelClient:
    """OCRModelClient 类的测试用例"""

    def test_ocr_model_client_singleton(self):
        """测试 OCRModelClient 是单例模式"""
        client1 = OCRModelClient()
        client2 = OCRModelClient()
        assert client1 is client2

    def test_ocr_model_client_initialization(self):
        """测试 OCRModelClient 初始化"""
        client = OCRModelClient()
        assert hasattr(client, '_req')
        assert client._req is None  # 初始化时为 None

    def test_init_method(self):
        """测试初始化方法"""
        client = OCRModelClient()
        
        host = "https://ocr.example.com"
        ak = "test_ak"
        sk = "test_sk"
        sig_type = SigVerType.wps2
        
        client.init(host, ak, sk, sig_type)
        
        # 验证AuthRequest对象已创建
        assert client._req is not None

    def test_init_method_default_sig_type(self):
        """测试使用默认签名类型的初始化方法"""
        client = OCRModelClient()
        
        client.init("https://ocr.example.com", "ak", "sk")
        
        # 验证AuthRequest对象已创建
        assert client._req is not None

    @pytest.mark.asyncio
    async def test_request_layout_model_success(self):
        """测试成功的布局模型请求"""
        client = OCRModelClient()
        
        # 模拟AuthRequest
        mock_req = AsyncMock()
        mock_response_data = {
            "code": 0,
            "data": {
                "boxes": [
                    {
                        "cls_id": 1,
                        "label": "text",
                        "score": 0.95,
                        "coordinate": [10.0, 20.0, 110.0, 70.0]
                    }
                ]
            }
        }
        mock_req.async_call.return_value = (200, json.dumps(mock_response_data))
        client._req = mock_req
        
        result = await client.request_layout_model("https://example.com/image.jpg")
        
        assert isinstance(result, LayOutData)
        mock_req.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_request_layout_model_api_error(self):
        """测试布局模型请求API错误"""
        client = OCRModelClient()
        
        # 模拟AuthRequest错误响应
        mock_req = AsyncMock()
        mock_req.async_call.return_value = (404, "Not found")
        client._req = mock_req
        
        result = await client.request_layout_model("https://example.com/image.jpg")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_request_layout_model_invalid_response(self):
        """测试布局模型请求无效响应"""
        client = OCRModelClient()
        
        # 模拟AuthRequest无效响应
        mock_req = AsyncMock()
        mock_response_data = {
            "code": 1,
            "message": "Error"
        }
        mock_req.async_call.return_value = (200, json.dumps(mock_response_data))
        client._req = mock_req
        
        result = await client.request_layout_model("https://example.com/image.jpg")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_request_ocr_success(self):
        """测试成功的OCR请求"""
        client = OCRModelClient()
        
        # 模拟AuthRequest
        mock_req = AsyncMock()
        mock_response_data = {
            "code": 0,
            "data": {
                "rec_texts": ["识别的文本内容", "第二行文本"]
            }
        }
        mock_req.async_call.return_value = (200, json.dumps(mock_response_data))
        client._req = mock_req
        
        result = await client.request_ocr("https://example.com/image.jpg")
        
        assert isinstance(result, OcrData)
        mock_req.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_request_ocr_timeout(self):
        """测试OCR请求超时"""
        client = OCRModelClient()
        
        # 模拟AuthRequest超时
        mock_req = AsyncMock()
        mock_req.async_call.side_effect = asyncio.TimeoutError()
        client._req = mock_req
        
        result = await client.request_ocr("https://example.com/image.jpg")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_request_ocr_exception(self):
        """测试OCR请求异常"""
        client = OCRModelClient()
        
        # 模拟AuthRequest异常
        mock_req = AsyncMock()
        mock_req.async_call.side_effect = Exception("Network error")
        client._req = mock_req
        
        result = await client.request_ocr("https://example.com/image.jpg")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_request_table_cls_success(self):
        """测试成功的表格分类请求"""
        client = OCRModelClient()
        
        # 模拟AuthRequest
        mock_req = AsyncMock()
        mock_response_data = {
            "code": 0,
            "data": {
                "table_type": "wired"
            }
        }
        mock_req.async_call.return_value = (200, json.dumps(mock_response_data))
        client._req = mock_req
        
        result = await client.request_table_cls("https://example.com/table.jpg")
        
        assert result == "wired"
        mock_req.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_request_table_cls_error(self):
        """测试表格分类请求错误"""
        client = OCRModelClient()
        
        # 模拟AuthRequest错误
        mock_req = AsyncMock()
        mock_req.async_call.return_value = (500, "Internal server error")
        client._req = mock_req
        
        result = await client.request_table_cls("https://example.com/table.jpg")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_request_wired_table_success(self):
        """测试成功的有线表格OCR请求"""
        client = OCRModelClient()
        
        # 模拟AuthRequest
        mock_req = AsyncMock()
        mock_response_data = {
            "code": 0,
            "data": {
                "table_html": "<table><tr><td>Cell 1</td></tr></table>"
            }
        }
        mock_req.async_call.return_value = (200, json.dumps(mock_response_data))
        client._req = mock_req
        
        result = await client.request_wired_table("https://example.com/table.jpg")
        
        assert result == "<table><tr><td>Cell 1</td></tr></table>"
        mock_req.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_request_wireless_table_success(self):
        """测试成功的无线表格OCR请求"""
        client = OCRModelClient()
        
        # 模拟AuthRequest
        mock_req = AsyncMock()
        mock_response_data = {
            "code": 0,
            "data": {
                "table_html": "<table><tr><td>Cell 1</td></tr></table>"
            }
        }
        mock_req.async_call.return_value = (200, json.dumps(mock_response_data))
        client._req = mock_req
        
        result = await client.request_wireless_table("https://example.com/table.jpg")
        
        assert result == "<table><tr><td>Cell 1</td></tr></table>"
        mock_req.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_request_text_embedding_success(self):
        """测试成功的文本嵌入请求"""
        client = OCRModelClient()
        
        # 模拟AuthRequest
        mock_req = AsyncMock()
        mock_response_data = {
            "code": 0,
            "data": {
                "embedding": [0.1, 0.2, 0.3, 0.4, 0.5]
            }
        }
        mock_req.async_call.return_value = (200, json.dumps(mock_response_data))
        client._req = mock_req
        
        result = await client.request_text_embedding("测试文本")
        
        assert result == [0.1, 0.2, 0.3, 0.4, 0.5]
        mock_req.async_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_request_text_embedding_error(self):
        """测试文本嵌入请求错误"""
        client = OCRModelClient()
        
        # 模拟AuthRequest错误
        mock_req = AsyncMock()
        mock_req.async_call.return_value = (400, "Bad request")
        client._req = mock_req
        
        result = await client.request_text_embedding("测试文本")
        
        assert result is None
