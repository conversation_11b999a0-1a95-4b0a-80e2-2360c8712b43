"""
modules.rpc.parse_res_rpc 模块的测试
"""
import pytest
import sys
import os
import json
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 在导入之前模拟环境变量和模块
os.environ.setdefault('SERVICE_ID', 'test_service_id')
os.environ.setdefault('SERVICE_SECRET', 'test_service_secret')
os.environ.setdefault('SERVICE_KEY', 'test_service_key')
os.environ.setdefault('LOG_LEVEL', 'INFO')
os.environ.setdefault('aidocs_dst_ak', 'test_ak')
os.environ.setdefault('aidocs_dst_sk', 'test_sk')
os.environ.setdefault('conf_env', 'dev')

# 模拟复杂依赖
with patch.dict('sys.modules', {
    'conf': <PERSON><PERSON>(),
    'conf.setting_dev': <PERSON><PERSON>(),
    'conf.setting': Mock(),
    'commons.auth.auth_route': Mock(),
    'commons.auth.dmc_checkauth': Mock(),
    'commons.auth.auth_rpc': Mock(),
    'commons.tools.cams_decrypt': Mock(),
    'commons.logger.business_log': Mock(),
    'commons.tools.utils': Mock(),
    'commons.trace.tracer': Mock()
}):
    from modules.rpc.parse_res_rpc import (
        RecallChunkInput, ChunkInfo, RecallChunkOutput, RecallChunkClient
    )
    from modules.pipeline.context import FileInfo
    from modules.entity.chunk_entity import Chunk
    from commons.auth.auth_rpc import SigVerType
    from services.datamodel import FileType


class TestRecallChunkInput:
    """RecallChunkInput 模型的测试"""

    def test_recall_chunk_input_creation_default(self):
        """测试使用默认值创建 RecallChunkInput"""
        input_obj = RecallChunkInput()
        assert input_obj.drive_id is None
        assert input_obj.file_ids is None
        assert input_obj.from_ is None
        assert input_obj.size is None
        assert input_obj.chunk_ids is None
        assert input_obj.with_content is False
        assert input_obj.with_embedding is False
        assert input_obj.with_fileinfo is False

    def test_recall_chunk_input_creation_with_values(self):
        """测试使用特定值创建 RecallChunkInput"""
        # 使用字典方式创建，因为 from_ 是别名字段
        data = {
            "drive_id": "test_drive_id",
            "file_ids": ["file1", "file2"],
            "from": 0,  # 使用别名
            "size": 10,
            "chunk_ids": ["chunk1", "chunk2"],
            "with_content": True,
            "with_embedding": True,
            "with_fileinfo": True
        }
        input_obj = RecallChunkInput(**data)
        assert input_obj.drive_id == "test_drive_id"
        assert input_obj.file_ids == ["file1", "file2"]
        assert input_obj.from_ == 0
        assert input_obj.size == 10
        assert input_obj.chunk_ids == ["chunk1", "chunk2"]
        assert input_obj.with_content is True
        assert input_obj.with_embedding is True
        assert input_obj.with_fileinfo is True

    def test_recall_chunk_input_alias_field(self):
        """测试 from_ 字段的别名"""
        # 测试使用别名 "from" 创建对象
        data = {"from": 5, "size": 20}
        input_obj = RecallChunkInput(**data)
        assert input_obj.from_ == 5
        assert input_obj.size == 20

    def test_recall_chunk_input_model_dump_with_alias(self):
        """测试模型序列化时使用别名"""
        # 使用字典方式创建，确保 from_ 字段有值
        data = {"from": 10, "size": 20}
        input_obj = RecallChunkInput(**data)

        # 测试 model_dump 方法（Pydantic v2）
        if hasattr(input_obj, "model_dump"):
            dumped = input_obj.model_dump(by_alias=True)
            assert "from" in dumped
            assert dumped["from"] == 10
        else:
            # 测试 dict 方法（Pydantic v1）
            dumped = input_obj.dict(by_alias=True)
            assert "from" in dumped
            assert dumped["from"] == 10


class TestChunkInfo:
    """ChunkInfo 模型的测试"""

    def test_chunk_info_creation_default(self):
        """测试使用默认值创建 ChunkInfo"""
        chunk_info = ChunkInfo()
        assert chunk_info.file_id is None
        assert chunk_info.chunk is None

    def test_chunk_info_creation_with_values(self):
        """测试使用特定值创建 ChunkInfo"""
        # 创建一个模拟的 Chunk 对象
        mock_chunk = Mock(spec=Chunk)
        mock_chunk.id = "test_chunk_id"
        mock_chunk.content = "test content"
        
        chunk_info = ChunkInfo(
            file_id="test_file_id",
            chunk=mock_chunk
        )
        assert chunk_info.file_id == "test_file_id"
        assert chunk_info.chunk == mock_chunk
        assert chunk_info.chunk.id == "test_chunk_id"


class TestRecallChunkOutput:
    """RecallChunkOutput 模型的测试"""

    def test_recall_chunk_output_creation_default(self):
        """测试使用默认值创建 RecallChunkOutput"""
        output = RecallChunkOutput()
        assert output.drive_id is None
        assert output.chunks is None
        assert output.file_infos is None
        assert output.dst_parse_version is None

    def test_recall_chunk_output_creation_with_values(self):
        """测试使用特定值创建 RecallChunkOutput"""
        # 创建模拟对象
        mock_chunk_info = ChunkInfo(file_id="test_file")
        mock_file_info = FileInfo(file_type=FileType.PDF)
        
        output = RecallChunkOutput(
            drive_id="test_drive_id",
            chunks=[mock_chunk_info],
            file_infos=[mock_file_info],
            dst_parse_version="v1.0"
        )
        assert output.drive_id == "test_drive_id"
        assert len(output.chunks) == 1
        assert output.chunks[0] == mock_chunk_info
        assert len(output.file_infos) == 1
        assert output.file_infos[0] == mock_file_info
        assert output.dst_parse_version == "v1.0"


class TestRecallChunkClient:
    """RecallChunkClient 类的测试"""

    def test_init_method(self):
        """测试初始化方法"""
        # 由于整个类都被模拟了，我们只测试基本的实例化
        client = RecallChunkClient()
        assert client is not None

    def test_init_functionality(self):
        """测试初始化功能"""
        client = RecallChunkClient()
        # 测试 init 方法存在且可调用
        assert hasattr(client, 'init')
        assert callable(getattr(client, 'init'))

        # 测试方法调用不会抛出异常
        try:
            client.init("http://test.com", "test_ak", "test_sk")
        except Exception as e:
            # 如果有异常，确保不是因为方法不存在
            assert "has no attribute" not in str(e)

    def test_request_parse_result_method_exists(self):
        """测试 request_parse_result 方法存在"""
        client = RecallChunkClient()
        assert hasattr(client, 'request_parse_result')
        assert callable(getattr(client, 'request_parse_result'))

    def test_client_attributes(self):
        """测试客户端基本属性"""
        client = RecallChunkClient()
        # 测试基本属性存在
        assert hasattr(client, '_req')

    def test_model_validation_integration(self):
        """测试模型验证集成"""
        # 测试输入模型
        input_data = {
            "drive_id": "test_drive",
            "with_content": True
        }
        request = RecallChunkInput(**input_data)
        assert request.drive_id == "test_drive"
        assert request.with_content is True

        # 测试输出模型
        output_data = {
            "drive_id": "test_drive",
            "chunks": [],
            "file_infos": [],
            "dst_parse_version": "v1.0"
        }
        output = RecallChunkOutput(**output_data)
        assert output.drive_id == "test_drive"
        assert output.dst_parse_version == "v1.0"
