"""
modules.rpc.drive_v5_rpc 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import json

from modules.rpc.drive_v5_rpc import DriveV5Rpc
from modules.entity.drive_entity import DriveFileResponse
from commons.auth.auth_rpc import SigVerType


class TestDriveV5Rpc:
    """DriveV5Rpc 类的测试用例"""

    def test_drive_v5_rpc_singleton(self):
        """测试 DriveV5Rpc 是单例模式"""
        rpc1 = DriveV5Rpc()
        rpc2 = DriveV5Rpc()
        assert rpc1 is rpc2

    def test_drive_v5_rpc_initialization(self):
        """测试 DriveV5Rpc 初始化"""
        rpc = DriveV5Rpc()
        assert hasattr(rpc, '_req')
        assert hasattr(rpc, '_download_uri_template')
        assert rpc._req is None  # 初始化时为 None
        assert rpc._download_uri_template is None  # 初始化时为 None

    def test_init_method(self):
        """测试初始化方法"""
        rpc = DriveV5Rpc()

        host = "https://drive.example.com"
        ak = "test_ak"
        sk = "test_sk"
        sig_type = SigVerType.wps2
        download_uri = "/api/download"

        rpc.init(host, ak, sk, sig_type, download_uri)

        # 验证AuthRequest对象已创建
        assert rpc._req is not None
        assert rpc._download_uri_template == download_uri

    def test_init_method_with_kwargs(self):
        """测试带有额外关键字参数的初始化方法"""
        rpc = DriveV5Rpc()

        rpc.init(
            host="https://drive.example.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2,
            download_uri="/api/download",
            timeout=30,
            retries=3
        )

        # 验证AuthRequest对象已创建和下载URI模板已设置
        assert rpc._req is not None
        assert rpc._download_uri_template == "/api/download"

    @pytest.mark.asyncio
    async def test_aget_download_url_success(self):
        """测试成功异步获取下载URL"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟成功响应
        mock_response = {
            "result": "ok",
            "fileinfo": {
                "url": "https://example.com/download/123456"
            }
        }

        # 模拟AuthRequest的async_call方法
        mock_auth_request = AsyncMock()
        mock_auth_request.async_call.return_value = (200, json.dumps(mock_response))
        rpc._req = mock_auth_request

        result = await rpc.aget_download_url("123456")

        assert result.is_success is True
        assert result.url == "https://example.com/download/123456"
        mock_auth_request.async_call.assert_called_once_with("GET", "/download/123456")

    @pytest.mark.asyncio
    async def test_aget_download_url_api_error(self):
        """测试异步获取下载URL时API错误的情况"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟AuthRequest的async_call方法返回404错误
        mock_auth_request = AsyncMock()
        mock_auth_request.async_call.return_value = (404, "File not found")
        rpc._req = mock_auth_request

        result = await rpc.aget_download_url("invalid_file_id")

        assert result.is_success is False
        assert result.status_code == 404
        assert "HTTP error: status 404" in result.error_message

    @pytest.mark.asyncio
    async def test_aget_download_url_json_parse_error(self):
        """测试异步获取下载URL时JSON解析错误的情况"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟AuthRequest的async_call方法返回无效JSON
        mock_auth_request = AsyncMock()
        mock_auth_request.async_call.return_value = (200, "invalid json")
        rpc._req = mock_auth_request

        result = await rpc.aget_download_url("123456")

        assert result.is_success is False
        assert "Request failed:" in result.error_message

    @pytest.mark.asyncio
    async def test_aget_download_url_exception(self):
        """测试异步获取下载URL时异常的情况"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟AuthRequest的async_call方法抛出异常
        mock_auth_request = AsyncMock()
        mock_auth_request.async_call.side_effect = Exception("Network error")
        rpc._req = mock_auth_request

        result = await rpc.aget_download_url("123456")

        assert result.is_success is False
        assert "Request failed: Network error" in result.error_message

    def test_get_download_url_success(self):
        """测试成功同步获取下载URL"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟成功响应
        mock_response = {
            "result": "ok",
            "fileinfo": {
                "url": "https://example.com/download/123456"
            }
        }

        # 模拟AuthRequest的call方法
        mock_auth_request = MagicMock()
        mock_auth_request.call.return_value = (200, json.dumps(mock_response))
        rpc._req = mock_auth_request

        result = rpc.get_download_url("123456")

        assert result.is_success is True
        assert result.url == "https://example.com/download/123456"
        mock_auth_request.call.assert_called_once_with("GET", "/download/123456")

    def test_get_download_url_error(self):
        """测试同步获取下载URL时错误的情况"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟AuthRequest的call方法返回500错误
        mock_auth_request = MagicMock()
        mock_auth_request.call.return_value = (500, "Internal server error")
        rpc._req = mock_auth_request

        result = rpc.get_download_url("123456")

        assert result.is_success is False
        assert result.status_code == 500
        assert "HTTP error: status 500" in result.error_message

    def test_drive_v5_rpc_has_required_methods(self):
        """测试DriveV5Rpc具有所需的接口方法"""
        rpc = DriveV5Rpc()

        # 测试它实现了接口方法
        assert hasattr(rpc, 'aget_download_url')
        assert hasattr(rpc, 'get_download_url')
        assert hasattr(rpc, 'init')

        assert callable(rpc.aget_download_url)
        assert callable(rpc.get_download_url)
        assert callable(rpc.init)

    @pytest.mark.asyncio
    async def test_aget_download_url_api_response_error(self):
        """测试异步获取下载URL时API响应错误"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟AuthRequest返回API错误响应
        mock_auth_request = AsyncMock()
        mock_response = {
            "result": "error",
            "message": "File not found"
        }
        mock_auth_request.async_call.return_value = (200, json.dumps(mock_response))
        rpc._req = mock_auth_request

        result = await rpc.aget_download_url("123456")

        assert result.is_success is False
        assert "API response error: error" in result.error_message
        assert result.status_code == 200

    def test_get_download_url_api_response_error(self):
        """测试同步获取下载URL时API响应错误"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟AuthRequest返回API错误响应
        mock_auth_request = MagicMock()
        mock_response = {
            "result": "failed",
            "fileinfo": None
        }
        mock_auth_request.call.return_value = (200, json.dumps(mock_response))
        rpc._req = mock_auth_request

        result = rpc.get_download_url("123456")

        assert result.is_success is False
        assert "API response error: failed" in result.error_message
        assert result.status_code == 200

    def test_get_download_url_exception(self):
        """测试同步获取下载URL时异常"""
        rpc = DriveV5Rpc()
        rpc.init("https://drive.example.com", "ak", "sk", SigVerType.wps2, "/download/{file_id}")

        # 模拟AuthRequest抛出异常
        mock_auth_request = MagicMock()
        mock_auth_request.call.side_effect = Exception("Connection error")
        rpc._req = mock_auth_request

        result = rpc.get_download_url("123456")

        assert result.is_success is False
        assert "Request failed: Connection error" in result.error_message

    @patch('builtins.print')
    async def test_async_main_function_success(self, mock_print):
        """测试异步main函数成功情况"""
        # 创建RPC实例并初始化
        rpc = DriveV5Rpc()
        rpc.init("http://test.com", "ak", "sk", SigVerType.wps2, "/download")

        # 模拟成功响应
        with patch.object(rpc, 'aget_download_url') as mock_aget:
            mock_response = MagicMock()
            mock_response.is_success = True
            mock_response.url = "http://success.url"
            mock_aget.return_value = mock_response

            # 模拟_async函数的执行
            v5_file_id = "1001624877391"
            res = await rpc.aget_download_url(v5_file_id)

            if res.is_success:
                print(f"Success: {res.url}")
            else:
                print(f"Error: {res.error_message} (status: {res.status_code})")

            # 验证成功打印
            mock_print.assert_called_with("Success: http://success.url")

    @patch('builtins.print')
    async def test_async_main_function_error(self, mock_print):
        """测试异步main函数错误情况"""
        # 创建RPC实例并初始化
        rpc = DriveV5Rpc()
        rpc.init("http://test.com", "ak", "sk", SigVerType.wps2, "/download")

        # 模拟错误响应
        with patch.object(rpc, 'aget_download_url') as mock_aget:
            mock_response = MagicMock()
            mock_response.is_success = False
            mock_response.error_message = "Test error"
            mock_response.status_code = 404
            mock_aget.return_value = mock_response

            # 模拟_async函数的执行
            v5_file_id = "1001624877391"
            res = await rpc.aget_download_url(v5_file_id)

            if res.is_success:
                print(f"Success: {res.url}")
            else:
                print(f"Error: {res.error_message} (status: {res.status_code})")

            # 验证错误打印
            mock_print.assert_called_with("Error: Test error (status: 404)")

    def test_download_uri_template_formatting(self):
        """测试下载URI模板格式化"""
        rpc = DriveV5Rpc()

        # 测试不同的URI模板
        test_cases = [
            ("/api/v5/files/{file_id}/download", "123"),
            ("/download?id={file_id}", "456"),
            ("/files/{file_id}/get", "789")
        ]

        for template, file_id in test_cases:
            rpc.init("http://test.com", "ak", "sk", SigVerType.wps2, template)

            # 验证URI模板被正确设置
            assert rpc._download_uri_template == template
