"""
modules.rpc.drive_rpc_manager 模块的单元测试
"""
import pytest
import os
from unittest.mock import patch, MagicMock

from modules.rpc.drive_rpc_manager import DriveRpcManager, RpcConfigManager, _rpc_config_manager
from commons.auth.auth_rpc import SigVerType
from modules.entity.drive_entity import DriveParams


class TestDriveRpcManager:
    """DriveRpcManager类的测试用例"""

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_initialize_drive_rpc_success(self, mock_factory_class, mock_logging):
        """测试成功的Drive RPC初始化"""
        # 设置模拟
        mock_factory = MagicMock()
        mock_rpc_instance = MagicMock()
        
        mock_factory_class.return_value = mock_factory
        mock_factory.get_rpc_instance.return_value = mock_rpc_instance
        
        # 测试参数
        rpc_type = "wpsv5"
        host = "https://drive.example.com"
        ak = "test_access_key"
        sk = "test_secret_key"
        sig_type = SigVerType.wps2
        download_uri = "/api/download"
        
        # 调用方法
        DriveRpcManager.initialize_drive_rpc(
            type=rpc_type,
            host=host,
            ak=ak,
            sk=sk,
            sig_type=sig_type,
            download_uri=download_uri
        )
        
        # 验证工厂被正确调用
        mock_factory_class.assert_called_once()
        mock_factory.get_rpc_instance.assert_called_once_with(rpc_type)
        
        # 验证RPC实例被初始化
        mock_rpc_instance.init.assert_called_once_with(
            host, ak, sk, sig_type, download_uri
        )
        
        # 验证成功日志
        mock_logging.info.assert_called_once()
        assert "Successfully initialized Drive RPC" in mock_logging.info.call_args[0][0]

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_initialize_drive_rpc_factory_error(self, mock_factory_class, mock_logging):
        """测试Drive RPC初始化时工厂错误的情况"""
        # 设置模拟抛出异常
        mock_factory_class.side_effect = Exception("Factory creation failed")
        
        # Test parameters
        rpc_type = "wpsv5"
        host = "https://drive.example.com"
        ak = "test_access_key"
        sk = "test_secret_key"
        sig_type = SigVerType.wps2
        download_uri = "/api/download"
        
        # 调用方法并期望异常
        with pytest.raises(Exception) as exc_info:
            DriveRpcManager.initialize_drive_rpc(
                type=rpc_type,
                host=host,
                ak=ak,
                sk=sk,
                sig_type=sig_type,
                download_uri=download_uri
            )
        
        assert "Factory creation failed" in str(exc_info.value)
        
        # 验证错误日志
        mock_logging.error.assert_called_once()
        assert "Failed to initialize Drive RPC" in mock_logging.error.call_args[0][0]

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_initialize_drive_rpc_get_instance_error(self, mock_factory_class, mock_logging):
        """测试Drive RPC初始化时获取实例错误的情况"""
        # Setup mocks
        mock_factory = MagicMock()
        mock_factory_class.return_value = mock_factory
        mock_factory.get_rpc_instance.side_effect = ValueError("RPC type not registered")
        
        # Test parameters
        rpc_type = "invalid_type"
        host = "https://drive.example.com"
        ak = "test_access_key"
        sk = "test_secret_key"
        sig_type = SigVerType.wps2
        download_uri = "/api/download"
        
        # Call the method and expect exception
        with pytest.raises(ValueError) as exc_info:
            DriveRpcManager.initialize_drive_rpc(
                type=rpc_type,
                host=host,
                ak=ak,
                sk=sk,
                sig_type=sig_type,
                download_uri=download_uri
            )
        
        assert "RPC type not registered" in str(exc_info.value)
        
        # Verify error logging
        mock_logging.error.assert_called_once()
        assert "Failed to initialize Drive RPC" in mock_logging.error.call_args[0][0]

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_initialize_drive_rpc_init_error(self, mock_factory_class, mock_logging):
        """测试Drive RPC初始化时初始化错误的情况"""
        # Setup mocks
        mock_factory = MagicMock()
        mock_rpc_instance = MagicMock()
        
        mock_factory_class.return_value = mock_factory
        mock_factory.get_rpc_instance.return_value = mock_rpc_instance
        mock_rpc_instance.init.side_effect = Exception("RPC initialization failed")
        
        # Test parameters
        rpc_type = "wpsv5"
        host = "https://drive.example.com"
        ak = "test_access_key"
        sk = "test_secret_key"
        sig_type = SigVerType.wps2
        download_uri = "/api/download"
        
        # Call the method and expect exception
        with pytest.raises(Exception) as exc_info:
            DriveRpcManager.initialize_drive_rpc(
                type=rpc_type,
                host=host,
                ak=ak,
                sk=sk,
                sig_type=sig_type,
                download_uri=download_uri
            )
        
        assert "RPC initialization failed" in str(exc_info.value)
        
        # Verify error logging
        mock_logging.error.assert_called_once()
        assert "Failed to initialize Drive RPC" in mock_logging.error.call_args[0][0]

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_initialize_drive_rpc_with_different_sig_types(self, mock_factory_class, mock_logging):
        """测试Drive RPC初始化时使用不同签名类型"""
        # Setup mocks
        mock_factory = MagicMock()
        mock_rpc_instance = MagicMock()
        
        mock_factory_class.return_value = mock_factory
        mock_factory.get_rpc_instance.return_value = mock_rpc_instance
        
        # 测试不同的签名类型
        sig_types = [SigVerType.wps2, SigVerType.wps4]
        
        for sig_type in sig_types:
            DriveRpcManager.initialize_drive_rpc(
                type="wpsv5",
                host="https://drive.example.com",
                ak="test_ak",
                sk="test_sk",
                sig_type=sig_type,
                download_uri="/api/download"
            )
            
            # 验证签名类型被正确传递
            call_args = mock_rpc_instance.init.call_args
            assert call_args[0][3] == sig_type  # sig_type是第4个参数

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_initialize_drive_rpc_parameter_validation(self, mock_factory_class, mock_logging):
        """测试Drive RPC初始化参数验证"""
        # Setup mocks
        mock_factory = MagicMock()
        mock_rpc_instance = MagicMock()
        
        mock_factory_class.return_value = mock_factory
        mock_factory.get_rpc_instance.return_value = mock_rpc_instance
        
        # 测试各种参数组合
        test_cases = [
            {
                "type": "wpsv5",
                "host": "https://drive.example.com",
                "ak": "ak",
                "sk": "sk",
                "sig_type": SigVerType.wps2,
                "download_uri": "/download"
            },
            {
                "type": "custom_type",
                "host": "http://localhost:8080",
                "ak": "local_ak",
                "sk": "local_sk",
                "sig_type": SigVerType.wps4,
                "download_uri": "/api/v1/download"
            }
        ]
        
        for test_case in test_cases:
            DriveRpcManager.initialize_drive_rpc(**test_case)
            
            # 验证所有参数被正确传递
            call_args = mock_rpc_instance.init.call_args
            assert call_args[0][0] == test_case["host"]
            assert call_args[0][1] == test_case["ak"]
            assert call_args[0][2] == test_case["sk"]
            assert call_args[0][3] == test_case["sig_type"]
            assert call_args[0][4] == test_case["download_uri"]

    def test_drive_rpc_manager_is_class_method(self):
        """测试initialize_drive_rpc是类方法"""
        # 验证它是类方法并且可以在不实例化的情况下调用
        assert hasattr(DriveRpcManager, 'initialize_drive_rpc')
        assert callable(DriveRpcManager.initialize_drive_rpc)
        
        # 应该能够在不创建实例的情况下调用
        with patch('modules.rpc.drive_rpc_manager.DriveRpcFactory'):
            try:
                DriveRpcManager.initialize_drive_rpc(
                    type="test",
                    host="test",
                    ak="test",
                    sk="test",
                    sig_type=SigVerType.wps2,
                    download_uri="/test"
                )
            except Exception:
                pass  # 我们期望由于模拟而失败，但它应该是可调用的

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_initialize_drive_rpc_logging_messages(self, mock_factory_class, mock_logging):
        """测试生成适当的日志消息"""
        # 为成功情况设置模拟
        mock_factory = MagicMock()
        mock_rpc_instance = MagicMock()
        
        mock_factory_class.return_value = mock_factory
        mock_factory.get_rpc_instance.return_value = mock_rpc_instance
        
        host = "https://test.example.com"
        
        DriveRpcManager.initialize_drive_rpc(
            type="wpsv5",
            host=host,
            ak="ak",
            sk="sk",
            sig_type=SigVerType.wps2,
            download_uri="/download"
        )
        
        # 验证成功日志包含主机信息
        mock_logging.info.assert_called_once()
        log_message = mock_logging.info.call_args[0][0]
        assert "Successfully initialized Drive RPC" in log_message
        assert host in log_message


class TestRpcConfigManager:
    """RpcConfigManager类的测试用例"""

    def test_singleton_pattern(self):
        """测试单例模式"""
        instance1 = RpcConfigManager()
        instance2 = RpcConfigManager()
        assert instance1 is instance2
        assert instance1 is _rpc_config_manager

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_load_config_success(self, mock_logging, mock_conf):
        """测试成功加载配置"""
        # 设置模拟
        mock_conf.rpc_config = {"implementations": {"wpsv5": {"enabled": True}}}
        mock_conf.get_validated_implementations.return_value = {
            "wpsv5": MagicMock(enabled=True)
        }

        # 创建新实例来测试_load_config
        with patch.object(RpcConfigManager, '_instance', None):
            with patch.object(RpcConfigManager, '_config', None):
                manager = RpcConfigManager()

                # 验证配置被加载
                assert manager._config == {"implementations": {"wpsv5": {"enabled": True}}}

                # 验证日志
                mock_logging.info.assert_called_once()
                assert "RPC config loaded successfully" in mock_logging.info.call_args[0][0]

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_load_config_failure(self, mock_logging, mock_conf):
        """测试加载配置失败"""
        # 设置模拟抛出异常
        mock_conf.get_validated_implementations.side_effect = Exception("Config error")

        # 创建新实例来测试_load_config
        with patch.object(RpcConfigManager, '_instance', None):
            with patch.object(RpcConfigManager, '_config', None):
                with pytest.raises(ValueError) as exc_info:
                    RpcConfigManager()

                assert "Invalid RPC configuration" in str(exc_info.value)
                mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_get_implementations(self, mock_conf):
        """测试获取所有RPC实现配置"""
        # 设置模拟
        expected_impls = {"wpsv5": {"enabled": True}, "custom": {"enabled": False}}
        mock_conf.rpc_config = {"implementations": expected_impls}
        mock_conf.get_validated_implementations.return_value = {}

        # 重置配置管理器
        manager = RpcConfigManager()
        manager._config = {"implementations": expected_impls}

        # 测试方法
        result = manager.get_implementations()
        assert result == expected_impls

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_get_enabled_implementations_success(self, mock_logging, mock_conf):
        """测试成功获取启用的RPC实现"""
        # 设置模拟
        enabled_impl = MagicMock()
        enabled_impl.enabled = True
        disabled_impl = MagicMock()
        disabled_impl.enabled = False

        mock_conf.get_validated_implementations.return_value = {
            "wpsv5": enabled_impl,
            "disabled": disabled_impl
        }

        manager = RpcConfigManager()
        result = manager.get_enabled_implementations()

        # 验证只返回启用的实现
        assert "wpsv5" in result
        assert "disabled" not in result
        assert result["wpsv5"] == enabled_impl

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_get_enabled_implementations_error(self, mock_logging, mock_conf):
        """测试获取启用实现时出错"""
        # 设置模拟抛出异常
        mock_conf.get_validated_implementations.side_effect = Exception("Validation error")

        manager = RpcConfigManager()
        result = manager.get_enabled_implementations()

        # 验证返回空字典并记录错误
        assert result == {}
        mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_is_implementation_enabled_true(self, mock_conf):
        """测试检查实现是否启用（启用情况）"""
        # 设置模拟
        enabled_impl = MagicMock()
        enabled_impl.enabled = True
        mock_conf.get_implementation_config.return_value = enabled_impl

        manager = RpcConfigManager()
        result = manager.is_implementation_enabled("wpsv5")

        assert result is True
        mock_conf.get_implementation_config.assert_called_once_with("wpsv5")

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_is_implementation_enabled_false(self, mock_conf):
        """测试检查实现是否启用（禁用情况）"""
        # 设置模拟
        disabled_impl = MagicMock()
        disabled_impl.enabled = False
        mock_conf.get_implementation_config.return_value = disabled_impl

        manager = RpcConfigManager()
        result = manager.is_implementation_enabled("disabled_type")

        assert result is False

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_is_implementation_enabled_not_found(self, mock_conf):
        """测试检查不存在的实现"""
        # 设置模拟抛出ValueError
        mock_conf.get_implementation_config.side_effect = ValueError("Not found")

        manager = RpcConfigManager()
        result = manager.is_implementation_enabled("nonexistent")

        assert result is False

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_is_implementation_enabled_error(self, mock_logging, mock_conf):
        """测试检查实现启用状态时出错"""
        # 设置模拟抛出其他异常
        mock_conf.get_implementation_config.side_effect = Exception("Unexpected error")

        manager = RpcConfigManager()
        result = manager.is_implementation_enabled("error_type")

        assert result is False
        mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager.os.environ')
    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_get_rpc_config_success(self, mock_conf, mock_environ):
        """测试成功获取RPC配置"""
        # 设置模拟
        mock_drive_params = MagicMock()
        mock_drive_params.ak_env = "TEST_AK"
        mock_drive_params.sk_env = "TEST_SK"
        mock_drive_params.get_config_tuple.return_value = (
            "https://test.com", "ak_value", "sk_value", SigVerType.wps2, "/download"
        )

        mock_conf.get_implementation_config.return_value = mock_drive_params
        mock_environ.get.side_effect = lambda key: {"TEST_AK": "ak_value", "TEST_SK": "sk_value"}.get(key)

        manager = RpcConfigManager()
        result = manager.get_rpc_config("wpsv5")

        # 验证结果
        assert result == ("https://test.com", "ak_value", "sk_value", SigVerType.wps2, "/download")
        mock_drive_params.get_config_tuple.assert_called_once_with("ak_value", "sk_value")

    @patch('modules.rpc.drive_rpc_manager.os.environ')
    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_get_rpc_config_missing_ak(self, mock_conf, mock_environ):
        """测试获取RPC配置时缺少AK"""
        # 设置模拟
        mock_drive_params = MagicMock()
        mock_drive_params.ak_env = "MISSING_AK"
        mock_drive_params.sk_env = "TEST_SK"

        mock_conf.get_implementation_config.return_value = mock_drive_params
        mock_environ.get.side_effect = lambda key: {"TEST_SK": "sk_value"}.get(key)

        manager = RpcConfigManager()

        with pytest.raises(ValueError) as exc_info:
            manager.get_rpc_config("wpsv5")

        assert "Missing Access Key environment variable: MISSING_AK" in str(exc_info.value)

    @patch('modules.rpc.drive_rpc_manager.os.environ')
    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_get_rpc_config_missing_sk(self, mock_conf, mock_environ):
        """测试获取RPC配置时缺少SK"""
        # 设置模拟
        mock_drive_params = MagicMock()
        mock_drive_params.ak_env = "TEST_AK"
        mock_drive_params.sk_env = "MISSING_SK"

        mock_conf.get_implementation_config.return_value = mock_drive_params
        mock_environ.get.side_effect = lambda key: {"TEST_AK": "ak_value"}.get(key)

        manager = RpcConfigManager()

        with pytest.raises(ValueError) as exc_info:
            manager.get_rpc_config("wpsv5")

        assert "Missing Secret Key environment variable: MISSING_SK" in str(exc_info.value)

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_get_rpc_config_error(self, mock_logging, mock_conf):
        """测试获取RPC配置时出错"""
        # 设置模拟抛出异常
        mock_conf.get_implementation_config.side_effect = Exception("Config error")

        manager = RpcConfigManager()

        with pytest.raises(Exception):
            manager.get_rpc_config("error_type")

        mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_reload_config(self, mock_logging, mock_conf):
        """测试重新加载配置"""
        # 设置模拟
        mock_conf.rpc_config = {"implementations": {"reloaded": {"enabled": True}}}
        mock_conf.get_validated_implementations.return_value = {"reloaded": MagicMock()}

        manager = RpcConfigManager()
        original_config = manager._config

        # 重新加载配置
        manager.reload_config()

        # 验证配置被重新加载
        assert manager._config == {"implementations": {"reloaded": {"enabled": True}}}
        mock_logging.info.assert_called_with("RPC configuration reloaded from environment variables")

    def test_get_available_types(self):
        """测试获取可用类型"""
        manager = RpcConfigManager()

        # 模拟get_enabled_implementations方法
        with patch.object(manager, 'get_enabled_implementations') as mock_get_enabled:
            mock_get_enabled.return_value = {"wpsv5": MagicMock(), "custom": MagicMock()}

            result = manager.get_available_types()

            assert set(result) == {"wpsv5", "custom"}

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_get_implementation_info_success(self, mock_conf):
        """测试成功获取实现信息"""
        # 设置模拟
        expected_params = MagicMock()
        mock_conf.get_implementation_config.return_value = expected_params

        manager = RpcConfigManager()
        result = manager.get_implementation_info("wpsv5")

        assert result == expected_params
        mock_conf.get_implementation_config.assert_called_once_with("wpsv5")

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    def test_get_implementation_info_not_found(self, mock_conf):
        """测试获取不存在的实现信息"""
        # 设置模拟抛出ValueError
        mock_conf.get_implementation_config.side_effect = ValueError("Not found")

        manager = RpcConfigManager()
        result = manager.get_implementation_info("nonexistent")

        assert result is None

    @patch('modules.rpc.drive_rpc_manager.ConfDriveRpcConfig')
    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_get_implementation_info_error(self, mock_logging, mock_conf):
        """测试获取实现信息时出错"""
        # 设置模拟抛出其他异常
        mock_conf.get_implementation_config.side_effect = Exception("Unexpected error")

        manager = RpcConfigManager()
        result = manager.get_implementation_info("error_type")

        assert result is None
        mock_logging.error.assert_called_once()


class TestDriveRpcManagerAdditional:
    """DriveRpcManager类的额外测试用例"""

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    def test_auto_initialize_success(self, mock_config_manager, mock_logging):
        """测试自动初始化成功"""
        # 设置模拟
        mock_config_manager.get_available_types.return_value = ["wpsv5", "custom"]

        with patch.object(DriveRpcManager, 'initialize_by_type') as mock_init_by_type:
            with patch.object(DriveRpcManager, 'get_available_types', return_value=["wpsv5", "custom"]):
                # 调用方法
                DriveRpcManager.auto_initialize()

                # 验证初始化被调用
                assert mock_init_by_type.call_count == 2
                mock_init_by_type.assert_any_call("wpsv5")
                mock_init_by_type.assert_any_call("custom")

                # 验证日志
                mock_logging.info.assert_any_call("Found enabled RPC types: ['wpsv5', 'custom']")
                mock_logging.info.assert_any_call("Auto-initialized 2 Drive RPC implementations")

    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_auto_initialize_no_enabled_types(self, mock_logging):
        """测试自动初始化时没有启用的类型"""
        with patch.object(DriveRpcManager, 'get_available_types', return_value=[]):
            with pytest.raises(ValueError) as exc_info:
                DriveRpcManager.auto_initialize()

            assert "No enabled RPC implementations found in config" in str(exc_info.value)
            mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_auto_initialize_partial_failure(self, mock_logging):
        """测试自动初始化部分失败"""
        with patch.object(DriveRpcManager, 'get_available_types', return_value=["wpsv5", "failing"]):
            with patch.object(DriveRpcManager, 'initialize_by_type') as mock_init_by_type:
                # 设置一个成功，一个失败
                mock_init_by_type.side_effect = [None, Exception("Init failed")]

                # 调用方法
                DriveRpcManager.auto_initialize()

                # 验证日志
                mock_logging.info.assert_any_call("Successfully initialized RPC type: wpsv5")
                mock_logging.warning.assert_any_call("Failed to initialize RPC type failing: Init failed")
                mock_logging.info.assert_any_call("Auto-initialized 1 Drive RPC implementations")
                mock_logging.warning.assert_any_call("Failed to initialize types: ['failing']")

    @patch('modules.rpc.drive_rpc_manager.logging')
    def test_auto_initialize_all_failed(self, mock_logging):
        """测试自动初始化全部失败"""
        with patch.object(DriveRpcManager, 'get_available_types', return_value=["failing1", "failing2"]):
            with patch.object(DriveRpcManager, 'initialize_by_type') as mock_init_by_type:
                # 设置全部失败
                mock_init_by_type.side_effect = Exception("Init failed")

                with pytest.raises(ValueError) as exc_info:
                    DriveRpcManager.auto_initialize()

                assert "Failed to initialize any RPC implementations" in str(exc_info.value)
                assert "Failed types: ['failing1', 'failing2']" in str(exc_info.value)

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    def test_initialize_by_type_success(self, mock_config_manager, mock_logging):
        """测试按类型初始化成功"""
        # 设置模拟
        mock_config_manager.is_implementation_enabled.return_value = True
        mock_config_manager.get_rpc_config.return_value = (
            "https://test.com", "ak", "sk", SigVerType.wps2, "/download"
        )

        with patch.object(DriveRpcManager, 'initialize_drive_rpc') as mock_init_drive_rpc:
            # 调用方法
            DriveRpcManager.initialize_by_type("wpsv5")

            # 验证调用
            mock_config_manager.is_implementation_enabled.assert_called_once_with("wpsv5")
            mock_config_manager.get_rpc_config.assert_called_once_with("wpsv5")
            mock_init_drive_rpc.assert_called_once_with(
                type="wpsv5",
                host="https://test.com",
                ak="ak",
                sk="sk",
                sig_type=SigVerType.wps2,
                download_uri="/download"
            )

            # 验证日志
            mock_logging.info.assert_called_with("Successfully initialized Drive RPC with type: wpsv5")

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    def test_initialize_by_type_not_enabled(self, mock_config_manager, mock_logging):
        """测试按类型初始化时类型未启用"""
        # 设置模拟
        mock_config_manager.is_implementation_enabled.return_value = False

        with pytest.raises(ValueError) as exc_info:
            DriveRpcManager.initialize_by_type("disabled_type")

        assert "RPC type disabled_type is not enabled in configuration" in str(exc_info.value)
        mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    def test_initialize_by_type_config_error(self, mock_config_manager, mock_logging):
        """测试按类型初始化时配置错误"""
        # 设置模拟
        mock_config_manager.is_implementation_enabled.return_value = True
        mock_config_manager.get_rpc_config.side_effect = Exception("Config error")

        with pytest.raises(Exception):
            DriveRpcManager.initialize_by_type("error_type")

        mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    @patch('modules.rpc.drive_rpc_manager.DriveRpcFactory')
    def test_get_available_types(self, mock_factory_class, mock_config_manager):
        """测试获取可用类型"""
        # 设置模拟
        mock_config_manager.get_available_types.return_value = ["wpsv5", "custom", "extra"]

        mock_factory = MagicMock()
        mock_factory.get_available_types.return_value = ["wpsv5", "custom", "other"]
        mock_factory_class.return_value = mock_factory

        # 调用方法
        result = DriveRpcManager.get_available_types()

        # 验证结果是两者的交集
        assert set(result) == {"wpsv5", "custom"}

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    def test_get_config_info_success(self, mock_config_manager, mock_logging):
        """测试成功获取配置信息"""
        # 设置模拟
        mock_params = MagicMock()
        mock_params.class_path = "test.path"
        mock_params.host = "https://test.com"
        mock_params.sig_type = SigVerType.wps2
        mock_params.enabled = True

        mock_config_manager.get_enabled_implementations.return_value = {
            "wpsv5": mock_params
        }

        # 调用方法
        result = DriveRpcManager.get_config_info()

        # 验证结果
        expected = {
            "available_types": ["wpsv5"],
            "enabled_implementations": {
                "wpsv5": {
                    "class_path": "test.path",
                    "host": "https://test.com",
                    "sig_type": "wps2",
                    "enabled": True
                }
            }
        }
        assert result == expected

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    def test_get_config_info_error(self, mock_config_manager, mock_logging):
        """测试获取配置信息时出错"""
        # 设置模拟抛出异常
        mock_config_manager.get_enabled_implementations.side_effect = Exception("Config error")

        # 调用方法
        result = DriveRpcManager.get_config_info()

        # 验证错误结果
        expected = {
            "available_types": [],
            "enabled_implementations": {},
            "error": "Config error"
        }
        assert result == expected
        mock_logging.error.assert_called_once()

    @patch('modules.rpc.drive_rpc_manager.logging')
    @patch('modules.rpc.drive_rpc_manager._rpc_config_manager')
    def test_reload_config(self, mock_config_manager, mock_logging):
        """测试重新加载配置"""
        # 调用方法
        DriveRpcManager.reload_config()

        # 验证调用
        mock_config_manager.reload_config.assert_called_once()
        mock_logging.info.assert_called_with("Drive RPC configuration reloaded from environment variables")
