"""
modules.rpc.kdc_rpc 模块的单元测试
"""
import pytest
import asyncio
import json
import time
from unittest.mock import patch, MagicMock, AsyncMock
from requests_toolbelt import MultipartEncoder

from modules.rpc.kdc_rpc import KDCRpc, create_task_with_name, PAGE_SIZE
from services.datamodel import FileType


class TestCreateTaskWithName:
    """create_task_with_name函数的测试用例"""

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_create_task_with_name_success(self, mock_wps365api):
        """测试成功创建任务"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={"result": "success"})
        
        # 测试参数
        uri = "/test/uri"
        post_body = {"test": "body"}
        post_form_data = None
        company_id = "test_company"
        use_async_func = True
        task_name = "test_task"
        
        # 调用函数
        result, returned_task_name = await create_task_with_name(
            uri, post_body, post_form_data, company_id, use_async_func, task_name
        )
        
        # 验证结果
        assert result == {"result": "success"}
        assert returned_task_name == task_name
        
        # 验证API调用
        mock_api_instance.apost.assert_called_once_with(
            uri=uri,
            body=post_body,
            form_data=post_form_data,
            resp_model=None,
            company_id=company_id,
            use_async_func=use_async_func
        )


class TestKDCRpc:
    """KDCRpc类的测试用例"""

    def test_singleton_pattern(self):
        """测试单例模式"""
        instance1 = KDCRpc()
        instance2 = KDCRpc()
        assert instance1 is instance2

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_success(self, mock_wps365api):
        """测试异步获取内容成功"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.aget = AsyncMock(return_value={"content": "test_content"})
        
        # 测试参数
        company_id = "test_company"
        drive_id = "test_drive"
        v7_file_id = "test_file"
        format = "kdc"
        include_elements = ["para", "table"]
        
        # 调用方法
        result = await KDCRpc.aget_content(
            company_id=company_id,
            drive_id=drive_id,
            v7_file_id=v7_file_id,
            format=format,
            include_elements=include_elements
        )
        
        # 验证结果
        assert result == {"content": "test_content"}
        
        # 验证API调用
        expected_uri = f"/v7/drives/{drive_id}/files/{v7_file_id}/content"
        expected_params = {
            "format": format,
            "include_elements": include_elements
        }
        mock_api_instance.aget.assert_called_once_with(
            uri=expected_uri,
            params=expected_params,
            resp_model=None,
            company_id=company_id
        )

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_with_all_params(self, mock_wps365api):
        """测试异步获取内容时传入所有参数"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.aget = AsyncMock(return_value={"content": "test_content"})
        
        # 测试参数
        company_id = "test_company"
        drive_id = "test_drive"
        v7_file_id = "test_file"
        format = "markdown"
        include_elements = ["all"]
        disable_pdf_detection = True
        page_range = "1-10"
        enable_multi_threading = True
        task_duration = "30"
        enable_upload_medias = True
        
        # 调用方法
        result = await KDCRpc.aget_content(
            company_id=company_id,
            drive_id=drive_id,
            v7_file_id=v7_file_id,
            format=format,
            include_elements=include_elements,
            disable_pdf_detection=disable_pdf_detection,
            page_range=page_range,
            enable_multi_threading=enable_multi_threading,
            task_duration=task_duration,
            enable_upload_medias=enable_upload_medias
        )
        
        # 验证结果
        assert result == {"content": "test_content"}
        
        # 验证API调用参数
        expected_params = {
            "format": format,
            "include_elements": include_elements,
            "disable_pdf_detection": disable_pdf_detection,
            "page_range": page_range,
            "enable_multi_threading": enable_multi_threading,
            "task_duration": task_duration,
            "enable_upload_medias": enable_upload_medias
        }
        mock_api_instance.aget.assert_called_once()
        call_args = mock_api_instance.aget.call_args
        assert call_args[1]["params"] == expected_params

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_success(self, mock_wps365api):
        """测试同步获取内容成功"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.get.return_value = {"content": "test_content"}
        
        # 测试参数
        company_id = "test_company"
        drive_id = "test_drive"
        v7_file_id = "test_file"
        
        # 调用方法
        result = KDCRpc.get_content(
            company_id=company_id,
            drive_id=drive_id,
            v7_file_id=v7_file_id
        )
        
        # 验证结果
        assert result == {"content": "test_content"}
        
        # 验证API调用
        expected_uri = f"/v7/drives/{drive_id}/files/{v7_file_id}/content"
        mock_api_instance.get.assert_called_once_with(
            uri=expected_uri,
            params={},
            resp_model=None,
            company_id=company_id
        )

    def test_open_args_model(self):
        """测试OpenArgs模型"""
        open_args = KDCRpc.OpenArgs(password="test_password")
        assert open_args.password == "test_password"
        assert open_args.dict() == {"password": "test_password"}

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_with_url(self, mock_wps365api):
        """测试通过URL异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "test_content"},
            "file_info": {"total_page_num": 5}
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"
        format = "kdc"
        filename = "test.pdf"

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            format=format,
            filename=filename
        )

        # 验证结果
        assert result is not None
        assert len(result) == 1
        assert result[0]["doc"]["content"] == "test_content"

        # 验证API调用
        mock_api_instance.apost.assert_called_once()
        call_args = mock_api_instance.apost.call_args
        assert call_args[1]["uri"] == "/v7/longtask/exporter/export_file_content"
        assert call_args[1]["body"]["url"] == file_url
        assert call_args[1]["body"]["format"] == format
        assert call_args[1]["body"]["filename"] == filename

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_with_bytes(self, mock_wps365api):
        """测试通过字节流异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "test_content"},
            "file_info": {"total_page_num": 3}
        })

        # 测试参数
        company_id = "test_company"
        file_bytes = b"test file content"
        format = "markdown"
        filename = "test.docx"

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_bytes,
            format=format,
            filename=filename
        )

        # 验证结果
        assert result is not None
        assert len(result) == 1
        assert result[0]["doc"]["content"] == "test_content"

        # 验证API调用
        mock_api_instance.apost.assert_called_once()
        call_args = mock_api_instance.apost.call_args
        assert call_args[1]["uri"] == "/v7/longtask/exporter/export_file_content"
        assert call_args[1]["form_data"] is not None
        assert call_args[1]["body"] is None

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    @patch('modules.rpc.kdc_rpc.MultiCoroutine')
    async def test_aget_content_by_url_or_file_pdf_multiple_pages(self, mock_multicoroutine, mock_wps365api):
        """测试PDF文件多页解析"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance

        # 第一次调用返回多页PDF信息
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "page1_content"},
            "file_info": {"total_page_num": 20}  # 超过PAGE_SIZE(8)
        })

        # 模拟多协程池
        mock_pool = MagicMock()
        mock_multicoroutine.return_value = mock_pool
        mock_pool.run_limit = AsyncMock(return_value={
            "kdc_task_page_9_16": ({"doc": {"content": "page2_content"}}, "page_9_16"),
            "kdc_task_page_17_20": ({"doc": {"content": "page3_content"}}, "page_17_20")
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"
        format = "kdc"
        filename = "test.pdf"
        file_type = FileType.PDF

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            format=format,
            filename=filename,
            file_type=file_type
        )

        # 验证结果
        assert result is not None
        assert len(result) == 3  # 第一页 + 2个额外的页面范围
        assert result[0]["doc"]["content"] == "page1_content"

        # 验证多协程池被使用
        mock_multicoroutine.assert_called_once()
        mock_pool.run_limit.assert_called_once()

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_with_open_args(self, mock_wps365api):
        """测试带有打开参数的异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "encrypted_content"},
            "file_info": {"total_page_num": 2}
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/encrypted.docx"
        open_args = KDCRpc.OpenArgs(password="secret123")

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            open_args=open_args
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "encrypted_content"

        # 验证API调用包含密码
        call_args = mock_api_instance.apost.call_args
        assert call_args[1]["body"]["open_args"] == {"password": "secret123"}

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_with_convert_options(self, mock_wps365api):
        """测试带有转换选项的异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "converted_content"},
            "file_info": {"total_page_num": 3}
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"
        convert_options = {
            "disable_pdf_detection": True,
            "page_range": "1-5",
            "fixed_chunk_size": True
        }

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            convert_options=convert_options
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "converted_content"

        # 验证API调用包含转换选项
        call_args = mock_api_instance.apost.call_args
        assert call_args[1]["body"]["convert_options"] == convert_options

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_poll_task_status_success(self, mock_wps365api):
        """测试轮询任务状态成功"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance

        # 模拟轮询过程：第一次返回running，第二次返回finished
        mock_api_instance.aget = AsyncMock(side_effect=[
            {"status": "running"},
            {"status": "finished", "data": {"result": "completed"}}
        ])

        # 测试参数
        task_id = "test_task_123"
        company_id = "test_company"

        # 调用方法
        result = await KDCRpc.poll_task_status(task_id, company_id)

        # 验证结果
        assert result == {"result": "completed"}

        # 验证API调用次数
        assert mock_api_instance.aget.call_count == 2

        # 验证API调用参数
        expected_uri = f"/v7/coop/asyn_export/{task_id}/query_job"
        for call in mock_api_instance.aget.call_args_list:
            assert call[1]["uri"] == expected_uri
            assert call[1]["company_id"] == company_id

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_poll_task_status_failed(self, mock_wps365api):
        """测试轮询任务状态失败"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.aget = AsyncMock(return_value={
            "status": "failed",
            "error": "Task processing failed"
        })

        # 测试参数
        task_id = "test_task_123"
        company_id = "test_company"

        # 调用方法并期望异常
        with pytest.raises(ValueError) as exc_info:
            await KDCRpc.poll_task_status(task_id, company_id)

        assert "任务 test_task_123 失败" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.time')
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_poll_task_status_timeout(self, mock_wps365api, mock_time):
        """测试轮询任务状态超时"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.aget = AsyncMock(return_value={"status": "running"})

        # 模拟时间超时
        mock_time.time.side_effect = [0, 3700]  # 超过3600秒限制

        # 测试参数
        task_id = "test_task_123"
        company_id = "test_company"

        # 调用方法并期望超时异常
        with pytest.raises(TimeoutError) as exc_info:
            await KDCRpc.poll_task_status(task_id, company_id)

        assert "轮询时间超过最大限制 3600 秒" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_async_aget_content_by_url_or_file_success(self, mock_wps365api):
        """测试异步获取内容（带轮询）成功"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance

        # 模拟创建任务返回task_id
        mock_api_instance.apost = AsyncMock(return_value={"task_id": "test_task_123"})

        # 模拟轮询返回结果
        mock_api_instance.aget = AsyncMock(return_value={
            "status": "finished",
            "data": {"result": "async_completed"}
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"

        # 调用方法
        result = await KDCRpc.async_aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url
        )

        # 验证结果
        assert result is not None
        assert len(result) == 1
        assert result[0] == {"result": "async_completed"}

        # 验证API调用
        mock_api_instance.apost.assert_called_once()
        mock_api_instance.aget.assert_called_once()

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_async_aget_content_by_url_or_file_no_task_id(self, mock_wps365api):
        """测试异步获取内容时没有返回task_id"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={"error": "no task_id"})

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"

        # 调用方法并期望异常
        with pytest.raises(ValueError) as exc_info:
            await KDCRpc.async_aget_content_by_url_or_file(
                company_id=company_id,
                file_url_or_bytes=file_url
            )

        assert "未能从响应中获取 task_id" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_async_aget_content_by_url_or_file_poll_error(self, mock_wps365api):
        """测试异步获取内容时轮询出错"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance

        # 模拟创建任务返回task_id
        mock_api_instance.apost = AsyncMock(return_value={"task_id": "test_task_123"})

        # 模拟轮询出错
        mock_api_instance.aget = AsyncMock(side_effect=Exception("Poll error"))

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"

        # 调用方法并期望异常
        with pytest.raises(RuntimeError) as exc_info:
            await KDCRpc.async_aget_content_by_url_or_file(
                company_id=company_id,
                file_url_or_bytes=file_url
            )

        assert "轮询任务状态时出错" in str(exc_info.value)

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_by_url_or_file_with_url(self, mock_wps365api):
        """测试通过URL同步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.post.return_value = {
            "doc": {"content": "sync_content"},
            "file_info": {"total_page_num": 3}
        }

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.docx"
        format = "plain"
        filename = "test.docx"

        # 调用方法
        result = KDCRpc.get_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            format=format,
            filename=filename
        )

        # 验证结果
        assert result is not None
        assert len(result) == 1
        assert result[0]["doc"]["content"] == "sync_content"

        # 验证API调用
        mock_api_instance.post.assert_called_once()
        call_args = mock_api_instance.post.call_args
        assert call_args[1]["uri"] == "/v7/longtask/exporter/export_file_content"
        assert call_args[1]["body"]["url"] == file_url
        assert call_args[1]["body"]["format"] == format

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_by_url_or_file_with_bytes(self, mock_wps365api):
        """测试通过字节流同步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.post.return_value = {
            "doc": {"content": "sync_bytes_content"},
            "file_info": {"total_page_num": 2}
        }

        # 测试参数
        company_id = "test_company"
        file_bytes = b"test file content"
        format = "kdc"
        filename = "test.pdf"

        # 调用方法
        result = KDCRpc.get_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_bytes,
            format=format,
            filename=filename
        )

        # 验证结果
        assert result is not None
        assert len(result) == 1
        assert result[0]["doc"]["content"] == "sync_bytes_content"

        # 验证API调用
        mock_api_instance.post.assert_called_once()
        call_args = mock_api_instance.post.call_args
        assert call_args[1]["form_data"] is not None
        assert call_args[1]["body"] is None

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_by_url_or_file_pdf_multiple_pages(self, mock_wps365api):
        """测试PDF文件多页同步解析"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance

        # 第一次调用返回多页PDF信息
        first_response = {
            "doc": {"content": "page1_content"},
            "file_info": {"total_page_num": 20}  # 超过PAGE_SIZE(8)
        }

        # 后续调用返回其他页面
        additional_responses = [
            {"doc": {"content": f"page{i}_content"}} for i in range(2, 4)
        ]

        mock_api_instance.post.side_effect = [first_response] + additional_responses

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"
        filename = "test.pdf"  # 包含"pdf"触发多页解析

        # 调用方法
        result = KDCRpc.get_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            filename=filename
        )

        # 验证结果
        assert result is not None
        assert len(result) >= 1  # 至少有第一页
        assert result[0]["doc"]["content"] == "page1_content"

        # 验证API被多次调用（分页）
        assert mock_api_instance.post.call_count >= 1

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_by_url_or_file_with_convert_options(self, mock_wps365api):
        """测试带有转换选项的同步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.post.return_value = {
            "doc": {"content": "converted_sync_content"},
            "file_info": {"total_page_num": 1}
        }

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.docx"
        convert_options = {
            "disable_pdf_detection": False,
            "page_range": "1-3"
        }

        # 调用方法
        result = KDCRpc.get_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            convert_options=convert_options
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "converted_sync_content"

        # 验证API调用包含转换选项
        call_args = mock_api_instance.post.call_args
        assert call_args[1]["body"]["convert_options"] == convert_options

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_by_url_or_file_with_open_args(self, mock_wps365api):
        """测试带有打开参数的同步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.post.return_value = {
            "doc": {"content": "encrypted_sync_content"},
            "file_info": {"total_page_num": 1}
        }

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/encrypted.docx"
        filename = "encrypted.docx"  # 需要提供filename避免None错误
        open_args = KDCRpc.OpenArgs(password="sync_password")

        # 调用方法
        result = KDCRpc.get_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            filename=filename,
            open_args=open_args
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "encrypted_sync_content"

        # 验证API调用包含密码
        call_args = mock_api_instance.post.call_args
        assert call_args[1]["body"]["open_args"] == {"password": "sync_password"}

    def test_page_size_constant(self):
        """测试PAGE_SIZE常量"""
        assert PAGE_SIZE == 8

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_with_request_id(self, mock_wps365api):
        """测试带有request_id的异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "content_with_request_id"},
            "file_info": {"total_page_num": 1}
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"
        request_id = "req_123456"

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            request_id=request_id
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "content_with_request_id"

        # 验证API调用包含request_id
        call_args = mock_api_instance.apost.call_args
        expected_uri = f"/v7/longtask/exporter/export_file_content?request_id={request_id}"
        assert call_args[1]["uri"] == expected_uri

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_with_all_optional_params(self, mock_wps365api):
        """测试同步获取内容时传入所有可选参数"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.get.return_value = {"content": "test_content"}

        # 测试参数
        company_id = "test_company"
        drive_id = "test_drive"
        v7_file_id = "test_file"
        format = "markdown"
        include_elements = ["all"]
        disable_pdf_detection = True
        page_range = "1-10"
        enable_multi_threading = True
        task_duration = "30"
        enable_upload_medias = True

        # 调用方法
        result = KDCRpc.get_content(
            company_id=company_id,
            drive_id=drive_id,
            v7_file_id=v7_file_id,
            format=format,
            include_elements=include_elements,
            disable_pdf_detection=disable_pdf_detection,
            page_range=page_range,
            enable_multi_threading=enable_multi_threading,
            task_duration=task_duration,
            enable_upload_medias=enable_upload_medias
        )

        # 验证结果
        assert result == {"content": "test_content"}

        # 验证API调用参数
        expected_params = {
            "format": format,
            "include_elements": include_elements,
            "disable_pdf_detection": disable_pdf_detection,
            "page_range": page_range,
            "enable_multi_threading": enable_multi_threading,
            "task_duration": task_duration,
            "enable_upload_medias": enable_upload_medias
        }
        mock_api_instance.get.assert_called_once()
        call_args = mock_api_instance.get.call_args
        assert call_args[1]["params"] == expected_params

    @patch('modules.rpc.kdc_rpc.Wps365api')
    def test_get_content_with_no_optional_params(self, mock_wps365api):
        """测试同步获取内容时不传入可选参数"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.get.return_value = {"content": "test_content"}

        # 测试参数（只传必需参数）
        company_id = "test_company"
        drive_id = "test_drive"
        v7_file_id = "test_file"

        # 调用方法
        result = KDCRpc.get_content(
            company_id=company_id,
            drive_id=drive_id,
            v7_file_id=v7_file_id
        )

        # 验证结果
        assert result == {"content": "test_content"}

        # 验证API调用参数为空字典
        mock_api_instance.get.assert_called_once()
        call_args = mock_api_instance.get.call_args
        assert call_args[1]["params"] == {}

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_with_none_optional_params(self, mock_wps365api):
        """测试异步获取内容时可选参数为None"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.aget = AsyncMock(return_value={"content": "test_content"})

        # 测试参数（可选参数显式设为None）
        company_id = "test_company"
        drive_id = "test_drive"
        v7_file_id = "test_file"

        # 调用方法
        result = await KDCRpc.aget_content(
            company_id=company_id,
            drive_id=drive_id,
            v7_file_id=v7_file_id,
            format=None,
            include_elements=None,
            disable_pdf_detection=None,
            page_range=None,
            enable_multi_threading=None,
            task_duration=None,
            enable_upload_medias=None
        )

        # 验证结果
        assert result == {"content": "test_content"}

        # 验证API调用参数为空字典（None值不会被添加）
        mock_api_instance.aget.assert_called_once()
        call_args = mock_api_instance.aget.call_args
        assert call_args[1]["params"] == {}

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_with_pdf_edet_opt(self, mock_wps365api):
        """测试带有pdf_edet_opt参数的异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "pdf_edet_content"},
            "file_info": {"total_page_num": 1}
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"
        pdf_edet_opt = {"option": "value"}

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            pdf_edet_opt=pdf_edet_opt
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "pdf_edet_content"

        # 验证API调用包含pdf_edet_opt
        call_args = mock_api_instance.apost.call_args
        assert call_args[1]["body"]["pdf_edet_opt"] == pdf_edet_opt

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_with_include_elements(self, mock_wps365api):
        """测试带有include_elements参数的异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "elements_content"},
            "file_info": {"total_page_num": 1}
        })

        # 测试参数
        company_id = "test_company"
        file_url = "https://example.com/test.docx"
        include_elements = ["para", "table", "image"]

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url,
            include_elements=include_elements
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "elements_content"

        # 验证API调用包含include_elements
        call_args = mock_api_instance.apost.call_args
        assert call_args[1]["body"]["include_elements"] == include_elements

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_without_filename(self, mock_wps365api):
        """测试不带filename参数的异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "no_filename_content"},
            "file_info": {"total_page_num": 1}
        })

        # 测试参数（不传filename）
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"

        # 调用方法
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url
            # 不传filename参数
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "no_filename_content"

        # 验证API调用不包含filename
        call_args = mock_api_instance.apost.call_args
        assert "filename" not in call_args[1]["body"]

    @pytest.mark.asyncio
    @patch('modules.rpc.kdc_rpc.Wps365api')
    async def test_aget_content_by_url_or_file_without_optional_params(self, mock_wps365api):
        """测试不带可选参数的异步获取内容"""
        # 设置模拟
        mock_api_instance = MagicMock()
        mock_wps365api.return_value = mock_api_instance
        mock_api_instance.apost = AsyncMock(return_value={
            "doc": {"content": "minimal_content"},
            "file_info": {"total_page_num": 1}
        })

        # 测试参数（只传必需参数）
        company_id = "test_company"
        file_url = "https://example.com/test.pdf"

        # 调用方法（不传任何可选参数）
        result = await KDCRpc.aget_content_by_url_or_file(
            company_id=company_id,
            file_url_or_bytes=file_url
        )

        # 验证结果
        assert result is not None
        assert result[0]["doc"]["content"] == "minimal_content"

        # 验证API调用只包含必需字段
        call_args = mock_api_instance.apost.call_args
        body = call_args[1]["body"]
        assert "url" in body
        # format有默认值，但可能不在body中
        assert "pdf_edet_opt" not in body
        assert "include_elements" not in body
        assert "open_args" not in body
        assert "filename" not in body
