"""
modules.entity.kdc_enttiy 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock
from typing import List, Optional

from modules.entity.kdc_enttiy import (
    # Enums
    OutlineLevel, RotateType, ParaAlignment, ComponentType, RunType, BlockType,
    SheetType, OperatorType, DBStatisticType, DBNodeType, DBFieldType,
    SlideContainerCategory, LayoutType,
    
    # Basic models
    Highlight, HyperLink, Reference, Rectangle, Size, ParaIndent, PageProp,
    DocProp, PPTProp, TableCell, TableRow, ParaProp, FootNote, EndNote,
    Footer, Header, Comment, Media, Component, TextBox, Table, Para, Run,
    Block, Node, Document,
    
    # Constants
    KDC_DOC_FIELD
)


class TestConstants:
    """常量的测试用例"""

    def test_kdc_doc_field(self):
        """测试KDC_DOC_FIELD常量"""
        assert KDC_DOC_FIELD == "doc"


class TestOutlineLevel:
    """OutlineLevel枚举的测试用例"""

    def test_outline_level_values(self):
        """测试OutlineLevel枚举值"""
        assert OutlineLevel.l1 == 1
        assert OutlineLevel.l2 == 2
        assert OutlineLevel.l3 == 3
        assert OutlineLevel.l4 == 4
        assert OutlineLevel.l5 == 5
        assert OutlineLevel.l6 == 6
        assert OutlineLevel.l7 == 7
        assert OutlineLevel.l8 == 8
        assert OutlineLevel.l9 == 9
        assert OutlineLevel.l10 == 10


class TestRotateType:
    """RotateType枚举的测试用例"""

    def test_rotate_type_values(self):
        """测试RotateType枚举值"""
        assert RotateType.rotate_0 == 0
        assert RotateType.rotate90 == 90
        assert RotateType.rotate180 == 180
        assert RotateType.rotate270 == 270


class TestParaAlignment:
    """ParaAlignment枚举的测试用例"""

    def test_para_alignment_values(self):
        """测试ParaAlignment枚举值"""
        assert ParaAlignment.left == "left"
        assert ParaAlignment.center == "center"
        assert ParaAlignment.right == "right"
        assert ParaAlignment.justify == "justify"
        assert ParaAlignment.distribute == "distribute"
        assert ParaAlignment.fill == "fill"
        assert ParaAlignment.center_continuous == "center_continuous"
        assert ParaAlignment.general == "general"


class TestComponentType:
    """ComponentType枚举的测试用例"""

    def test_component_type_values(self):
        """测试ComponentType枚举值"""
        assert ComponentType.image == "image"
        assert ComponentType.audio == "audio"
        assert ComponentType.video == "video"
        assert ComponentType.chart == "chart"
        assert ComponentType.dbsheet == "dbsheet"
        assert ComponentType.mindmap == "mindmap"
        assert ComponentType.flowchart == "flowchart"
        assert ComponentType.spreadsheet == "spreadsheet"
        assert ComponentType.other == "other"

    def test_component_type_missing_value(self):
        """测试ComponentType的_missing_方法"""
        # 测试未知值被转换为'other'
        unknown_type = ComponentType._missing_("unknown_type")
        assert unknown_type == ComponentType.other


class TestRunType:
    """RunType枚举的测试用例"""

    def test_run_type_values(self):
        """测试RunType枚举值"""
        assert RunType.normal == "normal"
        assert RunType.datetime == "datetime"
        assert RunType.formula == "formula"
        assert RunType.inline_component == "inline_component"
        assert RunType.endnote_reference == "endnote_reference"
        assert RunType.footnote_reference == "footnote_reference"


class TestBlockType:
    """BlockType枚举的测试用例"""

    def test_block_type_values(self):
        """测试BlockType枚举值"""
        assert BlockType.para == "para"
        assert BlockType.table == "table"
        assert BlockType.component == "component"
        assert BlockType.textbox == "textbox"
        assert BlockType.highlight == "highlight"


class TestSheetType:
    """SheetType枚举的测试用例"""

    def test_sheet_type_values(self):
        """测试SheetType枚举值"""
        assert SheetType.unknown == "unknown"
        assert SheetType.grid == "grid"
        assert SheetType.dialog == "dialog"
        assert SheetType.chart == "chart"
        assert SheetType.macro == "macro"
        assert SheetType.module == "module"
        assert SheetType.griddb == "griddb"
        assert SheetType.dashboard == "dashboard"
        assert SheetType.flexboard == "flexboard"
        assert SheetType.dashboard_db == "dashboard_db"


class TestHighlight:
    """Highlight模型的测试用例"""

    def test_highlight_creation(self):
        """测试Highlight创建"""
        highlight = Highlight(
            emoji="😀",
            background_color="#FF0000FF",
            blocks=[]
        )
        assert highlight.emoji == "😀"
        assert highlight.background_color == "#FF0000FF"
        assert highlight.blocks == []

    def test_highlight_optional_fields(self):
        """测试Highlight使用可选字段"""
        highlight = Highlight()
        assert highlight.emoji is None
        assert highlight.background_color is None
        assert highlight.blocks is None


class TestHyperLink:
    """HyperLink模型的测试用例"""

    def test_hyperlink_creation(self):
        """测试HyperLink创建"""
        hyperlink = HyperLink(
            display_text="Click here",
            target="https://example.com"
        )
        assert hyperlink.display_text == "Click here"
        assert hyperlink.target == "https://example.com"
        assert hyperlink.references is None

    def test_hyperlink_with_references(self):
        """测试HyperLink使用引用"""
        from modules.entity.kdc_enttiy import ReferenctType
        ref = Reference(id="ref_1", type=ReferenctType.run)
        hyperlink = HyperLink(
            display_text="Click here",
            target="https://example.com",
            references=[ref]
        )
        assert len(hyperlink.references) == 1
        assert hyperlink.references[0].id == "ref_1"


class TestReference:
    """Reference模型的测试用例"""

    def test_reference_creation(self):
        """测试Reference创建"""
        from modules.entity.kdc_enttiy import ReferenctType
        ref = Reference(id="ref_1", type=ReferenctType.run)
        assert ref.id == "ref_1"
        assert ref.type == ReferenctType.run


class TestRectangle:
    """Rectangle模型的测试用例"""

    def test_rectangle_creation(self):
        """测试Rectangle创建"""
        rect = Rectangle(x1=10, y1=20, x2=100, y2=200)
        assert rect.x1 == 10
        assert rect.y1 == 20
        assert rect.x2 == 100
        assert rect.y2 == 200


class TestSize:
    """Size模型的测试用例"""

    def test_size_creation(self):
        """测试Size创建"""
        size = Size(width=800, height=600)
        assert size.width == 800
        assert size.height == 600


class TestParaIndent:
    """ParaIndent模型的测试用例"""

    def test_para_indent_creation(self):
        """测试ParaIndent创建"""
        indent = ParaIndent(
            abs_left_indent=10,
            rel_left_indent=5,
            abs_first_indent=15,
            rel_first_indent=8
        )
        assert indent.abs_left_indent == 10
        assert indent.rel_left_indent == 5
        assert indent.abs_first_indent == 15
        assert indent.rel_first_indent == 8

    def test_para_indent_optional_fields(self):
        """测试ParaIndent使用可选字段"""
        indent = ParaIndent()
        assert indent.abs_left_indent is None
        assert indent.rel_left_indent is None
        assert indent.abs_first_indent is None
        assert indent.rel_first_indent is None


class TestPageProp:
    """PageProp模型的测试用例"""

    def test_page_prop_creation(self):
        """测试PageProp创建"""
        size = Size(width=800, height=600)
        indent = ParaIndent(abs_left_indent=10)
        
        page_prop = PageProp(
            size=size,
            rotate=RotateType.rotate90,
            offset_angle=1.5,
            dpi=300,
            indent=indent
        )

        assert page_prop.size.width == 800
        assert page_prop.rotate == RotateType.rotate90
        assert page_prop.offset_angle == 1.5
        assert page_prop.dpi == 300
        assert page_prop.indent.abs_left_indent == 10

    def test_page_prop_optional_fields(self):
        """测试PageProp使用可选字段"""
        page_prop = PageProp()
        assert page_prop.size is None
        assert page_prop.rotate is None
        assert page_prop.offset_angle is None
        assert page_prop.dpi is None
        assert page_prop.indent is None


class TestDocProp:
    """DocProp模型的测试用例"""

    def test_doc_prop_creation(self):
        """测试DocProp创建"""
        page_prop = PageProp(dpi=300)
        doc_prop = DocProp(
            page_count=10,
            page_props=[page_prop]
        )
        
        assert doc_prop.page_count == 10
        assert len(doc_prop.page_props) == 1
        assert doc_prop.page_props[0].dpi == 300

    def test_doc_prop_optional_fields(self):
        """测试DocProp使用可选字段"""
        doc_prop = DocProp()
        assert doc_prop.page_count is None
        assert doc_prop.page_props is None


class TestPPTProp:
    """PPTProp模型的测试用例"""

    def test_ppt_prop_creation(self):
        """测试PPTProp创建"""
        note_size = Size(width=800, height=600)
        slide_size = Size(width=1024, height=768)
        
        ppt_prop = PPTProp(
            note_size=note_size,
            slide_size=slide_size
        )
        
        assert ppt_prop.note_size.width == 800
        assert ppt_prop.slide_size.width == 1024

    def test_ppt_prop_optional_fields(self):
        """测试PPTProp使用可选字段"""
        ppt_prop = PPTProp()
        assert ppt_prop.note_size is None
        assert ppt_prop.slide_size is None


class TestTableCell:
    """TableCell模型的测试用例"""

    def test_table_cell_creation(self):
        """测试TableCell创建"""
        rect = Rectangle(x1=0, y1=0, x2=100, y2=50)
        cell = TableCell(
            blocks=[],
            row_span=2,
            col_span=3,
            id="cell_1",
            bounding_box=rect
        )

        assert cell.blocks == []
        assert cell.row_span == 2
        assert cell.col_span == 3
        assert cell.id == "cell_1"
        assert cell.bounding_box.x2 == 100

    def test_table_cell_optional_fields(self):
        """测试TableCell使用可选字段"""
        cell = TableCell()
        assert cell.blocks is None
        assert cell.row_span is None
        assert cell.col_span is None
        assert cell.id is None
        assert cell.bounding_box is None


class TestTableRow:
    """TableRow模型的测试用例"""

    def test_table_row_creation(self):
        """测试TableRow创建"""
        cell1 = TableCell(id="cell_1")
        cell2 = TableCell(id="cell_2")

        row = TableRow(cells=[cell1, cell2])

        assert len(row.cells) == 2
        assert row.cells[0].id == "cell_1"

    def test_table_row_optional_fields(self):
        """测试TableRow使用可选字段"""
        row = TableRow()
        assert row.cells is None


class TestParaProp:
    """ParaProp模型的测试用例"""

    def test_para_prop_creation(self):
        """测试ParaProp创建"""
        from modules.entity.kdc_enttiy import RunProp

        run_prop = RunProp()
        para_prop = ParaProp(
            def_run_prop=run_prop,
            alignment=ParaAlignment.center,
            outline_level=2,
            list_string="1."
        )

        assert para_prop.def_run_prop is not None
        assert para_prop.alignment == ParaAlignment.center
        assert para_prop.outline_level == 2
        assert para_prop.list_string == "1."

    def test_para_prop_optional_fields(self):
        """测试ParaProp使用可选字段"""
        para_prop = ParaProp()
        assert para_prop.def_run_prop is None
        assert para_prop.alignment is None
        assert para_prop.outline_level is None
        assert para_prop.list_string is None


class TestFootNote:
    """FootNote模型的测试用例"""

    def test_footnote_creation(self):
        """测试FootNote创建"""
        footnote = FootNote(
            id="footnote_1",
            blocks=[]
        )

        assert footnote.id == "footnote_1"
        assert footnote.blocks == []

    def test_footnote_optional_blocks(self):
        """测试FootNote使用可选块"""
        footnote = FootNote(id="footnote_1")
        assert footnote.id == "footnote_1"
        assert footnote.blocks is None


class TestEndNote:
    """EndNote模型的测试用例"""

    def test_endnote_creation(self):
        """测试EndNote创建"""
        endnote = EndNote(
            id="endnote_1",
            blocks=[]
        )

        assert endnote.id == "endnote_1"
        assert endnote.blocks == []

    def test_endnote_optional_blocks(self):
        """测试EndNote使用可选块"""
        endnote = EndNote(id="endnote_1")
        assert endnote.id == "endnote_1"
        assert endnote.blocks is None


class TestFooter:
    """Footer模型的测试用例"""

    def test_footer_creation(self):
        """测试Footer创建"""
        footer = Footer(
            id="footer_1",
            blocks=[]
        )

        assert footer.id == "footer_1"
        assert footer.blocks == []

    def test_footer_optional_blocks(self):
        """测试Footer使用可选块"""
        footer = Footer(id="footer_1")
        assert footer.id == "footer_1"
        assert footer.blocks is None


class TestHeader:
    """Header模型的测试用例"""

    def test_header_creation(self):
        """测试Header创建"""
        header = Header(
            id="header_1",
            blocks=[]
        )

        assert header.id == "header_1"
        assert header.blocks == []

    def test_header_optional_blocks(self):
        """测试Header使用可选块"""
        header = Header(id="header_1")
        assert header.id == "header_1"
        assert header.blocks is None


class TestComment:
    """Comment模型的测试用例"""

    def test_comment_creation(self):
        """测试Comment创建"""
        from modules.entity.kdc_enttiy import ReferenctType
        ref = Reference(id="ref_1", type=ReferenctType.run)
        comment = Comment(
            references=[ref],
            author="John Doe",
            date="2023-01-01T00:00:00Z",
            blocks=[],
            replys=[]
        )

        assert len(comment.references) == 1
        assert comment.author == "John Doe"
        assert comment.date == "2023-01-01T00:00:00Z"
        assert comment.blocks == []
        assert comment.replys == []

    def test_comment_optional_fields(self):
        """测试Comment使用可选字段"""
        from modules.entity.kdc_enttiy import ReferenctType
        ref = Reference(id="ref_1", type=ReferenctType.run)
        comment = Comment(
            references=[ref],
            author="John Doe",
            date="2023-01-01T00:00:00Z"
        )

        assert comment.blocks is None
        assert comment.replys is None


class TestMedia:
    """Media模型的测试用例"""

    def test_media_creation_with_data(self):
        """测试使用数据创建Media"""
        media = Media(
            id="media_1",
            data="base64encodeddata",
            mime_type="image/jpeg",
            sha1="abcdef123456"
        )

        assert media.id == "media_1"
        assert media.data == "base64encodeddata"
        assert media.url is None
        assert media.mime_type == "image/jpeg"
        assert media.sha1 == "abcdef123456"

    def test_media_creation_with_url(self):
        """测试使用URL创建Media"""
        media = Media(
            id="media_1",
            url="https://example.com/image.jpg",
            mime_type="image/jpeg"
        )

        assert media.id == "media_1"
        assert media.data is None
        assert media.url == "https://example.com/image.jpg"
        assert media.mime_type == "image/jpeg"

    def test_media_optional_fields(self):
        """测试Media使用可选字段"""
        media = Media(id="media_1")
        assert media.id == "media_1"
        assert media.data is None
        assert media.url is None
        assert media.mime_type is None
        assert media.sha1 is None


class TestComponent:
    """Component模型的测试用例"""

    def test_component_creation(self):
        """测试Component创建"""
        component = Component(
            type=ComponentType.image,
            media_id="media_1"
        )

        assert component.type == ComponentType.image
        assert component.media_id == "media_1"


class TestTextBox:
    """TextBox模型的测试用例"""

    def test_textbox_creation(self):
        """测试TextBox创建"""
        # 创建一个真实的Block用于测试
        block = Block(type=BlockType.para)

        textbox = TextBox(blocks=[block])

        assert len(textbox.blocks) == 1
        assert textbox.blocks[0].type == BlockType.para


class TestTable:
    """Table模型的测试用例"""

    def test_table_creation(self):
        """测试Table创建"""
        row = TableRow()
        table = Table(rows=[row])

        assert len(table.rows) == 1

    def test_table_optional_fields(self):
        """测试Table使用可选字段"""
        table = Table()
        assert table.rows is None


class TestRun:
    """Run模型的测试用例"""

    def test_run_creation(self):
        """测试Run创建"""
        from modules.entity.kdc_enttiy import RunProp, DateTime, Formula

        run_prop = RunProp()
        datetime_obj = DateTime(timestamp=1640995200000)
        formula_obj = Formula(text="=SUM(A1:A10)")
        component = Component(type=ComponentType.image, media_id="media_1")

        run = Run(
            id="run_1",
            type=RunType.normal,
            prop=run_prop,
            text="Sample text",
            datetime=datetime_obj,
            formula=formula_obj,
            inline_component=component,
            endnote_id="endnote_1",
            footnote_id="footnote_1"
        )

        assert run.id == "run_1"
        assert run.type == RunType.normal
        assert run.text == "Sample text"
        assert run.datetime.timestamp == 1640995200000
        assert run.formula.text == "=SUM(A1:A10)"
        assert run.inline_component.type == ComponentType.image
        assert run.endnote_id == "endnote_1"
        assert run.footnote_id == "footnote_1"

    def test_run_optional_fields(self):
        """测试Run使用可选字段"""
        run = Run()
        assert run.id is None
        assert run.type is None
        assert run.prop is None
        assert run.text is None
        assert run.datetime is None
        assert run.formula is None
        assert run.inline_component is None
        assert run.endnote_id is None
        assert run.footnote_id is None


class TestPara:
    """Para模型的测试用例"""

    def test_para_creation(self):
        """测试Para创建"""
        para_prop = ParaProp(alignment=ParaAlignment.left)
        run = Run(text="Sample text")

        para = Para(
            prop=para_prop,
            runs=[run]
        )

        assert para.prop.alignment == ParaAlignment.left
        assert len(para.runs) == 1
        assert para.runs[0].text == "Sample text"

    def test_para_optional_fields(self):
        """测试Para使用可选字段"""
        para = Para()
        assert para.prop is None
        assert para.runs is None


class TestBlock:
    """Block模型的测试用例"""

    def test_block_creation_para(self):
        """测试使用para创建Block"""
        para = Para()
        rect = Rectangle(x1=0, y1=0, x2=100, y2=50)

        block = Block(
            type=BlockType.para,
            para=para,
            page_index=1,
            bounding_box=rect,
            rotate=90.0,
            index=0,
            id="block_1"
        )

        assert block.type == BlockType.para
        assert block.para is not None
        assert block.page_index == 1
        assert block.bounding_box.x2 == 100
        assert block.rotate == 90.0
        assert block.index == 0
        assert block.id == "block_1"

    def test_block_creation_table(self):
        """测试使用table创建Block"""
        table = Table()

        block = Block(
            type=BlockType.table,
            table=table
        )

        assert block.type == BlockType.table
        assert block.table is not None
        assert block.para is None

    def test_block_creation_component(self):
        """测试使用component创建Block"""
        component = Component(type=ComponentType.image, media_id="media_1")

        block = Block(
            type=BlockType.component,
            component=component
        )

        assert block.type == BlockType.component
        assert block.component is not None
        assert block.component.type == ComponentType.image

    def test_block_creation_textbox(self):
        """测试使用textbox创建Block"""
        textbox = TextBox(blocks=[])

        block = Block(
            type=BlockType.textbox,
            textbox=textbox
        )

        assert block.type == BlockType.textbox
        assert block.textbox is not None

    def test_block_creation_highlight(self):
        """测试使用highlight创建Block"""
        highlight = Highlight(emoji="😀")

        block = Block(
            type=BlockType.highlight,
            highlight=highlight
        )

        assert block.type == BlockType.highlight
        assert block.highlight is not None
        assert block.highlight.emoji == "😀"

    def test_block_optional_fields(self):
        """测试Block使用可选字段"""
        block = Block(type=BlockType.para)

        assert block.type == BlockType.para
        assert block.para is None
        assert block.table is None
        assert block.textbox is None
        assert block.component is None
        assert block.highlight is None
        assert block.page_index is None
        assert block.bounding_box is None
        assert block.rotate is None
        assert block.index is None
        assert block.id is None
        assert block.tags is None


class TestNode:
    """Node模型的测试用例"""

    def test_node_creation(self):
        """测试Node创建"""
        block = Block(type=BlockType.para)
        child_node = Node(outline_level=2)

        node = Node(
            outline_level=1,
            blocks=[block],
            children=[child_node]
        )

        assert node.outline_level == 1
        assert len(node.blocks) == 1
        assert node.blocks[0].type == BlockType.para
        assert len(node.children) == 1
        assert node.children[0].outline_level == 2

    def test_node_optional_fields(self):
        """测试Node使用可选字段"""
        node = Node()
        assert node.outline_level is None
        assert node.blocks is None
        assert node.children is None


class TestDocument:
    """Document模型的测试用例"""

    def test_document_creation_with_tree(self):
        """测试使用树结构创建Document"""
        node = Node(outline_level=1)
        doc_prop = DocProp(page_count=10)
        media = Media(id="media_1")
        from modules.entity.kdc_enttiy import ReferenctType
        comment = Comment(
            references=[Reference(id="ref_1", type=ReferenctType.run)],
            author="John",
            date="2023-01-01T00:00:00Z"
        )
        hyperlink = HyperLink(display_text="Link", target="http://example.com")
        header = Header(id="header_1")
        footer = Footer(id="footer_1")
        footnote = FootNote(id="footnote_1")
        endnote = EndNote(id="endnote_1")

        document = Document(
            prop=doc_prop,
            tree=node,
            medias=[media],
            comments=[comment],
            hyperlinks=[hyperlink],
            headers=[header],
            footers=[footer],
            footnotes=[footnote],
            endnotes=[endnote],
            page_start=0
        )

        assert document.prop.page_count == 10
        assert document.tree.outline_level == 1
        assert len(document.medias) == 1
        assert len(document.comments) == 1
        assert len(document.hyperlinks) == 1
        assert len(document.headers) == 1
        assert len(document.footers) == 1
        assert len(document.footnotes) == 1
        assert len(document.endnotes) == 1
        assert document.page_start == 0

    def test_document_creation_with_blocks(self):
        """测试使用块结构创建Document"""
        block = Block(type=BlockType.para)

        document = Document(
            blocks=[block]
        )

        assert document.tree is None
        assert len(document.blocks) == 1
        assert document.blocks[0].type == BlockType.para

    def test_document_creation_with_ppt_prop(self):
        """测试使用PPT属性创建Document"""
        ppt_prop = PPTProp()

        document = Document(prop=ppt_prop)

        assert document.prop is not None
        assert isinstance(document.prop, PPTProp)

    def test_document_optional_fields(self):
        """测试Document使用可选字段"""
        document = Document()
        assert document.prop is None
        assert document.tree is None
        assert document.blocks is None
        assert document.medias is None
        assert document.comments is None
        assert document.hyperlinks is None
        assert document.headers is None
        assert document.footers is None
        assert document.footnotes is None
        assert document.endnotes is None
        assert document.page_start == 0  # 默认值


class TestOperatorType:
    """OperatorType枚举的测试用例"""

    def test_operator_type_values(self):
        """测试OperatorType枚举值"""
        assert OperatorType.none == "none"
        assert OperatorType.between == "between"
        assert OperatorType.not_between == "not_between"
        assert OperatorType.equal == "equal"
        assert OperatorType.not_equal == "not_equal"
        assert OperatorType.greater == "greater"
        assert OperatorType.less == "less"
        assert OperatorType.greater_equal == "greater_equal"
        assert OperatorType.less_equal == "less_equal"


class TestDBStatisticType:
    """DBStatisticType枚举的测试用例"""

    def test_db_statistic_type_values(self):
        """测试DBStatisticType枚举值"""
        assert DBStatisticType.null == "null"
        assert DBStatisticType.count == "count"
        assert DBStatisticType.sum == "sum"
        assert DBStatisticType.filled_count == "filled_count"
        assert DBStatisticType.unfilled_count == "unfilled_count"
        assert DBStatisticType.filled_percentage == "filled_percentage"
        assert DBStatisticType.unfilled_percentage == "unfilled_percentage"
        assert DBStatisticType.unique_count == "unique_count"
        assert DBStatisticType.unique_percentage == "unique_percentage"
        assert DBStatisticType.avg == "avg"
        assert DBStatisticType.max == "max"
        assert DBStatisticType.min == "min"
        assert DBStatisticType.checked_percentage == "checked_percentage"
        assert DBStatisticType.unchecked_percentage == "unchecked_percentage"
        assert DBStatisticType.min_time == "min_time"
        assert DBStatisticType.max_time == "max_time"
        assert DBStatisticType.day_range == "day_range"


class TestDBNodeType:
    """DBNodeType枚举的测试用例"""

    def test_db_node_type_values(self):
        """测试DBNodeType枚举值"""
        assert DBNodeType.folder == "folder"
        assert DBNodeType.sheet == "sheet"


class TestDBFieldType:
    """DBFieldType枚举的测试用例"""

    def test_db_field_type_values(self):
        """测试DBFieldType枚举值"""
        assert DBFieldType.date == "date"
        assert DBFieldType.time == "time"
        assert DBFieldType.number == "number"
        assert DBFieldType.currency == "currency"
        assert DBFieldType.percentage == "percentage"
        assert DBFieldType.multiline_text == "multiline_text"
        assert DBFieldType.id == "id"
        assert DBFieldType.phone == "phone"
        assert DBFieldType.email == "email"
        assert DBFieldType.hyperlink == "hyperlink"
        assert DBFieldType.checkbox == "checkbox"
        assert DBFieldType.single_select == "single_select"
        assert DBFieldType.multiple_select == "multiple_select"
        assert DBFieldType.rating == "rating"
        assert DBFieldType.contact == "contact"
        assert DBFieldType.complete == "complete"
        assert DBFieldType.cell_picture == "cell_picture"
        assert DBFieldType.attachment == "attachment"
        assert DBFieldType.automations == "automations"
        assert DBFieldType.auto_number == "auto_number"
        assert DBFieldType.created_by == "created_by"
        assert DBFieldType.created_time == "created_time"
        assert DBFieldType.last_modified_by == "last_modified_by"
        assert DBFieldType.last_modified_time == "last_modified_time"
        assert DBFieldType.formula == "formula"
        assert DBFieldType.link == "link"
        assert DBFieldType.oneway_link == "oneway_link"
        assert DBFieldType.note == "note"
        assert DBFieldType.address == "address"
        assert DBFieldType.cascade == "cascade"
        assert DBFieldType.botton == "botton"


class TestSlideContainerCategory:
    """SlideContainerCategory枚举的测试用例"""

    def test_slide_container_category_values(self):
        """测试SlideContainerCategory枚举值"""
        assert SlideContainerCategory.slide_masters == "slide_masters"
        assert SlideContainerCategory.notes_masters == "notes_masters"
        assert SlideContainerCategory.slide_layouts == "slide_layouts"
        assert SlideContainerCategory.slides == "slides"
        assert SlideContainerCategory.notepages == "notepages"


class TestLayoutType:
    """LayoutType枚举的测试用例"""

    def test_layout_type_values(self):
        """测试LayoutType枚举值"""
        assert LayoutType.title == "title"
        assert LayoutType.text == "text"
        assert LayoutType.two_column_text == "two_column_text"
        assert LayoutType.table == "table"
        assert LayoutType.text_and_chart == "text_and_chart"
        assert LayoutType.chart_and_text == "chart_and_text"
        assert LayoutType.org_chart == "org_chart"
        assert LayoutType.chart == "chart"
        assert LayoutType.text_and_clipart == "text_and_clipart"
        assert LayoutType.clipart_and_text == "clipart_and_text"
        assert LayoutType.title_only == "title_only"
        assert LayoutType.blank == "blank"
        assert LayoutType.text_and_object == "text_and_object"
        assert LayoutType.object_and_text == "object_and_text"
        assert LayoutType.large_object == "large_object"
        assert LayoutType.object == "object"
        assert LayoutType.text_and_media_clip == "text_and_media_clip"
        assert LayoutType.media_clip_and_text == "media_clip_and_text"
        assert LayoutType.object_over_text == "object_over_text"
        assert LayoutType.text_over_object == "text_over_object"
        assert LayoutType.text_and_two_objects == "text_and_two_objects"
        assert LayoutType.two_objects_and_text == "two_objects_and_text"
        assert LayoutType.two_objects_over_text == "two_objects_over_text"
