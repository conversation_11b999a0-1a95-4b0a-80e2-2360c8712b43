"""
modules.entity.drive_entity 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock

from modules.entity.drive_entity import (
    RpcName, DriveParams, DriveFileInfo, DriveFileResponse
)
from commons.auth.auth_rpc import SigVerType


class TestRpcName:
    """RpcName枚举的测试用例"""

    def test_rpc_name_values(self):
        """测试RpcName枚举值"""
        assert RpcName.WPSV5 == "wpsv5"

    def test_rpc_name_membership(self):
        """测试RpcName成员关系"""
        assert RpcName.WPSV5 in RpcName
        # 测试字符串值
        assert any(rpc.value == "wpsv5" for rpc in RpcName)
        assert not any(rpc.value == "unknown" for rpc in RpcName)


class TestDriveParams:
    """DriveParams类的测试用例"""

    def test_drive_params_creation_with_string_sig_type(self):
        """测试使用字符串sig_type创建DriveParams"""
        params = DriveParams(
            class_path="modules.rpc.drive_v5_rpc.DriveV5Rpc",
            enabled=True,
            host="https://drive.example.com",
            ak_env="DRIVE_AK",
            sk_env="DRIVE_SK",
            sig_type="wps2",
            download_uri="/api/v1/download/{file_id}"
        )
        
        assert params.class_path == "modules.rpc.drive_v5_rpc.DriveV5Rpc"
        assert params.enabled is True
        assert params.host == "https://drive.example.com"
        assert params.ak_env == "DRIVE_AK"
        assert params.sk_env == "DRIVE_SK"
        assert params.sig_type == SigVerType.wps2
        assert params.download_uri == "/api/v1/download/{file_id}"

    def test_drive_params_creation_with_enum_sig_type(self):
        """测试使用枚举sig_type创建DriveParams"""
        params = DriveParams(
            class_path="modules.rpc.drive_v5_rpc.DriveV5Rpc",
            enabled=False,
            host="https://drive.example.com",
            ak_env="DRIVE_AK",
            sk_env="DRIVE_SK",
            sig_type=SigVerType.wps4,
            download_uri="/api/v2/download/{file_id}"
        )
        
        assert params.class_path == "modules.rpc.drive_v5_rpc.DriveV5Rpc"
        assert params.enabled is False
        assert params.host == "https://drive.example.com"
        assert params.ak_env == "DRIVE_AK"
        assert params.sk_env == "DRIVE_SK"
        assert params.sig_type == SigVerType.wps4
        assert params.download_uri == "/api/v2/download/{file_id}"

    def test_drive_params_creation_wps4_string(self):
        """测试使用wps4字符串sig_type创建DriveParams"""
        params = DriveParams(
            class_path="test.class",
            host="https://test.com",
            ak_env="AK",
            sk_env="SK",
            sig_type="wps4",
            download_uri="/download"
        )
        
        assert params.sig_type == SigVerType.wps4

    def test_drive_params_invalid_string_sig_type(self):
        """测试使用无效字符串sig_type创建DriveParams"""
        with pytest.raises(ValueError, match="Invalid sig_type: invalid"):
            DriveParams(
                class_path="test.class",
                host="https://test.com",
                ak_env="AK",
                sk_env="SK",
                sig_type="invalid",
                download_uri="/download"
            )

    def test_drive_params_invalid_type_sig_type(self):
        """测试使用无效类型sig_type创建DriveParams"""
        from pydantic import ValidationError
        with pytest.raises(ValidationError):
            DriveParams(
                class_path="test.class",
                host="https://test.com",
                ak_env="AK",
                sk_env="SK",
                sig_type=123,  # 无效类型
                download_uri="/download"
            )

    def test_drive_params_get_config_tuple(self):
        """测试DriveParams的get_config_tuple方法"""
        params = DriveParams(
            class_path="test.class",
            host="https://test.com",
            ak_env="AK",
            sk_env="SK",
            sig_type=SigVerType.wps2,
            download_uri="/download/{file_id}"
        )
        
        ak = "test_ak"
        sk = "test_sk"
        
        result = params.get_config_tuple(ak, sk)
        
        expected = (
            "https://test.com",
            "test_ak",
            "test_sk",
            SigVerType.wps2,
            "/download/{file_id}"
        )
        
        assert result == expected

    def test_drive_params_default_enabled(self):
        """测试DriveParams默认启用值"""
        params = DriveParams(
            class_path="test.class",
            host="https://test.com",
            ak_env="AK",
            sk_env="SK",
            sig_type="wps2",
            download_uri="/download"
        )
        
        assert params.enabled is True  # 默认值


class TestDriveFileInfo:
    """DriveFileInfo类的测试用例"""

    def test_drive_file_info_creation_with_url(self):
        """测试使用URL创建DriveFileInfo"""
        file_info = DriveFileInfo(url="https://example.com/file.pdf")
        
        assert file_info.url == "https://example.com/file.pdf"

    def test_drive_file_info_creation_without_url(self):
        """测试不使用URL创建DriveFileInfo"""
        file_info = DriveFileInfo()
        
        assert file_info.url is None

    def test_drive_file_info_creation_none_url(self):
        """测试使用None URL创建DriveFileInfo"""
        file_info = DriveFileInfo(url=None)
        
        assert file_info.url is None

    def test_drive_file_info_creation_empty_url(self):
        """测试使用空URL创建DriveFileInfo"""
        file_info = DriveFileInfo(url="")
        
        assert file_info.url == ""


class TestDriveFileResponse:
    """DriveFileResponse类的测试用例"""

    def test_drive_file_response_creation_basic(self):
        """测试使用基本字段创建DriveFileResponse"""
        file_info = DriveFileInfo(url="https://example.com/file.pdf")
        
        response = DriveFileResponse(
            result="ok",
            fileinfo=file_info,
            error_message=None,
            status_code=200
        )
        
        assert response.result == "ok"
        assert response.fileinfo == file_info
        assert response.error_message is None
        assert response.status_code == 200

    def test_drive_file_response_creation_error(self):
        """测试使用错误创建DriveFileResponse"""
        response = DriveFileResponse(
            result="error",
            fileinfo=None,
            error_message="File not found",
            status_code=404
        )
        
        assert response.result == "error"
        assert response.fileinfo is None
        assert response.error_message == "File not found"
        assert response.status_code == 404

    def test_drive_file_response_success_response_class_method(self):
        """测试DriveFileResponse.success_response类方法"""
        url = "https://example.com/success.pdf"
        
        response = DriveFileResponse.success_response(url)
        
        assert response.result == "ok"
        assert response.fileinfo is not None
        assert response.fileinfo.url == url
        assert response.error_message is None
        assert response.status_code is None

    def test_drive_file_response_error_response_class_method(self):
        """测试DriveFileResponse.error_response类方法"""
        error_msg = "Network timeout"
        status_code = 500
        
        response = DriveFileResponse.error_response(error_msg, status_code)
        
        assert response.result == "error"
        assert response.fileinfo is None
        assert response.error_message == error_msg
        assert response.status_code == status_code

    def test_drive_file_response_error_response_custom_result(self):
        """测试DriveFileResponse.error_response使用自定义结果"""
        error_msg = "Unauthorized"
        status_code = 401
        result = "unauthorized"
        
        response = DriveFileResponse.error_response(error_msg, status_code, result)
        
        assert response.result == "unauthorized"
        assert response.fileinfo is None
        assert response.error_message == error_msg
        assert response.status_code == status_code

    def test_drive_file_response_error_response_no_status_code(self):
        """测试DriveFileResponse.error_response不使用状态码"""
        error_msg = "Unknown error"
        
        response = DriveFileResponse.error_response(error_msg)
        
        assert response.result == "error"
        assert response.fileinfo is None
        assert response.error_message == error_msg
        assert response.status_code is None

    def test_drive_file_response_is_success_property_true(self):
        """测试成功时DriveFileResponse.is_success属性"""
        response = DriveFileResponse.success_response("https://example.com/file.pdf")
        
        assert response.is_success is True

    def test_drive_file_response_is_success_property_false(self):
        """测试失败时DriveFileResponse.is_success属性"""
        response = DriveFileResponse.error_response("Error occurred")
        
        assert response.is_success is False

    def test_drive_file_response_is_success_property_other_result(self):
        """测试其他结果时DriveFileResponse.is_success属性"""
        response = DriveFileResponse(
            result="pending",
            fileinfo=None,
            error_message=None,
            status_code=202
        )
        
        assert response.is_success is False

    def test_drive_file_response_url_property_with_fileinfo(self):
        """测试fileinfo存在时DriveFileResponse.url属性"""
        url = "https://example.com/file.pdf"
        response = DriveFileResponse.success_response(url)
        
        assert response.url == url

    def test_drive_file_response_url_property_without_fileinfo(self):
        """测试fileinfo为None时DriveFileResponse.url属性"""
        response = DriveFileResponse.error_response("Error")
        
        assert response.url is None

    def test_drive_file_response_url_property_fileinfo_no_url(self):
        """测试fileinfo没有URL时DriveFileResponse.url属性"""
        file_info = DriveFileInfo(url=None)
        response = DriveFileResponse(
            result="ok",
            fileinfo=file_info
        )
        
        assert response.url is None
