"""
modules.entity.version_entity 模块的单元测试
"""
import pytest

from modules.entity.version_entity import RespVersionData


class TestRespVersionData:
    """RespVersionData类的测试用例"""

    def test_resp_version_data_creation_default(self):
        """测试使用默认值创建RespVersionData"""
        version_data = RespVersionData()
        
        assert version_data.version == ""

    def test_resp_version_data_creation_with_version(self):
        """测试使用版本字符串创建RespVersionData"""
        version_data = RespVersionData(version="1.0.0")
        
        assert version_data.version == "1.0.0"

    def test_resp_version_data_creation_empty_version(self):
        """测试使用空版本创建RespVersionData"""
        version_data = RespVersionData(version="")
        
        assert version_data.version == ""

    def test_resp_version_data_creation_semantic_version(self):
        """测试使用语义版本创建RespVersionData"""
        version_data = RespVersionData(version="2.1.3-beta.1")
        
        assert version_data.version == "2.1.3-beta.1"

    def test_resp_version_data_creation_build_version(self):
        """测试使用构建版本创建RespVersionData"""
        version_data = RespVersionData(version="1.0.0+build.123")
        
        assert version_data.version == "1.0.0+build.123"

    def test_resp_version_data_creation_date_version(self):
        """测试使用基于日期的版本创建RespVersionData"""
        version_data = RespVersionData(version="2024.01.15")
        
        assert version_data.version == "2024.01.15"

    def test_resp_version_data_creation_git_hash_version(self):
        """测试使用git哈希版本创建RespVersionData"""
        version_data = RespVersionData(version="abc123def456")
        
        assert version_data.version == "abc123def456"

    def test_resp_version_data_creation_long_version(self):
        """测试使用长版本字符串创建RespVersionData"""
        long_version = "1.0.0-alpha.1+build.2024.01.15.abc123def456.feature-branch"
        version_data = RespVersionData(version=long_version)
        
        assert version_data.version == long_version

    def test_resp_version_data_creation_unicode_version(self):
        """测试使用Unicode字符创建RespVersionData"""
        version_data = RespVersionData(version="版本1.0.0")
        
        assert version_data.version == "版本1.0.0"

    def test_resp_version_data_creation_special_chars_version(self):
        """测试使用特殊字符创建RespVersionData"""
        version_data = RespVersionData(version="v1.0.0@release#final")
        
        assert version_data.version == "v1.0.0@release#final"
