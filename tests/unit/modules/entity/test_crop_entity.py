"""
modules.entity.crop_entity 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock

from modules.entity.crop_entity import (
    Box, LayOutData, LayOutRep, OcrData, OcrRep, SealDetectionResult
)


class TestBox:
    """Box类的测试用例"""

    def test_box_creation(self):
        """测试使用所有字段创建Box"""
        box = Box(
            cls_id=1,
            label="table",
            score=0.95,
            coordinate=[10.5, 20.3, 100.7, 200.9]
        )
        
        assert box.cls_id == 1
        assert box.label == "table"
        assert box.score == 0.95
        assert box.coordinate == [10.5, 20.3, 100.7, 200.9]

    def test_box_creation_different_values(self):
        """测试使用不同值创建Box"""
        box = Box(
            cls_id=2,
            label="text",
            score=0.87,
            coordinate=[0.0, 0.0, 50.0, 50.0]
        )
        
        assert box.cls_id == 2
        assert box.label == "text"
        assert box.score == 0.87
        assert box.coordinate == [0.0, 0.0, 50.0, 50.0]

    def test_box_creation_zero_score(self):
        """测试使用零分数创建Box"""
        box = Box(
            cls_id=0,
            label="unknown",
            score=0.0,
            coordinate=[1.0, 2.0, 3.0, 4.0]
        )
        
        assert box.cls_id == 0
        assert box.label == "unknown"
        assert box.score == 0.0
        assert box.coordinate == [1.0, 2.0, 3.0, 4.0]

    def test_box_creation_high_score(self):
        """测试使用高分数创建Box"""
        box = Box(
            cls_id=5,
            label="image",
            score=0.999,
            coordinate=[100.0, 200.0, 300.0, 400.0]
        )
        
        assert box.cls_id == 5
        assert box.label == "image"
        assert box.score == 0.999
        assert box.coordinate == [100.0, 200.0, 300.0, 400.0]

    def test_box_creation_negative_coordinates(self):
        """测试使用负坐标创建Box"""
        box = Box(
            cls_id=3,
            label="negative",
            score=0.5,
            coordinate=[-10.0, -20.0, 30.0, 40.0]
        )
        
        assert box.cls_id == 3
        assert box.label == "negative"
        assert box.score == 0.5
        assert box.coordinate == [-10.0, -20.0, 30.0, 40.0]

    def test_box_creation_empty_label(self):
        """测试使用空标签创建Box"""
        box = Box(
            cls_id=1,
            label="",
            score=0.8,
            coordinate=[1.0, 2.0, 3.0, 4.0]
        )
        
        assert box.cls_id == 1
        assert box.label == ""
        assert box.score == 0.8
        assert box.coordinate == [1.0, 2.0, 3.0, 4.0]


class TestLayOutData:
    """LayOutData类的测试用例"""

    def test_layout_data_creation_empty(self):
        """测试使用空框创建LayOutData"""
        layout_data = LayOutData(boxes=[])
        
        assert layout_data.boxes == []

    def test_layout_data_creation_single_box(self):
        """测试使用单个框创建LayOutData"""
        box = Box(cls_id=1, label="table", score=0.9, coordinate=[1.0, 2.0, 3.0, 4.0])
        layout_data = LayOutData(boxes=[box])
        
        assert len(layout_data.boxes) == 1
        assert layout_data.boxes[0] == box

    def test_layout_data_creation_multiple_boxes(self):
        """测试使用多个框创建LayOutData"""
        box1 = Box(cls_id=1, label="table", score=0.9, coordinate=[1.0, 2.0, 3.0, 4.0])
        box2 = Box(cls_id=2, label="text", score=0.8, coordinate=[5.0, 6.0, 7.0, 8.0])
        box3 = Box(cls_id=3, label="image", score=0.95, coordinate=[9.0, 10.0, 11.0, 12.0])
        
        layout_data = LayOutData(boxes=[box1, box2, box3])
        
        assert len(layout_data.boxes) == 3
        assert layout_data.boxes[0] == box1
        assert layout_data.boxes[1] == box2
        assert layout_data.boxes[2] == box3

    def test_layout_data_creation_mixed_boxes(self):
        """测试使用不同类型框创建LayOutData"""
        boxes = [
            Box(cls_id=1, label="header", score=0.99, coordinate=[0.0, 0.0, 100.0, 20.0]),
            Box(cls_id=2, label="paragraph", score=0.85, coordinate=[0.0, 25.0, 100.0, 80.0]),
            Box(cls_id=3, label="footer", score=0.92, coordinate=[0.0, 180.0, 100.0, 200.0])
        ]
        
        layout_data = LayOutData(boxes=boxes)
        
        assert len(layout_data.boxes) == 3
        assert layout_data.boxes[0].label == "header"
        assert layout_data.boxes[1].label == "paragraph"
        assert layout_data.boxes[2].label == "footer"


class TestLayOutRep:
    """LayOutRep类的测试用例"""

    def test_layout_rep_creation_success(self):
        """测试使用成功响应创建LayOutRep"""
        box = Box(cls_id=1, label="table", score=0.9, coordinate=[1.0, 2.0, 3.0, 4.0])
        data = LayOutData(boxes=[box])
        
        layout_rep = LayOutRep(
            code=200,
            message="Success",
            data=data
        )
        
        assert layout_rep.code == 200
        assert layout_rep.message == "Success"
        assert layout_rep.data == data
        assert len(layout_rep.data.boxes) == 1

    def test_layout_rep_creation_error(self):
        """测试使用错误响应创建LayOutRep"""
        data = LayOutData(boxes=[])
        
        layout_rep = LayOutRep(
            code=500,
            message="Internal Server Error",
            data=data
        )
        
        assert layout_rep.code == 500
        assert layout_rep.message == "Internal Server Error"
        assert layout_rep.data == data
        assert len(layout_rep.data.boxes) == 0

    def test_layout_rep_creation_not_found(self):
        """测试使用未找到响应创建LayOutRep"""
        data = LayOutData(boxes=[])
        
        layout_rep = LayOutRep(
            code=404,
            message="Not Found",
            data=data
        )
        
        assert layout_rep.code == 404
        assert layout_rep.message == "Not Found"
        assert layout_rep.data == data


class TestOcrData:
    """OcrData类的测试用例"""

    def test_ocr_data_creation_empty(self):
        """测试使用空文本创建OcrData"""
        ocr_data = OcrData(rec_texts=[])
        
        assert ocr_data.rec_texts == []

    def test_ocr_data_creation_single_text(self):
        """测试使用单个文本创建OcrData"""
        ocr_data = OcrData(rec_texts=["Hello World"])
        
        assert len(ocr_data.rec_texts) == 1
        assert ocr_data.rec_texts[0] == "Hello World"

    def test_ocr_data_creation_multiple_texts(self):
        """测试使用多个文本创建OcrData"""
        texts = ["Line 1", "Line 2", "Line 3"]
        ocr_data = OcrData(rec_texts=texts)
        
        assert len(ocr_data.rec_texts) == 3
        assert ocr_data.rec_texts == texts

    def test_ocr_data_creation_empty_strings(self):
        """测试使用空字符串创建OcrData"""
        texts = ["", "Non-empty", ""]
        ocr_data = OcrData(rec_texts=texts)
        
        assert len(ocr_data.rec_texts) == 3
        assert ocr_data.rec_texts[0] == ""
        assert ocr_data.rec_texts[1] == "Non-empty"
        assert ocr_data.rec_texts[2] == ""

    def test_ocr_data_creation_unicode_texts(self):
        """测试使用Unicode文本创建OcrData"""
        texts = ["English", "中文", "العربية", "Русский"]
        ocr_data = OcrData(rec_texts=texts)
        
        assert len(ocr_data.rec_texts) == 4
        assert ocr_data.rec_texts[0] == "English"
        assert ocr_data.rec_texts[1] == "中文"
        assert ocr_data.rec_texts[2] == "العربية"
        assert ocr_data.rec_texts[3] == "Русский"


class TestOcrRep:
    """OcrRep类的测试用例"""

    def test_ocr_rep_creation_success(self):
        """测试使用成功响应创建OcrRep"""
        data = OcrData(rec_texts=["Recognized text"])
        
        ocr_rep = OcrRep(
            code=200,
            message="Success",
            data=data
        )
        
        assert ocr_rep.code == 200
        assert ocr_rep.message == "Success"
        assert ocr_rep.data == data
        assert len(ocr_rep.data.rec_texts) == 1

    def test_ocr_rep_creation_error(self):
        """测试使用错误响应创建OcrRep"""
        data = OcrData(rec_texts=[])
        
        ocr_rep = OcrRep(
            code=400,
            message="Bad Request",
            data=data
        )
        
        assert ocr_rep.code == 400
        assert ocr_rep.message == "Bad Request"
        assert ocr_rep.data == data
        assert len(ocr_rep.data.rec_texts) == 0

    def test_ocr_rep_creation_multiple_texts(self):
        """测试使用多个识别文本创建OcrRep"""
        texts = ["First line", "Second line", "Third line"]
        data = OcrData(rec_texts=texts)
        
        ocr_rep = OcrRep(
            code=200,
            message="Multiple texts recognized",
            data=data
        )
        
        assert ocr_rep.code == 200
        assert ocr_rep.message == "Multiple texts recognized"
        assert ocr_rep.data == data
        assert len(ocr_rep.data.rec_texts) == 3


class TestSealDetectionResult:
    """SealDetectionResult数据类的测试用例"""

    def test_seal_detection_result_creation(self):
        """测试SealDetectionResult创建"""
        box1 = Box(cls_id=1, label="seal", score=0.95, coordinate=[10.0, 20.0, 30.0, 40.0])
        box2 = Box(cls_id=2, label="stamp", score=0.88, coordinate=[50.0, 60.0, 70.0, 80.0])
        
        result = SealDetectionResult(
            input_path="/path/to/input.pdf",
            page_index=1,
            boxes=[box1, box2]
        )
        
        assert result.input_path == "/path/to/input.pdf"
        assert result.page_index == 1
        assert len(result.boxes) == 2
        assert result.boxes[0] == box1
        assert result.boxes[1] == box2

    def test_seal_detection_result_creation_empty_boxes(self):
        """测试使用空框创建SealDetectionResult"""
        result = SealDetectionResult(
            input_path="/path/to/empty.pdf",
            page_index=0,
            boxes=[]
        )
        
        assert result.input_path == "/path/to/empty.pdf"
        assert result.page_index == 0
        assert result.boxes == []

    def test_seal_detection_result_creation_different_page(self):
        """测试使用不同页面索引创建SealDetectionResult"""
        box = Box(cls_id=1, label="seal", score=0.9, coordinate=[1.0, 2.0, 3.0, 4.0])
        
        result = SealDetectionResult(
            input_path="/path/to/document.pdf",
            page_index=5,
            boxes=[box]
        )
        
        assert result.input_path == "/path/to/document.pdf"
        assert result.page_index == 5
        assert len(result.boxes) == 1
        assert result.boxes[0] == box

    def test_seal_detection_result_creation_negative_page(self):
        """测试使用负页面索引创建SealDetectionResult"""
        result = SealDetectionResult(
            input_path="/path/to/test.pdf",
            page_index=-1,
            boxes=[]
        )
        
        assert result.input_path == "/path/to/test.pdf"
        assert result.page_index == -1
        assert result.boxes == []

    def test_seal_detection_result_creation_long_path(self):
        """测试使用长文件路径创建SealDetectionResult"""
        long_path = "/very/long/path/to/some/deeply/nested/directory/structure/file.pdf"
        box = Box(cls_id=1, label="seal", score=0.85, coordinate=[0.0, 0.0, 100.0, 100.0])
        
        result = SealDetectionResult(
            input_path=long_path,
            page_index=10,
            boxes=[box]
        )
        
        assert result.input_path == long_path
        assert result.page_index == 10
        assert len(result.boxes) == 1
