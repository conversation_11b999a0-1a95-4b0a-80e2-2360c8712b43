"""
modules.entity.pre_check_entity 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock

from modules.entity.pre_check_entity import (
    ParserName, ParserConfig
)


class TestParserName:
    """ParserName枚举的测试用例"""

    def test_parser_name_values(self):
        """测试ParserName枚举值"""
        assert ParserName.MuPdfParser.value == "MuPdfParser"
        assert ParserName.KdcParser.value == "KdcParser"
        assert ParserName.ImageParser.value == "ImageParser"

    def test_parser_name_membership(self):
        """测试ParserName成员关系"""
        assert ParserName.MuPdfParser in ParserName
        assert ParserName.KdcParser in ParserName
        assert ParserName.ImageParser in ParserName
        # 测试字符串值
        assert any(parser.value == "MuPdfParser" for parser in ParserName)
        assert any(parser.value == "KdcParser" for parser in ParserName)
        assert any(parser.value == "ImageParser" for parser in ParserName)
        assert not any(parser.value == "UnknownParser" for parser in ParserName)

    def test_parser_name_iteration(self):
        """测试ParserName迭代"""
        expected_values = {"MuPdfParser", "KdcParser", "ImageParser"}
        actual_values = {parser.value for parser in ParserName}
        
        assert actual_values == expected_values


class TestParserConfig:
    """ParserConfig类的测试用例"""

    def test_parser_config_creation_default(self):
        """测试使用默认值创建ParserConfig"""
        config = ParserConfig()
        
        assert config.parser_name == ParserName.KdcParser
        assert config.processing_pages == set()
        assert config.is_all is False

    def test_parser_config_creation_mupdf_parser(self):
        """测试使用MuPdfParser创建ParserConfig"""
        config = ParserConfig(parser_name=ParserName.MuPdfParser)
        
        assert config.parser_name == ParserName.MuPdfParser
        assert config.processing_pages == set()
        assert config.is_all is False

    def test_parser_config_creation_image_parser(self):
        """测试使用ImageParser创建ParserConfig"""
        config = ParserConfig(parser_name=ParserName.ImageParser)
        
        assert config.parser_name == ParserName.ImageParser
        assert config.processing_pages == set()
        assert config.is_all is False

    def test_parser_config_creation_with_pages(self):
        """测试使用特定页面创建ParserConfig"""
        pages = {1, 3, 5, 7}
        config = ParserConfig(
            parser_name=ParserName.KdcParser,
            processing_pages=pages
        )
        
        assert config.parser_name == ParserName.KdcParser
        assert config.processing_pages == pages
        assert config.is_all is False

    def test_parser_config_creation_with_is_all(self):
        """测试使用is_all=True创建ParserConfig"""
        config = ParserConfig(
            parser_name=ParserName.MuPdfParser,
            is_all=True
        )
        
        assert config.parser_name == ParserName.MuPdfParser
        assert config.processing_pages == set()
        assert config.is_all is True

    def test_parser_config_creation_full(self):
        """测试使用所有参数创建ParserConfig"""
        pages = {2, 4, 6}
        config = ParserConfig(
            parser_name=ParserName.ImageParser,
            processing_pages=pages,
            is_all=True
        )
        
        assert config.parser_name == ParserName.ImageParser
        assert config.processing_pages == pages
        assert config.is_all is True

    def test_parser_config_creation_empty_pages(self):
        """测试使用空页面集创建ParserConfig"""
        config = ParserConfig(
            parser_name=ParserName.KdcParser,
            processing_pages=set()
        )
        
        assert config.parser_name == ParserName.KdcParser
        assert config.processing_pages == set()
        assert config.is_all is False

    def test_parser_config_creation_single_page(self):
        """测试使用单页创建ParserConfig"""
        config = ParserConfig(
            parser_name=ParserName.MuPdfParser,
            processing_pages={1}
        )
        
        assert config.parser_name == ParserName.MuPdfParser
        assert config.processing_pages == {1}
        assert config.is_all is False

    def test_parser_config_creation_large_page_set(self):
        """测试使用大页面集创建ParserConfig"""
        pages = set(range(1, 101))  # 页面1-100
        config = ParserConfig(
            parser_name=ParserName.KdcParser,
            processing_pages=pages
        )
        
        assert config.parser_name == ParserName.KdcParser
        assert config.processing_pages == pages
        assert len(config.processing_pages) == 100
        assert config.is_all is False

    def test_parser_config_creation_negative_pages(self):
        """测试使用负页码创建ParserConfig"""
        pages = {-1, 0, 1, 2}
        config = ParserConfig(
            parser_name=ParserName.ImageParser,
            processing_pages=pages
        )
        
        assert config.parser_name == ParserName.ImageParser
        assert config.processing_pages == pages
        assert config.is_all is False

    def test_parser_config_creation_duplicate_pages(self):
        """测试使用重复页面创建ParserConfig（集合应该处理这个）"""
        # 注意：集合自动处理重复项，所以这测试了行为
        pages_list = [1, 2, 3, 2, 1, 4, 3]
        pages_set = set(pages_list)
        
        config = ParserConfig(
            parser_name=ParserName.MuPdfParser,
            processing_pages=pages_set
        )
        
        assert config.parser_name == ParserName.MuPdfParser
        assert config.processing_pages == {1, 2, 3, 4}
        assert len(config.processing_pages) == 4
        assert config.is_all is False

    def test_parser_config_creation_mixed_scenarios(self):
        """测试使用各种混合场景创建ParserConfig"""
        # 场景1：所有页面与特定页面集（都为True）
        config1 = ParserConfig(
            parser_name=ParserName.KdcParser,
            processing_pages={1, 2, 3},
            is_all=True
        )
        assert config1.is_all is True
        assert config1.processing_pages == {1, 2, 3}
        
        # 场景2：没有页面但is_all=False
        config2 = ParserConfig(
            parser_name=ParserName.ImageParser,
            processing_pages=set(),
            is_all=False
        )
        assert config2.is_all is False
        assert config2.processing_pages == set()

    def test_parser_config_pages_mutability(self):
        """测试processing_pages集合在创建后可以修改"""
        config = ParserConfig(
            parser_name=ParserName.MuPdfParser,
            processing_pages={1, 2}
        )
        
        # 添加一个页面
        config.processing_pages.add(3)
        assert 3 in config.processing_pages
        assert len(config.processing_pages) == 3
        
        # 移除一个页面
        config.processing_pages.remove(1)
        assert 1 not in config.processing_pages
        assert len(config.processing_pages) == 2

    def test_parser_config_parser_name_modification(self):
        """测试parser_name在创建后可以修改"""
        config = ParserConfig(parser_name=ParserName.KdcParser)
        
        assert config.parser_name == ParserName.KdcParser
        
        config.parser_name = ParserName.MuPdfParser
        assert config.parser_name == ParserName.MuPdfParser

    def test_parser_config_is_all_modification(self):
        """测试创建后可以修改is_all"""
        config = ParserConfig(is_all=False)
        
        assert config.is_all is False
        
        config.is_all = True
        assert config.is_all is True
