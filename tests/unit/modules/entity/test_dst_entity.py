"""
modules.entity.dst_entity 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock, patch
from typing import List

from modules.entity.dst_entity import (
    DST, DSTType, DSTAttribute, BBox, PositionInfo, BlockCoordinate, ExcelCoordinate,
    ImageContentIndex, IMAGE_INDEX, sort_dst_list, breadth_first_sort, print_dst_tree,
    print_dst_indent_tree, get_dst_indent_tree, dst_to_json, get_page_dst, assign_order_to_dst
)


class TestImageContentIndex:
    """ImageContentIndex的测试用例"""

    def test_image_content_index_values(self):
        """测试ImageContentIndex常量值"""
        assert IMAGE_INDEX.URL == 0
        assert IMAGE_INDEX.OCR == 1
        assert IMAGE_INDEX.DESC == 2

    def test_image_content_index_instance(self):
        """测试ImageContentIndex实例创建"""
        index = ImageContentIndex()
        assert index.URL == 0
        assert index.OCR == 1
        assert index.DESC == 2


class TestBBox:
    """BBox模型的测试用例"""

    def test_bbox_creation(self):
        """测试使用有效坐标创建BBox"""
        bbox = BBox(x1=10, y1=20, x2=100, y2=200)
        assert bbox.x1 == 10
        assert bbox.y1 == 20
        assert bbox.x2 == 100
        assert bbox.y2 == 200
        assert bbox.rotate is None

    def test_bbox_with_rotation(self):
        """测试使用旋转创建BBox"""
        bbox = BBox(x1=10, y1=20, x2=100, y2=200, rotate=90)
        assert bbox.rotate == 90

    def test_bbox_negative_coordinates(self):
        """测试BBox使用负坐标（对PPT有效）"""
        bbox = BBox(x1=-10, y1=-20, x2=100, y2=200)
        assert bbox.x1 == -10
        assert bbox.y1 == -20


class TestBlockCoordinate:
    """BlockCoordinate模型的测试用例"""

    def test_block_coordinate_creation(self):
        """测试BlockCoordinate创建"""
        coord = BlockCoordinate(block_id="block_123", gcp=100, len=50)
        assert coord.block_id == "block_123"
        assert coord.gcp == 100
        assert coord.len == 50

    def test_block_coordinate_optional_fields(self):
        """测试BlockCoordinate使用可选字段"""
        coord = BlockCoordinate()
        assert coord.block_id is None
        assert coord.gcp is None
        assert coord.len is None


class TestPositionInfo:
    """PositionInfo模型的测试用例"""

    def test_position_info_with_bbox(self):
        """测试PositionInfo使用bbox"""
        bbox = BBox(x1=10, y1=20, x2=100, y2=200)
        pos = PositionInfo(bbox=bbox)
        assert pos.bbox == bbox
        assert pos.block_coordinate is None

    def test_position_info_with_block_coordinate(self):
        """测试PositionInfo使用块坐标"""
        coord = BlockCoordinate(block_id="block_123")
        pos = PositionInfo(block_coordinate=coord)
        assert pos.block_coordinate == coord
        assert pos.bbox is None

    def test_position_info_empty(self):
        """测试PositionInfo不使用字段"""
        pos = PositionInfo()
        assert pos.bbox is None
        assert pos.block_coordinate is None


class TestExcelCoordinate:
    """ExcelCoordinate模型的测试用例"""

    def test_excel_coordinate_creation(self):
        """测试ExcelCoordinate创建"""
        coord = ExcelCoordinate(
            sheet="Sheet1",
            top_left_span="A1",
            bottom_left_span="A10",
            top_right_span="E1",
            bottom_right_span="E10"
        )
        assert coord.sheet == "Sheet1"
        assert coord.top_left_span == "A1"
        assert coord.bottom_left_span == "A10"
        assert coord.top_right_span == "E1"
        assert coord.bottom_right_span == "E10"


class TestDSTAttribute:
    """DSTAttribute模型的测试用例"""

    def test_dst_attribute_creation_with_bbox(self):
        """测试使用BBox位置创建DSTAttribute"""
        bbox = BBox(x1=10, y1=20, x2=100, y2=200)
        pos = PositionInfo(bbox=bbox)
        attr = DSTAttribute(
            level=1,
            position=pos,
            page=1,
            hash="a" * 32
        )
        assert attr.level == 1
        assert attr.position == pos
        assert attr.page == 1
        assert attr.hash == "a" * 32

    def test_dst_attribute_creation_with_string_position(self):
        """测试使用字符串位置创建DSTAttribute"""
        attr = DSTAttribute(
            level=0,
            position="block_123",
            page=0,
            hash="b" * 32
        )
        assert attr.level == 0
        assert attr.position == "block_123"
        assert attr.page == 0

    def test_dst_attribute_creation_with_excel_coordinate(self):
        """测试使用ExcelCoordinate创建DSTAttribute"""
        excel_coord = ExcelCoordinate(
            sheet="Sheet1",
            top_left_span="A1",
            bottom_left_span="A10",
            top_right_span="E1",
            bottom_right_span="E10"
        )
        attr = DSTAttribute(
            level=2,
            position=excel_coord,
            page=1,
            hash="c" * 32
        )
        assert attr.level == 2
        assert attr.position == excel_coord
        assert attr.page == 1

    def test_dst_attribute_level_validation_negative(self):
        """测试DSTAttribute级别验证使用负值"""
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            DSTAttribute(
                level=-1,
                position="block_123",
                page=0,
                hash="d" * 32
            )

    def test_dst_attribute_page_validation_negative(self):
        """测试DSTAttribute页面验证使用负值"""
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            DSTAttribute(
                level=0,
                position="block_123",
                page=-1,
                hash="e" * 32
            )

    def test_dst_attribute_hash_validation_empty(self):
        """测试DSTAttribute哈希验证使用空值"""
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            DSTAttribute(
                level=0,
                position="block_123",
                page=0,
                hash=""
            )

    def test_dst_attribute_hash_validation_too_short(self):
        """测试DSTAttribute哈希验证使用过短值"""
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            DSTAttribute(
                level=0,
                position="block_123",
                page=0,
                hash="short"
            )

    def test_dst_attribute_hash_validation_too_long(self):
        """测试DSTAttribute哈希验证使用过长值"""
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            DSTAttribute(
                level=0,
                position="block_123",
                page=0,
                hash="a" * 65
            )

    def test_dst_attribute_position_validation_invalid_bbox(self):
        """测试DSTAttribute位置验证使用无效BBox坐标"""
        # 测试 x2 < x1
        with pytest.raises(ValueError, match="BBox坐标值无效"):
            bbox = BBox(x1=100, y1=20, x2=10, y2=200)
            pos = PositionInfo(bbox=bbox)
            DSTAttribute(
                level=0,
                position=pos,
                page=0,
                hash="f" * 32
            )

        # 测试 y2 < y1
        with pytest.raises(ValueError, match="BBox坐标值无效"):
            bbox = BBox(x1=10, y1=200, x2=100, y2=20)
            pos = PositionInfo(bbox=bbox)
            DSTAttribute(
                level=0,
                position=pos,
                page=0,
                hash="g" * 32
            )

    def test_dst_attribute_position_validation_direct_bbox(self):
        """测试DSTAttribute位置验证使用直接BBox（向后兼容）"""
        # 注意：当前Union类型定义不支持直接BBox
        # 此测试记录当前行为
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            bbox = BBox(x1=10, y1=20, x2=100, y2=200)
            DSTAttribute(
                level=0,
                position=bbox,  # 这应该失败，因为BBox不在Union中
                page=0,
                hash="h" * 32
            )

    def test_dst_attribute_position_validation_direct_block_coordinate(self):
        """测试DSTAttribute位置验证使用直接BlockCoordinate"""
        # 注意：当前Union类型定义不支持直接BlockCoordinate
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            coord = BlockCoordinate(block_id="block_123")
            DSTAttribute(
                level=0,
                position=coord,  # 这应该失败，因为BlockCoordinate不在Union中
                page=0,
                hash="j" * 32
            )

    def test_dst_attribute_position_validation_invalid_type(self):
        """测试DSTAttribute位置验证使用无效类型"""
        from pydantic_core import ValidationError
        with pytest.raises(ValidationError):
            DSTAttribute(
                level=0,
                position=123,  # 无效类型
                page=0,
                hash="k" * 32
            )


class TestDSTType:
    """DSTType枚举的测试用例"""

    def test_dst_type_values(self):
        """测试DSTType枚举值"""
        assert DSTType.ROOT == "root"
        assert DSTType.TITLE == "title"
        assert DSTType.TEXT == "text"
        assert DSTType.TABLE == "table"
        assert DSTType.IMAGE == "image"
        assert DSTType.CODE == "code"
        assert DSTType.FORMULA == "formula"
        assert DSTType.DBSHEET == "dbsheet"
        assert DSTType.VIDEO == "video"
        assert DSTType.AUDIO == "audio"
        assert DSTType.CHART == "chart"
        assert DSTType.OTHER == "other"
        assert DSTType.MINDMAP == "mindmap"
        assert DSTType.FLOWCHART == "flowchart"
        assert DSTType.SPREADSHEET == "spreadsheet"


class TestDST:
    """DST模型的测试用例"""

    def create_test_dst(self, dst_id="test_id", parent="-1", order=0, dst_type=DSTType.TEXT,
                       level=0, page=0, content=None):
        """创建测试DST的辅助方法"""
        if content is None:
            content = ["test content"]

        bbox = BBox(x1=10, y1=20, x2=100, y2=200)
        pos = PositionInfo(bbox=bbox)
        attr = DSTAttribute(
            level=level,
            position=pos,
            page=page,
            hash="a" * 32
        )

        return DST(
            id=dst_id,
            parent=parent,
            order=order,
            dst_type=dst_type,
            attributes=attr,
            content=content
        )

    def test_dst_creation(self):
        """测试DST创建"""
        dst = self.create_test_dst()
        assert dst.id == "test_id"
        assert dst.parent == "-1"
        assert dst.order == 0
        assert dst.dst_type == DSTType.TEXT
        assert dst.content == ["test content"]
        assert dst.image_pixel is None
        assert dst.mark is None
        assert dst.font_size == -1
        assert dst.bold is False
        assert dst.table_mark is None

    def test_dst_with_optional_fields(self):
        """测试使用可选字段创建DST"""
        dst = self.create_test_dst()
        dst.image_pixel = [800, 600]
        dst.mark = "header"
        dst.font_size = 12.5
        dst.bold = True
        dst.table_mark = "table_1"

        assert dst.image_pixel == [800, 600]
        assert dst.mark == "header"
        assert dst.font_size == 12.5
        assert dst.bold is True
        assert dst.table_mark == "table_1"

    def test_ensure_image_content_length(self):
        """测试ensure_image_content_length方法"""
        dst = self.create_test_dst(content=[])

        # 确保索引0的长度
        dst.ensure_image_content_length(0)
        assert len(dst.content) == 1
        assert dst.content[0] == ""

        # 确保索引2的长度（应该扩展到3个元素）
        dst.ensure_image_content_length(2)
        assert len(dst.content) == 3
        assert dst.content == ["", "", ""]

        # 确保索引1的长度（不应该改变）
        dst.ensure_image_content_length(1)
        assert len(dst.content) == 3

    def test_set_image_content(self):
        """测试set_image_content方法"""
        dst = self.create_test_dst(content=[])

        # 在索引0设置内容
        dst.set_image_content(0, "test_url")
        assert dst.content[0] == "test_url"

        # 在索引2设置内容（应该自动扩展）
        dst.set_image_content(2, "test_description")
        assert len(dst.content) == 3
        assert dst.content[2] == "test_description"
        assert dst.content[1] == ""  # 应该是空字符串

    def test_set_image_url(self):
        """测试set_image_url方法"""
        dst = self.create_test_dst(content=[])
        dst.set_image_url("http://example.com/image.jpg")

        assert dst.content[IMAGE_INDEX.URL] == "http://example.com/image.jpg"
        assert len(dst.content) >= IMAGE_INDEX.URL + 1

    def test_set_image_ocr(self):
        """测试set_image_ocr方法"""
        dst = self.create_test_dst(content=[])
        dst.set_image_ocr("OCR text result")

        assert dst.content[IMAGE_INDEX.OCR] == "OCR text result"
        assert len(dst.content) >= IMAGE_INDEX.OCR + 1

    def test_set_image_description(self):
        """测试set_image_description方法"""
        dst = self.create_test_dst(content=[])
        dst.set_image_description("Image description")

        assert dst.content[IMAGE_INDEX.DESC] == "Image description"
        assert len(dst.content) >= IMAGE_INDEX.DESC + 1

    def test_image_methods_combined(self):
        """测试所有图像方法协同工作"""
        dst = self.create_test_dst(content=[])

        dst.set_image_url("http://example.com/image.jpg")
        dst.set_image_ocr("OCR text result")
        dst.set_image_description("Image description")

        assert dst.content[IMAGE_INDEX.URL] == "http://example.com/image.jpg"
        assert dst.content[IMAGE_INDEX.OCR] == "OCR text result"
        assert dst.content[IMAGE_INDEX.DESC] == "Image description"
        assert len(dst.content) == 3


class TestSortDstList:
    """sort_dst_list函数的测试用例"""

    def create_test_dst_list(self):
        """创建用于排序的测试DST列表"""
        dst_list = []

        # 根节点
        dst_list.append(DST(
            id="1", parent="-1", order=0, dst_type=DSTType.ROOT,
            attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
            content=["Root"]
        ))

        # 子节点
        dst_list.append(DST(
            id="2", parent="1", order=0, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=1, position="pos", page=0, hash="b" * 32),
            content=["Child 1"]
        ))

        dst_list.append(DST(
            id="3", parent="1", order=1, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=1, position="pos", page=0, hash="c" * 32),
            content=["Child 2"]
        ))

        # 孙子节点
        dst_list.append(DST(
            id="4", parent="2", order=0, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=2, position="pos", page=0, hash="d" * 32),
            content=["Grandchild 1"]
        ))

        dst_list.append(DST(
            id="5", parent="2", order=1, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=2, position="pos", page=0, hash="e" * 32),
            content=["Grandchild 2"]
        ))

        return dst_list

    def test_sort_dst_list_depth_first(self):
        """测试sort_dst_list函数的深度优先排序"""
        dst_list = self.create_test_dst_list()
        sorted_list = sort_dst_list(dst_list)

        # 期望顺序：root, child1, grandchild1, grandchild2, child2
        expected_ids = ["1", "2", "4", "5", "3"]
        actual_ids = [dst.id for dst in sorted_list]

        assert actual_ids == expected_ids

    def test_sort_dst_list_empty(self):
        """测试sort_dst_list处理空列表"""
        result = sort_dst_list([])
        assert result == []

    def test_sort_dst_list_single_node(self):
        """测试sort_dst_list处理单个节点"""
        dst = DST(
            id="1", parent="-1", order=0, dst_type=DSTType.ROOT,
            attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
            content=["Root"]
        )
        result = sort_dst_list([dst])
        assert len(result) == 1
        assert result[0].id == "1"


class TestBreadthFirstSort:
    """breadth_first_sort函数的测试用例"""

    def create_test_dst_list(self):
        """创建用于排序的测试DST列表"""
        dst_list = []

        # 根节点
        dst_list.append(DST(
            id="1", parent="-1", order=0, dst_type=DSTType.ROOT,
            attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
            content=["Root"]
        ))

        # 子节点（顺序很重要）
        dst_list.append(DST(
            id="3", parent="1", order=1, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=1, position="pos", page=0, hash="c" * 32),
            content=["Child 2"]
        ))

        dst_list.append(DST(
            id="2", parent="1", order=0, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=1, position="pos", page=0, hash="b" * 32),
            content=["Child 1"]
        ))

        # 孙子节点
        dst_list.append(DST(
            id="5", parent="2", order=1, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=2, position="pos", page=0, hash="e" * 32),
            content=["Grandchild 2"]
        ))

        dst_list.append(DST(
            id="4", parent="2", order=0, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=2, position="pos", page=0, hash="d" * 32),
            content=["Grandchild 1"]
        ))

        return dst_list

    def test_breadth_first_sort(self):
        """测试breadth_first_sort函数"""
        dst_list = self.create_test_dst_list()
        sorted_list = breadth_first_sort(dst_list)

        # 期望顺序：root, child1, child2, grandchild1, grandchild2
        expected_ids = ["1", "2", "3", "4", "5"]
        actual_ids = [dst.id for dst in sorted_list]

        assert actual_ids == expected_ids

    def test_breadth_first_sort_empty(self):
        """测试breadth_first_sort处理空列表"""
        result = breadth_first_sort([])
        assert result == []

    def test_breadth_first_sort_no_root(self):
        """测试breadth_first_sort处理无根节点"""
        dst = DST(
            id="1", parent="nonexistent", order=0, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=1, position="pos", page=0, hash="a" * 32),
            content=["Orphan"]
        )
        result = breadth_first_sort([dst])
        assert result == []  # 没有根节点可以开始


class TestPrintFunctions:
    """打印函数的测试用例"""

    def create_simple_dst_list(self):
        """创建用于测试打印函数的简单DST列表"""
        return [
            DST(
                id="1", parent="-1", order=0, dst_type=DSTType.ROOT,
                attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
                content=["Root"]
            ),
            DST(
                id="2", parent="1", order=0, dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position="pos", page=0, hash="b" * 32),
                content=["Child"]
            )
        ]

    @patch('builtins.print')
    def test_print_dst_tree(self, mock_print):
        """测试print_dst_tree函数"""
        dst_list = self.create_simple_dst_list()
        print_dst_tree(dst_list)

        # 验证print被调用
        assert mock_print.call_count >= 1

    def test_print_dst_indent_tree(self):
        """测试print_dst_indent_tree函数"""
        dst_list = self.create_simple_dst_list()
        result = print_dst_indent_tree(dst_list)

        assert isinstance(result, str)
        assert "Root" in result
        assert "Child" in result

    def test_get_dst_indent_tree(self):
        """测试get_dst_indent_tree函数"""
        dst_list = self.create_simple_dst_list()
        result = get_dst_indent_tree(dst_list)

        assert isinstance(result, str)
        assert "Root" in result
        assert "Child" in result
        assert "├─" in result  # 树结构符号

    def test_print_functions_empty_list(self):
        """测试打印函数处理空列表"""
        result1 = print_dst_indent_tree([])
        result2 = get_dst_indent_tree([])

        assert result1 == ""
        assert result2 == ""


class TestDstToJson:
    """dst_to_json函数的测试用例"""

    def create_simple_dst_list(self):
        """创建用于JSON测试的简单DST列表"""
        return [
            DST(
                id="1", parent="-1", order=0, dst_type=DSTType.ROOT,
                attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
                content=["Root"]
            )
        ]

    @patch('builtins.print')
    @patch('json.dumps')
    def test_dst_to_json(self, mock_dumps, mock_print):
        """测试dst_to_json函数"""
        dst_list = self.create_simple_dst_list()
        mock_dumps.return_value = '{"test": "json"}'

        dst_to_json(dst_list)

        # 验证json.dumps使用正确参数被调用
        mock_dumps.assert_called_once()
        args, kwargs = mock_dumps.call_args
        assert kwargs.get('indent') == 4
        assert kwargs.get('ensure_ascii') is False

        # 验证print使用JSON输出被调用
        mock_print.assert_called_once_with('{"test": "json"}')

    def test_dst_to_json_empty_list(self):
        """测试dst_to_json处理空列表"""
        with patch('builtins.print') as mock_print, patch('json.dumps') as mock_dumps:
            mock_dumps.return_value = '[]'
            dst_to_json([])
            mock_dumps.assert_called_once()
            mock_print.assert_called_once_with('[]')


class TestGetPageDst:
    """get_page_dst函数的测试用例"""

    def create_multi_page_dst_list(self):
        """创建多页DST列表"""
        return [
            DST(
                id="root", parent="-1", order=0, dst_type=DSTType.ROOT,
                attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
                content=["Root"]
            ),
            DST(
                id="1", parent="root", order=0, dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position="pos", page=1, hash="b" * 32),
                content=["Page 1 Content"]
            ),
            DST(
                id="2", parent="root", order=1, dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position="pos", page=1, hash="c" * 32),
                content=["Page 1 More Content"]
            ),
            DST(
                id="3", parent="root", order=2, dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position="pos", page=2, hash="d" * 32),
                content=["Page 2 Content"]
            )
        ]

    def test_get_page_dst(self):
        """测试get_page_dst函数"""
        dst_list = self.create_multi_page_dst_list()
        page_map = get_page_dst(dst_list)

        # 根节点应该被排除
        assert 0 not in page_map

        # 第1页应该有2个DST
        assert 1 in page_map
        assert len(page_map[1]) == 2
        assert page_map[1][0].id == "1"
        assert page_map[1][1].id == "2"

        # 第2页应该有1个DST
        assert 2 in page_map
        assert len(page_map[2]) == 1
        assert page_map[2][0].id == "3"

    def test_get_page_dst_empty_list(self):
        """测试get_page_dst处理空列表"""
        result = get_page_dst([])
        assert result == {}

    def test_get_page_dst_only_root(self):
        """测试get_page_dst只处理根节点"""
        dst_list = [
            DST(
                id="root", parent="-1", order=0, dst_type=DSTType.ROOT,
                attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
                content=["Root"]
            )
        ]
        result = get_page_dst(dst_list)
        assert result == {}


class TestAssignOrderToDst:
    """assign_order_to_dst函数的测试用例"""

    def test_assign_order_to_dst(self):
        """测试assign_order_to_dst函数"""
        dst_list = [
            DST(
                id="1", parent="-1", order=999, dst_type=DSTType.ROOT,
                attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
                content=["First"]
            ),
            DST(
                id="2", parent="-1", order=888, dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position="pos", page=0, hash="b" * 32),
                content=["Second"]
            ),
            DST(
                id="3", parent="-1", order=777, dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position="pos", page=0, hash="c" * 32),
                content=["Third"]
            )
        ]

        result = assign_order_to_dst(dst_list)

        # 顺序应该从1开始依次分配
        assert result[0].order == 1
        assert result[1].order == 2
        assert result[2].order == 3

        # 应该返回相同的列表
        assert result is dst_list

    def test_assign_order_to_dst_empty_list(self):
        """测试assign_order_to_dst处理空列表"""
        result = assign_order_to_dst([])
        assert result == []

    def test_assign_order_to_dst_single_item(self):
        """测试assign_order_to_dst处理单个项目"""
        dst = DST(
            id="1", parent="-1", order=999, dst_type=DSTType.ROOT,
            attributes=DSTAttribute(level=0, position="pos", page=0, hash="a" * 32),
            content=["Single"]
        )
        result = assign_order_to_dst([dst])

        assert len(result) == 1
        assert result[0].order == 1


class TestFileTypeEnum:
    """FileType枚举的测试用例"""

    def test_file_type_values(self):
        """测试FileType枚举值"""
        from modules.entity.dst_entity import FileType

        # 文档类型
        assert FileType.DOC == "doc"
        assert FileType.DOCX == "docx"
        assert FileType.PDF == "pdf"
        assert FileType.OTL == "otl"
        assert FileType.TXT == "txt"

        # 图像类型
        assert FileType.JPEG == "jpeg"
        assert FileType.JPG == "jpg"
        assert FileType.PNG == "png"
        assert FileType.WEBP == "webp"

        # 电子表格类型
        assert FileType.XLSX == "xlsx"
        assert FileType.XLS == "xls"

        # 演示文稿类型
        assert FileType.PPT == "ppt"
        assert FileType.PPTX == "pptx"


class TestStepEnum:
    """Step枚举的测试用例"""

    def test_step_values(self):
        """测试Step枚举值"""
        from modules.entity.dst_entity import Step

        assert Step.DST == "dst"
        assert Step.CHUNK == "chunk"
        assert Step.FAKE_TITLE == "fake_title"
        assert Step.SUMMARY == "summary"
        assert Step.KEYWORDS == "keywords"


class TestMarkTypeEnum:
    """MarkType枚举的测试用例"""

    def test_mark_type_values(self):
        """测试MarkType枚举值"""
        from modules.entity.dst_entity import MarkType

        assert MarkType.HEADER == "header"
        assert MarkType.FOOTER == "footer"
        assert MarkType.CATALOG == "catalog"


class TestDSTAttributeValidators:
    """DSTAttribute验证器的测试用例（Pydantic v1风格）"""

    def test_position_validation_with_block_coordinate_in_position_info(self):
        """测试PositionInfo内BlockCoordinate的位置验证"""
        coord = BlockCoordinate(block_id="block_123")
        pos = PositionInfo(block_coordinate=coord)

        # 这应该正常工作
        attr = DSTAttribute(
            level=0,
            position=pos,
            page=0,
            hash="a" * 32
        )
        assert attr.position.block_coordinate.block_id == "block_123"

    def test_position_validation_with_excel_coordinate(self):
        """测试ExcelCoordinate的位置验证"""
        excel_coord = ExcelCoordinate(
            sheet="Sheet1",
            top_left_span="A1",
            bottom_left_span="A10",
            top_right_span="E1",
            bottom_right_span="E10"
        )

        # 这应该正常工作
        attr = DSTAttribute(
            level=0,
            position=excel_coord,
            page=0,
            hash="a" * 32
        )
        assert attr.position.sheet == "Sheet1"

    def test_position_validation_edge_cases(self):
        """测试位置验证边界情况"""
        # 测试PositionInfo同时包含bbox和block_coordinate
        bbox = BBox(x1=10, y1=20, x2=100, y2=200)
        coord = BlockCoordinate(block_id="block_123")
        pos = PositionInfo(bbox=bbox, block_coordinate=coord)

        attr = DSTAttribute(
            level=0,
            position=pos,
            page=0,
            hash="a" * 32
        )
        assert attr.position.bbox is not None
        assert attr.position.block_coordinate is not None

    def test_dst_attribute_with_minimal_valid_hash(self):
        """测试DSTAttribute使用最小有效哈希长度"""
        attr = DSTAttribute(
            level=0,
            position="block_123",
            page=0,
            hash="a" * 32  # 正好32个字符
        )
        assert len(attr.hash) == 32

    def test_dst_attribute_with_maximal_valid_hash(self):
        """测试DSTAttribute使用最大有效哈希长度"""
        attr = DSTAttribute(
            level=0,
            position="block_123",
            page=0,
            hash="a" * 64  # 正好64个字符
        )
        assert len(attr.hash) == 64

    def test_dst_attribute_level_zero(self):
        """测试DSTAttribute使用级别0（边界情况）"""
        attr = DSTAttribute(
            level=0,
            position="block_123",
            page=0,
            hash="a" * 32
        )
        assert attr.level == 0

    def test_dst_attribute_page_zero(self):
        """测试DSTAttribute使用页面0（边界情况）"""
        attr = DSTAttribute(
            level=0,
            position="block_123",
            page=0,
            hash="a" * 32
        )
        assert attr.page == 0
