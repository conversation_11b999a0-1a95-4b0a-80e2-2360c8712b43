"""
modules.entity.chunk_entity 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock

from modules.entity.chunk_entity import (
    DSTNode, LabelType, PageImage, Chunk
)
from modules.entity.dst_entity import DST


class TestDSTNode:
    """DSTNode类的测试用例"""

    def test_dst_node_creation_empty(self):
        """测试使用空列表创建DSTNode"""
        node = DSTNode(blocks=[], children=[])
        
        assert node.blocks == []
        assert node.children == []

    def test_dst_node_creation_with_blocks(self):
        """测试使用DST块创建DSTNode"""
        mock_dst1 = MagicMock(spec=DST)
        mock_dst2 = MagicMock(spec=DST)
        
        node = DSTNode(blocks=[mock_dst1, mock_dst2], children=[])
        
        assert len(node.blocks) == 2
        assert node.blocks[0] == mock_dst1
        assert node.blocks[1] == mock_dst2
        assert node.children == []

    def test_dst_node_creation_with_children(self):
        """测试使用子节点创建DSTNode"""
        child1 = DSTNode(blocks=[], children=[])
        child2 = DSTNode(blocks=[], children=[])
        
        parent = DSTNode(blocks=[], children=[child1, child2])
        
        assert parent.blocks == []
        assert len(parent.children) == 2
        assert parent.children[0] == child1
        assert parent.children[1] == child2

    def test_dst_node_creation_full(self):
        """测试同时使用块和子节点创建DSTNode"""
        mock_dst = MagicMock(spec=DST)
        child_node = DSTNode(blocks=[], children=[])
        
        node = DSTNode(blocks=[mock_dst], children=[child_node])
        
        assert len(node.blocks) == 1
        assert node.blocks[0] == mock_dst
        assert len(node.children) == 1
        assert node.children[0] == child_node

    def test_dst_node_nested_structure(self):
        """测试DSTNode的嵌套结构"""
        # 创建叶子节点
        leaf1 = DSTNode(blocks=[MagicMock(spec=DST)], children=[])
        leaf2 = DSTNode(blocks=[MagicMock(spec=DST)], children=[])
        
        # 创建中间节点
        intermediate = DSTNode(blocks=[], children=[leaf1, leaf2])
        
        # 创建根节点
        root = DSTNode(blocks=[MagicMock(spec=DST)], children=[intermediate])
        
        assert len(root.blocks) == 1
        assert len(root.children) == 1
        assert len(root.children[0].children) == 2
        assert len(root.children[0].children[0].blocks) == 1
        assert len(root.children[0].children[1].blocks) == 1


class TestLabelType:
    """LabelType枚举的测试用例"""

    def test_label_type_values(self):
        """测试LabelType枚举值"""
        assert LabelType.TEXT == "text"
        assert LabelType.TABLE == "table"
        assert LabelType.IMAGE == "image"
        assert LabelType.CODE == "code"
        assert LabelType.FORMULA == "formula"

    def test_label_type_membership(self):
        """测试LabelType成员关系"""
        assert LabelType.TEXT in LabelType
        assert LabelType.TABLE in LabelType
        assert LabelType.IMAGE in LabelType
        assert LabelType.CODE in LabelType
        assert LabelType.FORMULA in LabelType
        # 测试字符串值
        assert any(label.value == "text" for label in LabelType)
        assert any(label.value == "table" for label in LabelType)
        assert any(label.value == "image" for label in LabelType)
        assert any(label.value == "code" for label in LabelType)
        assert any(label.value == "formula" for label in LabelType)

    def test_label_type_iteration(self):
        """测试LabelType迭代"""
        expected_values = {"text", "table", "image", "code", "formula"}
        actual_values = {label.value for label in LabelType}
        
        assert actual_values == expected_values


class TestPageImage:
    """PageImage类的测试用例"""

    def test_page_image_creation(self):
        """测试PageImage创建"""
        page_image = PageImage(
            page_num=1,
            image_url="https://example.com/image.png"
        )
        
        assert page_image.page_num == 1
        assert page_image.image_url == "https://example.com/image.png"

    def test_page_image_creation_different_page(self):
        """测试使用不同页码创建PageImage"""
        page_image = PageImage(
            page_num=10,
            image_url="https://example.com/page10.jpg"
        )
        
        assert page_image.page_num == 10
        assert page_image.image_url == "https://example.com/page10.jpg"

    def test_page_image_creation_zero_page(self):
        """测试使用零页码创建PageImage"""
        page_image = PageImage(
            page_num=0,
            image_url="https://example.com/cover.png"
        )
        
        assert page_image.page_num == 0
        assert page_image.image_url == "https://example.com/cover.png"

    def test_page_image_creation_negative_page(self):
        """测试使用负页码创建PageImage"""
        page_image = PageImage(
            page_num=-1,
            image_url="https://example.com/back.png"
        )
        
        assert page_image.page_num == -1
        assert page_image.image_url == "https://example.com/back.png"


class TestChunk:
    """Chunk类的测试用例"""

    def test_chunk_creation_minimal(self):
        """测试使用最少必需字段创建Chunk"""
        chunk = Chunk(
            chunk_id="chunk_001",
            page_size=100,
            content="Sample chunk content",
            label=LabelType.TEXT,
            page_num=[1],
            block=["block_001"]
        )

        assert chunk.chunk_id == "chunk_001"
        assert chunk.page_size == 100
        assert chunk.content == "Sample chunk content"
        assert chunk.label == LabelType.TEXT
        assert chunk.page_num == [1]
        assert chunk.block == ["block_001"]
        # 测试默认值
        assert chunk.content_embedding == []
        assert chunk.dsts is None
        assert chunk.pre_chunk is None
        assert chunk.next_chunk is None
        assert chunk.mark is None
        assert chunk.table_image is None
        assert chunk.scan_pdf_image is None
        assert chunk.checkbox_image is None

    def test_chunk_creation_full(self):
        """测试使用所有字段创建Chunk"""
        page_image = PageImage(page_num=1, image_url="https://example.com/scan.png")
        checkbox_image = PageImage(page_num=1, image_url="https://example.com/checkbox.png")

        chunk = Chunk(
            chunk_id="chunk_002",
            page_size=200,
            content="Full chunk content",
            label=LabelType.TABLE,
            content_embedding=[0.1, 0.2, 0.3],
            page_num=[1, 2],
            block=["block_001", "block_002"],
            dsts=None,
            pre_chunk="chunk_001",
            next_chunk="chunk_003",
            mark="header",
            table_image="https://example.com/table.png",
            scan_pdf_image=[page_image],
            checkbox_image=[checkbox_image]
        )

        assert chunk.chunk_id == "chunk_002"
        assert chunk.page_size == 200
        assert chunk.content == "Full chunk content"
        assert chunk.label == LabelType.TABLE
        assert chunk.content_embedding == [0.1, 0.2, 0.3]
        assert chunk.page_num == [1, 2]
        assert chunk.block == ["block_001", "block_002"]
        assert chunk.dsts is None
        assert chunk.pre_chunk == "chunk_001"
        assert chunk.next_chunk == "chunk_003"
        assert chunk.mark == "header"
        assert chunk.table_image == "https://example.com/table.png"
        assert len(chunk.scan_pdf_image) == 1
        assert chunk.scan_pdf_image[0] == page_image
        assert len(chunk.checkbox_image) == 1
        assert chunk.checkbox_image[0] == checkbox_image

    def test_chunk_creation_multiple_pages(self):
        """测试使用多个页码创建Chunk"""
        chunk = Chunk(
            chunk_id="chunk_003",
            page_size=500,
            content="Multi-page content",
            label=LabelType.TEXT,
            page_num=[1, 2, 3, 4, 5],
            block=["block_001", "block_002", "block_003"]
        )

        assert chunk.chunk_id == "chunk_003"
        assert chunk.page_size == 500
        assert chunk.content == "Multi-page content"
        assert chunk.page_num == [1, 2, 3, 4, 5]

    def test_chunk_creation_different_label_types(self):
        """测试使用不同标签类型创建Chunk"""
        # 测试IMAGE类型
        image_chunk = Chunk(
            chunk_id="img_chunk",
            page_size=100,
            content="Image description",
            label=LabelType.IMAGE,
            page_num=[1],
            block=["block_img"]
        )
        assert image_chunk.label == LabelType.IMAGE

        # 测试CODE类型
        code_chunk = Chunk(
            chunk_id="code_chunk",
            page_size=100,
            content="def hello(): pass",
            label=LabelType.CODE,
            page_num=[2],
            block=["block_code"]
        )
        assert code_chunk.label == LabelType.CODE

        # 测试FORMULA类型
        formula_chunk = Chunk(
            chunk_id="formula_chunk",
            page_size=100,
            content="E = mc²",
            label=LabelType.FORMULA,
            page_num=[3],
            block=["block_formula"]
        )
        assert formula_chunk.label == LabelType.FORMULA

    def test_chunk_creation_empty_content(self):
        """测试使用空内容创建Chunk"""
        chunk = Chunk(
            chunk_id="empty_chunk",
            page_size=100,
            content="",
            label=LabelType.TEXT,
            page_num=[1],
            block=["block_empty"]
        )

        assert chunk.chunk_id == "empty_chunk"
        assert chunk.content == ""
        assert chunk.page_num == [1]

    def test_chunk_creation_multiple_images(self):
        """测试使用多个扫描和复选框图像创建Chunk"""
        scan_images = [
            PageImage(page_num=1, image_url="https://example.com/scan1.png"),
            PageImage(page_num=2, image_url="https://example.com/scan2.png")
        ]
        checkbox_images = [
            PageImage(page_num=1, image_url="https://example.com/cb1.png"),
            PageImage(page_num=1, image_url="https://example.com/cb2.png")
        ]

        chunk = Chunk(
            chunk_id="multi_image_chunk",
            page_size=200,
            content="Content with multiple images",
            label=LabelType.IMAGE,
            page_num=[1, 2],
            block=["block_img1", "block_img2"],
            scan_pdf_image=scan_images,
            checkbox_image=checkbox_images
        )

        assert len(chunk.scan_pdf_image) == 2
        assert len(chunk.checkbox_image) == 2
        assert chunk.scan_pdf_image[0].page_num == 1
        assert chunk.scan_pdf_image[1].page_num == 2
        assert chunk.checkbox_image[0].image_url == "https://example.com/cb1.png"
        assert chunk.checkbox_image[1].image_url == "https://example.com/cb2.png"

    def test_chunk_creation_long_embedding(self):
        """测试使用长嵌入向量创建Chunk"""
        long_embedding = [0.1 * i for i in range(1024)]  # 1024维向量

        chunk = Chunk(
            chunk_id="embedding_chunk",
            page_size=100,
            content="Content with embedding",
            label=LabelType.TEXT,
            page_num=[1],
            block=["block_embed"],
            content_embedding=long_embedding
        )

        assert len(chunk.content_embedding) == 1024
        assert chunk.content_embedding[0] == 0.0
        # Use approximate comparison for floating point values
        assert abs(chunk.content_embedding[1023] - 102.3) < 1e-10
