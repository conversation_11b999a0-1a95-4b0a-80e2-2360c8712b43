"""
modules.entity.checkbox_entity 模块的单元测试
"""
import pytest
from unittest.mock import MagicMock

from modules.entity.checkbox_entity import (
    CheckBoxCorrect, FRAME_LITTLE, FRAME_BIG,
    replace_frame, replace_frame_little
)
from modules.entity.parse_entity import Image


class TestCheckBoxCorrect:
    """CheckBoxCorrect类的测试用例"""

    def test_checkbox_correct_creation_minimal(self):
        """测试使用最少数据创建CheckBoxCorrect"""
        checkbox = CheckBoxCorrect(content="Test content")
        
        assert checkbox.content == "Test content"
        assert checkbox.page == 0  # 默认值
        assert checkbox.images == []  # 默认值

    def test_checkbox_correct_creation_full(self):
        """测试使用所有字段创建CheckBoxCorrect"""
        mock_image = MagicMock(spec=Image)
        
        checkbox = CheckBoxCorrect(
            content="Full test content",
            page=5,
            images=[mock_image]
        )
        
        assert checkbox.content == "Full test content"
        assert checkbox.page == 5
        assert checkbox.images == [mock_image]

    def test_checkbox_correct_creation_multiple_images(self):
        """测试使用多个图像创建CheckBoxCorrect"""
        mock_image1 = MagicMock(spec=Image)
        mock_image2 = MagicMock(spec=Image)
        
        checkbox = CheckBoxCorrect(
            content="Content with multiple images",
            page=2,
            images=[mock_image1, mock_image2]
        )
        
        assert checkbox.content == "Content with multiple images"
        assert checkbox.page == 2
        assert len(checkbox.images) == 2
        assert checkbox.images[0] == mock_image1
        assert checkbox.images[1] == mock_image2

    def test_checkbox_correct_empty_content(self):
        """测试CheckBoxCorrect使用空内容"""
        checkbox = CheckBoxCorrect(content="")
        
        assert checkbox.content == ""
        assert checkbox.page == 0
        assert checkbox.images == []

    def test_checkbox_correct_negative_page(self):
        """测试CheckBoxCorrect使用负页码"""
        checkbox = CheckBoxCorrect(content="Test", page=-1)
        
        assert checkbox.content == "Test"
        assert checkbox.page == -1
        assert checkbox.images == []


class TestFrameConstants:
    """框架常量的测试用例"""

    def test_frame_constants_values(self):
        """测试框架常量具有预期值"""
        assert FRAME_LITTLE == "□"
        assert FRAME_BIG == "☐"

    def test_frame_constants_different(self):
        """测试框架常量是不同的"""
        assert FRAME_LITTLE != FRAME_BIG


class TestFrameReplacementFunctions:
    """框架替换函数的测试用例"""

    def test_replace_frame_basic(self):
        """测试从小框到大框的基本框架替换"""
        text = "Check this □ box"
        result = replace_frame(text)
        
        assert result == "Check this ☐ box"

    def test_replace_frame_multiple(self):
        """测试多个框架的框架替换"""
        text = "□ First □ Second □ Third"
        result = replace_frame(text)
        
        assert result == "☐ First ☐ Second ☐ Third"

    def test_replace_frame_no_frames(self):
        """测试没有框架需要替换的框架替换"""
        text = "No frames here"
        result = replace_frame(text)
        
        assert result == "No frames here"

    def test_replace_frame_empty_string(self):
        """测试空字符串的框架替换"""
        text = ""
        result = replace_frame(text)
        
        assert result == ""

    def test_replace_frame_only_frames(self):
        """测试只有框架的框架替换"""
        text = "□□□"
        result = replace_frame(text)
        
        assert result == "☐☐☐"

    def test_replace_frame_mixed_content(self):
        """测试混合内容的框架替换"""
        text = "Start □ middle ☐ end □"
        result = replace_frame(text)
        
        # 应该只替换FRAME_LITTLE (□)，而不是FRAME_BIG (☐)
        assert result == "Start ☐ middle ☐ end ☐"

    def test_replace_frame_little_basic(self):
        """测试从大框到小框的基本框架替换"""
        text = "Check this ☐ box"
        result = replace_frame_little(text)
        
        assert result == "Check this □ box"

    def test_replace_frame_little_multiple(self):
        """测试多个大框架的框架替换"""
        text = "☐ First ☐ Second ☐ Third"
        result = replace_frame_little(text)
        
        assert result == "□ First □ Second □ Third"

    def test_replace_frame_little_no_frames(self):
        """测试没有大框架需要替换的框架替换"""
        text = "No big frames here □"
        result = replace_frame_little(text)
        
        assert result == "No big frames here □"

    def test_replace_frame_little_empty_string(self):
        """测试空字符串的框架替换"""
        text = ""
        result = replace_frame_little(text)
        
        assert result == ""

    def test_replace_frame_little_only_frames(self):
        """测试只有大框架的框架替换"""
        text = "☐☐☐"
        result = replace_frame_little(text)
        
        assert result == "□□□"

    def test_replace_frame_little_mixed_content(self):
        """测试混合内容的框架替换"""
        text = "Start ☐ middle □ end ☐"
        result = replace_frame_little(text)
        
        # 应该只替换FRAME_BIG (☐)，而不是FRAME_LITTLE (□)
        assert result == "Start □ middle □ end □"

    def test_frame_replacement_roundtrip(self):
        """测试框架替换函数是逆操作"""
        original_text = "Test □ with ☐ mixed □ frames ☐"
        
        # 先应用replace_frame然后应用replace_frame_little
        step1 = replace_frame(original_text)  # □ -> ☐
        step2 = replace_frame_little(step1)   # ☐ -> □
        
        # 应该将所有框架都变成小框架
        expected = "Test □ with □ mixed □ frames □"
        assert step2 == expected

    def test_frame_replacement_unicode_handling(self):
        """测试Unicode字符的框架替换"""
        text = "Unicode test: □ 中文 ☐ العربية □"
        
        result_big = replace_frame(text)
        assert result_big == "Unicode test: ☐ 中文 ☐ العربية ☐"
        
        result_little = replace_frame_little(result_big)
        assert result_little == "Unicode test: □ 中文 □ العربية □"

    def test_frame_replacement_special_characters(self):
        """测试特殊字符的框架替换"""
        text = "Special: □@#$%^&*()☐"
        
        result_big = replace_frame(text)
        assert result_big == "Special: ☐@#$%^&*()☐"
        
        result_little = replace_frame_little(result_big)
        assert result_little == "Special: □@#$%^&*()□"
