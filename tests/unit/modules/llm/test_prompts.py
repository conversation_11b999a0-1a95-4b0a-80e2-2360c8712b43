"""
modules.llm.prompts 模块的单元测试
"""
import pytest

from modules.llm.prompts import (
    SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
    PROMPT_CONTENT_GEN_TAGS_SFT
)


class TestPromptConstants:
    """提示常量的测试用例"""

    def test_summary_prompt_without_key_exists(self):
        """测试SUMMARY_PROMPT_WITHOUT_KEY常量存在且不为空"""
        assert SUMMARY_PROMPT_WITHOUT_KEY is not None
        assert isinstance(SUMMARY_PROMPT_WITHOUT_KEY, str)
        assert len(SUMMARY_PROMPT_WITHOUT_KEY.strip()) > 0

    def test_prompt_content_gen_title_sft_exists(self):
        """测试PROMPT_CONTENT_GEN_TITLE_SFT常量存在且不为空"""
        assert PROMPT_CONTENT_GEN_TITLE_SFT is not None
        assert isinstance(PROMPT_CONTENT_GEN_TITLE_SFT, str)
        assert len(PROMPT_CONTENT_GEN_TITLE_SFT.strip()) > 0

    def test_prompt_content_gen_tags_sft_exists(self):
        """测试PROMPT_CONTENT_GEN_TAGS_SFT常量存在且不为空"""
        assert PROMPT_CONTENT_GEN_TAGS_SFT is not None
        assert isinstance(PROMPT_CONTENT_GEN_TAGS_SFT, str)
        assert len(PROMPT_CONTENT_GEN_TAGS_SFT.strip()) > 0

    def test_summary_prompt_content(self):
        """测试SUMMARY_PROMPT_WITHOUT_KEY内容结构"""
        prompt = SUMMARY_PROMPT_WITHOUT_KEY

        # 应该包含摘要生成的关键元素
        assert "摘要" in prompt or "summary" in prompt.lower()
        assert "文本" in prompt or "text" in prompt.lower()

    def test_prompt_content_gen_title_content(self):
        """测试PROMPT_CONTENT_GEN_TITLE_SFT内容结构"""
        prompt = PROMPT_CONTENT_GEN_TITLE_SFT

        # 应该包含标题生成的关键元素
        assert "总结" in prompt or "title" in prompt.lower() or "主题" in prompt
        assert "文档" in prompt or "document" in prompt.lower()

    def test_prompt_content_gen_tags_content(self):
        """测试PROMPT_CONTENT_GEN_TAGS_SFT内容结构"""
        prompt = PROMPT_CONTENT_GEN_TAGS_SFT

        # 应该包含标签生成的关键元素
        assert "标签" in prompt or "tag" in prompt.lower() or "关键词" in prompt
        assert "文档" in prompt or "document" in prompt.lower()

    def test_prompts_are_different(self):
        """测试不同提示具有不同内容"""
        # 所有提示应该是不同的
        assert SUMMARY_PROMPT_WITHOUT_KEY != PROMPT_CONTENT_GEN_TITLE_SFT
        assert SUMMARY_PROMPT_WITHOUT_KEY != PROMPT_CONTENT_GEN_TAGS_SFT
        assert PROMPT_CONTENT_GEN_TITLE_SFT != PROMPT_CONTENT_GEN_TAGS_SFT

    def test_prompts_not_empty_after_strip(self):
        """测试去除空白字符后提示不为空"""
        assert len(SUMMARY_PROMPT_WITHOUT_KEY.strip()) > 0
        assert len(PROMPT_CONTENT_GEN_TITLE_SFT.strip()) > 0
        assert len(PROMPT_CONTENT_GEN_TAGS_SFT.strip()) > 0

    def test_prompts_contain_chinese_or_english(self):
        """测试提示包含中文或英文文本"""
        def contains_chinese_or_english(text):
            # 检查中文字符（简化范围）
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
            # 检查英文字母
            has_english = any(char.isalpha() and ord(char) < 128 for char in text)
            return has_chinese or has_english

        assert contains_chinese_or_english(SUMMARY_PROMPT_WITHOUT_KEY)
        assert contains_chinese_or_english(PROMPT_CONTENT_GEN_TITLE_SFT)
        assert contains_chinese_or_english(PROMPT_CONTENT_GEN_TAGS_SFT)

    def test_prompts_reasonable_length(self):
        """测试提示具有合理长度（不太短也不太长）"""
        # 提示应该至少有10个字符且少于10000个字符
        for prompt in [SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
                      PROMPT_CONTENT_GEN_TAGS_SFT]:
            assert 10 <= len(prompt) <= 10000, f"Prompt length {len(prompt)} is not reasonable"

    def test_prompts_no_obvious_placeholders(self):
        """测试提示不包含明显的占位符文本"""
        placeholder_indicators = ["TODO", "FIXME", "XXX", "PLACEHOLDER", "TBD"]

        for prompt in [SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
                      PROMPT_CONTENT_GEN_TAGS_SFT]:
            for indicator in placeholder_indicators:
                assert indicator not in prompt.upper(), f"Found placeholder '{indicator}' in prompt"

    def test_prompts_consistent_formatting(self):
        """测试提示具有一致的格式"""
        # 检查提示没有过多的空白字符
        for prompt in [SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
                      PROMPT_CONTENT_GEN_TAGS_SFT]:
            # 不应该有过多的连续空格（超过2个）
            assert "   " not in prompt, "Prompt should not have excessive consecutive spaces"

            # 检查去除空白字符后提示不为空
            assert len(prompt.strip()) > 0, "Prompt should not be empty after stripping"

    def test_prompts_have_different_purposes(self):
        """测试提示服务于不同目的"""
        # 不同的提示应该有不同的特征
        summary_len = len(SUMMARY_PROMPT_WITHOUT_KEY)
        title_len = len(PROMPT_CONTENT_GEN_TITLE_SFT)
        tags_len = len(PROMPT_CONTENT_GEN_TAGS_SFT)

        # 提示应该有不同的长度（表明不同的目的）
        lengths = [summary_len, title_len, tags_len]
        assert len(set(lengths)) > 1, "Prompts should have different characteristics"

    def test_prompts_encoding_compatibility(self):
        """测试提示可以正确编码/解码"""
        for prompt in [SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
                      PROMPT_CONTENT_GEN_TAGS_SFT]:
            # 应该能够编码为 UTF-8 并解码回来
            try:
                encoded = prompt.encode('utf-8')
                decoded = encoded.decode('utf-8')
                assert decoded == prompt, "Prompt should survive UTF-8 encoding/decoding"
            except UnicodeError:
                pytest.fail(f"Prompt contains characters that cannot be UTF-8 encoded")

    def test_prompts_json_safe(self):
        """测试提示可以安全地包含在JSON中"""
        import json

        for prompt in [SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
                      PROMPT_CONTENT_GEN_TAGS_SFT]:
            try:
                # 应该能够序列化为 JSON 并反序列化回来
                json_str = json.dumps({"prompt": prompt})
                parsed = json.loads(json_str)
                assert parsed["prompt"] == prompt, "Prompt should survive JSON serialization"
            except (TypeError, ValueError) as e:
                pytest.fail(f"Prompt cannot be safely included in JSON: {e}")

    def test_prompts_line_endings(self):
        """测试提示具有一致的行结束符"""
        for prompt in [SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
                      PROMPT_CONTENT_GEN_TAGS_SFT]:
            # 不应该混合不同的行结束符样式
            has_crlf = '\r\n' in prompt
            has_lf = '\n' in prompt and not has_crlf
            has_cr = '\r' in prompt and not has_crlf

            # 最多应该使用一种类型的行结束符
            line_ending_types = sum([has_crlf, has_lf, has_cr])
            assert line_ending_types <= 1, "Prompt should not mix different line ending styles"

    def test_prompts_module_level_constants(self):
        """测试提示被定义为模块级常量"""
        # 此测试确保常量可访问且正确定义
        from modules.llm.prompts import (
            SUMMARY_PROMPT_WITHOUT_KEY, PROMPT_CONTENT_GEN_TITLE_SFT,
            PROMPT_CONTENT_GEN_TAGS_SFT
        )

        # 所有都应该是字符串
        assert isinstance(SUMMARY_PROMPT_WITHOUT_KEY, str)
        assert isinstance(PROMPT_CONTENT_GEN_TITLE_SFT, str)
        assert isinstance(PROMPT_CONTENT_GEN_TAGS_SFT, str)

        # 所有都应该非空
        assert SUMMARY_PROMPT_WITHOUT_KEY
        assert PROMPT_CONTENT_GEN_TITLE_SFT
        assert PROMPT_CONTENT_GEN_TAGS_SFT
