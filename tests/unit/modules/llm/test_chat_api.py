"""
modules.llm.chat_api 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock

from modules.llm.chat_api import (
    LMModel, GET_TAGS_LORA, GET_TITLE_LORA
)
from commons.llm_gateway.llm import LLModelRpc, LLMChatStatus
from commons.llm_gateway.models.chat_data import SftBaseModelType, Message


class TestConstants:
    """模块常量的测试用例"""

    def test_lora_constants(self):
        """测试LORA常量值"""
        assert GET_TAGS_LORA == "get_tags"
        assert GET_TITLE_LORA == "get_title"


class TestLMModel:
    """LMModel类的测试用例"""

    def test_lmmodel_class_attributes(self):
        """测试 LMModel 类属性"""
        assert LMModel.gateway == LLModelRpc.Gateway.Public
        assert LMModel.selector is None
        assert LMModel.sft_base_model == SftBaseModelType.qwen25_14b_aidocs
        assert LMModel.kas_sft_base_model == SftBaseModelType.kas_qsearch_qwen2_5_14b

    @pytest.mark.asyncio
    async def test_generate_response_with_prompt_public_gateway(self):
        """测试使用公共网关的 generate_response 处理提示的情况"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = None
        
        mock_stream_item1 = MagicMock()
        mock_stream_item1.status = LLMChatStatus.OK
        mock_stream_item1.text = "Hello "
        
        mock_stream_item2 = MagicMock()
        mock_stream_item2.status = LLMChatStatus.OK
        mock_stream_item2.text = "World"
        
        async def mock_generator():
            yield mock_stream_item1
            yield mock_stream_item2
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(llm_prompt="Test prompt")
            
            assert status == LLMChatStatus.OK
            assert text == "Hello World"
            mock_instance.async_chat_text_stream.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_response_with_messages_public_gateway(self):
        """测试使用公共网关的 generate_response 处理消息的情况"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        
        messages = [
            Message(role="user", content="Hello"),
            Message(role="assistant", content="Hi there!")
        ]
        
        mock_stream_item = MagicMock()
        mock_stream_item.status = LLMChatStatus.OK
        mock_stream_item.text = "Response text"
        
        async def mock_generator():
            yield mock_stream_item
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(messages=messages)
            
            assert status == LLMChatStatus.OK
            assert text == "Response text"
            mock_instance.async_chat_text_stream.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_response_with_sft_gateway(self):
        """测试使用 SFT 网关的 generate_response"""
        LMModel.gateway = LLModelRpc.Gateway.Sft
        LMModel.sft_base_model = SftBaseModelType.qwen25_14b_aidocs
        
        mock_stream_item = MagicMock()
        mock_stream_item.status = LLMChatStatus.OK
        mock_stream_item.text = "SFT response"
        
        async def mock_generator():
            yield mock_stream_item
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(llm_prompt="Test prompt")
            
            assert status == LLMChatStatus.OK
            assert text == "SFT response"
            mock_instance.async_chat_text_stream.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_response_with_custom_selector(self):
        """测试使用自定义选择器的 generate_response"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = None
        
        custom_selector = LLModelRpc.ModelSelector(
            provider="test_provider",
            model="test_model",
            version="1.0"
        )
        
        mock_stream_item = MagicMock()
        mock_stream_item.status = LLMChatStatus.OK
        mock_stream_item.text = "Custom selector response"
        
        async def mock_generator():
            yield mock_stream_item
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(
                llm_prompt="Test prompt",
                selector=custom_selector
            )
            
            assert status == LLMChatStatus.OK
            assert text == "Custom selector response"
            mock_instance.async_chat_text_stream.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_response_with_parameters(self):
        """测试 generate_response 处理各种参数的情况"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        
        mock_stream_item = MagicMock()
        mock_stream_item.status = LLMChatStatus.OK
        mock_stream_item.text = "Parameterized response"
        
        async def mock_generator():
            yield mock_stream_item
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(
                llm_prompt="Test prompt",
                top_k=50.0,
                temperature=0.7,
                guided_json={"type": "object"}
            )
            
            assert status == LLMChatStatus.OK
            assert text == "Parameterized response"
            mock_instance.async_chat_text_stream.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_response_stream_error(self):
        """测试流返回错误时的 generate_response"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        
        mock_stream_item1 = MagicMock()
        mock_stream_item1.status = LLMChatStatus.OK
        mock_stream_item1.text = "Partial "
        
        mock_stream_item2 = MagicMock()
        mock_stream_item2.status = LLMChatStatus.FAIL
        mock_stream_item2.text = "Error occurred"
        
        async def mock_generator():
            yield mock_stream_item1
            yield mock_stream_item2
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(llm_prompt="Test prompt")
            
            assert status == LLMChatStatus.FAIL
            assert text == ""  # Should be empty on error

    @pytest.mark.asyncio
    async def test_generate_response_empty_stream(self):
        """测试 generate_response 处理空流的情况"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        
        async def mock_generator():
            return
            yield  # This will never be reached
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(llm_prompt="Test prompt")
            
            assert status == LLMChatStatus.OK
            assert text == ""

    @pytest.mark.asyncio
    async def test_generate_response_multiple_error_statuses(self):
        """测试 generate_response 处理不同错误状态的情况"""
        error_statuses = [
            LLMChatStatus.FAIL,
            LLMChatStatus.AUDIT,
            LLMChatStatus.PRIVILEGE,
            LLMChatStatus.LIMIT,
            LLMChatStatus.JSON_ERROR
        ]
        
        for error_status in error_statuses:
            LMModel.gateway = LLModelRpc.Gateway.Public
            
            mock_stream_item = MagicMock()
            mock_stream_item.status = error_status
            mock_stream_item.text = "Error text"
            
            async def mock_generator():
                yield mock_stream_item
            
            with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
                mock_instance = mock_llm_rpc.return_value
                mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
                
                status, text = await LMModel.generate_response(llm_prompt="Test prompt")
                
                assert status == error_status
                assert text == ""

    @pytest.mark.asyncio
    async def test_generate_response_class_selector_override(self):
        """测试类有选择器但参数覆盖它时的 generate_response"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = LLModelRpc.ModelSelector(
            provider="class_provider",
            model="class_model",
            version="1.0"
        )
        
        parameter_selector = LLModelRpc.ModelSelector(
            provider="param_provider",
            model="param_model",
            version="2.0"
        )
        
        mock_stream_item = MagicMock()
        mock_stream_item.status = LLMChatStatus.OK
        mock_stream_item.text = "Override response"
        
        async def mock_generator():
            yield mock_stream_item
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(
                llm_prompt="Test prompt",
                selector=parameter_selector
            )
            
            assert status == LLMChatStatus.OK
            assert text == "Override response"
            
            # Verify the parameter selector was used (not the class selector)
            call_args = mock_instance.async_chat_text_stream.call_args
            assert call_args is not None

    @pytest.mark.asyncio
    async def test_generate_response_long_text_accumulation(self):
        """测试 generate_response 处理多个流项目的情况"""
        LMModel.gateway = LLModelRpc.Gateway.Public
        
        # Create many stream items
        stream_items = []
        expected_text = ""
        for i in range(100):
            item = MagicMock()
            item.status = LLMChatStatus.OK
            item.text = f"part{i} "
            stream_items.append(item)
            expected_text += f"part{i} "
        
        async def mock_generator():
            for item in stream_items:
                yield item
        
        with patch('modules.llm.chat_api.LLModelRpc') as mock_llm_rpc:
            mock_instance = mock_llm_rpc.return_value
            mock_instance.async_chat_text_stream = AsyncMock(return_value=mock_generator())
            
            status, text = await LMModel.generate_response(llm_prompt="Test prompt")
            
            assert status == LLMChatStatus.OK
            assert text == expected_text
