"""
modules.cross_table.merge_table_util 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock

from modules.cross_table.merge_table_util import (
    merge_table_by_force, extract_trs, generate_new_html_table
)


class TestCrossTableMergeUtil:
    """跨表合并工具的测试用例"""

    def test_merge_table_by_force_success(self):
        """测试强制表格合并成功的情况"""
        table_list = [
            "<table><tr><td>Row 1</td></tr></table>",
            "<table><tr><td>Row 2</td></tr></table>"
        ]
        
        with patch('modules.cross_table.merge_table_util.extract_trs') as mock_extract:
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                mock_extract.side_effect = [
                    ["<tr><td>Row 1</td></tr>"],
                    ["<tr><td>Row 2</td></tr>"]
                ]
                mock_generate.return_value = "<table><tr><td>Row 1</td></tr><tr><td>Row 2</td></tr></table>"
                
                result = merge_table_by_force(table_list)
                
                assert result == "<table><tr><td>Row 1</td></tr><tr><td>Row 2</td></tr></table>"
                assert mock_extract.call_count == 2
                mock_generate.assert_called_once()

    def test_merge_table_by_force_empty_list(self):
        """测试合并空表格列表"""
        table_list = []
        
        with patch('modules.cross_table.merge_table_util.extract_trs') as mock_extract:
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                mock_generate.return_value = "<table></table>"
                
                result = merge_table_by_force(table_list)
                
                assert result == "<table></table>"
                mock_extract.assert_not_called()
                mock_generate.assert_called_once_with([])

    def test_merge_table_by_force_single_table(self):
        """测试合并单个表格"""
        table_list = ["<table><tr><td>Single Row</td></tr></table>"]
        
        with patch('modules.cross_table.merge_table_util.extract_trs') as mock_extract:
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                mock_extract.return_value = ["<tr><td>Single Row</td></tr>"]
                mock_generate.return_value = "<table><tr><td>Single Row</td></tr></table>"
                
                result = merge_table_by_force(table_list)
                
                assert result == "<table><tr><td>Single Row</td></tr></table>"
                mock_extract.assert_called_once()
                mock_generate.assert_called_once()

    def test_merge_table_by_force_with_empty_tables(self):
        """测试合并包含空表格的表格列表"""
        table_list = [
            "<table><tr><td>Row 1</td></tr></table>",
            "<table></table>",  # Empty table
            "<table><tr><td>Row 2</td></tr></table>"
        ]
        
        with patch('modules.cross_table.merge_table_util.extract_trs') as mock_extract:
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                mock_extract.side_effect = [
                    ["<tr><td>Row 1</td></tr>"],
                    [],  # 空表格返回空列表
                    ["<tr><td>Row 2</td></tr>"]
                ]
                mock_generate.return_value = "<table><tr><td>Row 1</td></tr><tr><td>Row 2</td></tr></table>"
                
                result = merge_table_by_force(table_list)
                
                assert result == "<table><tr><td>Row 1</td></tr><tr><td>Row 2</td></tr></table>"
                assert mock_extract.call_count == 3

    def test_merge_table_by_force_complex_tables(self):
        """测试合并包含多行多列的复杂表格"""
        table_list = [
            "<table><tr><td>A1</td><td>B1</td></tr><tr><td>A2</td><td>B2</td></tr></table>",
            "<table><tr><td>C1</td><td>D1</td></tr></table>"
        ]
        
        with patch('modules.cross_table.merge_table_util.extract_trs') as mock_extract:
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                mock_extract.side_effect = [
                    ["<tr><td>A1</td><td>B1</td></tr>", "<tr><td>A2</td><td>B2</td></tr>"],
                    ["<tr><td>C1</td><td>D1</td></tr>"]
                ]
                expected_result = "<table><tr><td>A1</td><td>B1</td></tr><tr><td>A2</td><td>B2</td></tr><tr><td>C1</td><td>D1</td></tr></table>"
                mock_generate.return_value = expected_result
                
                result = merge_table_by_force(table_list)
                
                assert result == expected_result
                mock_generate.assert_called_once_with([
                    ["<tr><td>A1</td><td>B1</td></tr>", "<tr><td>A2</td><td>B2</td></tr>"],
                    ["<tr><td>C1</td><td>D1</td></tr>"]
                ])

    def test_extract_trs_basic(self):
        """测试基本的TR提取功能"""
        # 如果我们能导入实际的extract_trs函数，这里会测试它
        # 现在我们通过merge_table_by_force来间接测试接口
        table_html = "<table><tr><td>Test</td></tr></table>"
        
        # 在不了解实现的情况下，我们无法直接测试extract_trs
        # 所以我们通过merge_table_by_force间接测试它
        with patch('modules.cross_table.merge_table_util.extract_trs', return_value=["<tr><td>Test</td></tr>"]):
            with patch('modules.cross_table.merge_table_util.generate_new_html_table', return_value="<table><tr><td>Test</td></tr></table>"):
                result = merge_table_by_force([table_html])
                assert result == "<table><tr><td>Test</td></tr></table>"

    def test_generate_new_html_table_basic(self):
        """测试基本的HTML表格生成功能"""
        # 通过merge_table_by_force测试接口
        table_list = ["<table><tr><td>Test</td></tr></table>"]
        
        with patch('modules.cross_table.merge_table_util.extract_trs', return_value=["<tr><td>Test</td></tr>"]):
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                mock_generate.return_value = "<table><tr><td>Test</td></tr></table>"
                
                result = merge_table_by_force(table_list)
                
                # 验证generate_new_html_table是否使用预期的结构调用
                mock_generate.assert_called_once_with([["<tr><td>Test</td></tr>"]])
                assert result == "<table><tr><td>Test</td></tr></table>"

    def test_merge_table_by_force_malformed_html(self):
        """测试合并包含格式错误HTML的表格"""
        table_list = [
            "<table><tr><td>Good Row</td></tr></table>",
            "<table><tr><td>Malformed Row</td></table>",  # 缺少 </tr>
        ]
        
        with patch('modules.cross_table.merge_table_util.extract_trs') as mock_extract:
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                # 假设extract_trs能优雅地处理格式错误的HTML
                mock_extract.side_effect = [
                    ["<tr><td>Good Row</td></tr>"],
                    ["<tr><td>Malformed Row</td></tr>"]  # 假设它修复了HTML
                ]
                mock_generate.return_value = "<table><tr><td>Good Row</td></tr><tr><td>Malformed Row</td></tr></table>"
                
                result = merge_table_by_force(table_list)
                
                assert result == "<table><tr><td>Good Row</td></tr><tr><td>Malformed Row</td></tr></table>"

    def test_merge_table_by_force_large_table_list(self):
        """测试合并大量表格"""
        table_list = [f"<table><tr><td>Row {i}</td></tr></table>" for i in range(100)]
        
        with patch('modules.cross_table.merge_table_util.extract_trs') as mock_extract:
            with patch('modules.cross_table.merge_table_util.generate_new_html_table') as mock_generate:
                mock_extract.side_effect = [[f"<tr><td>Row {i}</td></tr>"] for i in range(100)]
                mock_generate.return_value = "<table>merged_large_table</table>"
                
                result = merge_table_by_force(table_list)
                
                assert result == "<table>merged_large_table</table>"
                assert mock_extract.call_count == 100
                mock_generate.assert_called_once()
