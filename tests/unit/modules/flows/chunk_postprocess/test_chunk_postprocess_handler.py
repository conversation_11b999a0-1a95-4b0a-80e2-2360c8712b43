# Author: linqi
# Date: 2025/8/13
# Time: 12:03

import pytest

from modules.flows.chunk_postprocess.chunk_postprocess_handler import ChunkPostProcessNode
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.parse_entity import Image, ImageType
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute
from services.datamodel import FileType
from conf import ConfHandlerName


def _dst(id_, parent, level, page, text, dst_type: DSTType = DSTType.TEXT):
    return DST(
        id=id_, parent=parent, order=0, dst_type=dst_type,
        attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
        content=[text] if dst_type != DSTType.IMAGE else ["url", text],
    )


def _chunk(cid: str, text: str, pages, blocks, dsts):
    return Chunk(
        chunk_id=cid, page_size=1, content=text, label=LabelType.TEXT,
        page_num=pages, block=blocks, dsts=dsts,
    )


@pytest.mark.asyncio
async def test_postprocess_adds_image_descriptions_and_links(monkeypatch):
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    # stub async callback to no-op
    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    # Prepare context
    file_info = FileInfo(file_type=FileType.DOCX, page_size=2, word_count=10, width=100, height=200, is_scan=False)
    ctx = PipelineContext(file_info=file_info, need_img_desc=True)

    # Prepare chunks: one with image dst and matching id for description
    img_dst = _dst("img1", "-1", 10, 0, "ocr_desc", DSTType.IMAGE)
    text_dst = _dst("t1", "-1", 10, 0, "text")
    ch = _chunk("0", "凶☒冈 some text", [0], ["img1", "t1"], [img_dst, text_dst])
    ctx.chunks = [ch]

    # need_img_desc: provide desc dsts via handler_results
    ctx.need_img_desc = True
    desc_dst = _dst("img1", "-1", 10, 0, "new_desc", DSTType.IMAGE)
    ctx.handler_results[ConfHandlerName.image_desc_handler] = [desc_dst]

    # images: TABLE_IMAGE should link to chunk containing the dst id; CHECK_BOX_IMAGE should link by page if content matches
    table_image = Image(page_num=0, url="u1", chunk_ids=None, dst_ids=["img1"], image_type=ImageType.TABLE_IMAGE)
    checkbox_image = Image(page_num=0, url="u2", chunk_ids=None, dst_ids=None, image_type=ImageType.CHECK_BOX_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [table_image, checkbox_image]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # image description updated
    assert img_dst.content[2] == "new_desc"
    # TABLE_IMAGE linked via dst id
    assert table_image.chunk_ids == ["0"]
    # CHECK_BOX_IMAGE linked via page and specific chars detection (content includes [x])
    assert checkbox_image.chunk_ids == ["0"]


@pytest.mark.asyncio
async def test_postprocess_no_images_or_chunks_calls_noop(monkeypatch):
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    called = {"n": 0}

    async def _cb(*args, **kwargs):
        called["n"] += 1
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    # neither chunks nor images present
    out = await ChunkPostProcessNode("cpp").process(ctx)
    assert out is ctx
    # no callback should be invoked
    assert called["n"] == 0

