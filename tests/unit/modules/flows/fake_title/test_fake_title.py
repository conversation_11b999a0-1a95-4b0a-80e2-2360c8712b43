# Author: linqi
# Date: 2025/8/14
# Time: 16:40

import pytest
from unittest.mock import AsyncMock, Mock, patch

from modules.flows.fake_title.fake_title import fake_title_services, FakeTitleNode
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from modules.entity.parse_entity import ParseRes
from services.datamodel import FileType
from commons.llm_gateway.llm import LLMChatStatus
from modules.entity.parse_entity import Image, ImageType


def _create_test_dst(dst_id="test", content=None, dst_type=DSTType.TEXT):
    """Create a test DST object"""
    if content is None:
        content = ["test content"]
    return DST(
        id=dst_id,
        parent="root",
        order=0,
        dst_type=dst_type,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=1, y2=1)),
            page=0,
            hash="h" * 32
        ),
        content=content
    )


@pytest.mark.asyncio
async def test_fake_title_services_success(monkeypatch):
    """Test fake_title_services with successful response"""
    # Mock LMModel.generate_response
    async def mock_generate_response(messages, temperature, selector):
        return LLMChatStatus.OK, '"Generated Title"'
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.LMModel.generate_response",
        mock_generate_response
    )
    
    content = "This is test content for title generation"
    result = await fake_title_services(content)
    
    assert result == "Generated Title"  # Should strip quotes


@pytest.mark.asyncio
async def test_fake_title_services_empty_content():
    """Test fake_title_services with empty content"""
    result = await fake_title_services("")
    assert result == ""


@pytest.mark.asyncio
async def test_fake_title_services_none_content():
    """Test fake_title_services with None content"""
    result = await fake_title_services(None)
    assert result == ""


@pytest.mark.asyncio
async def test_fake_title_services_llm_failure(monkeypatch):
    """Test fake_title_services with LLM failure status"""
    # Mock LMModel.generate_response to return failure status
    async def mock_generate_response(messages, temperature, selector):
        return LLMChatStatus.FAILED, "Error message"
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.LMModel.generate_response",
        mock_generate_response
    )
    
    content = "This is test content"
    result = await fake_title_services(content)
    
    assert result == ""  # Should return empty string on failure


@pytest.mark.asyncio
async def test_fake_title_services_exception(monkeypatch):
    """Test fake_title_services with exception handling"""
    # Mock LMModel.generate_response to raise exception
    async def mock_generate_response(messages, temperature, selector):
        raise ValueError("LLM service error")
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.LMModel.generate_response",
        mock_generate_response
    )
    
    content = "This is test content"
    result = await fake_title_services(content)
    
    assert result == ""  # Should return empty string on exception


@pytest.mark.asyncio
async def test_fake_title_services_long_content(monkeypatch):
    """Test fake_title_services with content longer than 5000 characters"""
    # Mock LMModel.generate_response
    async def mock_generate_response(messages, temperature, selector):
        # Check that content is truncated to 5000 characters
        user_message = messages[1].content
        assert len(user_message) <= 5100  # Account for prompt template
        return LLMChatStatus.OK, '"Truncated Title"'
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.LMModel.generate_response",
        mock_generate_response
    )

    long_content = "a" * 600  # Must be > 5000 to test truncation
    result = await fake_title_services(long_content)
    
    assert result == "Truncated Title"


def test_fake_title_node_init():
    """Test FakeTitleNode initialization"""
    node = FakeTitleNode("test_fake_title")
    assert node.name == "test_fake_title"


@pytest.mark.asyncio
async def test_fake_title_node_process_success(monkeypatch):
    """Test FakeTitleNode.process with successful execution"""
    # Mock print_dst_indent_tree
    def mock_print_dst_tree(dst_list):
        return "Mocked DST tree content"
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.print_dst_indent_tree",
        mock_print_dst_tree
    )
    
    # Mock fake_title_services
    async def mock_fake_title_services(content):
        return "Generated Fake Title"
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.fake_title_services",
        mock_fake_title_services
    )
    
    # Mock OCRModelClient
    mock_ocr_client = Mock()
    mock_ocr_client.request_text_embedding = AsyncMock(return_value=[0.1, 0.2, 0.3])
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.OCRModelClient",
        lambda: mock_ocr_client
    )
    
    # Mock callback_parse_background
    async def mock_callback(res, name, token, need_callback, return_ks3_url, callback_url):
        pass
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.callback_parse_background",
        mock_callback
    )
    
    # Create context
    ctx = PipelineContext(
        file_info=FileInfo(
            file_type=FileType.PDF,
            page_size=10,
            word_count=100,
            width=800,
            height=600,
            is_scan=False,
            rotate_page={}  # Empty dict for no rotation
        )
    )
    ctx.dst = [_create_test_dst("test1")]
    ctx.embed_enabled = True
    ctx.handler_results = {}
    ctx.image = [Image(image_type=ImageType.INPUT_IMAGE, page_num=1, url="test_url")]
    ctx.parse_version = "1.0"
    ctx.token = "test_token"
    ctx.need_callback = True
    ctx.return_ks3_url = "http://test.ks3"
    ctx.callback_url = "http://callback.test"
    
    # Process
    node = FakeTitleNode("fake_title")
    result_ctx = await node.process(ctx)
    
    # Verify results
    assert result_ctx is not None, "FakeTitleNode.process should return context, not None"
    assert result_ctx == ctx
    assert "fake_title" in ctx.handler_results
    assert ctx.handler_results["fake_title"]["fake_title"] == "Generated Fake Title"
    assert ctx.handler_results["fake_title"]["fake_title_embedding"] == [0.1, 0.2, 0.3]


@pytest.mark.asyncio
async def test_fake_title_node_process_embed_disabled(monkeypatch):
    """Test FakeTitleNode.process with embedding disabled"""
    # Mock print_dst_indent_tree
    def mock_print_dst_tree(dst_list):
        return "Mocked DST tree content"
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.print_dst_indent_tree",
        mock_print_dst_tree
    )
    
    # Mock fake_title_services
    async def mock_fake_title_services(content):
        return "Generated Fake Title"
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.fake_title_services",
        mock_fake_title_services
    )
    
    # Mock callback_parse_background
    async def mock_callback(res, name, token, need_callback, return_ks3_url, callback_url):
        pass
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.callback_parse_background",
        mock_callback
    )
    
    # Create context with embedding disabled
    ctx = PipelineContext(
        file_info=FileInfo(
            file_type=FileType.PDF,
            page_size=10,
            word_count=100,
            width=800,
            height=600,
            is_scan=False,
            rotate_page={}  # Empty dict for no rotation
        )
    )
    ctx.dst = [_create_test_dst("test1")]
    ctx.embed_enabled = False  # Embedding disabled
    ctx.handler_results = {}
    ctx.image = [Image(image_type=ImageType.INPUT_IMAGE, page_num=1, url="test_url")]
    ctx.parse_version = "1.0"
    ctx.token = "test_token"
    ctx.need_callback = True
    ctx.return_ks3_url = "http://test.ks3"
    ctx.callback_url = "http://callback.test"
    
    # Process
    node = FakeTitleNode("fake_title")
    result_ctx = await node.process(ctx)
    
    # Verify results
    assert result_ctx is not None, "FakeTitleNode.process should return context, not None"
    assert result_ctx == ctx
    assert "fake_title" in ctx.handler_results
    assert ctx.handler_results["fake_title"]["fake_title"] == "Generated Fake Title"
    assert ctx.handler_results["fake_title"]["fake_title_embedding"] is None


@pytest.mark.asyncio
async def test_fake_title_node_process_exception(monkeypatch):
    """Test FakeTitleNode.process with exception handling"""
    # Mock print_dst_indent_tree to raise exception
    def mock_print_dst_tree(dst_list):
        raise ValueError("Tree processing error")
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.print_dst_indent_tree",
        mock_print_dst_tree
    )
    
    # Create context
    ctx = PipelineContext(
        file_info=FileInfo(
            file_type=FileType.PDF,
            page_size=10,
            word_count=100,
            width=800,
            height=600,
            is_scan=False,
            rotate_page={}  # Empty dict for no rotation
        )
    )
    ctx.dst = [_create_test_dst("test1")]
    ctx.handler_results = {}
    
    # Process should not raise exception (catches internally)
    node = FakeTitleNode("fake_title")
    result = await node.process(ctx)
    
    # Should return None due to exception (no return statement in except block)
    assert result is None


@pytest.mark.asyncio
async def test_fake_title_node_process_with_empty_dst(monkeypatch):
    """Test FakeTitleNode.process with empty DST list"""
    # Mock print_dst_indent_tree
    def mock_print_dst_tree(dst_list):
        return ""  # Empty content
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.print_dst_indent_tree",
        mock_print_dst_tree
    )
    
    # Mock fake_title_services
    async def mock_fake_title_services(content):
        return ""  # Empty title for empty content
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.fake_title_services",
        mock_fake_title_services
    )
    
    # Mock callback_parse_background
    async def mock_callback(res, name, token, need_callback, return_ks3_url, callback_url):
        pass
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.callback_parse_background",
        mock_callback
    )
    
    # Create context
    ctx = PipelineContext(
        file_info=FileInfo(
            file_type=FileType.PDF,
            page_size=0,
            word_count=0,
            width=0,
            height=0,
            is_scan=True,
            rotate_page={0: [90]}
        )
    )
    ctx.dst = []  # Empty DST list
    ctx.embed_enabled = False
    ctx.handler_results = {}
    ctx.image = [Image(image_type=ImageType.INPUT_IMAGE, page_num=1, url="test_url")]
    ctx.parse_version = "2.0"
    ctx.token = "empty_token"
    ctx.need_callback = False
    ctx.return_ks3_url = None
    ctx.callback_url = None
    
    # Process
    node = FakeTitleNode("fake_title_empty")
    result_ctx = await node.process(ctx)
    
    # Verify results
    assert result_ctx == ctx
    assert "fake_title_empty" in ctx.handler_results
    assert ctx.handler_results["fake_title_empty"]["fake_title"] == ""
    assert ctx.handler_results["fake_title_empty"]["fake_title_embedding"] is None


@pytest.mark.asyncio
async def test_fake_title_services_without_quotes(monkeypatch):
    """Test fake_title_services when response doesn't have quotes"""
    # Mock LMModel.generate_response to return text without quotes
    async def mock_generate_response(messages, temperature, selector):
        return LLMChatStatus.OK, 'Title Without Quotes'
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.LMModel.generate_response",
        mock_generate_response
    )
    
    content = "This is test content"
    result = await fake_title_services(content)
    
    assert result == "Title Without Quotes"  # Should work even without quotes


@pytest.mark.asyncio
async def test_fake_title_node_embedding_exception(monkeypatch):
    """Test FakeTitleNode.process when embedding request fails"""
    # Mock print_dst_indent_tree
    def mock_print_dst_tree(dst_list):
        return "Test content"
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.print_dst_indent_tree",
        mock_print_dst_tree
    )
    
    # Mock fake_title_services
    async def mock_fake_title_services(content):
        return "Test Title"
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.fake_title_services",
        mock_fake_title_services
    )
    
    # Mock OCRModelClient to raise exception
    mock_ocr_client = Mock()
    mock_ocr_client.request_text_embedding = AsyncMock(side_effect=Exception("Embedding error"))
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.OCRModelClient",
        lambda: mock_ocr_client
    )
    
    # Mock callback_parse_background
    async def mock_callback(res, name, token, need_callback, return_ks3_url, callback_url):
        pass
    
    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.callback_parse_background",
        mock_callback
    )
    
    # Create context
    ctx = PipelineContext(
        file_info=FileInfo(
            file_type=FileType.PDF,
            page_size=10,
            word_count=100,
            width=800,
            height=600,
            is_scan=False,
            rotate_page={}  # Empty dict for no rotation
        )
    )
    ctx.dst = [_create_test_dst("test1")]
    ctx.embed_enabled = True  # Embedding enabled but will fail
    ctx.handler_results = {}
    ctx.image = [Image(image_type=ImageType.INPUT_IMAGE, page_num=1, url="test_url")]
    ctx.parse_version = "1.0"
    ctx.token = "test_token"
    ctx.need_callback = True
    ctx.return_ks3_url = "http://test.ks3"
    ctx.callback_url = "http://callback.test"
    
    # Process should handle embedding exception gracefully
    node = FakeTitleNode("fake_title")
    # The exception will be caught internally and logged, so the function returns None
    result = await node.process(ctx)
    
    # Since the exception is caught internally, the function returns None
    assert result is None


def test_fake_title_services_message_construction():
    """Test that fake_title_services constructs messages correctly"""
    # This test verifies the message structure without making actual calls
    from modules.flows.fake_title.fake_title import PROMPT_CONTENT_GEN_TITLE_SFT
    from commons.llm_gateway.llm import Message
    
    # Test content
    test_content = "Sample content for title generation"
    
    # Manually construct what the messages should look like
    expected_messages = [
        Message(role='system', content="You are Qwen, created by Alibaba Cloud. You are a helpful assistant."),
        Message(role='user', content=PROMPT_CONTENT_GEN_TITLE_SFT.format(test_content))
    ]
    
    # Verify message structure
    assert expected_messages[0].role == 'system'
    assert expected_messages[1].role == 'user'
    assert test_content in expected_messages[1].content


@pytest.mark.asyncio
async def test_fake_title_node_parse_res_construction(monkeypatch):
    """Test that ParseRes is constructed correctly with all context data"""
    # Mock dependencies
    def mock_print_dst_tree(dst_list):
        return "Test DST content"
    
    async def mock_fake_title_services(content):
        return "Test Generated Title"
    
    async def mock_callback(res, name, token, need_callback, return_ks3_url, callback_url):
        # Verify ParseRes construction
        assert isinstance(res, ParseRes)
        assert res.fake_title == "Test Generated Title"
        assert res.fake_title_embedding is None
        assert res.page_size == 25
        assert res.word_count == 200
        assert res.width == 1000
        assert res.height == 800
        assert res.is_scan is True
        assert res.image == [Image(image_type=ImageType.INPUT_IMAGE, page_num=1, url="test_url")]
        assert res.rotate_page == {0: [180]}
        assert res.parse_version == "2.5"
    
    monkeypatch.setattr("modules.flows.fake_title.fake_title.print_dst_indent_tree", mock_print_dst_tree)
    monkeypatch.setattr("modules.flows.fake_title.fake_title.fake_title_services", mock_fake_title_services)
    monkeypatch.setattr("modules.flows.fake_title.fake_title.callback_parse_background", mock_callback)
    
    # Create context with specific values
    ctx = PipelineContext(
        file_info=FileInfo(
            file_type=FileType.DOCX,
            page_size=25,
            word_count=200,
            width=1000,
            height=800,
            is_scan=True,
            rotate_page={0: [180]}
        )
    )
    ctx.dst = [_create_test_dst("test1")]
    ctx.embed_enabled = False
    ctx.handler_results = {}
    ctx.image = [Image(image_type=ImageType.INPUT_IMAGE, page_num=1, url="test_url")]
    ctx.parse_version = "2.5"
    ctx.token = "specific_token"
    ctx.need_callback = True
    ctx.return_ks3_url = "http://specific.ks3"
    ctx.callback_url = "http://specific.callback"
    
    # Process
    node = FakeTitleNode("fake_title_test")
    await node.process(ctx)
    
    # The assertions are done in the mock_callback function


@pytest.mark.asyncio
async def test_fake_title_node_simple_debug(monkeypatch):
    """Simple debug test to isolate the issue"""

    # Mock callback_parse_background
    async def mock_callback(res, name, token, need_callback, return_ks3_url, callback_url):
        pass

    monkeypatch.setattr(
        "modules.flows.fake_title.fake_title.callback_parse_background",
        mock_callback
    )

    try:
        # Create minimal context
        ctx = PipelineContext(
            file_info=FileInfo(file_type=FileType.PDF)
        )
        ctx.dst = []
        ctx.handler_results = {}
        ctx.embed_enabled = False
        ctx.parse_version = "1.0"
        
        # Create node
        node = FakeTitleNode("debug_test")
        
        # This should tell us where the error is occurring
        result = await node.process(ctx)
        
        # If we get here, the issue was elsewhere
        assert result is not None
        
    except Exception as e:
        pytest.fail(f"Debug test failed with: {e}")