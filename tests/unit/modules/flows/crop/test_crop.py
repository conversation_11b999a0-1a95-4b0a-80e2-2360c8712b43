# Author: linqi
# Date: 2025/8/13
# Time: 12:40

import pytest

from modules.flows.crop import crop as crop_mod
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.parse_entity import Image as ParseImage, ImageType
from modules.pipeline.base import PipelineHandler
from services.datamodel import FileType
from conf import ConfHandlerName

def _dst(id_, parent, level, page, text, dst_type=DSTType.TEXT, mark=None, bbox=None):
    attr = DSTAttribute(level=level, position=PositionInfo(bbox=bbox if bbox is not None else BBox(x1=0, y1=0, x2=100, y2=100)), page=page, hash="h" * 32)
    d = DST(id=id_, parent=parent, order=0, dst_type=dst_type, attributes=attr, content=[text])
    d.mark = mark
    return d


def test_has_multiple_specific_chars():
    assert crop_mod.has_multiple_specific_chars("凶☒ some text") is True
    assert crop_mod.has_multiple_specific_chars("only one ☒") is False
    assert crop_mod.has_multiple_specific_chars("") is False


def test_classify_and_filter_dsts():
    d1 = _dst("a", "-1", 10, 0, "凶 text ☒")
    d2 = _dst("b", "-1", 10, 0, "more")
    d3 = _dst("img", "-1", 10, 0, "", dst_type=DSTType.IMAGE)
    page_map, content = crop_mod.classify_and_filter_dsts([d1, d2, d3])
    assert 0 in page_map and "a" in page_map[0]
    assert content and content[0].page == 0


def test_cut_single_page_uses_cache_and_upload(monkeypatch):
    calls = {"n": 0}

    def _cut_pic(index, pdf_document, rotate, x0, x1, y0, y1):
        calls["n"] += 1
        return "YmFzZTY0"  # base64

    def _upload_image(img_b64):
        return f"url-{img_b64}"

    monkeypatch.setattr(crop_mod, "cut_pic", _cut_pic)
    # Patch the symbol used inside crop module (imported name)
    monkeypatch.setattr(crop_mod, "upload_image", _upload_image)

    captured = {0: "cached-url"}
    # cached path
    url1 = crop_mod.cut_single_page(100, 200, 0, pdf_document=object(), rotate=0, captured_images=captured)
    assert url1 == "cached-url" and calls["n"] == 0
    # non-cached path
    url2 = crop_mod.cut_single_page(100, 200, 1, pdf_document=object(), rotate=90, captured_images=captured)
    assert url2.startswith("url-") and calls["n"] == 1 and captured[1] == url2


def test_cut_pic_builds_base64(monkeypatch):
    class FakePixmap:
        width = 2
        height = 2
        samples = b"\x00" * 12

    class FakePage:
        def get_pixmap(self, matrix=None, clip=None):
            return FakePixmap()

    class FakeDoc:
        def load_page(self, index):
            return FakePage()

    class FakeImage:
        def __init__(self, *args, **kwargs):
            self._rotated = False

        def rotate(self, *args, **kwargs):
            self._rotated = True
            return self

        def save(self, buf, format="PNG"):
            buf.write(b"PNGDATA")

    class FakeImageModule:
        @staticmethod
        def frombytes(*args, **kwargs):
            return FakeImage()

    # monkeypatch fitz and PIL.Image
    class FakeRect:
        def __init__(self, *args, **kwargs):
            pass

    class FakeMatrix:
        def __init__(self, *args, **kwargs):
            pass

    monkeypatch.setattr(crop_mod, "fitz", type("F", (), {"Rect": FakeRect, "Matrix": FakeMatrix}))
    monkeypatch.setattr(crop_mod, "Image", FakeImageModule)

    b64 = crop_mod.cut_pic(0, FakeDoc(), rotate=0, x0=0, x1=100, y0=0, y1=100)
    assert isinstance(b64, str) and len(b64) > 0


@pytest.mark.asyncio
async def test_get_checkbox_crop(monkeypatch):
    # Return url for any page
    monkeypatch.setattr(crop_mod, "cut_single_page", lambda *a, **k: "url")
    # Avoid real PDF opening by PyMuPDF
    monkeypatch.setattr(crop_mod, "fitz", type("F", (), {"open": staticmethod(lambda **kwargs: object())}))

    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF, page_size=2, width=200, height=300))
    ctx.kdc_input = type("K", (), {"file_url_or_bytes": b"%PDF-1.4"})

    d1 = _dst("a", "-1", 10, 0, "凶☒ content")
    d2 = _dst("b", "-1", 10, 1, "plain")
    res = crop_mod.get_checkbox_crop(ctx, ctx.file_info, [d1, d2])
    # Should produce one checkbox entry for page 0 with image
    assert res and res[0].page == 0 and res[0].images and res[0].images[0].url == "url"


def test_get_table_page_crop(monkeypatch):
    # Return url for page screenshot
    monkeypatch.setattr(crop_mod, "cut_single_page", lambda *a, **k: "url")

    file_info = FileInfo(file_type=FileType.PDF, page_size=2, width=200, height=300)
    # two TABLE dst across pages
    bbox = BBox(x1=0, y1=0, x2=100, y2=100)
    d1 = DST(id="t1", parent="-1", order=0, dst_type=DSTType.TABLE, attributes=DSTAttribute(level=10, position=PositionInfo(bbox=bbox), page=0, hash="h"*32), content=["<table>"])
    d2 = DST(id="t2", parent="-1", order=0, dst_type=DSTType.TABLE, attributes=DSTAttribute(level=10, position=PositionInfo(bbox=bbox), page=1, hash="h"*32), content=["<table>"])
    out = crop_mod.get_table_page_crop(pdf_document=object(), dsts=[d1, d2], file_info=file_info, rotate_map={}, captured_images={})
    assert len(out) == 2 and all(img.image_type == ImageType.SCAN_IMAGE for img in out)


def test_get_pdf_scan_crop(monkeypatch):
    monkeypatch.setattr(crop_mod, "cut_single_page", lambda *a, **k: "url")
    file_info = FileInfo(file_type=FileType.PDF, page_size=3, width=100, height=200)
    out = crop_mod.get_pdf_scan_crop(pdf_document=object(), file_info=file_info, rotate_map={}, captured_images={})
    assert len(out) == 3 and out[0].image_type == ImageType.SCAN_IMAGE


def test_get_table_crop_produces_rotated_image(monkeypatch):
    # Fake fitz and PIL.Image
    class FakePixmap:
        width = 2
        height = 2
        samples = b"\x00" * 12

    class FakePage:
        def get_pixmap(self, matrix=None, clip=None):
            return FakePixmap()

    class FakeDoc:
        def load_page(self, idx):
            return FakePage()

    class FakeImage:
        def __init__(self, *a, **k):
            self.rotated = False
        def rotate(self, *a, **k):
            self.rotated = True
            return self
        def save(self, buf, format="PNG"):
            buf.write(b"PNGDATA")

    class FakeImageModule:
        @staticmethod
        def frombytes(*a, **k):
            return FakeImage()

    monkeypatch.setattr(crop_mod, "fitz", type("F", (), {"Rect": lambda *a, **k: None, "Matrix": lambda *a, **k: None}))
    monkeypatch.setattr(crop_mod, "Image", FakeImageModule)
    monkeypatch.setattr(crop_mod, "upload_image", lambda b64: "url")
    monkeypatch.setattr(crop_mod, "calculate_original_bbox", lambda bbox, w, h: (0, 0, 200, 200))

    bbox = BBox(x1=0, y1=0, x2=100, y2=100, rotate=90)
    pos = PositionInfo(bbox=bbox)
    dtable = DST(id="tb", parent="-1", order=0, dst_type=DSTType.TABLE,
                 attributes=DSTAttribute(level=10, position=pos, page=0, hash="h"*32), content=["<table>"])
    out = crop_mod.get_table_crop(FakeDoc(), [dtable], FileInfo(file_type=FileType.PDF, width=200, height=300))
    assert out and out[0].image_type == ImageType.TABLE_IMAGE and out[0].url == "url"


@pytest.mark.asyncio
async def test_cropnode_process_with_stubbed_crop_shot(monkeypatch):
    # stub crop_shot to return predictable images
    async def _arun(func, *args, **kwargs):
        # call stub sync func
        return [ParseImage(page_num=0, url="u", image_type=ImageType.SCAN_IMAGE)]

    from commons.thread.multiprocess import MultiProcess
    monkeypatch.setattr(MultiProcess, "arun", lambda self, f, *a, **k: _arun(f, *a, **k))

    ctx = PipelineContext(file_info=FileInfo(file_type="pdf", page_size=1, width=100, height=100))
    ctx.kdc_input = type("K", (), {"file_url_or_bytes": b"%PDF-1.4"})

    out = await crop_mod.CropNode("crop").process(ctx)
    assert ConfHandlerName.screenshot_handler not in out.handler_results  # crop handler writes under its own name
    assert "crop" in out.handler_results and out.handler_results["crop"][0].url == "u"


def test_get_table_page_crop_is_scan_early_return():
    out = crop_mod.get_table_page_crop(pdf_document=object(), dsts=[], file_info=FileInfo(file_type=FileType.PDF, page_size=1, width=10, height=10, is_scan=True), rotate_map={}, captured_images={})
    assert out == []


def test_get_pdf_scan_crop_with_none_url(monkeypatch):
    # return None for one page to hit continue branch
    seq = iter(["u1", None, "u3"])
    monkeypatch.setattr(crop_mod, "cut_single_page", lambda *a, **k: next(seq))
    file_info = FileInfo(file_type=FileType.PDF, page_size=3, width=10, height=10)
    out = crop_mod.get_pdf_scan_crop(pdf_document=object(), file_info=file_info, rotate_map={}, captured_images={})
    assert [img.url for img in out] == ["u1", "u3"]


def test_get_checkbox_crop_non_pdf_returns_empty():
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX, page_size=1, width=10, height=10))
    out = crop_mod.get_checkbox_crop(ctx, ctx.file_info, [])
    assert out == []


def test_get_checkbox_crop_with_rotate_map(monkeypatch):
    # cut_single_page stub and fitz.open stub
    monkeypatch.setattr(crop_mod, "cut_single_page", lambda *a, **k: "url")
    monkeypatch.setattr(crop_mod, "fitz", type("F", (), {"open": staticmethod(lambda **kwargs: object())}))
    fi = FileInfo(file_type=FileType.PDF, page_size=1, width=10, height=10, rotate_page={"90": [0]})
    ctx = PipelineContext(file_info=fi)
    ctx.kdc_input = type("K", (), {"file_url_or_bytes": b"%PDF-1.4"})
    d = _dst("a", "-1", 10, 0, "☑ 凶")
    out = crop_mod.get_checkbox_crop(ctx, fi, [d])
    assert out and out[0].images and out[0].images[0].url == "url"


def test_load_pdf_bytes_with_url(monkeypatch):
    class Resp:
        content = b"%PDF-1.4"
        def raise_for_status(self):
            return None
    class FakeReq:
        def get(self, url, verify=False):
            return Resp()
    class FakePool:
        def get_file_req(self):
            return FakeReq()
    monkeypatch.setattr(crop_mod, "ConnPool", lambda: FakePool())
    out = crop_mod.load_pdf_bytes("http://x")
    assert out == b"%PDF-1.4"


def test_crop_shot_returns_existing_images():
    imgs = [ParseImage(page_num=0, url="u", image_type=ImageType.SCAN_IMAGE)]
    ctx = PipelineContext(file_info=FileInfo(file_type="pdf", page_size=1, width=10, height=10))
    ctx.image = imgs
    out = crop_mod.crop_shot(ctx, [])
    assert out == imgs


def test_crop_shot_non_pdf_logs_and_noop():
    ctx = PipelineContext(file_info=FileInfo(file_type="docx", page_size=1, width=10, height=10))
    out = crop_mod.crop_shot(ctx, [])
    assert out == []


def test_crop_shot_pdf_is_scan_true(monkeypatch):
    # stub load and fitz and get_pdf_scan_crop
    monkeypatch.setattr(crop_mod, "load_pdf_bytes", lambda x: b"%PDF-1.4")
    monkeypatch.setattr(crop_mod, "fitz", type("F", (), {"open": staticmethod(lambda **kwargs: object())}))
    monkeypatch.setattr(crop_mod, "get_pdf_scan_crop", lambda *a, **k: [ParseImage(page_num=0, url="u", image_type=ImageType.SCAN_IMAGE)])
    monkeypatch.setattr(crop_mod, "get_table_page_crop", lambda *a, **k: [])
    monkeypatch.setattr(crop_mod, "get_table_crop", lambda *a, **k: [])

    fi = FileInfo(file_type="pdf", page_size=1, width=10, height=10, is_scan=True, rotate_page={"90": [0]})
    ctx = PipelineContext(file_info=fi)
    ctx.kdc_input = type("K", (), {"file_url_or_bytes": b"%PDF-1.4"})
    # Ensure downstream access to dst handler key does not fail
    ctx.handler_results[ConfHandlerName.dst_handler] = []
    out = crop_mod.crop_shot(ctx, [])
    assert out and out[0].url == "u"


def test_crop_shot_pdf_table_paths(monkeypatch):
    monkeypatch.setattr(crop_mod, "load_pdf_bytes", lambda x: b"%PDF-1.4")
    monkeypatch.setattr(crop_mod, "fitz", type("F", (), {"open": staticmethod(lambda **kwargs: object())}))
    monkeypatch.setattr(crop_mod, "get_pdf_scan_crop", lambda *a, **k: [])
    monkeypatch.setattr(crop_mod, "get_table_page_crop", lambda *a, **k: [ParseImage(page_num=0, url="p", image_type=ImageType.SCAN_IMAGE)])
    monkeypatch.setattr(crop_mod, "get_table_crop", lambda *a, **k: [ParseImage(page_num=0, url="t", image_type=ImageType.TABLE_IMAGE)])
    ctx = PipelineContext(file_info=FileInfo(file_type="pdf", page_size=1, width=10, height=10))
    ctx.kdc_input = type("K", (), {"file_url_or_bytes": b"%PDF-1.4"})
    ctx.handler_results[ConfHandlerName.dst_handler] = []
    out = crop_mod.crop_shot(ctx, [])
    assert [img.url for img in out] == ["p", "t"]


@pytest.mark.asyncio
async def test_cropnode_process_exception_path(monkeypatch):
    from commons.thread.multiprocess import MultiProcess
    def _raise(*a, **k):
        raise RuntimeError("boom")
    monkeypatch.setattr(MultiProcess, "arun", lambda self, f, *a, **k: _raise())
    ctx = PipelineContext(file_info=FileInfo(file_type="pdf", page_size=1, width=10, height=10))
    ctx.kdc_input = type("K", (), {"file_url_or_bytes": b"%PDF-1.4"})
    with pytest.raises(RuntimeError):
        await crop_mod.CropNode("crop").process(ctx)

