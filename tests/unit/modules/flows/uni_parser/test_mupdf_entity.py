# Author: linqi
# Date: 2025/8/16
# Time: 16:45

import pytest
from typing import List, Tuple

from modules.flows.uni_parser.mupdf_entity import (
    Span, MupdfLine, is_horizontally_to_the_right
)


class TestSpan:
    """Test Span model"""

    def test_span_creation_empty(self):
        """Test Span creation with no parameters"""
        span = Span()
        assert span.size is None
        assert span.flags is None
        assert span.bidi is None
        assert span.char_flags is None
        assert span.font is None
        assert span.color is None
        assert span.alpha is None
        assert span.ascender is None
        assert span.descender is None
        assert span.text == ""  # Default value
        assert span.origin is None
        assert span.bbox is None

    def test_span_creation_with_params(self):
        """Test Span creation with parameters"""
        span = Span(
            size=12.0,
            flags=4,
            bidi=0,
            char_flags=0,
            font="Arial",
            color=0,
            alpha=255,
            ascender=10.0,
            descender=-2.0,
            text="Hello World",
            origin=[100.0, 200.0],
            bbox=[100.0, 190.0, 200.0, 202.0]
        )
        
        assert span.size == 12.0
        assert span.flags == 4
        assert span.bidi == 0
        assert span.char_flags == 0
        assert span.font == "Arial"
        assert span.color == 0
        assert span.alpha == 255
        assert span.ascender == 10.0
        assert span.descender == -2.0
        assert span.text == "Hello World"
        assert span.origin == [100.0, 200.0]
        assert span.bbox == [100.0, 190.0, 200.0, 202.0]

    def test_span_partial_params(self):
        """Test Span creation with partial parameters"""
        span = Span(
            text="Test text",
            font="Times New Roman",
            size=14.0
        )
        
        assert span.text == "Test text"
        assert span.font == "Times New Roman"
        assert span.size == 14.0
        assert span.flags is None
        assert span.color is None

    def test_span_empty_text_default(self):
        """Test that text defaults to empty string"""
        span = Span(font="Arial")
        assert span.text == ""

    def test_span_list_fields(self):
        """Test Span with list fields"""
        origin = [50.5, 75.2]
        bbox = [50.0, 70.0, 150.0, 80.0]
        
        span = Span(origin=origin, bbox=bbox)
        
        assert span.origin == origin
        assert span.bbox == bbox


class TestMupdfLine:
    """Test MupdfLine model"""

    def test_mupdfline_creation_empty(self):
        """Test MupdfLine creation with no parameters"""
        line = MupdfLine()
        assert line.spans is None
        assert line.wmode is None
        assert line.dir is None
        assert line.bbox is None

    def test_mupdfline_creation_with_params(self):
        """Test MupdfLine creation with parameters"""
        spans = [
            Span(text="Hello", font="Arial"),
            Span(text=" World", font="Arial")
        ]
        direction = [1.0, 0.0]
        bbox = [100.0, 200.0, 300.0, 220.0]
        
        line = MupdfLine(
            spans=spans,
            wmode=0,
            dir=direction,
            bbox=bbox
        )
        
        assert line.spans == spans
        assert line.wmode == 0
        assert line.dir == direction
        assert line.bbox == bbox

    def test_mupdfline_with_multiple_spans(self):
        """Test MupdfLine with multiple spans"""
        spans = [
            Span(text="First", size=12.0),
            Span(text="Second", size=14.0),
            Span(text="Third", size=10.0)
        ]
        
        line = MupdfLine(spans=spans, wmode=1)
        
        assert len(line.spans) == 3
        assert line.spans[0].text == "First"
        assert line.spans[1].text == "Second"
        assert line.spans[2].text == "Third"
        assert line.wmode == 1

    def test_mupdfline_empty_spans_list(self):
        """Test MupdfLine with empty spans list"""
        line = MupdfLine(spans=[])
        assert line.spans == []
        assert len(line.spans) == 0


class TestIsHorizontallyToTheRight:
    """Test is_horizontally_to_the_right function"""

    def test_perfect_horizontal_right(self):
        """Test perfect horizontal right direction"""
        result = is_horizontally_to_the_right((1.0, 0.0))
        assert result is True

    def test_perfect_horizontal_left(self):
        """Test horizontal left direction"""
        result = is_horizontally_to_the_right((-1.0, 0.0))
        assert result is False

    def test_perfect_vertical_up(self):
        """Test vertical up direction"""
        result = is_horizontally_to_the_right((0.0, 1.0))
        assert result is False

    def test_perfect_vertical_down(self):
        """Test vertical down direction"""
        result = is_horizontally_to_the_right((0.0, -1.0))
        assert result is False

    def test_diagonal_direction(self):
        """Test diagonal directions"""
        result = is_horizontally_to_the_right((0.5, 0.5))
        assert result is False
        
        result = is_horizontally_to_the_right((1.5, 0.1))
        assert result is False

    def test_within_tolerance_horizontal_right(self):
        """Test directions within tolerance of horizontal right"""
        # Just within default tolerance (1e-6)
        result = is_horizontally_to_the_right((1.0000005, 0.0000005))
        assert result is True
        
        # Just outside default tolerance
        result = is_horizontally_to_the_right((1.000002, 0.000001))
        assert result is False

    def test_custom_tolerance(self):
        """Test with custom tolerance"""
        # Should be True with higher tolerance
        result = is_horizontally_to_the_right((1.01, 0.01), tolerance=0.02)
        assert result is True
        
        # Should be False with lower tolerance
        result = is_horizontally_to_the_right((1.01, 0.01), tolerance=0.005)
        assert result is False

    def test_negative_horizontal_components(self):
        """Test with negative horizontal components"""
        result = is_horizontally_to_the_right((-0.5, 0.0))
        assert result is False
        
        result = is_horizontally_to_the_right((0.5, 0.0))
        assert result is False

    def test_zero_direction(self):
        """Test with zero direction vector"""
        result = is_horizontally_to_the_right((0.0, 0.0))
        assert result is False

    def test_invalid_input_length(self):
        """Test with invalid input length"""
        with pytest.raises(ValueError, match="Input must be a tuple of two floats"):
            is_horizontally_to_the_right((1.0,))
        
        with pytest.raises(ValueError, match="Input must be a tuple of two floats"):
            is_horizontally_to_the_right((1.0, 0.0, 0.0))
        
        with pytest.raises(ValueError, match="Input must be a tuple of two floats"):
            is_horizontally_to_the_right([])

    def test_edge_cases_tolerance(self):
        """Test edge cases with tolerance boundaries"""
        tolerance = 1e-6
        
        # Within tolerance boundary - should be True
        result = is_horizontally_to_the_right((1.0, 0.0))
        assert result is True
        
        # Very close to perfect horizontal right - should be True
        result = is_horizontally_to_the_right((1.0 + tolerance/2, tolerance/2))
        assert result is True
        
        result = is_horizontally_to_the_right((1.0 - tolerance/2, -tolerance/2))
        assert result is True
        
        # Just outside tolerance boundary - should be False
        result = is_horizontally_to_the_right((1.0 + tolerance * 2, 0.0))
        assert result is False

    def test_floating_point_precision(self):
        """Test floating point precision edge cases"""
        # Very small deviations that should be within tolerance
        result = is_horizontally_to_the_right((0.9999999, 0.0000001))
        assert result is True
        
        result = is_horizontally_to_the_right((1.0000001, -0.0000001))
        assert result is True

    def test_large_deviations(self):
        """Test with large deviations from horizontal right"""
        result = is_horizontally_to_the_right((2.0, 0.0))
        assert result is False
        
        result = is_horizontally_to_the_right((1.0, 1.0))
        assert result is False
        
        result = is_horizontally_to_the_right((0.0, 0.0))
        assert result is False