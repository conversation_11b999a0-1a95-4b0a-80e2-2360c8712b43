# Author: linqi
# Date: 2025/8/16
# Time: 16:45

import pytest
from unittest.mock import MagicMock, patch, mock_open
from io import BytesIO
import hashlib
import requests

from modules.flows.uni_parser.image import (
    ImageParser, parse_image_to_dst, _load_image
)
from modules.flows.uni_parser.uniparse_template import UniParseTemplate
from modules.pipeline.context import PipelineContext
from modules.entity.dst_entity import DST, DSTType
from modules.entity.parse_entity import Image as ParseEntityImage, ImageType


@pytest.fixture
def image_parser():
    """Create an ImageParser instance for testing"""
    return ImageParser()


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    context.kdc_input = MagicMock()
    context.kdc_input.file_url_or_bytes = "http://test.image.url"
    context.file_info = MagicMock()
    context.image = []
    context.update_multiple_parse_dsts_unsafe = MagicMock()
    return context


@pytest.fixture
def mock_image_bytes():
    """Create mock image bytes"""
    return b"fake_image_data_for_testing"


class TestImageParser:
    """Test ImageParser class"""

    def test_image_parser_inheritance(self, image_parser):
        """Test ImageParser inherits from UniParseTemplate"""
        assert isinstance(image_parser, UniParseTemplate)
        assert isinstance(image_parser, ImageParser)

    @pytest.mark.asyncio
    async def test_image_parser_uniparse_process(self, image_parser, mock_context, mock_image_bytes, monkeypatch):
        """Test ImageParser.uniparse_process method"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock _load_image function
        def mock_load_image(file_url_or_bytes):
            return mock_image_bytes
        
        # Mock parse_image_to_dst function
        async def mock_parse_image_to_dst(image_bytes, context):
            return {0: ["mock_dst_list"]}
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.image.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.image._load_image', mock_load_image)
        monkeypatch.setattr('modules.flows.uni_parser.image.parse_image_to_dst', mock_parse_image_to_dst)
        
        # Execute
        result_context = await image_parser.uniparse_process(mock_context, [0], True)
        
        # Verify
        assert result_context == mock_context
        mock_context.update_multiple_parse_dsts_unsafe.assert_called_once_with(0, ["mock_dst_list"])

    @pytest.mark.asyncio
    async def test_image_parser_uniparse_process_multiple_pages(self, image_parser, mock_context, mock_image_bytes, monkeypatch):
        """Test ImageParser.uniparse_process with multiple pages result"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock _load_image function
        def mock_load_image(file_url_or_bytes):
            return mock_image_bytes
        
        # Mock parse_image_to_dst function that returns multiple pages
        async def mock_parse_image_to_dst(image_bytes, context):
            return {
                0: ["dst_list_page_0"],
                1: ["dst_list_page_1"]
            }
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.image.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.image._load_image', mock_load_image)
        monkeypatch.setattr('modules.flows.uni_parser.image.parse_image_to_dst', mock_parse_image_to_dst)
        
        # Execute
        result_context = await image_parser.uniparse_process(mock_context, [0, 1], False)
        
        # Verify
        assert result_context == mock_context
        # Should be called twice, once for each page
        assert mock_context.update_multiple_parse_dsts_unsafe.call_count == 2
        mock_context.update_multiple_parse_dsts_unsafe.assert_any_call(0, ["dst_list_page_0"])
        mock_context.update_multiple_parse_dsts_unsafe.assert_any_call(1, ["dst_list_page_1"])


class TestParseImageToDst:
    """Test parse_image_to_dst function"""

    @pytest.mark.asyncio
    async def test_parse_image_to_dst_success(self, mock_context, mock_image_bytes, monkeypatch):
        """Test parse_image_to_dst successful execution"""
        # Mock PIL Image
        mock_pil_image = MagicMock()
        mock_pil_image.size = (800, 600)
        mock_pil_image.close = MagicMock()
        
        mock_image_open = MagicMock(return_value=mock_pil_image)
        
        # Mock upload_image
        mock_upload_image = MagicMock(return_value="http://uploaded.image.url")
        
        # Mock build functions
        mock_root_dst = MagicMock()
        mock_root_dst.id = "root_dst_id"
        
        mock_build_root_dst = MagicMock(return_value=mock_root_dst)
        mock_build_dst_id = MagicMock(return_value="generated_dst_id")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.image.Image.open', mock_image_open)
        monkeypatch.setattr('modules.flows.uni_parser.image.upload_image', mock_upload_image)
        monkeypatch.setattr('modules.flows.uni_parser.image.build_root_dst', mock_build_root_dst)
        monkeypatch.setattr('modules.flows.uni_parser.image.build_dst_id', mock_build_dst_id)
        
        # Execute
        result = await parse_image_to_dst(mock_image_bytes, mock_context)
        
        # Verify result structure
        assert isinstance(result, dict)
        assert 0 in result
        dst_list = result[0]
        assert len(dst_list) == 2  # root_dst + image_dst
        
        # Verify context updates
        assert len(mock_context.image) == 1
        image_obj = mock_context.image[0]
        assert isinstance(image_obj, ParseEntityImage)
        assert image_obj.page_num == 0
        assert image_obj.url == "http://uploaded.image.url"
        assert image_obj.image_type == ImageType.INPUT_IMAGE
        
        # Verify file_info updates
        assert mock_context.file_info.page_size == 1
        assert mock_context.file_info.width == 800
        assert mock_context.file_info.height == 600
        
        # Verify mocks were called
        mock_upload_image.assert_called_once_with(mock_image_bytes)
        mock_pil_image.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_parse_image_to_dst_upload_failure(self, mock_context, mock_image_bytes, monkeypatch):
        """Test parse_image_to_dst when image upload fails"""
        # Mock PIL Image
        mock_pil_image = MagicMock()
        mock_pil_image.size = (800, 600)
        mock_pil_image.close = MagicMock()
        
        mock_image_open = MagicMock(return_value=mock_pil_image)
        
        # Mock upload_image to return None (failure)
        mock_upload_image = MagicMock(return_value=None)
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.image.Image.open', mock_image_open)
        monkeypatch.setattr('modules.flows.uni_parser.image.upload_image', mock_upload_image)
        
        # Execute and expect exception
        with pytest.raises(ValueError, match="Image upload failed, image_url is empty"):
            await parse_image_to_dst(mock_image_bytes, mock_context)

    @pytest.mark.asyncio
    async def test_parse_image_to_dst_empty_url(self, mock_context, mock_image_bytes, monkeypatch):
        """Test parse_image_to_dst when image upload returns empty string"""
        # Mock PIL Image
        mock_pil_image = MagicMock()
        mock_pil_image.size = (1024, 768)
        mock_pil_image.close = MagicMock()
        
        mock_image_open = MagicMock(return_value=mock_pil_image)
        
        # Mock upload_image to return empty string
        mock_upload_image = MagicMock(return_value="")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.image.Image.open', mock_image_open)
        monkeypatch.setattr('modules.flows.uni_parser.image.upload_image', mock_upload_image)
        
        # Execute and expect exception
        with pytest.raises(ValueError, match="Image upload failed, image_url is empty"):
            await parse_image_to_dst(mock_image_bytes, mock_context)

    @pytest.mark.asyncio
    async def test_parse_image_to_dst_dst_structure(self, mock_context, mock_image_bytes, monkeypatch):
        """Test parse_image_to_dst creates correct DST structure"""
        # Mock PIL Image
        mock_pil_image = MagicMock()
        mock_pil_image.size = (1200, 800)
        mock_pil_image.close = MagicMock()
        
        # Mock upload_image
        mock_upload_image = MagicMock(return_value="http://test.image.url")
        
        # Mock build functions
        mock_root_dst = MagicMock()
        mock_root_dst.id = "root_id_123"
        
        mock_build_root_dst = MagicMock(return_value=mock_root_dst)
        mock_build_dst_id = MagicMock(return_value="image_dst_456")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.image.Image.open', MagicMock(return_value=mock_pil_image))
        monkeypatch.setattr('modules.flows.uni_parser.image.upload_image', mock_upload_image)
        monkeypatch.setattr('modules.flows.uni_parser.image.build_root_dst', mock_build_root_dst)
        monkeypatch.setattr('modules.flows.uni_parser.image.build_dst_id', mock_build_dst_id)
        
        # Execute
        result = await parse_image_to_dst(mock_image_bytes, mock_context)
        
        # Verify DST structure
        dst_list = result[0]
        assert len(dst_list) == 2
        
        root_dst = dst_list[0]
        image_dst = dst_list[1]
        
        # Verify root DST
        assert root_dst == mock_root_dst
        
        # Verify image DST
        assert isinstance(image_dst, DST)
        assert image_dst.id == "image_dst_456"
        assert image_dst.parent == "root_id_123"
        assert image_dst.order == 1
        assert image_dst.dst_type == DSTType.IMAGE
        assert image_dst.content == ["http://test.image.url"]
        assert image_dst.image_pixel == [1200, 800]
        
        # Verify attributes
        assert image_dst.attributes.page == 0
        assert image_dst.attributes.level == 0
        expected_hash = hashlib.sha1(mock_image_bytes).hexdigest()
        assert image_dst.attributes.hash == expected_hash


class TestLoadImage:
    """Test _load_image function"""

    def test_load_image_from_url(self, monkeypatch):
        """Test _load_image with URL input"""
        # Mock response
        mock_response = MagicMock()
        mock_response.content = b"image_data_from_url"
        mock_response.raise_for_status = MagicMock()
        
        mock_requests_get = MagicMock(return_value=mock_response)
        monkeypatch.setattr('modules.flows.uni_parser.image.requests.get', mock_requests_get)
        
        # Execute
        result = _load_image("http://test.image.url")
        
        # Verify
        assert result == b"image_data_from_url"
        mock_requests_get.assert_called_once_with("http://test.image.url")
        mock_response.raise_for_status.assert_called_once()

    def test_load_image_from_bytes(self):
        """Test _load_image with bytes input"""
        image_bytes = b"direct_image_bytes"
        
        # Execute
        result = _load_image(image_bytes)
        
        # Verify - should return bytes as-is
        assert result == image_bytes

    def test_load_image_invalid_type(self):
        """Test _load_image with invalid input type"""
        # Execute and expect exception
        with pytest.raises(ValueError, match="无效的文件格式，需要URL字符串或二进制数据"):
            _load_image(123)
        
        with pytest.raises(ValueError, match="无效的文件格式，需要URL字符串或二进制数据"):
            _load_image(None)
        
        with pytest.raises(ValueError, match="无效的文件格式，需要URL字符串或二进制数据"):
            _load_image(['not', 'valid'])

    def test_load_image_http_error(self, monkeypatch):
        """Test _load_image handles HTTP errors"""
        # Mock requests to raise HTTP error
        def mock_requests_get(url):
            response = MagicMock()
            response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Not Found")
            return response
        
        monkeypatch.setattr('modules.flows.uni_parser.image.requests.get', mock_requests_get)
        
        # Execute and expect exception
        with pytest.raises(requests.exceptions.HTTPError):
            _load_image("http://invalid.url")

    def test_load_image_network_error(self, monkeypatch):
        """Test _load_image handles network errors"""
        # Mock requests to raise connection error
        mock_requests_get = MagicMock(side_effect=requests.exceptions.ConnectionError("Network error"))
        monkeypatch.setattr('modules.flows.uni_parser.image.requests.get', mock_requests_get)
        
        # Execute and expect exception
        with pytest.raises(requests.exceptions.ConnectionError):
            _load_image("http://unreachable.url")

    def test_load_image_empty_bytes(self):
        """Test _load_image with empty bytes"""
        # Execute
        result = _load_image(b"")
        
        # Verify - should return empty bytes
        assert result == b""