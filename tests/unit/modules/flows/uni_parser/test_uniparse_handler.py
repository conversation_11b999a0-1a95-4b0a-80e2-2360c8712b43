# Author: linqi
# Date: 2025/8/16
# Time: 16:45

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
import asyncio

from modules.flows.uni_parser.uniparse_handler import (
    ParserToolFactory, UniParseNode
)
from modules.flows.uni_parser.uniparse_template import Uni<PERSON>arseTemplate
from modules.entity.pre_check_entity import Parser<PERSON>ame, ParserConfig
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext


@pytest.fixture
def uniparse_node():
    """Create a UniParseNode instance for testing"""
    return UniParseNode("test_uniparse")


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    context.handler_results = {}
    return context


class TestParserToolFactory:
    """Test ParserToolFactory class"""

    def test_factory_has_parsers(self):
        """Test ParserToolFactory has expected parsers mapping"""
        parsers = ParserToolFactory._parsers
        
        # Should have three types of parsers
        assert ParserName.KdcParser in parsers
        assert ParserName.MuPdfParser in parsers
        assert ParserName.ImageParser in parsers
        
        # Should reference the correct classes (we can't import the actual classes easily, 
        # so we just check they exist and are callable)
        assert callable(parsers[ParserName.KdcParser])
        assert callable(parsers[ParserName.MuPdfParser])
        assert callable(parsers[ParserName.ImageParser])

    def test_get_parser_kdc(self, monkeypatch):
        """Test get_parser returns KDC parser"""
        # Mock the KDCParser class
        mock_kdc_instance = MagicMock(spec=UniParseTemplate)
        mock_kdc_class = MagicMock(return_value=mock_kdc_instance)
        
        # Replace the KDCParser in the factory
        original_parsers = ParserToolFactory._parsers.copy()
        ParserToolFactory._parsers[ParserName.KdcParser] = mock_kdc_class
        
        try:
            # Execute
            parser = ParserToolFactory.get_parser(ParserName.KdcParser)
            
            # Verify
            assert parser == mock_kdc_instance
            mock_kdc_class.assert_called_once()
        finally:
            # Restore original parsers
            ParserToolFactory._parsers = original_parsers

    def test_get_parser_mupdf(self, monkeypatch):
        """Test get_parser returns MuPDF parser"""
        # Mock the MUPdfParser class
        mock_mupdf_instance = MagicMock(spec=UniParseTemplate)
        mock_mupdf_class = MagicMock(return_value=mock_mupdf_instance)
        
        # Replace the MUPdfParser in the factory
        original_parsers = ParserToolFactory._parsers.copy()
        ParserToolFactory._parsers[ParserName.MuPdfParser] = mock_mupdf_class
        
        try:
            # Execute
            parser = ParserToolFactory.get_parser(ParserName.MuPdfParser)
            
            # Verify
            assert parser == mock_mupdf_instance
            mock_mupdf_class.assert_called_once()
        finally:
            # Restore original parsers
            ParserToolFactory._parsers = original_parsers

    def test_get_parser_image(self, monkeypatch):
        """Test get_parser returns Image parser"""
        # Mock the ImageParser class
        mock_image_instance = MagicMock(spec=UniParseTemplate)
        mock_image_class = MagicMock(return_value=mock_image_instance)
        
        # Replace the ImageParser in the factory
        original_parsers = ParserToolFactory._parsers.copy()
        ParserToolFactory._parsers[ParserName.ImageParser] = mock_image_class
        
        try:
            # Execute
            parser = ParserToolFactory.get_parser(ParserName.ImageParser)
            
            # Verify
            assert parser == mock_image_instance
            mock_image_class.assert_called_once()
        finally:
            # Restore original parsers
            ParserToolFactory._parsers = original_parsers

    def test_get_parser_invalid_name(self):
        """Test get_parser raises ValueError for invalid parser name"""
        # Create a mock parser name that doesn't exist
        invalid_parser_name = "InvalidParser"
        
        # Execute and expect exception
        with pytest.raises(ValueError, match="No parser available for parser name"):
            ParserToolFactory.get_parser(invalid_parser_name)

    def test_get_parser_none_name(self):
        """Test get_parser handles None parser name"""
        with pytest.raises(ValueError, match="No parser available for parser name"):
            ParserToolFactory.get_parser(None)


class TestUniParseNode:
    """Test UniParseNode class"""

    def test_uniparse_node_inheritance(self, uniparse_node):
        """Test UniParseNode inherits from PipelineHandler"""
        assert isinstance(uniparse_node, PipelineHandler)
        assert isinstance(uniparse_node, UniParseNode)
        assert uniparse_node.name == "test_uniparse"

    def test_uniparse_node_initialization(self):
        """Test UniParseNode initialization"""
        name = "custom_uniparse"
        node = UniParseNode(name)
        assert node.name == name

    @pytest.mark.asyncio
    async def test_uniparse_node_process_single_parser(self, uniparse_node, mock_context, monkeypatch):
        """Test UniParseNode.process with single parser configuration"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Create mock parser config
        mock_parser_config = ParserConfig(
            parser_name=ParserName.KdcParser,
            processing_pages={0, 1, 2},
            is_all=False
        )
        
        # Set up context with pre_check result
        mock_context.handler_results = {
            "pre_check_handler": [mock_parser_config]
        }
        
        # Mock parser
        mock_parser = MagicMock(spec=UniParseTemplate)
        mock_parser.uniparse_process = AsyncMock(return_value=mock_context)
        
        # Mock factory
        def mock_get_parser(parser_name):
            return mock_parser
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ConfHandlerName', mock_conf_handler_name)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ParserToolFactory.get_parser', mock_get_parser)
        
        # Execute
        result_context = await uniparse_node.process(mock_context)
        
        # Verify
        assert result_context == mock_context
        mock_parser.uniparse_process.assert_called_once_with(
            mock_context, [0, 1, 2], False
        )

    @pytest.mark.asyncio
    async def test_uniparse_node_process_multiple_parsers(self, uniparse_node, mock_context, monkeypatch):
        """Test UniParseNode.process with multiple parser configurations"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Create mock parser configs
        kdc_config = ParserConfig(
            parser_name=ParserName.KdcParser,
            processing_pages={0, 1},
            is_all=False
        )
        mupdf_config = ParserConfig(
            parser_name=ParserName.MuPdfParser,
            processing_pages={2, 3},
            is_all=False
        )
        
        # Set up context with pre_check results
        mock_context.handler_results = {
            "pre_check_handler": [kdc_config, mupdf_config]
        }
        
        # Mock parsers
        mock_kdc_parser = MagicMock(spec=UniParseTemplate)
        mock_kdc_parser.uniparse_process = AsyncMock(return_value=mock_context)
        
        mock_mupdf_parser = MagicMock(spec=UniParseTemplate)
        mock_mupdf_parser.uniparse_process = AsyncMock(return_value=mock_context)
        
        # Mock factory
        def mock_get_parser(parser_name):
            if parser_name == ParserName.KdcParser:
                return mock_kdc_parser
            elif parser_name == ParserName.MuPdfParser:
                return mock_mupdf_parser
            else:
                raise ValueError(f"Unexpected parser name: {parser_name}")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ConfHandlerName', mock_conf_handler_name)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ParserToolFactory.get_parser', mock_get_parser)
        
        # Execute
        result_context = await uniparse_node.process(mock_context)
        
        # Verify
        assert result_context == mock_context
        
        # Both parsers should have been called
        mock_kdc_parser.uniparse_process.assert_called_once_with(
            mock_context, [0, 1], False
        )
        mock_mupdf_parser.uniparse_process.assert_called_once_with(
            mock_context, [2, 3], False
        )

    @pytest.mark.asyncio
    async def test_uniparse_node_process_empty_config(self, uniparse_node, mock_context, monkeypatch):
        """Test UniParseNode.process with empty parser configuration"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Set up context with empty pre_check results
        mock_context.handler_results = {
            "pre_check_handler": []
        }
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ConfHandlerName', mock_conf_handler_name)
        
        # Execute
        result_context = await uniparse_node.process(mock_context)
        
        # Verify - should return context without doing anything
        assert result_context == mock_context

    @pytest.mark.asyncio
    async def test_uniparse_node_process_parser_exception(self, uniparse_node, mock_context, monkeypatch):
        """Test UniParseNode.process when parser raises exception"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Create mock parser config
        mock_parser_config = ParserConfig(
            parser_name=ParserName.KdcParser,
            processing_pages={0},
            is_all=True
        )
        
        # Set up context
        mock_context.handler_results = {
            "pre_check_handler": [mock_parser_config]
        }
        
        # Mock parser that raises exception
        mock_parser = MagicMock(spec=UniParseTemplate)
        mock_parser.uniparse_process = AsyncMock(side_effect=Exception("Parser error"))
        
        # Mock factory
        def mock_get_parser(parser_name):
            return mock_parser
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ConfHandlerName', mock_conf_handler_name)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ParserToolFactory.get_parser', mock_get_parser)
        
        # Execute and expect exception to propagate
        with pytest.raises(Exception, match="Parser error"):
            await uniparse_node.process(mock_context)

    @pytest.mark.asyncio
    async def test_uniparse_node_process_factory_exception(self, uniparse_node, mock_context, monkeypatch):
        """Test UniParseNode.process when factory raises exception"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Create mock parser config with valid parser name but invalid value
        mock_parser_config = MagicMock()
        mock_parser_config.parser_name = "InvalidParser"
        mock_parser_config.processing_pages = {0}
        mock_parser_config.is_all = True
        
        # Set up context
        mock_context.handler_results = {
            "pre_check_handler": [mock_parser_config]
        }
        
        # Mock factory that raises exception
        def mock_get_parser(parser_name):
            raise ValueError(f"No parser available for parser name: {parser_name}")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ConfHandlerName', mock_conf_handler_name)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ParserToolFactory.get_parser', mock_get_parser)
        
        # Execute and expect exception to propagate
        with pytest.raises(ValueError, match="No parser available for parser name"):
            await uniparse_node.process(mock_context)

    @pytest.mark.asyncio
    async def test_uniparse_node_process_concurrent_execution(self, uniparse_node, mock_context, monkeypatch):
        """Test that multiple parsers run concurrently"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Create multiple parser configs
        configs = [
            ParserConfig(parser_name=ParserName.KdcParser, processing_pages={0}, is_all=False),
            ParserConfig(parser_name=ParserName.MuPdfParser, processing_pages={1}, is_all=False),
            ParserConfig(parser_name=ParserName.ImageParser, processing_pages={2}, is_all=False)
        ]
        
        mock_context.handler_results = {
            "pre_check_handler": configs
        }
        
        # Track call order to verify concurrency
        call_order = []
        
        async def mock_uniparse_process(context, pages, is_all, parser_name):
            call_order.append(f"{parser_name}_start")
            await asyncio.sleep(0.01)  # Simulate async work
            call_order.append(f"{parser_name}_end")
            return context
        
        # Mock parsers
        mock_kdc = MagicMock(spec=UniParseTemplate)
        mock_kdc.uniparse_process = lambda ctx, pages, is_all: mock_uniparse_process(ctx, pages, is_all, "KDC")
        
        mock_mupdf = MagicMock(spec=UniParseTemplate)
        mock_mupdf.uniparse_process = lambda ctx, pages, is_all: mock_uniparse_process(ctx, pages, is_all, "MuPDF")
        
        mock_image = MagicMock(spec=UniParseTemplate)
        mock_image.uniparse_process = lambda ctx, pages, is_all: mock_uniparse_process(ctx, pages, is_all, "Image")
        
        def mock_get_parser(parser_name):
            if parser_name == ParserName.KdcParser:
                return mock_kdc
            elif parser_name == ParserName.MuPdfParser:
                return mock_mupdf
            elif parser_name == ParserName.ImageParser:
                return mock_image
            else:
                raise ValueError(f"Unexpected parser: {parser_name}")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ConfHandlerName', mock_conf_handler_name)
        monkeypatch.setattr('modules.flows.uni_parser.uniparse_handler.ParserToolFactory.get_parser', mock_get_parser)
        
        # Execute
        result_context = await uniparse_node.process(mock_context)
        
        # Verify
        assert result_context == mock_context
        
        # All parsers should have started and ended (concurrent execution)
        assert len(call_order) == 6
        assert call_order.count("KDC_start") == 1
        assert call_order.count("KDC_end") == 1
        assert call_order.count("MuPDF_start") == 1
        assert call_order.count("MuPDF_end") == 1
        assert call_order.count("Image_start") == 1
        assert call_order.count("Image_end") == 1