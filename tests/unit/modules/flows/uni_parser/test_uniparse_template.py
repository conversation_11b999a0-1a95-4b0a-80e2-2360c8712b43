# Author: linqi
# Date: 2025/8/16
# Time: 16:45

import pytest
from abc import ABC
from typing import List, Union

from modules.flows.uni_parser.uniparse_template import UniParseTemplate
from modules.pipeline.context import PipelineContext


def test_uniparse_template_is_abstract():
    """Test that UniParseTemplate is an abstract base class"""
    assert issubclass(UniParseTemplate, ABC)
    
    # Should not be able to instantiate directly
    with pytest.raises(TypeError):
        UniParseTemplate()


def test_uniparse_template_abstract_method():
    """Test that uniparse_process is an abstract method"""
    # Create a concrete implementation
    class ConcreteUniParser(UniParseTemplate):
        async def uniparse_process(self, context: PipelineContext, pages: Union[List[int], str], is_all: bool) -> PipelineContext:
            return context
    
    # Should be able to instantiate concrete implementation
    concrete = ConcreteUniParser()
    assert isinstance(concrete, UniParseTemplate)


def test_uniparse_template_missing_implementation():
    """Test that missing implementation raises TypeError"""
    # Create incomplete implementation
    class IncompleteUniParser(UniParseTemplate):
        pass
    
    # Should not be able to instantiate incomplete implementation
    with pytest.raises(TypeError, match="Can't instantiate abstract class"):
        IncompleteUniParser()


@pytest.mark.asyncio
async def test_concrete_implementation_works():
    """Test that a proper concrete implementation works"""
    from services.datamodel import FileType
    from modules.pipeline.context import FileInfo
    
    class TestUniParser(UniParseTemplate):
        async def uniparse_process(self, context: PipelineContext, pages: Union[List[int], str], is_all: bool) -> PipelineContext:
            # Mock implementation that just returns the context
            return context
    
    parser = TestUniParser()
    # Create context with properly initialized FileInfo
    file_info = FileInfo(file_type=FileType.PDF)
    mock_context = PipelineContext(file_info=file_info)
    
    result = await parser.uniparse_process(mock_context, [1, 2, 3], False)
    assert result == mock_context


@pytest.mark.asyncio 
async def test_method_signature():
    """Test that the abstract method has correct signature"""
    from services.datamodel import FileType
    from modules.pipeline.context import FileInfo
    
    class TestUniParser(UniParseTemplate):
        async def uniparse_process(self, context: PipelineContext, pages: Union[List[int], str], is_all: bool) -> PipelineContext:
            # Test different parameter types
            assert isinstance(context, PipelineContext)
            assert isinstance(pages, (list, str))
            assert isinstance(is_all, bool)
            return context
    
    parser = TestUniParser()
    # Create context with properly initialized FileInfo
    file_info = FileInfo(file_type=FileType.PDF)
    mock_context = PipelineContext(file_info=file_info)
    
    # Test with list of pages
    await parser.uniparse_process(mock_context, [1, 2], True)
    
    # Test with string pages
    await parser.uniparse_process(mock_context, "1-5", False)