# Author: linqi
# Date: 2025/8/16
# Time: 16:04

import pytest
import logging
from unittest.mock import MagicMock

from modules.flows.keywords.keywords import KeywordsNode
from modules.pipeline.context import PipelineContext
from modules.entity.parse_entity import ParseRes, Image, ImageType


@pytest.fixture
def keywords_node():
    """Create a KeywordsNode instance for testing"""
    return KeywordsNode("test_keywords")


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    
    # Mock file_info
    context.file_info = MagicMock()
    context.file_info.page_size = 10
    context.file_info.word_count = 1000
    context.file_info.width = 800
    context.file_info.height = 600
    context.file_info.is_scan = False
    context.file_info.rotate_page = {1: [0, 90]}  # Dict[int, List[int]]
    
    # Mock other attributes
    context.dst = ["mock_dst_data"]
    context.embed_enabled = False
    context.handler_results = {}
    # Create a proper Image object for the image field
    mock_image = Image(
        page_num=1, 
        url="http://test.image.url", 
        image_type=ImageType.INPUT_IMAGE
    )
    context.image = [mock_image]  # List[Image]
    context.parse_version = "1.0"
    context.token = "test_token"
    context.need_callback = True
    context.return_ks3_url = False
    context.callback_url = "http://test.callback"
    
    return context


def test_keywords_node_initialization():
    """Test KeywordsNode initialization"""
    name = "test_keywords"
    node = KeywordsNode(name)
    
    assert node.name == name
    assert isinstance(node, KeywordsNode)


@pytest.mark.asyncio
async def test_keywords_node_process_without_embedding(keywords_node, mock_context, monkeypatch):
    """Test KeywordsNode.process without embedding enabled"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "模拟的文档内容用于关键词提取"
    
    async def mock_get_tag_services(content):
        return ["关键词1", "关键词2", "测试"]
    
    async def mock_callback_parse_background(*args, **kwargs):
        pass
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    monkeypatch.setattr('modules.flows.keywords.keywords.callback_parse_background', mock_callback_parse_background)
    
    # Set embed_enabled to False
    mock_context.embed_enabled = False
    
    # Execute
    result_context = await keywords_node.process(mock_context)
    
    # Verify results
    assert result_context == mock_context
    assert "test_keywords" in mock_context.handler_results
    
    handler_result = mock_context.handler_results["test_keywords"]
    assert handler_result["keywords"] == ["关键词1", "关键词2", "测试"]
    assert handler_result["keywords_embedding"] is None


@pytest.mark.asyncio
async def test_keywords_node_process_with_embedding(keywords_node, mock_context, monkeypatch):
    """Test KeywordsNode.process with embedding enabled"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "模拟的文档内容用于关键词提取"
    
    async def mock_get_tag_services(content):
        return ["关键词1", "关键词2"]
    
    # Mock OCRModelClient
    class MockOCRModelClient:
        async def request_text_embedding(self, text):
            return [0.1, 0.2, 0.3, 0.4, 0.5]  # Mock embedding vector
    
    async def mock_callback_parse_background(*args, **kwargs):
        pass
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    monkeypatch.setattr('modules.flows.keywords.keywords.OCRModelClient', MockOCRModelClient)
    monkeypatch.setattr('modules.flows.keywords.keywords.callback_parse_background', mock_callback_parse_background)
    
    # Set embed_enabled to True
    mock_context.embed_enabled = True
    
    # Execute
    result_context = await keywords_node.process(mock_context)
    
    # Verify results
    assert result_context == mock_context
    assert "test_keywords" in mock_context.handler_results
    
    handler_result = mock_context.handler_results["test_keywords"]
    assert handler_result["keywords"] == ["关键词1", "关键词2"]
    assert handler_result["keywords_embedding"] == [0.1, 0.2, 0.3, 0.4, 0.5]


@pytest.mark.asyncio
async def test_keywords_node_process_empty_keywords(keywords_node, mock_context, monkeypatch):
    """Test KeywordsNode.process when get_tag_services returns empty list"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "没有关键词的内容"
    
    async def mock_get_tag_services(content):
        return []  # Empty keywords
    
    async def mock_callback_parse_background(*args, **kwargs):
        pass
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    monkeypatch.setattr('modules.flows.keywords.keywords.callback_parse_background', mock_callback_parse_background)
    
    # Execute
    result_context = await keywords_node.process(mock_context)
    
    # Verify results
    assert result_context == mock_context
    handler_result = mock_context.handler_results["test_keywords"]
    assert handler_result["keywords"] == []
    assert handler_result["keywords_embedding"] is None


@pytest.mark.asyncio
async def test_keywords_node_process_embedding_with_empty_keywords(keywords_node, mock_context, monkeypatch):
    """Test KeywordsNode.process with embedding enabled but empty keywords"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "空内容"
    
    async def mock_get_tag_services(content):
        return []
    
    class MockOCRModelClient:
        async def request_text_embedding(self, text):
            # Should receive empty string
            assert text == ""
            return []
    
    async def mock_callback_parse_background(*args, **kwargs):
        pass
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    monkeypatch.setattr('modules.flows.keywords.keywords.OCRModelClient', MockOCRModelClient)
    monkeypatch.setattr('modules.flows.keywords.keywords.callback_parse_background', mock_callback_parse_background)
    
    # Set embed_enabled to True
    mock_context.embed_enabled = True
    
    # Execute
    result_context = await keywords_node.process(mock_context)
    
    # Verify results
    handler_result = mock_context.handler_results["test_keywords"]
    assert handler_result["keywords"] == []
    assert handler_result["keywords_embedding"] == []


@pytest.mark.asyncio
async def test_keywords_node_process_callback_with_different_params(keywords_node, mock_context, monkeypatch):
    """Test KeywordsNode.process verifies callback parameters"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "测试内容"
    
    async def mock_get_tag_services(content):
        return ["测试关键词"]
    
    callback_calls = []
    async def mock_callback_parse_background(res, name, token, need_callback, return_ks3_url, callback_url):
        callback_calls.append({
            'res': res,
            'name': name,
            'token': token,
            'need_callback': need_callback,
            'return_ks3_url': return_ks3_url,
            'callback_url': callback_url
        })
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    monkeypatch.setattr('modules.flows.keywords.keywords.callback_parse_background', mock_callback_parse_background)
    
    # Modify context parameters
    mock_context.token = "custom_token"
    mock_context.need_callback = False
    mock_context.return_ks3_url = True
    mock_context.callback_url = "http://custom.callback.url"
    
    # Execute
    await keywords_node.process(mock_context)
    
    # Verify callback was called with correct parameters
    assert len(callback_calls) == 1
    call = callback_calls[0]
    assert call['name'] == "test_keywords"
    assert call['token'] == "custom_token"
    assert call['need_callback'] == False
    assert call['return_ks3_url'] == True
    assert call['callback_url'] == "http://custom.callback.url"
    assert isinstance(call['res'], ParseRes)


@pytest.mark.asyncio
async def test_keywords_node_process_parse_res_creation(keywords_node, mock_context, monkeypatch):
    """Test KeywordsNode.process creates ParseRes with correct parameters"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "测试内容"
    
    async def mock_get_tag_services(content):
        return ["关键词A", "关键词B"]
    
    parse_res_objects = []
    async def mock_callback_parse_background(res, *args, **kwargs):
        parse_res_objects.append(res)
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    monkeypatch.setattr('modules.flows.keywords.keywords.callback_parse_background', mock_callback_parse_background)
    
    # Execute
    await keywords_node.process(mock_context)
    
    # Verify ParseRes object
    assert len(parse_res_objects) == 1
    parse_res = parse_res_objects[0]
    
    # Create expected mock image for comparison
    expected_mock_image = Image(
        page_num=1, 
        url="http://test.image.url", 
        image_type=ImageType.INPUT_IMAGE
    )
    
    assert parse_res.keywords == ["关键词A", "关键词B"]
    assert parse_res.keywords_embedding is None
    assert parse_res.page_size == 10
    assert parse_res.word_count == 1000
    assert parse_res.width == 800
    assert parse_res.height == 600
    assert parse_res.is_scan == False
    assert parse_res.image == [expected_mock_image]  # Should be List[Image]
    assert parse_res.rotate_page == {1: [0, 90]}  # Should be Dict[int, List[int]]
    assert parse_res.parse_version == "1.0"


@pytest.mark.asyncio
async def test_keywords_node_process_exception_handling(keywords_node, mock_context, monkeypatch, caplog):
    """Test KeywordsNode.process handles exceptions properly"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies to raise an exception
    def mock_print_dst_indent_tree(dst_list):
        raise Exception("Test exception in print_dst_indent_tree")
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    
    # Execute with logging capture
    with caplog.at_level(logging.ERROR):
        result = await keywords_node.process(mock_context)
    
    # The method should handle the exception gracefully and return None
    assert result is None
    
    # Check that error was logged
    assert "Error in KeywordsNode.process" in caplog.text
    assert "Test exception in print_dst_indent_tree" in caplog.text


@pytest.mark.asyncio
async def test_keywords_node_process_get_tag_services_exception(keywords_node, mock_context, monkeypatch, caplog):
    """Test KeywordsNode.process when get_tag_services raises exception"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "正常内容"
    
    async def mock_get_tag_services(content):
        raise Exception("Test exception in get_tag_services")
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    
    # Execute with logging capture
    with caplog.at_level(logging.ERROR):
        result = await keywords_node.process(mock_context)
    
    # Should handle exception gracefully and return None
    assert result is None
    assert "Error in KeywordsNode.process" in caplog.text


@pytest.mark.asyncio
async def test_keywords_node_process_ocr_client_exception(keywords_node, mock_context, monkeypatch, caplog):
    """Test KeywordsNode.process when OCRModelClient raises exception"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    def mock_print_dst_indent_tree(dst_list):
        return "正常内容"
    
    async def mock_get_tag_services(content):
        return ["关键词1", "关键词2"]
    
    class MockOCRModelClient:
        async def request_text_embedding(self, text):
            raise Exception("Test OCR exception")
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.keywords.keywords.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.keywords.keywords.print_dst_indent_tree', mock_print_dst_indent_tree)
    monkeypatch.setattr('modules.flows.keywords.keywords.get_tag_services', mock_get_tag_services)
    monkeypatch.setattr('modules.flows.keywords.keywords.OCRModelClient', MockOCRModelClient)
    
    # Set embed_enabled to True to trigger OCR call
    mock_context.embed_enabled = True
    
    # Execute with logging capture
    with caplog.at_level(logging.ERROR):
        result = await keywords_node.process(mock_context)
    
    # Should handle exception gracefully and return None
    assert result is None
    assert "Error in KeywordsNode.process" in caplog.text