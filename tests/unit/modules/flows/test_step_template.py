# -*- coding: utf-8 -*-
"""
Unit tests for modules.flows.step_template module
"""

import pytest
from abc import ABC, abstractmethod

from modules.flows.step_template import StepTemplate


class TestStepTemplate:
    """Test StepTemplate abstract base class"""

    def test_cannot_instantiate_abstract_class(self):
        """Test that StepTemplate cannot be directly instantiated"""
        with pytest.raises(TypeError, match="Can't instantiate abstract class StepTemplate"):
            StepTemplate("test_step")

    def test_step_template_is_abc(self):
        """Test that StepTemplate inherits from ABC"""
        assert issubclass(StepTemplate, ABC)

    def test_tmp_method_is_abstract(self):
        """Test that tmp method is marked as abstract"""
        # Check that tmp method exists and is abstract
        assert hasattr(StepTemplate, 'tmp')
        assert getattr(StepTemplate.tmp, '__isabstractmethod__', False)

    def test_abstract_methods_list(self):
        """Test that StepTemplate has the expected abstract methods"""
        abstract_methods = StepTemplate.__abstractmethods__
        assert 'tmp' in abstract_methods
        assert len(abstract_methods) == 1

    def test_concrete_subclass_without_implementation_fails(self):
        """Test that subclass without implementing abstract methods cannot be instantiated"""
        
        class IncompleteStepTemplate(StepTemplate):
            pass  # Missing implementation of tmp method
        
        with pytest.raises(TypeError, match="Can't instantiate abstract class IncompleteStepTemplate"):
            IncompleteStepTemplate("test_step")

    def test_concrete_subclass_with_implementation_succeeds(self):
        """Test that subclass with proper implementation can be instantiated"""
        
        class ConcreteStepTemplate(StepTemplate):
            def tmp(self):
                return "implemented"
        
        # Should not raise any exception
        instance = ConcreteStepTemplate("test_step")
        assert instance.step_name == "test_step"
        assert instance.tmp() == "implemented"

    def test_subclass_initialization(self):
        """Test initialization of concrete subclass"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test_implementation"
        
        step_name = "my_test_step"
        instance = TestStepImpl(step_name)
        
        assert instance.step_name == step_name
        assert hasattr(instance, 'run')
        assert callable(instance.run)
        assert hasattr(instance, 'tmp')
        assert callable(instance.tmp)

    def test_run_method_exists_and_callable(self):
        """Test that run method exists and is callable in concrete subclass"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        instance = TestStepImpl("test_step")
        
        # run method should exist and be callable
        assert hasattr(instance, 'run')
        assert callable(instance.run)
        
        # run method should return None (empty implementation)
        result = instance.run()
        assert result is None

    def test_step_name_attribute_storage(self):
        """Test that step_name is properly stored as instance attribute"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        test_names = ["step1", "my_step", "complex_step_name", ""]
        
        for step_name in test_names:
            instance = TestStepImpl(step_name)
            assert instance.step_name == step_name
            assert isinstance(instance.step_name, str)

    def test_multiple_subclasses_independence(self):
        """Test that multiple subclasses work independently"""
        
        class StepTemplateA(StepTemplate):
            def tmp(self):
                return "A"
        
        class StepTemplateB(StepTemplate):
            def tmp(self):
                return "B"
        
        instance_a = StepTemplateA("step_a")
        instance_b = StepTemplateB("step_b")
        
        assert instance_a.step_name == "step_a"
        assert instance_b.step_name == "step_b"
        assert instance_a.tmp() == "A"
        assert instance_b.tmp() == "B"
        
        # They should be different instances
        assert instance_a is not instance_b
        assert type(instance_a) is not type(instance_b)

    def test_subclass_can_override_run_method(self):
        """Test that subclass can override the run method"""
        
        class CustomStepTemplate(StepTemplate):
            def __init__(self, step_name: str):
                super().__init__(step_name)
                self.run_called = False
            
            def tmp(self):
                return "custom"
            
            def run(self):
                self.run_called = True
                return "custom_run_result"
        
        instance = CustomStepTemplate("custom_step")
        assert instance.step_name == "custom_step"
        assert not instance.run_called
        
        result = instance.run()
        assert instance.run_called
        assert result == "custom_run_result"

    def test_subclass_inheritance_chain(self):
        """Test that inheritance chain works properly"""
        
        class BaseStepImpl(StepTemplate):
            def tmp(self):
                return "base"
            
            def common_method(self):
                return "common"
        
        class ExtendedStepImpl(BaseStepImpl):
            def tmp(self):
                return "extended"
        
        base_instance = BaseStepImpl("base_step")
        extended_instance = ExtendedStepImpl("extended_step")
        
        assert base_instance.step_name == "base_step"
        assert extended_instance.step_name == "extended_step"
        
        assert base_instance.tmp() == "base"
        assert extended_instance.tmp() == "extended"
        
        # Both should have common_method
        assert base_instance.common_method() == "common"
        assert extended_instance.common_method() == "common"
        
        # Check inheritance
        assert isinstance(base_instance, StepTemplate)
        assert isinstance(extended_instance, StepTemplate)
        assert isinstance(extended_instance, BaseStepImpl)

    def test_step_template_with_complex_tmp_implementation(self):
        """Test StepTemplate with more complex tmp method implementation"""
        
        class ComplexStepTemplate(StepTemplate):
            def __init__(self, step_name: str):
                super().__init__(step_name)
                self.data = {}
            
            def tmp(self):
                # Complex implementation that uses step_name
                return f"tmp_result_for_{self.step_name}"
            
            def add_data(self, key, value):
                self.data[key] = value
                return self
        
        instance = ComplexStepTemplate("complex_step")
        
        assert instance.tmp() == "tmp_result_for_complex_step"
        
        # Test method chaining
        instance.add_data("key1", "value1").add_data("key2", "value2")
        assert instance.data == {"key1": "value1", "key2": "value2"}

    def test_step_name_type_validation(self):
        """Test that step_name parameter is properly handled"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        # Test with different types of step names
        instance1 = TestStepImpl("string_name")
        assert instance1.step_name == "string_name"
        
        # Test with empty string
        instance2 = TestStepImpl("")
        assert instance2.step_name == ""
        
        # Note: The original implementation doesn't validate types,
        # but we can test that it accepts what it gets
        instance3 = TestStepImpl("step_with_numbers_123")
        assert instance3.step_name == "step_with_numbers_123"

    def test_abstract_method_signature(self):
        """Test the signature and properties of the abstract tmp method"""
        
        # Check that tmp method is properly decorated
        tmp_method = StepTemplate.tmp
        assert hasattr(tmp_method, '__isabstractmethod__')
        assert tmp_method.__isabstractmethod__ is True
        
        # Check method name
        assert tmp_method.__name__ == 'tmp'

    def test_class_hierarchy_structure(self):
        """Test the class hierarchy and method resolution order"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        instance = TestStepImpl("test")
        
        # Check MRO (Method Resolution Order)
        mro = TestStepImpl.__mro__
        assert StepTemplate in mro
        assert ABC in mro
        assert object in mro
        
        # Check that instance is properly typed
        assert isinstance(instance, TestStepImpl)
        assert isinstance(instance, StepTemplate)
        assert isinstance(instance, ABC)

    def test_multiple_abstract_methods_subclass(self):
        """Test subclass that adds more abstract methods"""
        
        class ExtendedStepTemplate(StepTemplate):
            @abstractmethod
            def additional_method(self):
                pass
        
        # Should not be able to instantiate with only tmp implemented
        class PartialImpl(ExtendedStepTemplate):
            def tmp(self):
                return "partial"
        
        with pytest.raises(TypeError):
            PartialImpl("partial_step")
        
        # Should be able to instantiate with both methods implemented
        class FullImpl(ExtendedStepTemplate):
            def tmp(self):
                return "full"
            
            def additional_method(self):
                return "additional"
        
        instance = FullImpl("full_step")
        assert instance.tmp() == "full"
        assert instance.additional_method() == "additional"
        assert instance.step_name == "full_step"


class TestStepTemplateEdgeCases:
    """Test edge cases and special scenarios"""

    def test_step_template_with_none_step_name(self):
        """Test behavior with None as step_name"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        # The original implementation doesn't validate input,
        # so it should accept None
        instance = TestStepImpl(None)
        assert instance.step_name is None

    def test_step_template_method_visibility(self):
        """Test that all methods have correct visibility"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        instance = TestStepImpl("test")
        
        # All methods should be public (no leading underscore)
        public_methods = [method for method in dir(instance) 
                         if callable(getattr(instance, method)) and not method.startswith('_')]
        
        assert 'run' in public_methods
        assert 'tmp' in public_methods

    def test_step_template_attribute_access(self):
        """Test attribute access patterns"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        instance = TestStepImpl("test_step")
        
        # Test direct attribute access
        assert hasattr(instance, 'step_name')
        assert getattr(instance, 'step_name') == "test_step"
        
        # Test attribute modification
        instance.step_name = "modified_step"
        assert instance.step_name == "modified_step"

    def test_step_template_string_representation(self):
        """Test string representation of StepTemplate instances"""
        
        class TestStepImpl(StepTemplate):
            def tmp(self):
                return "test"
        
        instance = TestStepImpl("repr_test")
        
        # Check that string representation includes class name
        str_repr = str(instance)
        assert "TestStepImpl" in str_repr
        
        # Check repr
        repr_str = repr(instance)
        assert "TestStepImpl" in repr_str