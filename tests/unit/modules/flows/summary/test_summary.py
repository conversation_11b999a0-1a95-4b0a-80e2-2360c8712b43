# Author: linqi
# Date: 2025/8/16
# Time: 16:35

import pytest
from unittest.mock import MagicMock, AsyncMock, patch
import logging

from modules.flows.summary.summary import (
    ReqSummary, RespSummaryData, RespSummary, 
    summary_services, SummaryNode
)
from typing import Optional
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext
from modules.entity.parse_entity import Parse<PERSON><PERSON>, Image, ImageType
from routers.httpcode import HTTPCODE


@pytest.fixture
def summary_node():
    """Create a SummaryNode instance for testing"""
    return SummaryNode("test_summary")


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    
    # Mock file_info
    context.file_info = MagicMock()
    context.file_info.page_size = 10
    context.file_info.word_count = 1000
    context.file_info.width = 800
    context.file_info.height = 600
    context.file_info.is_scan = False
    context.file_info.rotate_page = {1: [0, 90]}
    
    # Mock other attributes
    context.dst = ["mock_dst_data"]
    context.embed_enabled = False
    context.handler_results = {}
    
    # Create proper Image object for the image field
    mock_image = Image(
        page_num=1, 
        url="http://test.image.url", 
        image_type=ImageType.INPUT_IMAGE
    )
    context.image = [mock_image]
    context.parse_version = "1.0"
    context.token = "test_token"
    context.need_callback = True
    context.return_ks3_url = False
    context.callback_url = "http://test.callback"
    
    return context


class TestDataModels:
    """Test data model classes"""

    def test_req_summary_creation(self):
        """Test ReqSummary model creation"""
        req = ReqSummary(content="test content")
        assert req.content == "test content"

    def test_req_summary_validation(self):
        """Test ReqSummary model validation"""
        # Should allow None content (since original code checks for None)
        req = ReqSummary(content="")
        assert req.content is ""
        
        # Should also allow empty dict creation
        req2 = ReqSummary(content="")
        assert req2.content is ""

    def test_resp_summary_data_creation(self):
        """Test RespSummaryData model creation"""
        data = RespSummaryData(text_summary="test summary")
        assert data.text_summary == "test summary"
        assert data.high_level_key == ""  # Default value

    def test_resp_summary_data_with_key(self):
        """Test RespSummaryData model with high level key"""
        data = RespSummaryData(text_summary="test summary", high_level_key="important")
        assert data.text_summary == "test summary"
        assert data.high_level_key == "important"

    def test_resp_summary_creation(self):
        """Test RespSummary model creation"""
        data = RespSummaryData(text_summary="test summary")
        resp = RespSummary(code=HTTPCODE.OK, data=data)
        assert resp.code == HTTPCODE.OK
        assert resp.data == data

    def test_resp_summary_default_data(self):
        """Test RespSummary model with default data"""
        resp = RespSummary(code=HTTPCODE.OK)
        assert resp.code == HTTPCODE.OK
        assert resp.data is None


class TestSummaryServices:
    """Test summary_services function"""

    @pytest.mark.asyncio
    async def test_summary_services_empty_content(self):
        """Test summary_services with empty content"""
        req = ReqSummary(content="")
        result = await summary_services(req)
        
        assert isinstance(result, RespSummary)
        assert result.code == HTTPCODE.OK
        assert result.data.text_summary == ""

    @pytest.mark.asyncio
    async def test_summary_services_none_content(self):
        """Test summary_services with None content"""
        req = ReqSummary(content="")
        result = await summary_services(req)
        
        assert isinstance(result, RespSummary)
        assert result.code == HTTPCODE.OK
        assert result.data.text_summary == ""

    @pytest.mark.asyncio
    async def test_summary_services_successful_processing(self, monkeypatch):
        """Test summary_services with successful content processing"""
        # Mock extract_sections
        def mock_extract_sections(content, section_length):
            return f"extracted: {content[:50]}"
        
        # Mock LMModel.generate_response
        async def mock_generate_response(prompt, top_k):
            return "success", "这是生成的总结内容"
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.extract_sections', mock_extract_sections)
        
        # Mock LMModel
        mock_lm_model = MagicMock()
        mock_lm_model.generate_response = mock_generate_response
        monkeypatch.setattr('modules.flows.summary.summary.LMModel', mock_lm_model)
        
        # Test data
        req = ReqSummary(content="这是一个测试文档内容，需要进行总结处理。")
        
        # Execute
        result = await summary_services(req)
        
        # Verify
        assert isinstance(result, RespSummary)
        assert result.code == HTTPCODE.OK
        assert result.data.text_summary == "这是生成的总结内容"

    @pytest.mark.asyncio
    async def test_summary_services_long_content_truncation(self, monkeypatch):
        """Test summary_services truncates long content to 5000 characters"""
        # Mock extract_sections
        def mock_extract_sections(content, section_length):
            # Return content longer than 5000 chars to test truncation
            return "extracted content " * 500  # Much longer than 5000 chars
        
        # Mock LMModel to capture the actual prompt sent
        captured_prompt = None
        async def mock_generate_response(prompt, top_k):
            nonlocal captured_prompt
            captured_prompt = prompt
            return "success", "总结内容"
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.extract_sections', mock_extract_sections)
        
        mock_lm_model = MagicMock()
        mock_lm_model.generate_response = mock_generate_response
        monkeypatch.setattr('modules.flows.summary.summary.LMModel', mock_lm_model)
        
        # Test with long content
        req = ReqSummary(content="长内容" * 1000)
        
        # Execute
        result = await summary_services(req)
        
        # Verify truncation occurred
        assert isinstance(result, RespSummary)
        assert result.code == HTTPCODE.OK
        # The prompt should be truncated to 5000 characters
        # Note: exact length check depends on SUMMARY_PROMPT_WITHOUT_KEY format

    @pytest.mark.asyncio
    async def test_summary_services_exception_handling(self, monkeypatch):
        """Test summary_services handles exceptions properly"""
        # Mock extract_sections to raise exception
        def mock_extract_sections(content, section_length):
            raise Exception("Test exception")
        
        # Mock error_trace
        def mock_error_trace():
            pass
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.extract_sections', mock_extract_sections)
        monkeypatch.setattr('modules.flows.summary.summary.error_trace', mock_error_trace)
        
        # Test data
        req = ReqSummary(content="测试内容")
        
        # Execute
        result = await summary_services(req)
        
        # Verify error response
        assert isinstance(result, RespSummary)
        assert result.code == HTTPCODE.ERROR

    @pytest.mark.asyncio
    async def test_summary_services_llm_generation_failure(self, monkeypatch):
        """Test summary_services when LLM generation fails"""
        # Mock extract_sections
        def mock_extract_sections(content, section_length):
            return content
        
        # Mock LMModel to raise exception
        async def mock_generate_response(prompt, top_k):
            raise Exception("LLM generation failed")
        
        # Mock error_trace
        def mock_error_trace():
            pass
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.extract_sections', mock_extract_sections)
        monkeypatch.setattr('modules.flows.summary.summary.error_trace', mock_error_trace)
        
        mock_lm_model = MagicMock()
        mock_lm_model.generate_response = mock_generate_response
        monkeypatch.setattr('modules.flows.summary.summary.LMModel', mock_lm_model)
        
        # Test data
        req = ReqSummary(content="测试内容")
        
        # Execute
        result = await summary_services(req)
        
        # Verify error response
        assert isinstance(result, RespSummary)
        assert result.code == HTTPCODE.ERROR


class TestSummaryNode:
    """Test SummaryNode class"""

    def test_summary_node_inheritance(self, summary_node):
        """Test SummaryNode inherits from PipelineHandler"""
        assert isinstance(summary_node, PipelineHandler)
        assert isinstance(summary_node, SummaryNode)
        assert summary_node.name == "test_summary"

    def test_summary_node_initialization(self):
        """Test SummaryNode initialization"""
        name = "custom_summary"
        node = SummaryNode(name)
        assert node.name == name

    @pytest.mark.asyncio
    async def test_summary_node_process_without_embedding(self, summary_node, mock_context, monkeypatch):
        """Test SummaryNode.process without embedding enabled"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock dependencies
        def mock_print_dst_indent_tree(dst_list):
            return "模拟的文档内容用于总结生成"
        
        async def mock_summary_services(req):
            return RespSummary(
                code=HTTPCODE.OK,
                data=RespSummaryData(text_summary="生成的总结内容")
            )
        
        async def mock_callback_parse_background(*args, **kwargs):
            pass
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.summary.summary.print_dst_indent_tree', mock_print_dst_indent_tree)
        monkeypatch.setattr('modules.flows.summary.summary.summary_services', mock_summary_services)
        monkeypatch.setattr('modules.flows.summary.summary.callback_parse_background', mock_callback_parse_background)
        
        # Set embed_enabled to False
        mock_context.embed_enabled = False
        
        # Execute
        result_context = await summary_node.process(mock_context)
        
        # Verify results
        assert result_context == mock_context
        assert "test_summary" in mock_context.handler_results
        
        handler_result = mock_context.handler_results["test_summary"]
        assert handler_result["summary"] == "生成的总结内容"
        assert handler_result["summary_embedding"] is None

    @pytest.mark.asyncio
    async def test_summary_node_process_with_embedding(self, summary_node, mock_context, monkeypatch):
        """Test SummaryNode.process with embedding enabled"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock dependencies
        def mock_print_dst_indent_tree(dst_list):
            return "模拟的文档内容用于总结生成"
        
        async def mock_summary_services(req):
            return RespSummary(
                code=HTTPCODE.OK,
                data=RespSummaryData(text_summary="生成的总结内容")
            )
        
        # Mock OCRModelClient
        class MockOCRModelClient:
            async def request_text_embedding(self, text):
                return [0.1, 0.2, 0.3, 0.4, 0.5]
        
        async def mock_callback_parse_background(*args, **kwargs):
            pass
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.summary.summary.print_dst_indent_tree', mock_print_dst_indent_tree)
        monkeypatch.setattr('modules.flows.summary.summary.summary_services', mock_summary_services)
        monkeypatch.setattr('modules.flows.summary.summary.OCRModelClient', MockOCRModelClient)
        monkeypatch.setattr('modules.flows.summary.summary.callback_parse_background', mock_callback_parse_background)
        
        # Set embed_enabled to True
        mock_context.embed_enabled = True
        
        # Execute
        result_context = await summary_node.process(mock_context)
        
        # Verify results
        assert result_context == mock_context
        assert "test_summary" in mock_context.handler_results
        
        handler_result = mock_context.handler_results["test_summary"]
        assert handler_result["summary"] == "生成的总结内容"
        assert handler_result["summary_embedding"] == [0.1, 0.2, 0.3, 0.4, 0.5]

    @pytest.mark.asyncio
    async def test_summary_node_process_callback_verification(self, summary_node, mock_context, monkeypatch):
        """Test SummaryNode.process callback parameter verification"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock dependencies
        def mock_print_dst_indent_tree(dst_list):
            return "测试内容"
        
        async def mock_summary_services(req):
            return RespSummary(
                code=HTTPCODE.OK,
                data=RespSummaryData(text_summary="总结内容")
            )
        
        callback_calls = []
        async def mock_callback_parse_background(res, name, token, need_callback, return_ks3_url, callback_url):
            callback_calls.append({
                'res': res,
                'name': name,
                'token': token,
                'need_callback': need_callback,
                'return_ks3_url': return_ks3_url,
                'callback_url': callback_url
            })
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.summary.summary.print_dst_indent_tree', mock_print_dst_indent_tree)
        monkeypatch.setattr('modules.flows.summary.summary.summary_services', mock_summary_services)
        monkeypatch.setattr('modules.flows.summary.summary.callback_parse_background', mock_callback_parse_background)
        
        # Execute
        await summary_node.process(mock_context)
        
        # Verify callback was called with correct parameters
        assert len(callback_calls) == 1
        call = callback_calls[0]
        assert call['name'] == "test_summary"
        assert call['token'] == "test_token"
        assert call['need_callback'] == True
        assert call['return_ks3_url'] == False
        assert call['callback_url'] == "http://test.callback"
        assert isinstance(call['res'], ParseRes)

    @pytest.mark.asyncio
    async def test_summary_node_process_parse_res_creation(self, summary_node, mock_context, monkeypatch):
        """Test SummaryNode.process creates ParseRes with correct parameters"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock dependencies
        def mock_print_dst_indent_tree(dst_list):
            return "测试内容"
        
        async def mock_summary_services(req):
            return RespSummary(
                code=HTTPCODE.OK,
                data=RespSummaryData(text_summary="测试总结内容")
            )
        
        parse_res_objects = []
        async def mock_callback_parse_background(res, *args, **kwargs):
            parse_res_objects.append(res)
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.summary.summary.print_dst_indent_tree', mock_print_dst_indent_tree)
        monkeypatch.setattr('modules.flows.summary.summary.summary_services', mock_summary_services)
        monkeypatch.setattr('modules.flows.summary.summary.callback_parse_background', mock_callback_parse_background)
        
        # Execute
        await summary_node.process(mock_context)
        
        # Verify ParseRes object
        assert len(parse_res_objects) == 1
        parse_res = parse_res_objects[0]
        
        # Create expected mock image for comparison
        expected_mock_image = Image(
            page_num=1, 
            url="http://test.image.url", 
            image_type=ImageType.INPUT_IMAGE
        )
        
        assert parse_res.summary == "测试总结内容"
        assert parse_res.summary_embedding is None
        assert parse_res.page_size == 10
        assert parse_res.word_count == 1000
        assert parse_res.width == 800
        assert parse_res.height == 600
        assert parse_res.is_scan == False
        assert parse_res.image == [expected_mock_image]
        assert parse_res.rotate_page == {1: [0, 90]}
        assert parse_res.parse_version == "1.0"

    @pytest.mark.asyncio
    async def test_summary_node_process_exception_handling(self, summary_node, mock_context, monkeypatch, caplog):
        """Test SummaryNode.process handles exceptions properly"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock dependencies to raise exception
        def mock_print_dst_indent_tree(dst_list):
            raise Exception("Test exception in print_dst_indent_tree")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.summary.summary.print_dst_indent_tree', mock_print_dst_indent_tree)
        
        # Execute with logging capture
        with caplog.at_level(logging.ERROR):
            result = await summary_node.process(mock_context)
        
        # The method should handle the exception gracefully and return None
        assert result is None
        
        # Check that error was logged
        assert "Error in SummaryNode.process" in caplog.text
        assert "Test exception in print_dst_indent_tree" in caplog.text

    @pytest.mark.asyncio
    async def test_summary_node_process_summary_services_exception(self, summary_node, mock_context, monkeypatch, caplog):
        """Test SummaryNode.process when summary_services raises exception"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock dependencies
        def mock_print_dst_indent_tree(dst_list):
            return "正常内容"
        
        async def mock_summary_services(req):
            raise Exception("Test exception in summary_services")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.summary.summary.print_dst_indent_tree', mock_print_dst_indent_tree)
        monkeypatch.setattr('modules.flows.summary.summary.summary_services', mock_summary_services)
        
        # Execute with logging capture
        with caplog.at_level(logging.ERROR):
            result = await summary_node.process(mock_context)
        
        # Should handle exception gracefully and return None
        assert result is None
        assert "Error in SummaryNode.process" in caplog.text

    @pytest.mark.asyncio
    async def test_summary_node_process_embedding_exception(self, summary_node, mock_context, monkeypatch, caplog):
        """Test SummaryNode.process when OCRModelClient raises exception"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock dependencies
        def mock_print_dst_indent_tree(dst_list):
            return "正常内容"
        
        async def mock_summary_services(req):
            return RespSummary(
                code=HTTPCODE.OK,
                data=RespSummaryData(text_summary="总结内容")
            )
        
        class MockOCRModelClient:
            async def request_text_embedding(self, text):
                raise Exception("Test OCR exception")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.summary.summary.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.summary.summary.print_dst_indent_tree', mock_print_dst_indent_tree)
        monkeypatch.setattr('modules.flows.summary.summary.summary_services', mock_summary_services)
        monkeypatch.setattr('modules.flows.summary.summary.OCRModelClient', MockOCRModelClient)
        
        # Set embed_enabled to True to trigger OCR call
        mock_context.embed_enabled = True
        
        # Execute with logging capture
        with caplog.at_level(logging.ERROR):
            result = await summary_node.process(mock_context)
        
        # Should handle exception gracefully and return None
        assert result is None
        assert "Error in SummaryNode.process" in caplog.text