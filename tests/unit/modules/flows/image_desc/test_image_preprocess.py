# Author: linqi
# Date: 2025/8/15
# Time: 11:17

import pytest
import base64

from modules.flows.image_desc.image_preprocess import ImagePreprocess
from commons.llm_gateway.llm import LLMChatStatus, LLModelRpc
from commons.llm_gateway.models.chat_data import (
    MultiModalContent, MultiModalType
)






@pytest.mark.asyncio
async def test_get_image_desc_no_image_provided():
    """Test get_image_desc with no image provided"""
    context = "这是一段上下文"
    
    with pytest.raises(AssertionError):
        await ImagePreprocess.get_image_desc(context)


@pytest.mark.asyncio
async def test_get_image_desc_both_image_types_provided(monkeypatch):
    """Test get_image_desc with both URL and base64 provided (base64 takes precedence)"""
    context = "这是一段上下文"
    image_url = "http://example.com/image.jpg"
    image_base64 = base64.b64encode(b"fake image data").decode()

    # Create a simple mock class that avoids AsyncMock completely
    class MockLLModelRpc:
        class Gateway:
            Public = "public"

        class ModelSelector:
            pass

        class SftBaseModel:
            def __init__(self, **kwargs):
                pass

        def __init__(self):
            pass

        async def async_multimodal(self, *args, **kwargs):
            return (LLMChatStatus.OK, "结果")

    # Replace the LLModelRpc with our simple mock
    monkeypatch.setattr('modules.flows.image_desc.image_preprocess.LLModelRpc', MockLLModelRpc)

    status, result = await ImagePreprocess.get_image_desc(
        context, image_url=image_url, image_base64=image_base64
    )

    # Verify return values
    assert status == LLMChatStatus.OK
    assert result == "结果"

def test_image_preprocess_singleton():
    """Test ImagePreprocess is a singleton"""
    instance1 = ImagePreprocess()
    instance2 = ImagePreprocess()
    
    assert instance1 is instance2


@pytest.mark.asyncio
async def test_get_image_desc_invalid_url():
    """Test get_image_desc with invalid URL"""
    context = "这是一段上下文"
    invalid_url = "not-a-valid-url"
    
    status, result = await ImagePreprocess.get_image_desc(
        context, image_url=invalid_url
    )
    
    assert status == "fail"
    assert result is None


@pytest.mark.asyncio
async def test_get_image_desc_valid_url(monkeypatch):
    """Test get_image_desc with valid URL"""
    context = "这是一段上下文"
    valid_url = "http://example.com/valid-image.jpg"

    # Create a simple mock class that avoids AsyncMock completely
    class MockLLModelRpc:
        class Gateway:
            Public = "public"
        
        class ModelSelector:
            pass
            
        class SftBaseModel:
            def __init__(self, **kwargs):
                pass
        
        def __init__(self):
            pass
            
        async def async_multimodal(self, *args, **kwargs):
            return (LLMChatStatus.OK, "图片描述结果")
    
    # Replace the LLModelRpc with our simple mock
    monkeypatch.setattr('modules.flows.image_desc.image_preprocess.LLModelRpc', MockLLModelRpc)

    status, result = await ImagePreprocess.get_image_desc(
        context, image_url=valid_url
    )

    # Verify return values
    assert status == LLMChatStatus.OK
    assert result == "图片描述结果"


def test_image_preprocess_class_attributes():
    """Test ImagePreprocess class attributes"""
    # Test that the class has the expected attributes
    assert hasattr(ImagePreprocess, 'gateway')
    assert hasattr(ImagePreprocess, 'selector')
    assert hasattr(ImagePreprocess, 'multimodal_selector')
    assert ImagePreprocess.selector is None
    assert ImagePreprocess.multimodal_selector is None

