# Author: linqi
# Date: 2025/8/15
# Time: 11:17

import pytest
import copy
import async<PERSON>
from unittest.mock import Mock, AsyncMock, patch
from typing import List

from modules.flows.image_desc.desc_handler import DescNode
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from commons.llm_gateway.llm import LLMChatStatus
from commons.logger.business_log import BusinessLogger
from services.datamodel import FileType


def _create_test_dst(dst_id="test", content=None, dst_type=DSTType.TEXT, 
                    image_pixel=None, page=0):
    """Create a test DST object"""
    if content is None:
        content = ["test content"]
    if image_pixel is None:
        image_pixel = [800, 600] if dst_type == DSTType.IMAGE else None
    
    return DST(
        id=dst_id,
        parent="root",
        order=0,
        dst_type=dst_type,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=0, y1=100, x2=100, y2=120)),
            page=page,
            hash="h" * 32
        ),
        content=content,
        image_pixel=image_pixel
    )


def _create_test_context():
    """Create a test PipelineContext"""
    file_info = FileInfo(
        file_type=FileType.PDF,
        page_size=10,
        word_count=1000,
        width=800,
        height=600,
        is_scan=False
    )
    context = PipelineContext(file_info=file_info)
    context.dst = []
    context.handler_results = {}
    context.business_log = Mock(spec=BusinessLogger)
    return context


def test_desc_node_init():
    """Test DescNode initialization"""
    node = DescNode("test_desc")
    assert node.name == "test_desc"


@pytest.mark.asyncio
async def test_process_single_image_dst_success():
    """Test _process_single_image_dst with successful processing"""
    node = DescNode("desc")
    context = _create_test_context()
    
    # Create test DST list with context
    text_dst1 = _create_test_dst("text1", ["前文内容1"], DSTType.TEXT)
    text_dst2 = _create_test_dst("text2", ["前文内容2"], DSTType.TEXT)
    image_dst = _create_test_dst("img1", ["http://example.com/image.jpg"], DSTType.IMAGE)
    text_dst3 = _create_test_dst("text3", ["后文内容"], DSTType.TEXT)
    
    dst_list = [text_dst1, text_dst2, image_dst, text_dst3]
    
    with patch('modules.flows.image_desc.desc_handler.ImagePreprocess') as MockImagePreprocess:
        mock_preprocess = MockImagePreprocess.return_value
        
        # Use tracked async function to avoid coroutine warnings
        call_history = []
        async def mock_get_image_desc(*args, **kwargs):
            call_history.append((args, kwargs))
            return (LLMChatStatus.OK, "图片描述结果")
        
        # Assign the function directly instead of using AsyncMock
        mock_preprocess.get_image_desc = mock_get_image_desc
        
        result = await node._process_single_image_dst(context, 2, image_dst, dst_list)
        
        # Verify result
        assert result[0] == "img1"  # dst_id
        assert result[1] == LLMChatStatus.OK  # status
        assert result[2] == "图片描述结果"  # res_text
        
        # Verify ImagePreprocess was called with correct context
        assert len(call_history) == 1
        call_args = call_history[0]
        assert "前文内容1前文内容2\n后文内容" in call_args[1]["context"]
        assert call_args[1]["image_url"] == "http://example.com/image.jpg"


@pytest.mark.asyncio
async def test_process_single_image_dst_with_context_length_limit():
    """Test _process_single_image_dst with context length limitation"""
    node = DescNode("desc")
    context = _create_test_context()
    
    # Create test DST list with long context
    long_text = "x" * 60  # Long enough to exceed 100 char limit
    text_dst1 = _create_test_dst("text1", [long_text], DSTType.TEXT)
    text_dst2 = _create_test_dst("text2", [long_text], DSTType.TEXT)
    image_dst = _create_test_dst("img1", ["http://example.com/image.jpg"], DSTType.IMAGE)
    text_dst3 = _create_test_dst("text3", [long_text], DSTType.TEXT)
    
    dst_list = [text_dst1, text_dst2, image_dst, text_dst3]
    
    with patch('modules.flows.image_desc.desc_handler.ImagePreprocess') as MockImagePreprocess:
        mock_preprocess = MockImagePreprocess.return_value
        
        # Use tracked async function to avoid coroutine warnings
        call_history = []
        async def mock_get_image_desc(*args, **kwargs):
            call_history.append((args, kwargs))
            return (LLMChatStatus.OK, "描述")
        
        # Assign the function directly instead of using AsyncMock
        mock_preprocess.get_image_desc = mock_get_image_desc
        
        result = await node._process_single_image_dst(context, 2, image_dst, dst_list)
        
        # Verify context is limited
        call_args = call_history[0]
        context_text = call_args[1]["context"]
        # Should not exceed reasonable length due to 100 char limit per direction
        assert len(context_text) < 250  # Some buffer for formatting


@pytest.mark.asyncio
async def test_process_single_image_dst_with_non_text_neighbors():
    """Test _process_single_image_dst skipping non-text DSTs in context"""
    node = DescNode("desc")
    context = _create_test_context()
    
    # Create test DST list with non-text DSTs
    text_dst1 = _create_test_dst("text1", ["前文"], DSTType.TEXT)
    table_dst = _create_test_dst("table1", ["表格内容"], DSTType.TABLE)
    image_dst = _create_test_dst("img1", ["http://example.com/image.jpg"], DSTType.IMAGE)
    image_dst2 = _create_test_dst("img2", ["http://example.com/image2.jpg"], DSTType.IMAGE)
    text_dst2 = _create_test_dst("text2", ["后文"], DSTType.TEXT)
    
    dst_list = [text_dst1, table_dst, image_dst, image_dst2, text_dst2]
    
    with patch('modules.flows.image_desc.desc_handler.ImagePreprocess') as MockImagePreprocess:
        mock_preprocess = MockImagePreprocess.return_value
        
        # Use tracked async function to avoid coroutine warnings
        call_history = []
        async def mock_get_image_desc(*args, **kwargs):
            call_history.append((args, kwargs))
            return (LLMChatStatus.OK, "描述")
        
        # Assign the function directly instead of using AsyncMock
        mock_preprocess.get_image_desc = mock_get_image_desc
        
        result = await node._process_single_image_dst(context, 2, image_dst, dst_list)
        
        # Verify only text DSTs are included in context
        call_args = call_history[0]
        context_text = call_args[1]["context"]
        assert "前文" in context_text
        assert "后文" in context_text
        assert "表格内容" not in context_text


@pytest.mark.asyncio
async def test_process_single_image_dst_exception():
    """Test _process_single_image_dst with exception handling"""
    node = DescNode("desc")
    context = _create_test_context()
    
    image_dst = _create_test_dst("img1", ["http://example.com/image.jpg"], DSTType.IMAGE)
    dst_list = [image_dst]
    
    with patch('modules.flows.image_desc.desc_handler.ImagePreprocess') as MockImagePreprocess:
        mock_preprocess = MockImagePreprocess.return_value
        # Use direct async function that raises exception instead of AsyncMock
        async def mock_get_image_desc_exception(*args, **kwargs):
            raise Exception("Network error")
        mock_preprocess.get_image_desc = mock_get_image_desc_exception
        
        result = await node._process_single_image_dst(context, 0, image_dst, dst_list)
        
        # Verify exception is handled
        assert result[0] == "img1"
        assert result[1] == LLMChatStatus.FAIL
        assert result[2] is None
        
        # Verify error is logged
        context.business_log.error.assert_called_once()


@pytest.mark.asyncio
async def test_process_with_image_filtering():
    """Test DescNode.process with image size filtering"""
    node = DescNode("desc")
    context = _create_test_context()
    
    # Create DSTs with different image sizes
    small_image = _create_test_dst("small", ["http://small.jpg"], DSTType.IMAGE, 
                                  image_pixel=[100, 50])  # Small image
    large_image = _create_test_dst("large", ["http://large.jpg"], DSTType.IMAGE,
                                  image_pixel=[1000, 800])  # Large image
    text_dst = _create_test_dst("text", ["文本内容"], DSTType.TEXT)
    
    context.dst = [small_image, large_image, text_dst]
    
    with patch('modules.flows.image_desc.desc_handler.ConfImageFilter') as MockConfImageFilter, \
         patch('modules.flows.image_desc.desc_handler.ConfCoroutine') as MockConfCoroutine, \
         patch('modules.flows.image_desc.desc_handler.MultiCoroutine') as MockMultiCoroutine, \
         patch.object(node, '_process_single_image_dst') as mock_process_single:
        
        # Configure filter settings
        MockConfImageFilter.width = 200
        MockConfImageFilter.height = 200
        MockConfCoroutine.image_desc_limit = 5
        
        # Mock _process_single_image_dst to return an awaitable that resolves to the result
        # Create a future that's already resolved
        future = asyncio.Future()
        future.set_result(("large", LLMChatStatus.OK, "大图描述"))
        mock_process_single.return_value = future
        
        # Setup MultiCoroutine mock
        mock_pool = Mock()
        mock_pool.add_task = Mock()
        # Use direct async function instead of AsyncMock
        async def mock_run_limit(*args, **kwargs):
            return {"desc_large": ("large", LLMChatStatus.OK, "大图描述")}
        mock_pool.run_limit = mock_run_limit
        MockMultiCoroutine.return_value = mock_pool
        
        result_context = await node.process(context)
        
        # Verify only large image was processed
        mock_pool.add_task.assert_called_once()
        call_args = mock_pool.add_task.call_args[0]
        assert call_args[0] == "desc_large"  # task_key
        
        # Verify result
        assert result_context == context
        assert "desc" in context.handler_results
        dst_list = context.handler_results["desc"]
        
        # Find the large image DST and verify description was added
        large_dst = next(dst for dst in dst_list if dst.id == "large")
        assert len(large_dst.content) == 2
        assert large_dst.content[1] == "大图描述"


# @pytest.mark.asyncio
# async def test_process_deep_copy_behavior():
#     """Test that DescNode.process uses deep copy of DST list"""
#     node = DescNode("desc")
#     context = _create_test_context()
#
#     original_dst = _create_test_dst("test", ["original content"], DSTType.TEXT)
#     context.dst = [original_dst]
#
#     with patch('modules.flows.image_desc.desc_handler.copy.deepcopy') as mock_deepcopy, \
#          patch('modules.flows.image_desc.desc_handler.MultiCoroutine') as MockMultiCoroutine, \
#          patch.object(node, '_process_single_image_dst') as mock_process_single:
#
#         # Setup deep copy mock
#         copied_dst = _create_test_dst("test", ["copied content"], DSTType.TEXT)
#         mock_deepcopy.return_value = [copied_dst]
#
#         # Mock _process_single_image_dst to return an awaitable that resolves to the result
#         future2 = asyncio.Future()
#         future2.set_result(("test", LLMChatStatus.OK, "result"))
#         mock_process_single.return_value = future2
#
#         mock_pool = Mock()
#         mock_pool.add_task = Mock()
#         # Use direct async function instead of AsyncMock
#         async def mock_run_limit2(*args, **kwargs):
#             return {}
#         mock_pool.run_limit = mock_run_limit2
#         MockMultiCoroutine.return_value = mock_pool
#
#         await node.process(context)
#
#         # Verify deepcopy was called with context.dst
#         mock_deepcopy.assert_called_once_with(context.dst)
