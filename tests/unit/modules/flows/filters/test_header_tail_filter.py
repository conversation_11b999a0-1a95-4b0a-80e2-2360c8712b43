# Author: linqi
# Date: 2025/8/14
# Time: 17:00

import pytest
import uuid
from unittest.mock import Mock, patch
from typing import List

from modules.flows.filters.header_tail_filter import (
    get_matching_dsts, is_equal_image, merge_header_footer_into_chunk,
    mark_page_header_tail_text, mark_header_and_tail
)
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo, MarkType
from modules.entity.chunk_entity import Chunk, LabelType
from modules.pipeline.context import FileInfo
from services.datamodel import FileType


def _create_test_dst(dst_id=None, content=None, dst_type=DSTType.TEXT, page=0, 
                    x1=0, y1=100, x2=100, y2=120, hash_val=None):
    """Create a test DST object"""
    if dst_id is None:
        dst_id = f"dst_{uuid.uuid4().hex[:8]}"
    if content is None:
        content = ["test content"]
    if hash_val is None:
        hash_val = "h" * 32
    elif len(hash_val) < 32:
        # Pad hash to meet minimum length requirement
        hash_val = hash_val + "h" * (32 - len(hash_val))
    
    return DST(
        id=dst_id,
        parent="root",
        order=0,
        dst_type=dst_type,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=x1, y1=y1, x2=x2, y2=y2)),
            page=page,
            hash=hash_val
        ),
        content=content
    )


def _create_file_info(width=800, height=600):
    """Create a test FileInfo object"""
    return FileInfo(
        file_type=FileType.PDF,
        page_size=5,
        word_count=1000,
        width=width,
        height=height,
        is_scan=False
    )


def test_get_matching_dsts_text_match():
    """Test get_matching_dsts function with matching text DSTs"""
    file_info = _create_file_info()
    
    # Create matching text DSTs (header at top)
    prev_header = _create_test_dst("prev_h", ["Page 1"], y1=50, y2=70)
    curr_header = _create_test_dst("curr_h", ["Page 2"], y1=50, y2=70)  # Numbers replaced -> "Page 《NUM》"
    
    # Create non-matching content in middle
    prev_content = _create_test_dst("prev_c", ["Different content"], y1=300, y2=320)
    curr_content = _create_test_dst("curr_c", ["Other content"], y1=300, y2=320)
    
    # Create matching text DSTs (footer at bottom)
    prev_footer = _create_test_dst("prev_f", ["Footer text"], y1=550, y2=570)
    curr_footer = _create_test_dst("curr_f", ["Footer text"], y1=550, y2=570)
    
    prev_dsts = [prev_header, prev_content, prev_footer]
    curr_dsts = [curr_header, curr_content, curr_footer]
    
    headers, footers, is_all_text_headers, is_all_text_footers = get_matching_dsts(
        prev_dsts, curr_dsts, file_info
    )
    
    # Headers processing: should match only the first pair until it hits non-matching content
    assert len(headers) == 2  # prev_header, curr_header only
    assert prev_header.id in headers
    assert curr_header.id in headers
    assert is_all_text_headers is True
    
    # Footers processing: processes in reverse, should match only the last pair
    assert len(footers) == 2  # prev_footer, curr_footer only
    assert prev_footer.id in footers
    assert curr_footer.id in footers
    assert is_all_text_footers is True


def test_get_matching_dsts_no_match():
    """Test get_matching_dsts function with non-matching DSTs"""
    file_info = _create_file_info()
    
    # Create non-matching DSTs
    prev_dst1 = _create_test_dst("prev1", ["Different content"], y1=50, y2=70)
    curr_dst1 = _create_test_dst("curr1", ["Another content"], y1=50, y2=70)
    
    prev_dsts = [prev_dst1]
    curr_dsts = [curr_dst1]
    
    headers, footers, is_all_text_headers, is_all_text_footers = get_matching_dsts(
        prev_dsts, curr_dsts, file_info
    )
    
    # Should not match anything
    assert len(headers) == 0
    assert len(footers) == 0
    assert is_all_text_headers is True
    assert is_all_text_footers is True


def test_get_matching_dsts_different_types():
    """Test get_matching_dsts function with different DST types"""
    file_info = _create_file_info()
    
    # Create DSTs with different types
    prev_dst1 = _create_test_dst("prev1", ["Text"], DSTType.TEXT)
    curr_dst1 = _create_test_dst("curr1", ["Image"], DSTType.IMAGE)
    
    prev_dsts = [prev_dst1]
    curr_dsts = [curr_dst1]
    
    headers, footers, is_all_text_headers, is_all_text_footers = get_matching_dsts(
        prev_dsts, curr_dsts, file_info
    )
    
    # Should not match due to different types
    assert len(headers) == 0
    assert len(footers) == 0


def test_get_matching_dsts_image_match():
    """Test get_matching_dsts function with matching image DSTs"""
    file_info = _create_file_info()
    
    # Create matching image DSTs
    prev_dst1 = _create_test_dst("prev1", ["img1.jpg", "description"], DSTType.IMAGE, 
                                hash_val="same_hash")
    curr_dst1 = _create_test_dst("curr1", ["img1.jpg", "description"], DSTType.IMAGE,
                                hash_val="same_hash")
    
    prev_dsts = [prev_dst1]
    curr_dsts = [curr_dst1]
    
    headers, footers, is_all_text_headers, is_all_text_footers = get_matching_dsts(
        prev_dsts, curr_dsts, file_info
    )
    
    # Should match images
    assert len(headers) == 2  # prev_dst1, curr_dst1
    assert prev_dst1.id in headers
    assert curr_dst1.id in headers
    assert is_all_text_headers is False  # Contains images


def test_is_equal_image_same_hash():
    """Test is_equal_image function with same hash"""
    file_info = _create_file_info()
    
    dst_prev = _create_test_dst("prev", ["img.jpg"], DSTType.IMAGE, hash_val="same_hash")
    dst_curr = _create_test_dst("curr", ["img.jpg"], DSTType.IMAGE, hash_val="same_hash")
    
    result = is_equal_image(dst_prev, dst_curr, file_info)
    
    assert result is True


def test_is_equal_image_different_hash_but_similar():
    """Test is_equal_image function with different hash but similar dimensions"""
    file_info = _create_file_info(height=600)
    
    # Create images with similar dimensions and same detail
    dst_prev = _create_test_dst("prev", ["img1.jpg", "detail"], DSTType.IMAGE, 
                               x1=10, y1=10, x2=110, y2=60, hash_val="hash1")  # 100x50
    dst_curr = _create_test_dst("curr", ["img2.jpg", "detail"], DSTType.IMAGE,
                               x1=15, y1=15, x2=115, y2=65, hash_val="hash2")  # 100x50
    
    result = is_equal_image(dst_prev, dst_curr, file_info)
    
    # Should match due to similar dimensions and same detail, and height < 15% of page
    assert result is True


def test_is_equal_image_too_large():
    """Test is_equal_image function with images too large"""
    file_info = _create_file_info(height=600)
    
    # Create images that are too large (> 15% of page height)
    dst_prev = _create_test_dst("prev", ["img1.jpg", "detail"], DSTType.IMAGE,
                               x1=0, y1=0, x2=100, y2=100, hash_val="hash1")  # Height > 15% of 600
    dst_curr = _create_test_dst("curr", ["img2.jpg", "detail"], DSTType.IMAGE,
                               x1=0, y1=0, x2=100, y2=100, hash_val="hash2")
    
    result = is_equal_image(dst_prev, dst_curr, file_info)
    
    # Should not match due to large size
    assert result is False


def test_is_equal_image_different_details():
    """Test is_equal_image function with different image details"""
    file_info = _create_file_info()
    
    dst_prev = _create_test_dst("prev", ["img1.jpg", "detail1"], DSTType.IMAGE,
                               x1=0, y1=0, x2=50, y2=30, hash_val="hash1")
    dst_curr = _create_test_dst("curr", ["img2.jpg", "detail2"], DSTType.IMAGE,
                               x1=0, y1=0, x2=50, y2=30, hash_val="hash2")
    
    result = is_equal_image(dst_prev, dst_curr, file_info)
    
    # Should not match due to different details
    assert result is False


def test_merge_header_footer_into_chunk_with_headers_footers():
    """Test merge_header_footer_into_chunk function with headers and footers"""
    # Create DSTs with header and footer marks
    header_dst = _create_test_dst("header1", ["Header Text"], page=1)
    header_dst.mark = MarkType.HEADER
    
    footer_dst = _create_test_dst("footer1", ["Footer Text"], page=1)
    footer_dst.mark = MarkType.FOOTER
    
    regular_dst = _create_test_dst("regular1", ["Regular content"], page=1)
    
    dst_list = [header_dst, regular_dst, footer_dst]
    
    result = merge_header_footer_into_chunk(dst_list, page_size=5)
    
    assert result is not None
    assert isinstance(result, Chunk)
    assert result.label == LabelType.TEXT
    assert "Header Text Footer Text" in result.content
    assert result.page_size == 5
    assert result.page_num == [1]
    assert len(result.dsts) == 2  # Header and footer DSTs
    assert len(result.block) == 2


def test_merge_header_footer_into_chunk_with_images():
    """Test merge_header_footer_into_chunk function with image headers/footers"""
    # Create image DST with header mark
    header_dst = _create_test_dst("header1", ["logo.png", "Company Logo"], DSTType.IMAGE, page=1)
    header_dst.mark = MarkType.HEADER
    
    footer_dst = _create_test_dst("footer1", ["Footer Text"], page=1)
    footer_dst.mark = MarkType.FOOTER
    
    dst_list = [header_dst, footer_dst]
    
    result = merge_header_footer_into_chunk(dst_list, page_size=3)
    
    assert result is not None
    assert "Company Logo Footer Text" in result.content


def test_merge_header_footer_into_chunk_no_headers_footers():
    """Test merge_header_footer_into_chunk function with no headers or footers"""
    # Create DSTs without header/footer marks
    dst1 = _create_test_dst("dst1", ["Regular content 1"], page=1)
    dst2 = _create_test_dst("dst2", ["Regular content 2"], page=1)
    
    dst_list = [dst1, dst2]
    
    result = merge_header_footer_into_chunk(dst_list, page_size=2)
    
    assert result is None


def test_merge_header_footer_into_chunk_empty_list():
    """Test merge_header_footer_into_chunk function with empty DST list"""
    dst_list = []
    
    result = merge_header_footer_into_chunk(dst_list, page_size=0)
    
    assert result is None


def test_mark_page_header_tail_text():
    """Test mark_page_header_tail_text function (wrapper)"""
    file_info = _create_file_info()
    dst_list = [_create_test_dst("dst1", ["content"], page=1)]
    page_map = {1: dst_list}
    sorted_pages = [1]
    
    with patch('modules.flows.filters.header_tail_filter.mark_header_and_tail') as mock_mark:
        mock_mark.return_value = dst_list
        
        result = mark_page_header_tail_text(dst_list, page_map, sorted_pages, file_info)
        
        assert result == dst_list
        mock_mark.assert_called_once_with(dst_list, page_map, sorted_pages, file_info)


# def test_mark_header_and_tail_basic():
#     """Test mark_header_and_tail function with basic functionality"""
#     file_info = _create_file_info()
#
#     # Create DSTs for two pages with matching headers (same content after number replacement)
#     page1_header = _create_test_dst("p1h", ["Header 1"], y1=50, y2=70, page=1)
#     page1_content = _create_test_dst("p1c", ["Content 1"], y1=200, y2=220, page=1)
#
#     page2_header = _create_test_dst("p2h", ["Header 2"], y1=50, y2=70, page=2)  # "Header 1" vs "Header 2" -> "Header 《NUM》"
#     page2_content = _create_test_dst("p2c", ["Content 2"], y1=200, y2=220, page=2)
#
#     dst_list = [page1_header, page1_content, page2_header, page2_content]
#     page_map = {
#         1: [page1_header, page1_content],
#         2: [page2_header, page2_content]
#     }
#     sorted_pages = [1, 2]
#
#     # Debug: Test get_matching_dsts directly first
#     headers_dict, footers_dict, _, _ = get_matching_dsts(
#         page_map[1], page_map[2], file_info
#     )
#
#     result = mark_header_and_tail(dst_list, page_map, sorted_pages, file_info)
#
#     # Debug output to understand what's happening
#     # If this fails, we'll see what headers_dict contains
#     print(f"Headers dict: {headers_dict}")
#     print(f"Footers dict: {footers_dict}")
#     print(f"Page1 header marked: {result[0].mark}")
#     print(f"Page2 header marked: {result[2].mark}")
#
#     # Headers should be marked (both DSTs with matching IDs will be in headers dict)
#     assert result[0].mark == MarkType.HEADER
#     assert result[1].mark is None  # Content should not be marked
#     assert result[2].mark == MarkType.HEADER
#     assert result[3].mark is None


def test_mark_header_and_tail_large_header_area():
    """Test mark_header_and_tail function when header area is too large"""
    file_info = _create_file_info(height=600)
    
    # Create DSTs with large header area (> 15% of page height)
    page1_header1 = _create_test_dst("p1h1", ["Header 1"], y1=50, y2=70, page=1)
    page1_header2 = _create_test_dst("p1h2", ["Header 2"], y1=80, y2=200, page=1)  # Large gap
    
    page2_header1 = _create_test_dst("p2h1", ["Header 1"], y1=50, y2=70, page=2)
    page2_header2 = _create_test_dst("p2h2", ["Header 2"], y1=80, y2=200, page=2)
    
    dst_list = [page1_header1, page1_header2, page2_header1, page2_header2]
    page_map = {
        1: [page1_header1, page1_header2],
        2: [page2_header1, page2_header2]
    }
    sorted_pages = [1, 2]
    
    result = mark_header_and_tail(dst_list, page_map, sorted_pages, file_info)
    
    # Headers should not be marked due to large area
    assert all(dst.mark != MarkType.HEADER for dst in result)


def test_mark_header_and_tail_mixed_content():
    """Test mark_header_and_tail function with mixed content types"""
    file_info = _create_file_info()
    
    # Create DSTs with mixed types
    page1_header = _create_test_dst("p1h", ["Header"], y1=50, y2=70, page=1)
    page1_table = _create_test_dst("p1t", ["Table"], DSTType.TABLE, y1=60, y2=80, page=1)
    
    page2_header = _create_test_dst("p2h", ["Header"], y1=50, y2=70, page=2)
    page2_image = _create_test_dst("p2i", ["Image"], DSTType.IMAGE, y1=60, y2=80, page=2)
    
    dst_list = [page1_header, page1_table, page2_header, page2_image]
    page_map = {
        1: [page1_header, page1_table],
        2: [page2_header, page2_image]
    }
    sorted_pages = [1, 2]
    
    result = mark_header_and_tail(dst_list, page_map, sorted_pages, file_info)
    
    # Text headers should be marked, but not table in header area (when all_text_headers is False)
    assert result[0].mark == MarkType.HEADER
    assert result[1].mark != MarkType.HEADER  # Table should not be marked as header
    assert result[2].mark == MarkType.HEADER


def test_mark_header_and_tail_single_page():
    """Test mark_header_and_tail function with single page (no comparison possible)"""
    file_info = _create_file_info()
    
    dst1 = _create_test_dst("dst1", ["Single page content"], page=1)
    dst_list = [dst1]
    page_map = {1: [dst1]}
    sorted_pages = [1]
    
    result = mark_header_and_tail(dst_list, page_map, sorted_pages, file_info)
    
    # No marking should happen with single page
    assert result[0].mark is None


def test_mark_header_and_tail_empty_pages():
    """Test mark_header_and_tail function with empty pages"""
    file_info = _create_file_info()
    
    dst_list = []
    page_map = {}
    sorted_pages = []
    
    result = mark_header_and_tail(dst_list, page_map, sorted_pages, file_info)
    
    assert result == []


def test_mark_header_and_tail_footers():
    """Test mark_header_and_tail function specifically for footers"""
    file_info = _create_file_info()
    
    # Create DSTs with matching footers at bottom of pages (identical text should match)
    page1_content = _create_test_dst("p1c", ["Content 1"], y1=200, y2=220, page=1)
    page1_footer = _create_test_dst("p1f", ["Footer text"], y1=210, y2=230, page=1)
    
    page2_content = _create_test_dst("p2c", ["Content 2"], y1=200, y2=220, page=2)
    page2_footer = _create_test_dst("p2f", ["Footer text"], y1=210, y2=230, page=2)  # Identical to page1_footer
    
    dst_list = [page1_content, page1_footer, page2_content, page2_footer]
    page_map = {
        1: [page1_content, page1_footer],
        2: [page2_content, page2_footer]
    }
    sorted_pages = [1, 2]
    
    result = mark_header_and_tail(dst_list, page_map, sorted_pages, file_info)
    
    # Footers should be marked (identical "Footer text" should match)
    assert result[0].mark == MarkType.HEADER
    assert result[1].mark == MarkType.HEADER
    assert result[2].mark == MarkType.HEADER
    assert result[3].mark == MarkType.HEADER
