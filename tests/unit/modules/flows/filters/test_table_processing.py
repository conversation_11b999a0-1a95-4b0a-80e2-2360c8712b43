# Author: linqi
# Date: 2025/8/14
# Time: 17:00

import pytest
import uuid
import numpy as np
from unittest.mock import Mock, patch
from typing import List

from modules.flows.filters.table_processing import (
    get_similarity, TitleProcessor, TableProcessorContext,
    TITLE_LENGTH_LIMIT, SIMILARITY_THRESHOLD
)
from modules.entity.chunk_entity import Chunk, LabelType


def _create_test_chunk(chunk_id=None, label=LabelType.TEXT, content="test content", 
                      content_embedding=None, page_num=None, pre_chunk=None, next_chunk=None):
    """Create a test Chunk object"""
    if chunk_id is None:
        chunk_id = uuid.uuid4().hex
    if page_num is None:
        page_num = [1]
    
    chunk = Chunk(
        chunk_id=chunk_id,
        label=label,
        content=content,
        page_size=1,
        page_num=page_num,
        dsts=[],
        mark=None,
        block=[]
    )
    
    chunk.content_embedding = content_embedding
    chunk.pre_chunk = pre_chunk
    chunk.next_chunk = next_chunk
    
    return chunk


def test_get_similarity_with_embeddings():
    """Test get_similarity function with valid embeddings"""
    chunk1 = _create_test_chunk(content_embedding=[1.0, 0.0, 0.0])
    chunk2 = _create_test_chunk(content_embedding=[1.0, 0.0, 0.0])  # Same embedding
    
    similarity = get_similarity(chunk1, chunk2)
    
    # Should return 1.0 for identical embeddings
    assert abs(similarity - 1.0) < 1e-6


def test_get_similarity_different_embeddings():
    """Test get_similarity function with different embeddings"""
    chunk1 = _create_test_chunk(content_embedding=[1.0, 0.0, 0.0])
    chunk2 = _create_test_chunk(content_embedding=[0.0, 1.0, 0.0])  # Orthogonal
    
    similarity = get_similarity(chunk1, chunk2)
    
    # Should return 0.0 for orthogonal embeddings
    assert abs(similarity - 0.0) < 1e-6


def test_get_similarity_none_embeddings():
    """Test get_similarity function with None embeddings"""
    chunk1 = _create_test_chunk(content_embedding=None)
    chunk2 = _create_test_chunk(content_embedding=[1.0, 0.0, 0.0])
    
    similarity = get_similarity(chunk1, chunk2)
    
    # Should return 0.0 when either embedding is None
    assert similarity == 0.0


def test_get_similarity_empty_embeddings():
    """Test get_similarity function with empty embeddings"""
    chunk1 = _create_test_chunk(content_embedding=[])
    chunk2 = _create_test_chunk(content_embedding=[1.0, 0.0, 0.0])
    
    similarity = get_similarity(chunk1, chunk2)
    
    # Should return 0.0 when either embedding is empty
    assert similarity == 0.0


def test_get_similarity_both_empty():
    """Test get_similarity function with both embeddings empty"""
    chunk1 = _create_test_chunk(content_embedding=[])
    chunk2 = _create_test_chunk(content_embedding=[])
    
    similarity = get_similarity(chunk1, chunk2)
    
    assert similarity == 0.0


def test_title_processor_basic():
    """Test TitleProcessor with basic table and text chunks"""
    processor = TitleProcessor()
    
    # Create chunks: text, table, text
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, "Short title", [0.8, 0.2])
    table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table content", [0.7, 0.3])
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, "Another title", [0.6, 0.4])
    
    chunks = [text_chunk1, table_chunk, text_chunk2]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        # Mock similarity - text1 more similar to table than text2
        mock_similarity.side_effect = [0.8, 0.5]  # pre_similarity, next_similarity
        
        result = processor.process(chunks)
        
        # Table should be linked to text1 (higher similarity)
        assert result[1].pre_chunk == "text1"
        assert result[0].next_chunk == "table1"
        assert result[1].next_chunk is None
        assert result[2].pre_chunk is None


def test_title_processor_next_chunk_higher_similarity():
    """Test TitleProcessor when next chunk has higher similarity"""
    processor = TitleProcessor()
    
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, "Title", [0.3, 0.7])
    table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table", [0.5, 0.5])
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, "Better title", [0.6, 0.4])
    
    chunks = [text_chunk1, table_chunk, text_chunk2]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        # Mock next_similarity > pre_similarity
        mock_similarity.side_effect = [0.3, 0.9]
        
        result = processor.process(chunks)
        
        # Table should be linked to text2 (higher similarity)
        assert result[1].next_chunk == "text2"
        assert result[2].pre_chunk == "table1"
        assert result[1].pre_chunk is None
        assert result[0].next_chunk is None


def test_title_processor_similarity_below_threshold():
    """Test TitleProcessor when similarity is below threshold"""
    processor = TitleProcessor()
    
    with patch('modules.flows.filters.table_processing.SIMILARITY_THRESHOLD', 0.5):
        text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, "Title", [0.1, 0.9])
        table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table", [0.5, 0.5])
        text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, "Title", [0.9, 0.1])
        
        chunks = [text_chunk1, table_chunk, text_chunk2]
        
        with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
            # Mock similarities below threshold
            mock_similarity.side_effect = [0.3, 0.4]  # Both below 0.5
            
            result = processor.process(chunks)
            
            # No links should be created
            assert result[1].pre_chunk is None
            assert result[1].next_chunk is None
            assert result[0].next_chunk is None
            assert result[2].pre_chunk is None


def test_title_processor_long_text_chunks():
    """Test TitleProcessor with text chunks exceeding length limit"""
    processor = TitleProcessor()
    
    # Create long text chunks
    long_content = "x" * (TITLE_LENGTH_LIMIT + 1)
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, long_content)
    table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table content")
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, long_content)
    
    chunks = [text_chunk1, table_chunk, text_chunk2]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        result = processor.process(chunks)
        
        # get_similarity should not be called since text chunks are too long
        mock_similarity.assert_not_called()
        
        # No links should be created
        assert result[1].pre_chunk is None
        assert result[1].next_chunk is None


def test_title_processor_only_pre_chunk_valid():
    """Test TitleProcessor when only pre-chunk is valid but needs both to proceed"""
    processor = TitleProcessor()
    
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, "Short title")
    table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table content")
    long_content = "x" * (TITLE_LENGTH_LIMIT + 1)
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, long_content)  # Too long
    
    chunks = [text_chunk1, table_chunk, text_chunk2]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        result = processor.process(chunks)
        
        # No links should be created because logic requires both pre_chunk and next_chunk to be valid
        # before it starts similarity comparison
        assert result[1].pre_chunk is None
        assert result[0].next_chunk is None
        assert result[1].next_chunk is None
        assert result[2].pre_chunk is None
        
        # get_similarity should not be called since next_chunk is invalid
        mock_similarity.assert_not_called()


def test_title_processor_only_next_chunk_valid():
    """Test TitleProcessor when only next-chunk is valid but needs both to proceed"""
    processor = TitleProcessor()
    
    long_content = "x" * (TITLE_LENGTH_LIMIT + 1)
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, long_content)  # Too long
    table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table content")
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, "Short title")
    
    chunks = [text_chunk1, table_chunk, text_chunk2]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        result = processor.process(chunks)
        
        # No links should be created because logic requires both pre_chunk and next_chunk to be valid
        # before it starts similarity comparison
        assert result[1].next_chunk is None
        assert result[2].pre_chunk is None
        assert result[1].pre_chunk is None
        assert result[0].next_chunk is None
        
        # get_similarity should not be called since pre_chunk is invalid
        mock_similarity.assert_not_called()


def test_title_processor_separate_linking_scenarios():
    """Test separate scenarios where only one valid neighbor exists"""
    processor = TitleProcessor()
    
    # Scenario 1: Table with only valid pre-chunk (no next chunk at all)
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, "Short title")
    table_chunk1 = _create_test_chunk("table1", LabelType.TABLE, "Table content")
    
    chunks1 = [text_chunk1, table_chunk1]  # No next chunk
    result1 = processor.process(chunks1)
    
    # Should not link anything (needs both pre and next)
    assert result1[1].pre_chunk is None
    assert result1[0].next_chunk is None
    
    # Scenario 2: Table with only valid next-chunk (no pre chunk)
    table_chunk2 = _create_test_chunk("table2", LabelType.TABLE, "Table content") 
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, "Short title")
    
    chunks2 = [table_chunk2, text_chunk2]  # No pre chunk
    result2 = processor.process(chunks2)
    
    # Should not link anything (needs both pre and next)
    assert result2[0].next_chunk is None
    assert result2[1].pre_chunk is None


def test_title_processor_non_table_chunks():
    """Test TitleProcessor with non-table chunks"""
    processor = TitleProcessor()
    
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, "Title")
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, "Content")  # Not a table
    text_chunk3 = _create_test_chunk("text3", LabelType.TEXT, "Title")
    
    chunks = [text_chunk1, text_chunk2, text_chunk3]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        result = processor.process(chunks)
        
        # get_similarity should not be called since middle chunk is not a table
        mock_similarity.assert_not_called()
        
        # No links should be created
        assert all(chunk.pre_chunk is None for chunk in result)
        assert all(chunk.next_chunk is None for chunk in result)


def test_title_processor_edge_cases():
    """Test TitleProcessor with edge cases"""
    processor = TitleProcessor()
    
    # Test with only one chunk
    single_chunk = [_create_test_chunk("only", LabelType.TABLE, "Table")]
    result = processor.process(single_chunk)
    assert result[0].pre_chunk is None
    assert result[0].next_chunk is None
    
    # Test with only two chunks
    two_chunks = [
        _create_test_chunk("text1", LabelType.TEXT, "Title"),
        _create_test_chunk("table1", LabelType.TABLE, "Table")
    ]
    result = processor.process(two_chunks)
    # Should not process since there's no next chunk for the table
    assert result[1].pre_chunk is None
    assert result[1].next_chunk is None
    
    # Test with empty list
    result = processor.process([])
    assert result == []


def test_title_processor_boundary_length():
    """Test TitleProcessor with text chunks at length boundary"""
    processor = TitleProcessor()
    
    # Create text chunks exactly at the limit
    boundary_content = "x" * TITLE_LENGTH_LIMIT
    text_chunk1 = _create_test_chunk("text1", LabelType.TEXT, boundary_content)
    table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table content")
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, boundary_content)
    
    chunks = [text_chunk1, table_chunk, text_chunk2]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        mock_similarity.side_effect = [0.8, 0.6]  # pre > next
        
        result = processor.process(chunks)
        
        # Should process since length is exactly at limit (<=)
        assert result[1].pre_chunk == "text1"
        assert result[0].next_chunk == "table1"


def test_table_processor_context():
    """Test TableProcessorContext class"""
    # Test initialization
    processor = TitleProcessor()
    context = TableProcessorContext(processor)
    
    assert context.processor == processor
    
    # Test set_processor
    new_processor = TitleProcessor()
    context.set_processor(new_processor)
    assert context.processor == new_processor
    
    # Test process_chunks
    chunks = [_create_test_chunk("test", LabelType.TEXT, "content")]
    
    with patch.object(new_processor, 'process') as mock_process:
        mock_process.return_value = chunks
        
        result = context.process_chunks(chunks)
        
        assert result == chunks
        mock_process.assert_called_once_with(chunks)


def test_table_processor_context_process_chunks():
    """Test TableProcessorContext.process_chunks with actual processing"""
    processor = TitleProcessor()
    context = TableProcessorContext(processor)
    
    text_chunk = _create_test_chunk("text1", LabelType.TEXT, "Title", [0.8, 0.2])
    table_chunk = _create_test_chunk("table1", LabelType.TABLE, "Table", [0.7, 0.3])
    text_chunk2 = _create_test_chunk("text2", LabelType.TEXT, "Title", [0.6, 0.4])
    
    chunks = [text_chunk, table_chunk, text_chunk2]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        mock_similarity.side_effect = [0.9, 0.5]  # pre > next
        
        result = context.process_chunks(chunks)
        
        assert len(result) == 3
        assert result[1].pre_chunk == "text1"
        assert result[0].next_chunk == "table1"


def test_title_processor_multiple_tables():
    """Test TitleProcessor with multiple tables"""
    processor = TitleProcessor()
    
    chunks = [
        _create_test_chunk("text1", LabelType.TEXT, "Title 1"),
        _create_test_chunk("table1", LabelType.TABLE, "Table 1"),
        _create_test_chunk("text2", LabelType.TEXT, "Title 2"),
        _create_test_chunk("table2", LabelType.TABLE, "Table 2"),
        _create_test_chunk("text3", LabelType.TEXT, "Title 3")
    ]
    
    with patch('modules.flows.filters.table_processing.get_similarity') as mock_similarity:
        # Mock similarities for both tables
        mock_similarity.side_effect = [0.8, 0.6, 0.7, 0.9]  # table1: pre>next, table2: next>pre
        
        result = processor.process(chunks)
        
        # First table linked to previous text
        assert result[1].pre_chunk == "text1"
        assert result[0].next_chunk == "table1"
        
        # Second table linked to next text
        assert result[3].next_chunk == "text3"
        assert result[4].pre_chunk == "table2"
        
        # Middle text should not be linked
        assert result[2].pre_chunk is None
        assert result[2].next_chunk is None