# Author: linqi
# Date: 2025/8/14
# Time: 17:00

import pytest
from unittest.mock import Mock, patch
from typing import List, Dict

from modules.flows.filters.catalog_filter import mark_catalog, get_catalog_pages
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo, MarkType
from conf import Dst<PERSON>ilt<PERSON>


def _create_test_dst(dst_id="test", content=None, dst_type=DSTType.TEXT, page=0, mark=None):
    """Create a test DST object"""
    if content is None:
        content = ["test content"]
    dst = DST(
        id=dst_id,
        parent="root",
        order=0,
        dst_type=dst_type,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=0, y1=100, x2=100, y2=120)),
            page=page,
            hash="h" * 32
        ),
        content=content
    )
    dst.mark = mark
    return dst


def test_mark_catalog_with_catalog_key():
    """Test mark_catalog function when catalog key is found"""
    # Mock DstFilter configuration
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录", "CONTENTS", "TABLE OF CONTENTS"]):
        
        # Create test DSTs with catalog content
        dst1 = _create_test_dst("dst1", ["目录"], page=1)
        dst2 = _create_test_dst("dst2", ["第一章 介绍"], page=1)
        dst3 = _create_test_dst("dst3", ["正文内容"], page=2)
        
        dst_list = [dst1, dst2, dst3]
        page_map = {
            1: [dst1, dst2],
            2: [dst3]
        }
        sorted_pages = [1, 2]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Verify that page 1 DSTs are marked as catalog
        assert result[0].mark == MarkType.CATALOG
        assert result[1].mark == MarkType.CATALOG
        assert result[2].mark is None  # Page 2 should not be marked


def test_mark_catalog_with_catalog_flags():
    """Test mark_catalog function when catalog flags are found"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]), \
         patch.object(DstFilter, 'catalog_filter_flag', ["..."]), \
         patch.object(DstFilter, 'catalog_match_ratio', 0.5):
        
        # Create initial catalog page
        dst1 = _create_test_dst("dst1", ["目录"], page=1)
        dst2 = _create_test_dst("dst2", ["第一章...5"], page=2)
        dst3 = _create_test_dst("dst3", ["第二章...10"], page=2)
        dst4 = _create_test_dst("dst4", ["正文内容"], page=3)
        
        dst_list = [dst1, dst2, dst3, dst4]
        page_map = {
            1: [dst1],
            2: [dst2, dst3],
            3: [dst4]
        }
        sorted_pages = [1, 2, 3]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Verify that pages 1 and 2 are marked as catalog
        assert result[0].mark == MarkType.CATALOG  # Page 1
        assert result[1].mark == MarkType.CATALOG  # Page 2
        assert result[2].mark == MarkType.CATALOG  # Page 2
        assert result[3].mark is None  # Page 3


def test_mark_catalog_no_catalog_found():
    """Test mark_catalog function when no catalog is found"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        # Create DSTs without catalog content
        dst1 = _create_test_dst("dst1", ["第一章"], page=1)
        dst2 = _create_test_dst("dst2", ["正文内容"], page=2)
        
        dst_list = [dst1, dst2]
        page_map = {
            1: [dst1],
            2: [dst2]
        }
        sorted_pages = [1, 2]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Verify that no DSTs are marked as catalog
        assert result[0].mark is None
        assert result[1].mark is None


def test_mark_catalog_exceeds_max_page_num():
    """Test mark_catalog function when page index exceeds max catalog page number"""
    with patch.object(DstFilter, 'catalog_max_page_num', 0), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        # Create DSTs where catalog appears after max page index
        # catalog_max_page_num=0 means only check index 0 (first page)
        dst1 = _create_test_dst("dst1", ["正文"], page=1)  # index 0 - will be checked
        dst2 = _create_test_dst("dst2", ["目录"], page=2)  # index 1 - won't be checked due to max limit
        
        dst_list = [dst1, dst2]
        page_map = {
            1: [dst1],
            2: [dst2]
        }
        sorted_pages = [1, 2]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Page 2 (index 1) won't be checked because i > catalog_max_page_num (0) and no catalog found yet
        assert result[0].mark is None  # Page 1 - no catalog
        assert result[1].mark is None  # Page 2 - not checked due to max limit


def test_mark_catalog_stops_when_no_catalog_found():
    """Test mark_catalog function stops checking when no catalog found and exceeds max page index"""
    with patch.object(DstFilter, 'catalog_max_page_num', 1), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        # Create DSTs where no catalog is found in early page indexes
        # catalog_max_page_num=1 means check indexes 0 and 1, stop at index 2 if no catalog found
        dst1 = _create_test_dst("dst1", ["正文"], page=1)  # index 0 - checked
        dst2 = _create_test_dst("dst2", ["正文"], page=2)  # index 1 - checked
        dst3 = _create_test_dst("dst3", ["目录"], page=3)  # index 2 - not checked due to limit
        
        dst_list = [dst1, dst2, dst3]
        page_map = {
            1: [dst1],
            2: [dst2], 
            3: [dst3]
        }
        sorted_pages = [1, 2, 3]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # No DSTs should be marked because processing stops at index 2 (i > max_page_num=1)
        # and no catalog was found in indexes 0 and 1
        assert all(dst.mark is None for dst in result)


def test_mark_catalog_found_within_limit():
    """Test mark_catalog function when catalog is found within page index limit"""
    with patch.object(DstFilter, 'catalog_max_page_num', 2), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        # Create DSTs where catalog is found within the limit
        dst1 = _create_test_dst("dst1", ["正文"], page=1)      # index 0 - checked
        dst2 = _create_test_dst("dst2", ["目录"], page=2)      # index 1 - checked, catalog found
        dst3 = _create_test_dst("dst3", ["第一章"], page=2)    # same page as catalog
        dst4 = _create_test_dst("dst4", ["更多内容"], page=3)   # index 2 - still checked because catalog found
        
        dst_list = [dst1, dst2, dst3, dst4]
        page_map = {
            1: [dst1],
            2: [dst2, dst3],
            3: [dst4]
        }
        sorted_pages = [1, 2, 3]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Page 2 is marked as catalog, all DSTs on that page should be marked
        assert result[0].mark is None         # Page 1 - not catalog
        assert result[1].mark == MarkType.CATALOG  # Page 2 - catalog page
        assert result[2].mark == MarkType.CATALOG  # Page 2 - catalog page
        assert result[3].mark is None         # Page 3 - not catalog


def test_mark_catalog_with_mixed_dst_types():
    """Test mark_catalog function with mixed DST types"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        # Create DSTs with mixed types
        dst_text = _create_test_dst("text1", ["目录"], DSTType.TEXT, page=1)
        dst_table = _create_test_dst("table1", ["表格内容"], DSTType.TABLE, page=1)
        dst_image = _create_test_dst("image1", ["图片"], DSTType.IMAGE, page=1)
        
        dst_list = [dst_text, dst_table, dst_image]
        page_map = {1: [dst_text, dst_table, dst_image]}
        sorted_pages = [1]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Only TEXT DSTs should be considered for catalog marking
        assert result[0].mark == MarkType.CATALOG  # TEXT DST
        assert result[1].mark == MarkType.CATALOG  # All DSTs on catalog page get marked
        assert result[2].mark == MarkType.CATALOG


def test_mark_catalog_with_marked_dsts():
    """Test mark_catalog function when DSTs already have marks"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        # Create DSTs - one with existing mark (ignored for detection), one without mark (used for detection)
        dst1 = _create_test_dst("dst1", ["目录"], page=1, mark=MarkType.HEADER)  # Ignored for detection
        dst2 = _create_test_dst("dst2", ["目录"], page=1)  # Used for detection
        
        dst_list = [dst1, dst2]
        page_map = {1: [dst1, dst2]}
        sorted_pages = [1]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # dst2 (no mark) will be used for catalog detection and page 1 will be marked as catalog
        # Both DSTs will then get CATALOG mark regardless of their previous marks
        assert result[0].mark == MarkType.CATALOG  # Overwritten from HEADER
        assert result[1].mark == MarkType.CATALOG  # Set to CATALOG


def test_mark_catalog_empty_page_map():
    """Test mark_catalog function with empty page map"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10):
        
        dst_list = []
        page_map = {}
        sorted_pages = []
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        assert result == []


def test_mark_catalog_page_not_in_map():
    """Test mark_catalog function when page is not in page_map"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        dst1 = _create_test_dst("dst1", ["内容"], page=1)
        dst_list = [dst1]
        page_map = {}  # Page 1 not in map
        sorted_pages = [1]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Should handle missing pages gracefully
        assert result[0].mark is None


def test_mark_catalog_ratio_threshold():
    """Test mark_catalog function with catalog match ratio threshold"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]), \
         patch.object(DstFilter, 'catalog_filter_flag', ["..."]), \
         patch.object(DstFilter, 'catalog_match_ratio', 0.6):  # 60% threshold
        
        # Create catalog page first
        dst1 = _create_test_dst("dst1", ["目录"], page=1)
        
        # Create page 2 with insufficient match ratio (only 1 out of 3 = 33%)
        dst2 = _create_test_dst("dst2", ["第一章...5"], page=2)
        dst3 = _create_test_dst("dst3", ["第二章"], page=2)  # No flag
        dst4 = _create_test_dst("dst4", ["第三章"], page=2)  # No flag
        
        dst_list = [dst1, dst2, dst3, dst4]
        page_map = {
            1: [dst1],
            2: [dst2, dst3, dst4]
        }
        sorted_pages = [1, 2]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Page 1 should be catalog, but page 2 should not (ratio too low)
        assert result[0].mark == MarkType.CATALOG  # Page 1
        assert result[1].mark is None  # Page 2 - insufficient ratio
        assert result[2].mark is None
        assert result[3].mark is None


def test_get_catalog_pages_with_catalog():
    """Test get_catalog_pages function when catalog DSTs exist"""
    dst1 = _create_test_dst("dst1", ["内容"], page=1, mark=MarkType.CATALOG)
    dst2 = _create_test_dst("dst2", ["内容"], page=3, mark=MarkType.CATALOG)
    dst3 = _create_test_dst("dst3", ["内容"], page=2, mark=MarkType.HEADER)
    dst4 = _create_test_dst("dst4", ["内容"], page=5, mark=MarkType.CATALOG)
    
    dst_list = [dst1, dst2, dst3, dst4]
    
    result = get_catalog_pages(dst_list)
    
    # Should return the maximum catalog page number
    assert result == 5


def test_get_catalog_pages_no_catalog():
    """Test get_catalog_pages function when no catalog DSTs exist"""
    dst1 = _create_test_dst("dst1", ["内容"], page=1, mark=MarkType.HEADER)
    dst2 = _create_test_dst("dst2", ["内容"], page=2, mark=MarkType.FOOTER)
    dst3 = _create_test_dst("dst3", ["内容"], page=3)  # No mark
    
    dst_list = [dst1, dst2, dst3]
    
    result = get_catalog_pages(dst_list)
    
    # Should return -1 when no catalog found
    assert result == -1


def test_get_catalog_pages_empty_list():
    """Test get_catalog_pages function with empty DST list"""
    dst_list = []
    
    result = get_catalog_pages(dst_list)
    
    assert result == -1


def test_get_catalog_pages_single_catalog():
    """Test get_catalog_pages function with single catalog DST"""
    dst1 = _create_test_dst("dst1", ["目录"], page=2, mark=MarkType.CATALOG)
    dst_list = [dst1]
    
    result = get_catalog_pages(dst_list)
    
    assert result == 2


def test_mark_catalog_content_with_newlines():
    """Test mark_catalog function with content containing newlines"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]):
        
        # Create DST with newlines in content
        dst1 = _create_test_dst("dst1", ["目\n录"], page=1)
        dst2 = _create_test_dst("dst2", ["第一章"], page=1)
        
        dst_list = [dst1, dst2]
        page_map = {1: [dst1, dst2]}
        sorted_pages = [1]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Should handle newlines by removing them for comparison
        assert result[0].mark == MarkType.CATALOG
        assert result[1].mark == MarkType.CATALOG


def test_mark_catalog_flag_with_newlines():
    """Test mark_catalog function with flags containing newlines"""
    with patch.object(DstFilter, 'catalog_max_page_num', 10), \
         patch.object(DstFilter, 'catalog_filter_key', ["目录"]), \
         patch.object(DstFilter, 'catalog_filter_flag', ["..."]), \
         patch.object(DstFilter, 'catalog_match_ratio', 0.5):
        
        # Create initial catalog page
        dst1 = _create_test_dst("dst1", ["目录"], page=1)
        
        # Create page with flags containing newlines
        dst2 = _create_test_dst("dst2", ["第一章\n...5"], page=2)
        dst3 = _create_test_dst("dst3", ["第二章...10"], page=2)
        
        dst_list = [dst1, dst2, dst3]
        page_map = {
            1: [dst1],
            2: [dst2, dst3]
        }
        sorted_pages = [1, 2]
        
        result = mark_catalog(dst_list, page_map, sorted_pages)
        
        # Should handle newlines in flag matching
        assert result[0].mark == MarkType.CATALOG  # Page 1
        assert result[1].mark == MarkType.CATALOG  # Page 2
        assert result[2].mark == MarkType.CATALOG  # Page 2