# Author: linqi
# Date: 2025/8/14
# Time: 17:00

import pytest
from unittest.mock import Mock, patch
from typing import List, Dict

from modules.flows.filters.filter import dst_mark
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo, MarkType
from modules.pipeline.context import FileInfo
from services.datamodel import FileType


def _create_test_dst(dst_id="test", content=None, dst_type=DSTType.TEXT, page=0, y1=100, y2=120):
    """Create a test DST object"""
    if content is None:
        content = ["test content"]
    return DST(
        id=dst_id,
        parent="root",
        order=0,
        dst_type=dst_type,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=0, y1=y1, x2=100, y2=y2)),
            page=page,
            hash="h" * 32
        ),
        content=content
    )


def _create_file_info():
    """Create a test FileInfo object"""
    return FileInfo(
        file_type=FileType.PDF,
        page_size=5,
        word_count=1000,
        width=800,
        height=600,
        is_scan=False
    )


def test_dst_mark_basic_functionality():
    """Test dst_mark function with basic functionality"""
    # Create test data
    dst1 = _create_test_dst("dst1", ["Text on page 1"], page=1)
    dst2 = _create_test_dst("dst2", ["Text on page 2"], page=2)
    dst_list = [dst1, dst2]
    
    page_map = {
        1: [dst1],
        2: [dst2]
    }
    
    file_info = _create_file_info()
    
    # Mock the sub-functions
    with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
         patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
        
        # Configure mocks
        mock_header_tail.return_value = dst_list
        mock_catalog.return_value = dst_list
        
        # Call function
        result = dst_mark(dst_list, page_map, file_info)
        
        # Verify results
        assert result == dst_list
        mock_header_tail.assert_called_once_with(dst_list, page_map, [1, 2], file_info)
        mock_catalog.assert_called_once_with(dst_list, page_map, [1, 2])


def test_dst_mark_empty_input():
    """Test dst_mark function with empty inputs"""
    dst_list = []
    page_map = {}
    file_info = _create_file_info()
    
    with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
         patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
        
        mock_header_tail.return_value = []
        mock_catalog.return_value = []
        
        result = dst_mark(dst_list, page_map, file_info)
        
        assert result == []
        mock_header_tail.assert_called_once_with([], {}, [], file_info)
        mock_catalog.assert_called_once_with([], {}, [])


def test_dst_mark_single_page():
    """Test dst_mark function with single page"""
    dst1 = _create_test_dst("dst1", ["Single page content"], page=0)
    dst_list = [dst1]
    page_map = {0: [dst1]}
    file_info = _create_file_info()
    
    with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
         patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
        
        mock_header_tail.return_value = dst_list
        mock_catalog.return_value = dst_list
        
        result = dst_mark(dst_list, page_map, file_info)
        
        assert result == dst_list
        mock_header_tail.assert_called_once_with(dst_list, page_map, [0], file_info)
        mock_catalog.assert_called_once_with(dst_list, page_map, [0])


def test_dst_mark_multiple_pages_out_of_order():
    """Test dst_mark function with multiple pages not in order"""
    dst1 = _create_test_dst("dst1", ["Content page 3"], page=3)
    dst2 = _create_test_dst("dst2", ["Content page 1"], page=1)
    dst3 = _create_test_dst("dst3", ["Content page 2"], page=2)
    dst_list = [dst1, dst2, dst3]
    
    page_map = {
        3: [dst1],
        1: [dst2],
        2: [dst3]
    }
    
    file_info = _create_file_info()
    
    with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
         patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
        
        mock_header_tail.return_value = dst_list
        mock_catalog.return_value = dst_list
        
        result = dst_mark(dst_list, page_map, file_info)
        
        # Verify that pages are sorted correctly
        expected_sorted_pages = [1, 2, 3]
        mock_header_tail.assert_called_once_with(dst_list, page_map, expected_sorted_pages, file_info)
        mock_catalog.assert_called_once_with(dst_list, page_map, expected_sorted_pages)


def test_dst_mark_mixed_dst_types():
    """Test dst_mark function with mixed DST types"""
    dst_text = _create_test_dst("text1", ["Text content"], DSTType.TEXT, page=1)
    dst_table = _create_test_dst("table1", ["Table content"], DSTType.TABLE, page=1)
    dst_image = _create_test_dst("image1", ["Image content"], DSTType.IMAGE, page=1)
    
    dst_list = [dst_text, dst_table, dst_image]
    page_map = {1: dst_list}
    file_info = _create_file_info()
    
    with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
         patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
        
        mock_header_tail.return_value = dst_list
        mock_catalog.return_value = dst_list
        
        result = dst_mark(dst_list, page_map, file_info)
        
        assert len(result) == 3
        assert result == dst_list


def test_dst_mark_with_marks_applied():
    """Test dst_mark function ensuring marks are properly applied"""
    dst1 = _create_test_dst("dst1", ["Header content"], page=1)
    dst2 = _create_test_dst("dst2", ["Regular content"], page=1)
    dst3 = _create_test_dst("dst3", ["Footer content"], page=1)
    
    dst_list = [dst1, dst2, dst3]
    page_map = {1: dst_list}
    file_info = _create_file_info()
    
    def mock_header_tail_func(dst_list, page_map, sorted_pages, file_info):
        # Simulate marking header and footer
        dst_list[0].mark = MarkType.HEADER
        dst_list[2].mark = MarkType.FOOTER
        return dst_list
    
    def mock_catalog_func(dst_list, page_map, sorted_pages):
        # Simulate catalog marking (no change in this case)
        return dst_list
    
    with patch('modules.flows.filters.filter.mark_page_header_tail_text', side_effect=mock_header_tail_func), \
         patch('modules.flows.filters.filter.mark_catalog', side_effect=mock_catalog_func):
        
        result = dst_mark(dst_list, page_map, file_info)
        
        assert result[0].mark == MarkType.HEADER
        assert result[1].mark is None  # Regular content should not be marked
        assert result[2].mark == MarkType.FOOTER


def test_dst_mark_page_map_missing_pages():
    """Test dst_mark function when page_map has missing pages"""
    dst1 = _create_test_dst("dst1", ["Content page 1"], page=1)
    dst3 = _create_test_dst("dst3", ["Content page 3"], page=3)
    
    dst_list = [dst1, dst3]
    # Page 2 is missing from page_map
    page_map = {
        1: [dst1],
        3: [dst3]
    }
    
    file_info = _create_file_info()
    
    with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
         patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
        
        mock_header_tail.return_value = dst_list
        mock_catalog.return_value = dst_list
        
        result = dst_mark(dst_list, page_map, file_info)
        
        # Should still work with missing pages
        expected_sorted_pages = [1, 3]
        mock_header_tail.assert_called_once_with(dst_list, page_map, expected_sorted_pages, file_info)
        mock_catalog.assert_called_once_with(dst_list, page_map, expected_sorted_pages)


def test_dst_mark_different_file_types():
    """Test dst_mark function with different file types"""
    dst_list = [_create_test_dst("dst1", ["Content"], page=1)]
    page_map = {1: dst_list}
    
    # Test with different file types
    file_types = [FileType.PDF, FileType.DOCX, FileType.PPTX]
    
    for file_type in file_types:
        file_info = FileInfo(
            file_type=file_type,
            page_size=1,
            word_count=100,
            width=800,
            height=600,
            is_scan=False
        )
        
        with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
             patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
            
            mock_header_tail.return_value = dst_list
            mock_catalog.return_value = dst_list
            
            result = dst_mark(dst_list, page_map, file_info)
            
            assert result == dst_list
            mock_header_tail.assert_called_once_with(dst_list, page_map, [1], file_info)
            mock_catalog.assert_called_once_with(dst_list, page_map, [1])


def test_dst_mark_large_document():
    """Test dst_mark function with a large document"""
    # Create a large document with many pages
    dst_list = []
    page_map = {}
    
    for page in range(1, 101):  # 100 pages
        for i in range(5):  # 5 DSTs per page
            dst = _create_test_dst(f"dst_{page}_{i}", [f"Content page {page} item {i}"], page=page)
            dst_list.append(dst)
            if page not in page_map:
                page_map[page] = []
            page_map[page].append(dst)
    
    file_info = _create_file_info()
    
    with patch('modules.flows.filters.filter.mark_page_header_tail_text') as mock_header_tail, \
         patch('modules.flows.filters.filter.mark_catalog') as mock_catalog:
        
        mock_header_tail.return_value = dst_list
        mock_catalog.return_value = dst_list
        
        result = dst_mark(dst_list, page_map, file_info)
        
        assert len(result) == 500  # 100 pages * 5 DSTs per page
        
        # Verify sorted pages (should be 1 to 100)
        expected_sorted_pages = list(range(1, 101))
        mock_header_tail.assert_called_once_with(dst_list, page_map, expected_sorted_pages, file_info)
        mock_catalog.assert_called_once_with(dst_list, page_map, expected_sorted_pages)