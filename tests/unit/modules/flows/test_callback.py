# -*- coding: utf-8 -*-
"""
Unit tests for modules.flows.callback module
"""

import pytest
import json
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime

from modules.flows.callback import callback_parse_background, upload_result_and_set_status
from modules.entity.parse_entity import GeneralParseStatus, RespGeneralParseData, ParseRes


class TestCallbackParseBackground:
    """Test callback_parse_background function"""

    @pytest.mark.asyncio
    async def test_callback_parse_background_success_with_callback_and_ks3_url(self):
        """Test successful callback with need_callback=True and return_ks3_url=True"""
        # Mock result
        mock_result = MagicMock()
        mock_result.dict.return_value = {"test": "data"}
        
        # Mock dependencies
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host_uri, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = "http://test-ks3-url.com"
            mock_get_host_uri.return_value = ("test-host", "test-uri")
            mock_rpc_instance = MagicMock()
            mock_rpc_instance.send_callback = AsyncMock()
            mock_rpc_client.return_value = mock_rpc_instance
            
            # Execute
            await callback_parse_background(
                result=mock_result,
                step_name="test_step", 
                token="test_token",
                need_callback=True,
                return_ks3_url=True,
                callback_url="http://test-callback.com/api"
            )
            
            # Verify
            mock_upload.assert_called_once_with(mock_result, "test_step", "test_token")
            mock_get_host_uri.assert_called_once_with("http://test-callback.com/api")
            mock_rpc_instance.send_callback.assert_called_once()
            
            # Check the callback data
            call_args = mock_rpc_instance.send_callback.call_args
            assert call_args[1]['host'] == "test-host"
            assert call_args[1]['uri'] == "test-uri"
            assert call_args[1]['method'] == "POST"
            assert call_args[1]['data'].status == GeneralParseStatus.ok
            assert call_args[1]['data'].token == "test_token"
            assert call_args[1]['data'].res_ks3_url == "http://test-ks3-url.com"
            assert call_args[1]['data'].parse_res is None

    @pytest.mark.asyncio
    async def test_callback_parse_background_success_with_callback_and_result_data(self):
        """Test successful callback with need_callback=True and return_ks3_url=False"""
        # Mock result
        mock_result = MagicMock()
        mock_result.dict.return_value = {"test": "data"}
        
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host_uri, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = "http://test-ks3-url.com"
            mock_get_host_uri.return_value = ("test-host", "test-uri")
            mock_rpc_instance = MagicMock()
            mock_rpc_instance.send_callback = AsyncMock()
            mock_rpc_client.return_value = mock_rpc_instance
            
            # Execute
            await callback_parse_background(
                result=mock_result,
                step_name="test_step", 
                token="test_token",
                need_callback=True,
                return_ks3_url=False,
                callback_url="http://test-callback.com/api"
            )
            
            # Verify callback data contains parse_res instead of res_ks3_url
            call_args = mock_rpc_instance.send_callback.call_args
            assert call_args[1]['data'].res_ks3_url is None
            assert call_args[1]['data'].parse_res == mock_result

    @pytest.mark.asyncio
    async def test_callback_parse_background_success_without_callback(self):
        """Test successful processing without callback"""
        mock_result = MagicMock()
        
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host_uri, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = "http://test-ks3-url.com"
            
            # Execute
            await callback_parse_background(
                result=mock_result,
                step_name="test_step", 
                token="test_token",
                need_callback=False,
                return_ks3_url=True,
                callback_url="http://test-callback.com/api"
            )
            
            # Verify upload is called but no callback
            mock_upload.assert_called_once_with(mock_result, "test_step", "test_token")
            mock_get_host_uri.assert_not_called()
            mock_rpc_client.assert_not_called()

    @pytest.mark.asyncio
    async def test_callback_parse_background_with_none_result(self):
        """Test callback with None result (failure case)"""
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host_uri, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = None
            mock_get_host_uri.return_value = ("test-host", "test-uri")
            mock_rpc_instance = MagicMock()
            mock_rpc_instance.send_callback = AsyncMock()
            mock_rpc_client.return_value = mock_rpc_instance
            
            # Execute
            await callback_parse_background(
                result=None,
                step_name="test_step", 
                token="test_token",
                need_callback=True,
                return_ks3_url=True,
                callback_url="http://test-callback.com/api"
            )
            
            # Verify status is set to fail
            call_args = mock_rpc_instance.send_callback.call_args
            assert call_args[1]['data'].status == GeneralParseStatus.fail

    @pytest.mark.asyncio
    async def test_callback_parse_background_callback_exception(self):
        """Test callback exception handling"""
        mock_result = MagicMock()
        
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host_uri, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = "http://test-ks3-url.com"
            mock_get_host_uri.return_value = ("test-host", "test-uri")
            mock_rpc_instance = MagicMock()
            mock_rpc_instance.send_callback = AsyncMock(side_effect=Exception("Callback failed"))
            mock_rpc_client.return_value = mock_rpc_instance
            
            # Execute and expect exception
            with pytest.raises(Exception, match="Failed to callback result"):
                await callback_parse_background(
                    result=mock_result,
                    step_name="test_step", 
                    token="test_token",
                    need_callback=True,
                    return_ks3_url=True,
                    callback_url="http://test-callback.com/api"
                )


class TestUploadResultAndSetStatus:
    """Test upload_result_and_set_status function"""

    def test_upload_result_and_set_status_success(self):
        """Test successful upload and status setting"""
        # Mock result object
        mock_result = MagicMock()
        mock_result.dict.return_value = {"test": "data", "chunks": ["chunk1"]}
        
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.Redis5Dao') as mock_redis_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime:
            
            # Setup mocks
            mock_conf_store.back_store_dir = "test_store_dir"
            mock_datetime.now.return_value.strftime.return_value = "20250817"
            
            mock_store_instance = MagicMock()
            mock_store_instance.upload_from_bytes.return_value = True
            mock_store_instance.generate_url.return_value = "http://generated-url.com"
            mock_store_dao.return_value = mock_store_instance
            
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            # Execute
            result = upload_result_and_set_status(mock_result, "test_step", "test_token")
            
            # Verify
            assert result == "http://generated-url.com"
            mock_result.dict.assert_called_once()
            
            expected_path = "test_store_dir/20250817/parse_background_content_test_step_test_token"
            expected_data = json.dumps({"test": "data", "chunks": ["chunk1"]}).encode("utf-8")
            
            mock_store_instance.upload_from_bytes.assert_called_once_with(expected_path, expected_data)
            mock_store_instance.generate_url.assert_called_once_with(expected_path, 1492073594)
            mock_redis_instance.set.assert_called_once()

    def test_upload_result_and_set_status_upload_failure(self):
        """Test upload failure exception"""
        mock_result = MagicMock()
        mock_result.dict.return_value = {"test": "data"}
        
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime:
            
            mock_conf_store.back_store_dir = "test_store_dir"
            mock_datetime.now.return_value.strftime.return_value = "20250817"
            
            mock_store_instance = MagicMock()
            mock_store_instance.upload_from_bytes.return_value = False  # Upload fails
            mock_store_dao.return_value = mock_store_instance
            
            # Execute and expect exception
            with pytest.raises(Exception, match="Failed to upload parsing result to KS3"):
                upload_result_and_set_status(mock_result, "test_step", "test_token")

    def test_upload_result_and_set_status_with_none_result(self):
        """Test handling None result"""
        with patch('modules.flows.callback.Redis5Dao') as mock_redis_dao:
            
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            # Execute
            result = upload_result_and_set_status(None, "test_step", "test_token")
            
            # Verify
            assert result is None
            mock_redis_instance.set.assert_called_once_with(
                "parse_background_status_test_token", 
                GeneralParseStatus.fail, 
                ex=604800  # DAY_7
            )

    def test_upload_result_and_set_status_redis_operations(self):
        """Test Redis operations in detail"""
        mock_result = MagicMock()
        mock_result.dict.return_value = {"content": "test"}
        
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.Redis5Dao') as mock_redis_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime:
            
            # Setup mocks
            mock_conf_store.back_store_dir = "test_dir"
            mock_datetime.now.return_value.strftime.return_value = "20250817"
            
            mock_store_instance = MagicMock()
            mock_store_instance.upload_from_bytes.return_value = True
            mock_store_instance.generate_url.return_value = "http://test-url.com"
            mock_store_dao.return_value = mock_store_instance
            
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            # Execute
            upload_result_and_set_status(mock_result, "my_step", "my_token")
            
            # Verify Redis set call
            mock_redis_instance.set.assert_called_once_with(
                "parse_background_content_path_my_step_my_token",
                "http://test-url.com",
                ex=604800  # DAY_7
            )

    def test_upload_result_and_set_status_datetime_formatting(self):
        """Test datetime formatting for KS3 path"""
        mock_result = MagicMock()
        mock_result.dict.return_value = {"test": "data"}
        
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.Redis5Dao') as mock_redis_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime:
            
            # Mock datetime to return specific format
            mock_now = MagicMock()
            mock_now.strftime.return_value = "20250101"
            mock_datetime.now.return_value = mock_now
            
            mock_conf_store.back_store_dir = "store"
            mock_store_instance = MagicMock()
            mock_store_instance.upload_from_bytes.return_value = True
            mock_store_instance.generate_url.return_value = "http://url.com"
            mock_store_dao.return_value = mock_store_instance
            mock_redis_dao.return_value = MagicMock()
            
            # Execute
            upload_result_and_set_status(mock_result, "step", "token")
            
            # Verify datetime format call
            mock_now.strftime.assert_called_once_with('%Y%m%d')
            
            # Verify path construction
            expected_path = "store/20250101/parse_background_content_step_token"
            mock_store_instance.upload_from_bytes.assert_called_once()
            call_args = mock_store_instance.upload_from_bytes.call_args[0]
            assert call_args[0] == expected_path

    def test_upload_result_and_set_status_json_serialization(self):
        """Test JSON serialization of result"""
        mock_result = MagicMock()
        test_data = {
            "chunks": ["chunk1", "chunk2"],
            "summary": "test summary",
            "keywords": ["keyword1", "keyword2"]
        }
        mock_result.dict.return_value = test_data
        
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.Redis5Dao') as mock_redis_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime:
            
            mock_conf_store.back_store_dir = "test_dir"
            mock_datetime.now.return_value.strftime.return_value = "20250817"
            
            mock_store_instance = MagicMock()
            mock_store_instance.upload_from_bytes.return_value = True
            mock_store_instance.generate_url.return_value = "http://test.com"
            mock_store_dao.return_value = mock_store_instance
            mock_redis_dao.return_value = MagicMock()
            
            # Execute
            upload_result_and_set_status(mock_result, "step", "token")
            
            # Verify JSON serialization
            expected_json = json.dumps(test_data).encode("utf-8")
            call_args = mock_store_instance.upload_from_bytes.call_args[0]
            assert call_args[1] == expected_json


class TestCallbackIntegration:
    """Integration tests for callback module"""

    @pytest.mark.asyncio
    async def test_full_callback_flow_success(self):
        """Test full callback flow from start to finish"""
        # Create a realistic result object
        mock_result = MagicMock()
        mock_result.dict.return_value = {
            "chunks": ["chunk1", "chunk2"],
            "summary": "Test summary",
            "keywords": ["test", "keywords"]
        }
        
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.Redis5Dao') as mock_redis_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host_uri, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            # Setup all mocks
            mock_conf_store.back_store_dir = "test_store"
            mock_datetime.now.return_value.strftime.return_value = "20250817"
            
            mock_store_instance = MagicMock()
            mock_store_instance.upload_from_bytes.return_value = True
            mock_store_instance.generate_url.return_value = "http://ks3-result-url.com"
            mock_store_dao.return_value = mock_store_instance
            
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            mock_get_host_uri.return_value = ("callback-host.com", "/callback/endpoint")
            
            mock_rpc_instance = MagicMock()
            mock_rpc_instance.send_callback = AsyncMock()
            mock_rpc_client.return_value = mock_rpc_instance
            
            # Execute full flow
            await callback_parse_background(
                result=mock_result,
                step_name="integration_test_step",
                token="integration_test_token",
                need_callback=True,
                return_ks3_url=True,
                callback_url="http://callback-host.com/callback/endpoint"
            )
            
            # Verify upload happened
            mock_store_instance.upload_from_bytes.assert_called_once()
            mock_store_instance.generate_url.assert_called_once()
            
            # Verify Redis operation
            mock_redis_instance.set.assert_called_once()
            
            # Verify callback
            mock_get_host_uri.assert_called_once_with("http://callback-host.com/callback/endpoint")
            mock_rpc_instance.send_callback.assert_called_once()
            
            # Verify callback data
            callback_data = mock_rpc_instance.send_callback.call_args[1]['data']
            assert callback_data.status == GeneralParseStatus.ok
            assert callback_data.token == "integration_test_token"
            assert callback_data.res_ks3_url == "http://ks3-result-url.com"
            assert callback_data.parse_res is None