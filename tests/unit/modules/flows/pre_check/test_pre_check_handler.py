# Author: linqi
# Date: 2025/8/16
# Time: 16:17

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
import time

from modules.flows.pre_check.pre_check_handler import PreCheckFactory, PreCheckNode
from modules.flows.pre_check.pdf import PDFPre<PERSON>heck
from modules.flows.pre_check.image import Image<PERSON><PERSON><PERSON>heck
from modules.flows.pre_check.pre_check_template import Pre<PERSON><PERSON>ck<PERSON>emplate
from modules.entity.pre_check_entity import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ParserName
from modules.pipeline.context import <PERSON><PERSON>ine<PERSON>ontext, FileType
from modules.pipeline.base import <PERSON><PERSON>ineHandler


@pytest.fixture
def precheck_node():
    """Create a PreCheckNode instance for testing"""
    return PreCheckNode("test_precheck")


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    context.file_info = MagicMock()
    context.file_info.file_type = FileType.PDF
    context.business_log = MagicMock()
    context.handler_results = {}
    return context


class TestPreCheckFactory:
    """Test PreCheckFactory class"""

    def test_precheck_factory_has_processors(self):
        """Test PreCheckFactory has expected processors mapping"""
        processors = PreCheckFactory._precheck_processors
        
        # Should have PDF processor
        assert FileType.PDF in processors
        assert processors[FileType.PDF] == PDFPreCheck
        
        # Should have image processors
        assert FileType.JPG in processors
        assert FileType.JPEG in processors
        assert FileType.PNG in processors
        assert FileType.WEBP in processors
        assert processors[FileType.JPG] == ImagePreCheck
        assert processors[FileType.JPEG] == ImagePreCheck
        assert processors[FileType.PNG] == ImagePreCheck
        assert processors[FileType.WEBP] == ImagePreCheck

    def test_get_precheck_processor_pdf_enabled(self, monkeypatch):
        """Test get_precheck_processor returns PDF processor when enabled"""
        # Mock MultipleParse.enable = True
        mock_multiple_parse = MagicMock()
        mock_multiple_parse.enable = True
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.MultipleParse', mock_multiple_parse)
        
        # Execute
        processor = PreCheckFactory.get_precheck_processor(FileType.PDF)
        
        # Should return PDFPreCheck instance
        assert isinstance(processor, PDFPreCheck)
        assert isinstance(processor, PreCheckTemplate)

    def test_get_precheck_processor_image_enabled(self, monkeypatch):
        """Test get_precheck_processor returns Image processor when enabled"""
        # Mock MultipleParse.enable = True
        mock_multiple_parse = MagicMock()
        mock_multiple_parse.enable = True
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.MultipleParse', mock_multiple_parse)
        
        # Execute
        processor = PreCheckFactory.get_precheck_processor(FileType.JPG)
        
        # Should return ImagePreCheck instance
        assert isinstance(processor, ImagePreCheck)
        assert isinstance(processor, PreCheckTemplate)

    def test_get_precheck_processor_disabled(self, monkeypatch):
        """Test get_precheck_processor returns None when disabled"""
        # Mock MultipleParse.enable = False
        mock_multiple_parse = MagicMock()
        mock_multiple_parse.enable = False
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.MultipleParse', mock_multiple_parse)
        
        # Execute
        processor = PreCheckFactory.get_precheck_processor(FileType.PDF)
        
        # Should return None when disabled
        assert processor is None

    def test_get_precheck_processor_unsupported_type(self, monkeypatch):
        """Test get_precheck_processor returns None for unsupported file type"""
        # Mock MultipleParse.enable = True
        mock_multiple_parse = MagicMock()
        mock_multiple_parse.enable = True
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.MultipleParse', mock_multiple_parse)
        
        # Execute with unsupported file type
        processor = PreCheckFactory.get_precheck_processor(FileType.DOCX)
        
        # Should return None for unsupported types
        assert processor is None

    def test_get_precheck_processor_all_image_types(self, monkeypatch):
        """Test get_precheck_processor works for all supported image types"""
        # Mock MultipleParse.enable = True
        mock_multiple_parse = MagicMock()
        mock_multiple_parse.enable = True
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.MultipleParse', mock_multiple_parse)
        
        image_types = [FileType.JPG, FileType.JPEG, FileType.PNG, FileType.WEBP]
        
        for file_type in image_types:
            processor = PreCheckFactory.get_precheck_processor(file_type)
            assert isinstance(processor, ImagePreCheck)
            assert isinstance(processor, PreCheckTemplate)


class TestPreCheckNode:
    """Test PreCheckNode class"""

    def test_precheck_node_inheritance(self, precheck_node):
        """Test PreCheckNode inherits from PipelineHandler"""
        assert isinstance(precheck_node, PipelineHandler)
        assert isinstance(precheck_node, PreCheckNode)
        assert precheck_node.name == "test_precheck"

    def test_precheck_node_initialization(self):
        """Test PreCheckNode initialization"""
        name = "test_node"
        node = PreCheckNode(name)
        
        assert node.name == name
        assert isinstance(node, PreCheckNode)

    @pytest.mark.asyncio
    async def test_precheck_node_process_success(self, precheck_node, mock_context, monkeypatch):
        """Test PreCheckNode.process successful execution"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock PreCheckFactory
        mock_processor = MagicMock(spec=PreCheckTemplate)
        mock_processor.precheck_process = AsyncMock(return_value=[
            ParserConfig(parser_name=ParserName.KdcParser, is_all=True, processing_pages=set())
        ])
        
        def mock_get_precheck_processor(file_type):
            return mock_processor
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.PreCheckFactory.get_precheck_processor', mock_get_precheck_processor)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.ConfHandlerName', mock_conf_handler_name)
        
        # Execute
        result_context = await precheck_node.process(mock_context)
        
        # Verify results
        assert result_context == mock_context
        assert "pre_check_handler" in mock_context.handler_results
        
        # Verify processor was called
        mock_processor.precheck_process.assert_called_once_with(mock_context)
        
        # Verify business log was called
        mock_context.business_log.info.assert_called_once()

    @pytest.mark.asyncio
    async def test_precheck_node_process_no_processor(self, precheck_node, mock_context, monkeypatch):
        """Test PreCheckNode.process when no processor is found"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock PreCheckFactory to return None
        def mock_get_precheck_processor(file_type):
            return None
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.PreCheckFactory.get_precheck_processor', mock_get_precheck_processor)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.ConfHandlerName', mock_conf_handler_name)
        
        # Execute
        result_context = await precheck_node.process(mock_context)
        
        # Verify results
        assert result_context == mock_context
        assert "pre_check_handler" in mock_context.handler_results
        
        # Should create default KDC config
        config = mock_context.handler_results["pre_check_handler"][0]
        assert isinstance(config, ParserConfig)
        assert config.parser_name == ParserName.KdcParser
        assert config.is_all is True
        assert config.processing_pages == set()
        
        # Verify error was logged
        mock_context.business_log.error.assert_called_once()

    @pytest.mark.asyncio
    async def test_precheck_node_process_exception_handling(self, precheck_node, mock_context, monkeypatch):
        """Test PreCheckNode.process handles exceptions properly"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock PreCheckFactory to raise exception
        def mock_get_precheck_processor(file_type):
            raise Exception("Test exception")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.PreCheckFactory.get_precheck_processor', mock_get_precheck_processor)
        
        # Execute and expect exception to be re-raised
        with pytest.raises(Exception, match="Test exception"):
            await precheck_node.process(mock_context)
        
        # Verify error was logged
        mock_context.business_log.error.assert_called_once()

    @pytest.mark.asyncio
    async def test_precheck_node_process_timing(self, precheck_node, mock_context, monkeypatch):
        """Test PreCheckNode.process includes timing information"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock PreCheckFactory
        mock_processor = MagicMock(spec=PreCheckTemplate)
        
        # Mock async precheck_process to simulate some processing time
        async def mock_precheck_process(context):
            # Simulate some processing time
            await asyncio.sleep(0.01)
            return [ParserConfig(parser_name=ParserName.KdcParser, is_all=True, processing_pages=set())]
        
        mock_processor.precheck_process = mock_precheck_process
        
        def mock_get_precheck_processor(file_type):
            return mock_processor
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Apply mocks
        import asyncio
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.PreCheckFactory.get_precheck_processor', mock_get_precheck_processor)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.ConfHandlerName', mock_conf_handler_name)
        
        # Execute
        result_context = await precheck_node.process(mock_context)
        
        # Verify timing log was called
        mock_context.business_log.info.assert_called_once()
        call_args = mock_context.business_log.info.call_args[0][0]
        assert "PreCheckNode.process took" in call_args
        assert "seconds" in call_args

    @pytest.mark.asyncio
    async def test_precheck_node_process_processor_exception(self, precheck_node, mock_context, monkeypatch):
        """Test PreCheckNode.process when processor raises exception"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock PreCheckFactory
        mock_processor = MagicMock(spec=PreCheckTemplate)
        
        async def mock_precheck_process(context):
            raise Exception("Processor exception")
        
        mock_processor.precheck_process = mock_precheck_process
        
        def mock_get_precheck_processor(file_type):
            return mock_processor
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.PreCheckFactory.get_precheck_processor', mock_get_precheck_processor)
        
        # Execute and expect exception to be re-raised
        with pytest.raises(Exception, match="Processor exception"):
            await precheck_node.process(mock_context)
        
        # Verify error was logged
        mock_context.business_log.error.assert_called_once()

    @pytest.mark.asyncio
    async def test_precheck_node_process_different_file_types(self, precheck_node, monkeypatch):
        """Test PreCheckNode.process with different file types"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Track which file types were requested
        requested_file_types = []
        
        def mock_get_precheck_processor(file_type):
            requested_file_types.append(file_type)
            if file_type == FileType.PDF:
                mock_processor = MagicMock(spec=PDFPreCheck)
            else:
                mock_processor = MagicMock(spec=ImagePreCheck)
            
            mock_processor.precheck_process = AsyncMock(return_value=[
                ParserConfig(parser_name=ParserName.KdcParser, is_all=True, processing_pages=set())
            ])
            return mock_processor
        
        # Mock ConfHandlerName
        mock_conf_handler_name = MagicMock()
        mock_conf_handler_name.pre_check_handler = "pre_check_handler"
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.PreCheckFactory.get_precheck_processor', mock_get_precheck_processor)
        monkeypatch.setattr('modules.flows.pre_check.pre_check_handler.ConfHandlerName', mock_conf_handler_name)
        
        # Test with different file types
        file_types = [FileType.PDF, FileType.JPG, FileType.PNG]
        
        for file_type in file_types:
            context = MagicMock(spec=PipelineContext)
            context.file_info = MagicMock()
            context.file_info.file_type = file_type
            context.business_log = MagicMock()
            context.handler_results = {}
            
            await precheck_node.process(context)
        
        # Verify correct file types were requested
        assert requested_file_types == file_types