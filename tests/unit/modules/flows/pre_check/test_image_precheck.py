# Author: linqi
# Date: 2025/8/16
# Time: 16:17

import pytest
from unittest.mock import MagicMock

from modules.flows.pre_check.image import ImagePreCheck
from modules.flows.pre_check.pre_check_template import PreCheckTemplate
from modules.entity.pre_check_entity import ParserConfig, ParserName
from modules.pipeline.context import Pipeline<PERSON>ontext


@pytest.fixture
def image_precheck():
    """Create an ImagePreCheck instance for testing"""
    return ImagePreCheck()


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    return context


def test_image_precheck_inheritance(image_precheck):
    """Test ImagePreCheck inherits from PreCheckTemplate"""
    assert isinstance(image_precheck, PreCheckTemplate)
    assert isinstance(image_precheck, ImagePreCheck)


@pytest.mark.asyncio
async def test_image_precheck_process_basic(image_precheck, mock_context, monkeypatch):
    """Test ImagePreCheck.precheck_process basic functionality"""
    # Mock trace decorator to avoid tracer warnings
    def mock_async_trace_span(func):
        return func
    
    monkeypatch.setattr('modules.flows.pre_check.image.async_trace_span', mock_async_trace_span)
    
    # Execute
    result = await image_precheck.precheck_process(mock_context)
    
    # Verify result
    assert isinstance(result, list)
    assert len(result) == 1
    
    config = result[0]
    assert isinstance(config, ParserConfig)
    assert config.parser_name == ParserName.ImageParser
    assert config.processing_pages == {0}
    assert config.is_all is True


@pytest.mark.asyncio
async def test_image_precheck_process_return_type(image_precheck, mock_context, monkeypatch):
    """Test ImagePreCheck.precheck_process returns correct type"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    monkeypatch.setattr('modules.flows.pre_check.image.async_trace_span', mock_async_trace_span)
    
    # Execute
    result = await image_precheck.precheck_process(mock_context)
    
    # Verify return type is list of ParserConfig
    assert isinstance(result, list)
    for config in result:
        assert isinstance(config, ParserConfig)


@pytest.mark.asyncio
async def test_image_precheck_process_consistent_result(image_precheck, monkeypatch):
    """Test ImagePreCheck.precheck_process returns consistent results"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    monkeypatch.setattr('modules.flows.pre_check.image.async_trace_span', mock_async_trace_span)
    
    # Create multiple contexts
    context1 = MagicMock(spec=PipelineContext)
    context2 = MagicMock(spec=PipelineContext)
    
    # Execute multiple times
    result1 = await image_precheck.precheck_process(context1)
    result2 = await image_precheck.precheck_process(context2)
    
    # Results should be the same regardless of context
    assert len(result1) == len(result2) == 1
    
    config1 = result1[0]
    config2 = result2[0]
    
    assert config1.parser_name == config2.parser_name == ParserName.ImageParser
    assert config1.processing_pages == config2.processing_pages == {0}
    assert config1.is_all == config2.is_all is True


@pytest.mark.asyncio
async def test_image_precheck_process_context_not_modified(image_precheck, mock_context, monkeypatch):
    """Test ImagePreCheck.precheck_process doesn't modify the context"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    monkeypatch.setattr('modules.flows.pre_check.image.async_trace_span', mock_async_trace_span)
    
    # Store original context state
    original_context = MagicMock(spec=PipelineContext)
    
    # Execute
    result = await image_precheck.precheck_process(original_context)
    
    # Verify context is unchanged (no attributes should have been set/modified)
    # Since we're using a MagicMock, we can check that no unexpected calls were made
    assert isinstance(result, list)
    assert len(result) == 1


def test_image_precheck_instantiation():
    """Test ImagePreCheck can be instantiated"""
    precheck = ImagePreCheck()
    assert isinstance(precheck, ImagePreCheck)
    assert isinstance(precheck, PreCheckTemplate)


@pytest.mark.asyncio
async def test_image_precheck_process_parser_config_attributes(image_precheck, mock_context, monkeypatch):
    """Test that the returned ParserConfig has all expected attributes"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    monkeypatch.setattr('modules.flows.pre_check.image.async_trace_span', mock_async_trace_span)
    
    # Execute
    result = await image_precheck.precheck_process(mock_context)
    config = result[0]
    
    # Verify all ParserConfig attributes
    assert hasattr(config, 'parser_name')
    assert hasattr(config, 'processing_pages')
    assert hasattr(config, 'is_all')
    
    assert config.parser_name == ParserName.ImageParser
    assert isinstance(config.processing_pages, set)
    assert 0 in config.processing_pages
    assert isinstance(config.is_all, bool)
    assert config.is_all is True