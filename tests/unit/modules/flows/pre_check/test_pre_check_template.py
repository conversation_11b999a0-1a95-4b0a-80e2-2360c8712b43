# Author: linqi
# Date: 2025/8/16
# Time: 16:17

import pytest
from abc import ABC

from modules.flows.pre_check.pre_check_template import PreCheckTemplate


def test_pre_check_template_is_abstract():
    """Test that PreCheckTemplate is an abstract base class"""
    assert issubclass(PreCheckTemplate, ABC)
    
    # Should not be able to instantiate directly
    with pytest.raises(TypeError):
        PreCheckTemplate()


def test_pre_check_template_abstract_method():
    """Test that precheck_process is an abstract method"""
    # Create a concrete implementation
    class ConcretePreCheck(PreCheckTemplate):
        async def precheck_process(self, context):
            return "implemented"
    
    # Should be able to instantiate concrete implementation
    concrete = ConcretePreCheck()
    assert isinstance(concrete, PreCheckTemplate)


def test_pre_check_template_missing_implementation():
    """Test that missing implementation raises TypeError"""
    # Create incomplete implementation
    class IncompletePreCheck(PreCheckTemplate):
        pass
    
    # Should not be able to instantiate incomplete implementation
    with pytest.raises(TypeError, match="Can't instantiate abstract class"):
        IncompletePreCheck()


@pytest.mark.asyncio
async def test_concrete_implementation_works():
    """Test that a proper concrete implementation works"""
    class TestPreCheck(PreCheckTemplate):
        async def precheck_process(self, context):
            return f"processed context: {context}"
    
    pre_check = TestPreCheck()
    result = await pre_check.precheck_process("test_context")
    assert result == "processed context: test_context"