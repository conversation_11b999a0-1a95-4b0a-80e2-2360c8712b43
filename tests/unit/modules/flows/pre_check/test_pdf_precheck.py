# Author: linqi
# Date: 2025/8/16
# Time: 16:17

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
import io
from PIL import Image

from modules.flows.pre_check.pdf import (
    PDFPreCheck, 
    extract_and_upload_full_page_images,
)
from modules.flows.pre_check.pre_check_template import Pre<PERSON><PERSON><PERSON>Template
from modules.entity.pre_check_entity import ParserConfig, ParserName
from modules.pipeline.context import PipelineContext, FileInfo, FileType


@pytest.fixture
def pdf_precheck():
    """Create a PDFPreCheck instance for testing"""
    return PDFPreCheck()


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    context.kdc_input = MagicMock()
    context.kdc_input.file_url_or_bytes = "http://test.pdf"
    context.page_count = None
    context.file_info = MagicMock(spec=FileInfo)
    context.file_info.page_size = 10
    context.business_log = MagicMock()
    return context


@pytest.fixture
def mock_pdf_document():
    """Create a mock PDF document"""
    doc = MagicMock()
    doc.page_count = 3
    
    # Mock pages
    pages = []
    for i in range(3):
        page = MagicMock()
        page.number = i
        page.rect = MagicMock()
        page.rect.width = 600
        page.rect.height = 800
        page.rotation = 0
        page.get_text.return_value = f"Page {i} text content"
        page.get_images.return_value = []  # No images by default
        page.find_tables.return_value = MagicMock()
        page.find_tables.return_value.tables = []
        page.parent = doc
        pages.append(page)
    
    doc.load_page = lambda i: pages[i]
    doc.close = MagicMock()
    return doc


def test_pdf_precheck_inheritance(pdf_precheck):
    """Test PDFPreCheck inherits from PreCheckTemplate"""
    assert isinstance(pdf_precheck, PreCheckTemplate)
    assert isinstance(pdf_precheck, PDFPreCheck)


@pytest.mark.asyncio
async def test_extract_and_upload_full_page_images_empty_pdf(mock_pdf_document, monkeypatch):
    """Test extract_and_upload_full_page_images with PDF containing no images"""
    # 使用AsyncMock而不是MagicMock来模拟异步函数
    mock_upload_image = AsyncMock()
    monkeypatch.setattr('modules.flows.pre_check.pdf._upload_image', mock_upload_image)

    # 执行
    result = await extract_and_upload_full_page_images(mock_pdf_document)

    # 应该在没有匹配图像时返回空列表
    assert result == []

    # 上传函数不应被调用
    mock_upload_image.assert_not_called()


@pytest.mark.asyncio
async def test_extract_and_upload_full_page_images_with_matching_images(mock_pdf_document, monkeypatch):
    """Test extract_and_upload_full_page_images with matching page-sized images"""
    # Create mock image data
    mock_image_data = {
        "image": b"fake_image_data"
    }
    
    # Mock PIL Image
    mock_pil_image = MagicMock()
    mock_pil_image.size = (600, 800)  # Matches page size
    mock_pil_image.save = MagicMock()
    
    # Mock page with matching image
    page = mock_pdf_document.load_page(0)
    page.get_images.return_value = [(123, "fake_image")]  # xref, other_data
    page.parent.extract_image.return_value = mock_image_data
    
    # Mock dependencies - AsyncMock for async function that will be called
    mock_upload_image = AsyncMock(return_value="http://test.image.url")
    
    with patch('modules.flows.pre_check.pdf.Image.open', return_value=mock_pil_image):
        with patch('modules.flows.pre_check.pdf.io.BytesIO'):
            with patch('modules.flows.pre_check.pdf.base64.b64encode', return_value=b'encoded_image'):
                monkeypatch.setattr('modules.flows.pre_check.pdf._upload_image', mock_upload_image)
                
                # Execute
                result = await extract_and_upload_full_page_images(mock_pdf_document)
    
    # Should return one image info
    assert len(result) == 1
    assert result[0]["page_num"] == 0
    assert result[0]["image_url"] == "http://test.image.url"
    
    # Upload should have been called once
    mock_upload_image.assert_called_once()


@pytest.mark.asyncio
async def test_extract_and_upload_full_page_images_exception_handling(mock_pdf_document, monkeypatch, caplog):
    """Test extract_and_upload_full_page_images handles exceptions"""
    # Make get_images raise an exception
    page = mock_pdf_document.load_page(0)
    page.get_images.side_effect = Exception("Test exception")
    
    # Mock logger
    import logging
    
    # Execute with logging capture
    with caplog.at_level(logging.ERROR):
        result = await extract_and_upload_full_page_images(mock_pdf_document)
    
    # Should return empty list on exception
    assert result == []
    
    # Should log error
    assert "Error extracting images from PDF" in caplog.text


@pytest.mark.asyncio
async def test_pdf_precheck_process_scan_document(pdf_precheck, mock_context, monkeypatch):
    """Test PDFPreCheck.precheck_process with scan document (all images)"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    async def mock_get_fileinfo(file_url):
        return 3  # 3 pages
    
    async def mock_open_pdf(file_url):
        doc = MagicMock()
        doc.page_count = 3
        doc.close = MagicMock()
        return doc
    
    def mock_find_flag_pages(doc):
        return [0, 1, 2], [], "short text"  # All pages are image-only, very short text
    
    def mock_create_file_info_from_pdf(doc):
        file_info = MagicMock(spec=FileInfo)
        file_info.page_size = 3
        file_info.word_count = 5
        file_info.width = 600
        file_info.height = 800
        file_info.rotate_page = {}
        return file_info
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.pre_check.pdf.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.pre_check.pdf.get_fileinfo', mock_get_fileinfo)
    monkeypatch.setattr('modules.flows.pre_check.pdf.open_pdf', mock_open_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.find_flag_pages', mock_find_flag_pages)
    monkeypatch.setattr('modules.flows.pre_check.pdf.create_file_info_from_pdf', mock_create_file_info_from_pdf)
    
    # Execute
    result = await pdf_precheck.precheck_process(mock_context)
    
    # Should return single KDC config with is_all=True for scan document
    assert len(result) == 1
    assert isinstance(result[0], ParserConfig)
    assert result[0].parser_name == ParserName.KdcParser
    assert result[0].is_all is True
    assert mock_context.file_info.is_scan is True


@pytest.mark.asyncio
async def test_pdf_precheck_process_mixed_content(pdf_precheck, mock_context, monkeypatch):
    """Test PDFPreCheck.precheck_process with mixed content (some text, some images)"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    async def mock_get_fileinfo(file_url):
        return 5  # 5 pages
    
    async def mock_open_pdf(file_url):
        doc = MagicMock()
        doc.page_count = 5
        doc.close = MagicMock()
        return doc
    
    def mock_find_flag_pages(doc):
        return [1, 3], [2], "This is a longer text content with sufficient characters per page"  # Mixed content
    
    def mock_create_file_info_from_pdf(doc):
        file_info = MagicMock(spec=FileInfo)
        file_info.page_size = 5
        file_info.word_count = 100
        file_info.width = 600
        file_info.height = 800
        file_info.rotate_page = {}
        return file_info
    
    async def mock_extract_and_upload_full_page_images(doc):
        return []  # No cover images
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.pre_check.pdf.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.pre_check.pdf.get_fileinfo', mock_get_fileinfo)
    monkeypatch.setattr('modules.flows.pre_check.pdf.open_pdf', mock_open_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.find_flag_pages', mock_find_flag_pages)
    monkeypatch.setattr('modules.flows.pre_check.pdf.create_file_info_from_pdf', mock_create_file_info_from_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.extract_and_upload_full_page_images', mock_extract_and_upload_full_page_images)
    
    # Execute
    result = await pdf_precheck.precheck_process(mock_context)
    
    # Should return both KDC and MuPDF configs
    assert len(result) == 2
    
    # Find KDC and MuPDF configs
    kdc_config = next(config for config in result if config.parser_name == ParserName.KdcParser)
    mupdf_config = next(config for config in result if config.parser_name == ParserName.MuPdfParser)
    
    # KDC should handle image-only pages and table pages
    assert kdc_config.is_all is False
    assert 1 in kdc_config.processing_pages  # Image-only page
    assert 2 in kdc_config.processing_pages  # Table page
    assert 3 in kdc_config.processing_pages  # Image-only page
    
    # MuPDF should handle remaining pages
    assert mupdf_config.is_all is False
    assert 0 in mupdf_config.processing_pages
    assert 4 in mupdf_config.processing_pages


@pytest.mark.asyncio
async def test_pdf_precheck_process_page_limit_exceeded(pdf_precheck, mock_context, monkeypatch):
    """Test PDFPreCheck.precheck_process when page limit is exceeded"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    # Set page limit
    mock_context.page_count = 5
    
    # Mock dependencies
    async def mock_get_fileinfo(file_url):
        return 10  # More than limit
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.pre_check.pdf.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.pre_check.pdf.get_fileinfo', mock_get_fileinfo)
    
    # Execute and expect ValueError
    with pytest.raises(ValueError, match="File page count \\(10\\) exceeds limit \\(5\\)"):
        await pdf_precheck.precheck_process(mock_context)


@pytest.mark.asyncio
async def test_pdf_precheck_process_with_wired_table_classification(pdf_precheck, mock_context, monkeypatch):
    """Test PDFPreCheck.precheck_process with wired table classification"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    async def mock_get_fileinfo(file_url):
        return 3
    
    async def mock_open_pdf(file_url):
        doc = MagicMock()
        doc.page_count = 3
        doc.close = MagicMock()
        return doc
    
    def mock_find_flag_pages(doc):
        return [], [], "Sufficient text content for normal processing"
    
    def mock_create_file_info_from_pdf(doc):
        file_info = MagicMock(spec=FileInfo)
        file_info.page_size = 3
        file_info.word_count = 100
        file_info.width = 600
        file_info.height = 800
        file_info.rotate_page = {}
        return file_info
    
    async def mock_extract_and_upload_full_page_images(doc):
        return [
            {"page_num": 0, "image_url": "http://test.image1.url"},
            {"page_num": 2, "image_url": "http://test.image2.url"}
        ]
    
    # Mock OCR client
    class MockOCRModelClient:
        async def request_table_cls(self, img_url):
            if "image1" in img_url:
                return "wired"  # This should be marked for KDC
            return "normal"  # This should not
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.pre_check.pdf.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.pre_check.pdf.get_fileinfo', mock_get_fileinfo)
    monkeypatch.setattr('modules.flows.pre_check.pdf.open_pdf', mock_open_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.find_flag_pages', mock_find_flag_pages)
    monkeypatch.setattr('modules.flows.pre_check.pdf.create_file_info_from_pdf', mock_create_file_info_from_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.extract_and_upload_full_page_images', mock_extract_and_upload_full_page_images)
    monkeypatch.setattr('modules.flows.pre_check.pdf.OCRModelClient', MockOCRModelClient)
    
    # Execute
    result = await pdf_precheck.precheck_process(mock_context)
    
    # Should return both configs
    assert len(result) == 2
    
    kdc_config = next(config for config in result if config.parser_name == ParserName.KdcParser)
    mupdf_config = next(config for config in result if config.parser_name == ParserName.MuPdfParser)
    
    # Page 0 should be in KDC due to "wired" classification
    assert 0 in kdc_config.processing_pages
    # Page 2 should not be in KDC (normal classification)
    assert 2 not in kdc_config.processing_pages
    
    # MuPDF should handle the other pages
    assert 1 in mupdf_config.processing_pages
    assert 2 in mupdf_config.processing_pages


@pytest.mark.asyncio
async def test_pdf_precheck_process_exception_handling(pdf_precheck, mock_context, monkeypatch):
    """Test PDFPreCheck.precheck_process handles exceptions properly"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    # Mock get_fileinfo to raise exception
    async def mock_get_fileinfo(file_url):
        raise Exception("Test exception")
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.pre_check.pdf.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.pre_check.pdf.get_fileinfo', mock_get_fileinfo)
    
    # Execute and expect exception to be re-raised
    with pytest.raises(Exception, match="Test exception"):
        await pdf_precheck.precheck_process(mock_context)


@pytest.mark.asyncio
async def test_pdf_precheck_process_only_kdc_pages(pdf_precheck, mock_context, monkeypatch):
    """Test PDFPreCheck.precheck_process when only KDC pages exist (not all pages are image-only)"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    async def mock_get_fileinfo(file_url):
        return 3  # 3 pages total
    
    async def mock_open_pdf(file_url):
        doc = MagicMock()
        doc.page_count = 3
        doc.close = MagicMock()
        return doc
    
    def mock_find_flag_pages(doc):
        # Only pages 0 and 2 are image-only (not all pages), and page 1 has tables
        # This way it won't be treated as a scan document
        return [0, 2], [1], "This document has sufficient text content per page to not be considered a scan document with enough characters"
    
    def mock_create_file_info_from_pdf(doc):
        file_info = MagicMock(spec=FileInfo)
        file_info.page_size = 3
        file_info.word_count = 150  # Sufficient text
        file_info.width = 600
        file_info.height = 800
        file_info.rotate_page = {}
        return file_info
    
    async def mock_extract_and_upload_full_page_images(doc):
        return []
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.pre_check.pdf.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.pre_check.pdf.get_fileinfo', mock_get_fileinfo)
    monkeypatch.setattr('modules.flows.pre_check.pdf.open_pdf', mock_open_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.find_flag_pages', mock_find_flag_pages)
    monkeypatch.setattr('modules.flows.pre_check.pdf.create_file_info_from_pdf', mock_create_file_info_from_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.extract_and_upload_full_page_images', mock_extract_and_upload_full_page_images)
    
    # Execute
    result = await pdf_precheck.precheck_process(mock_context)
    
    # Should return only KDC config (since all pages need KDC processing)
    assert len(result) == 1
    assert result[0].parser_name == ParserName.KdcParser
    assert result[0].is_all is False
    assert 0 in result[0].processing_pages  # Image-only page
    assert 1 in result[0].processing_pages  # Table page
    assert 2 in result[0].processing_pages  # Image-only page


@pytest.mark.asyncio
async def test_pdf_precheck_process_only_mupdf_pages(pdf_precheck, mock_context, monkeypatch):
    """Test PDFPreCheck.precheck_process when only MuPDF pages exist"""
    # Mock trace decorator
    def mock_async_trace_span(func):
        return func
    
    # Mock dependencies
    async def mock_get_fileinfo(file_url):
        return 2
    
    async def mock_open_pdf(file_url):
        doc = MagicMock()
        doc.page_count = 2
        doc.close = MagicMock()
        return doc
    
    def mock_find_flag_pages(doc):
        return [], [], "Sufficient text content for all pages"  # No special pages
    
    def mock_create_file_info_from_pdf(doc):
        file_info = MagicMock(spec=FileInfo)
        file_info.page_size = 2
        file_info.word_count = 100
        file_info.width = 600
        file_info.height = 800
        file_info.rotate_page = {}
        return file_info
    
    async def mock_extract_and_upload_full_page_images(doc):
        return []
    
    # Apply mocks
    monkeypatch.setattr('modules.flows.pre_check.pdf.async_trace_span', mock_async_trace_span)
    monkeypatch.setattr('modules.flows.pre_check.pdf.get_fileinfo', mock_get_fileinfo)
    monkeypatch.setattr('modules.flows.pre_check.pdf.open_pdf', mock_open_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.find_flag_pages', mock_find_flag_pages)
    monkeypatch.setattr('modules.flows.pre_check.pdf.create_file_info_from_pdf', mock_create_file_info_from_pdf)
    monkeypatch.setattr('modules.flows.pre_check.pdf.extract_and_upload_full_page_images', mock_extract_and_upload_full_page_images)
    
    # Execute
    result = await pdf_precheck.precheck_process(mock_context)
    
    # Should return only MuPDF config
    assert len(result) == 1
    assert result[0].parser_name == ParserName.MuPdfParser
    assert result[0].is_all is False
    assert 0 in result[0].processing_pages
    assert 1 in result[0].processing_pages