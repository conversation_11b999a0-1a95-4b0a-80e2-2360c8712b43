# Author: linqi
# Date: 2025/8/16
# Time: 16:28

import pytest
from unittest.mock import MagicMock, AsyncMock, patch, mock_open
import base64
import uuid
import copy
from io import BytesIO
from PIL import Image
import requests

from modules.flows.pretreat.image_handler import Image2PDFNode, MAX_IMAGE_PIXELS, URL_EXPIRATION_TIME
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext
from modules.entity.parse_entity import Image as ParseImage, ImageType
from services.datamodel import FileType


@pytest.fixture
def image2pdf_node():
    """Create an Image2PDFNode instance for testing"""
    return Image2PDFNode()


@pytest.fixture
def custom_image2pdf_node():
    """Create an Image2PDFNode instance with custom name"""
    return Image2PDFNode("custom_image_processor")


@pytest.fixture
def mock_context():
    """Create a mock PipelineContext for testing"""
    context = MagicMock(spec=PipelineContext)
    
    # Mock kdc_input
    context.kdc_input = MagicMock()
    context.kdc_input.file_url_or_bytes = "http://test.image.url"
    
    # Mock file_info
    context.file_info = MagicMock()
    context.file_info.file_name = "test_image.jpg"
    context.file_info.file_type = FileType.JPG
    
    return context


@pytest.fixture
def mock_image_bytes():
    """Create mock image bytes"""
    # Create a small test image
    image = Image.new("RGB", (100, 100), color="red")
    buffer = BytesIO()
    image.save(buffer, format="JPEG")
    return buffer.getvalue()


class TestImage2PDFNode:
    """Test Image2PDFNode class"""

    def test_image2pdf_node_inheritance(self, image2pdf_node):
        """Test Image2PDFNode inherits from PipelineHandler"""
        assert isinstance(image2pdf_node, PipelineHandler)
        assert isinstance(image2pdf_node, Image2PDFNode)

    def test_image2pdf_node_default_initialization(self, image2pdf_node):
        """Test Image2PDFNode default initialization"""
        assert image2pdf_node.name == "image_to_pdf_processor"

    def test_image2pdf_node_custom_initialization(self, custom_image2pdf_node):
        """Test Image2PDFNode custom initialization"""
        assert custom_image2pdf_node.name == "custom_image_processor"

    @pytest.mark.asyncio
    async def test_process_success_with_url(self, image2pdf_node, mock_context, mock_image_bytes, monkeypatch):
        """Test Image2PDFNode.process with successful URL processing"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock external dependencies
        async def mock_fetch_file_bytes(file_url_or_bytes):
            return mock_image_bytes
        
        def mock_upload_and_generate_url(file_name, file_bytes):
            return f"http://storage.test/{file_name}"
        
        def mock_to_pdf(image_bytes):
            return "test.pdf", "http://storage.test/temp/test.pdf"
        
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "test_uuid_hex"
            return mock_uuid
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr(image2pdf_node, '_fetch_file_bytes', mock_fetch_file_bytes)
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        monkeypatch.setattr(image2pdf_node, '_to_pdf', mock_to_pdf)
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Execute
        result_context = await image2pdf_node.process(mock_context)
        
        # Verify results
        assert result_context == mock_context
        
        # Verify context updates
        assert len(mock_context.image) == 1
        image_obj = mock_context.image[0]
        assert isinstance(image_obj, ParseImage)
        assert image_obj.page_num == 0
        assert image_obj.url == "http://storage.test/test_uuid_hex_test_image.jpg"
        assert image_obj.image_type == ImageType.INPUT_IMAGE
        
        # Verify raw_file_info is set
        assert mock_context.raw_file_info is not None
        assert mock_context.raw_file_info.page_size == 1
        
        # Verify kdc_input and file_info updates
        assert mock_context.kdc_input.file_url_or_bytes == "http://storage.test/temp/test.pdf"
        assert mock_context.file_info.file_type == FileType.PDF
        assert mock_context.file_info.file_name == "test.pdf"

    @pytest.mark.asyncio
    async def test_process_success_with_bytes(self, image2pdf_node, mock_context, mock_image_bytes, monkeypatch):
        """Test Image2PDFNode.process with bytes input"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Change context to use bytes instead of URL
        mock_context.kdc_input.file_url_or_bytes = mock_image_bytes
        
        # Mock external dependencies
        async def mock_fetch_file_bytes(file_url_or_bytes):
            return file_url_or_bytes  # Return bytes as-is
        
        def mock_upload_and_generate_url(file_name, file_bytes):
            return f"http://storage.test/{file_name}"
        
        def mock_to_pdf(image_bytes):
            return "converted.pdf", "http://storage.test/temp/converted.pdf"
        
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "bytes_test_uuid"
            return mock_uuid
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr(image2pdf_node, '_fetch_file_bytes', mock_fetch_file_bytes)
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        monkeypatch.setattr(image2pdf_node, '_to_pdf', mock_to_pdf)
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Execute
        result_context = await image2pdf_node.process(mock_context)
        
        # Verify results
        assert result_context == mock_context
        assert mock_context.file_info.file_name == "converted.pdf"

    @pytest.mark.asyncio
    async def test_process_request_exception(self, image2pdf_node, mock_context, monkeypatch, caplog):
        """Test Image2PDFNode.process handles RequestException"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock fetch_file_bytes to raise RequestException
        async def mock_fetch_file_bytes(file_url_or_bytes):
            raise requests.exceptions.RequestException("Network error")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr(image2pdf_node, '_fetch_file_bytes', mock_fetch_file_bytes)
        
        # Execute and expect exception
        import logging
        with caplog.at_level(logging.ERROR):
            with pytest.raises(requests.exceptions.RequestException):
                await image2pdf_node.process(mock_context)
        
        # Verify error was logged
        assert "Failed to fetch file" in caplog.text

    @pytest.mark.asyncio
    async def test_process_general_exception(self, image2pdf_node, mock_context, monkeypatch, caplog):
        """Test Image2PDFNode.process handles general exceptions"""
        # Mock trace decorator
        def mock_async_trace_span(func):
            return func
        
        # Mock fetch_file_bytes to raise general exception
        async def mock_fetch_file_bytes(file_url_or_bytes):
            raise Exception("General error")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.async_trace_span', mock_async_trace_span)
        monkeypatch.setattr(image2pdf_node, '_fetch_file_bytes', mock_fetch_file_bytes)
        
        # Execute and expect exception
        import logging
        with caplog.at_level(logging.ERROR):
            with pytest.raises(Exception):
                await image2pdf_node.process(mock_context)
        
        # Verify error was logged
        assert "Image2PDFNode process failed" in caplog.text

    @pytest.mark.asyncio
    async def test_fetch_file_bytes_from_url(self, image2pdf_node, mock_image_bytes, monkeypatch):
        """Test _fetch_file_bytes with URL input"""
        # Mock requests.get
        mock_response = MagicMock()
        mock_response.content = mock_image_bytes
        mock_response.raise_for_status = MagicMock()
        
        mock_requests_get = MagicMock(return_value=mock_response)
        monkeypatch.setattr('modules.flows.pretreat.image_handler.requests.get', mock_requests_get)
        
        # Execute
        result = await image2pdf_node._fetch_file_bytes("http://test.image.url")
        
        # Verify
        assert result == mock_image_bytes
        mock_requests_get.assert_called_once_with("http://test.image.url")
        mock_response.raise_for_status.assert_called_once()

    @pytest.mark.asyncio
    async def test_fetch_file_bytes_from_bytes(self, image2pdf_node, mock_image_bytes):
        """Test _fetch_file_bytes with bytes input"""
        # Execute
        result = await image2pdf_node._fetch_file_bytes(mock_image_bytes)
        
        # Verify - should return bytes as-is
        assert result == mock_image_bytes

    @pytest.mark.asyncio
    async def test_fetch_file_bytes_http_error(self, image2pdf_node, monkeypatch):
        """Test _fetch_file_bytes handles HTTP errors"""
        # Mock requests.get to raise HTTP error
        def mock_requests_get(url):
            response = MagicMock()
            response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Not Found")
            return response
        
        monkeypatch.setattr('modules.flows.pretreat.image_handler.requests.get', mock_requests_get)
        
        # Execute and expect exception
        with pytest.raises(requests.exceptions.HTTPError):
            await image2pdf_node._fetch_file_bytes("http://test.invalid.url")

    def test_upload_and_generate_url_success(self, image2pdf_node, mock_image_bytes, monkeypatch):
        """Test _upload_and_generate_url successful upload"""
        # Mock StoreDao
        mock_store_dao = MagicMock()
        mock_store_dao.upload_from_bytes.return_value = True
        mock_store_dao.generate_url.return_value = "http://storage-internal.test/path/file.jpg"
        
        mock_store_dao_class = MagicMock(return_value=mock_store_dao)
        monkeypatch.setattr('modules.flows.pretreat.image_handler.StoreDao', mock_store_dao_class)
        
        # Execute
        result = image2pdf_node._upload_and_generate_url("test_file.jpg", mock_image_bytes)
        
        # Verify
        assert result == "http://storage.test/path/file.jpg"  # -internal removed
        mock_store_dao.upload_from_bytes.assert_called_once_with("aidocs_dst_server/test_file.jpg", mock_image_bytes)
        mock_store_dao.generate_url.assert_called_once_with("aidocs_dst_server/test_file.jpg", URL_EXPIRATION_TIME)

    def test_upload_and_generate_url_upload_failure(self, image2pdf_node, mock_image_bytes, monkeypatch):
        """Test _upload_and_generate_url when upload fails"""
        # Mock StoreDao
        mock_store_dao = MagicMock()
        mock_store_dao.upload_from_bytes.return_value = False
        
        mock_store_dao_class = MagicMock(return_value=mock_store_dao)
        monkeypatch.setattr('modules.flows.pretreat.image_handler.StoreDao', mock_store_dao_class)
        
        # Execute
        result = image2pdf_node._upload_and_generate_url("test_file.jpg", mock_image_bytes)
        
        # Verify
        assert result == ""
        mock_store_dao.upload_from_bytes.assert_called_once()

    def test_to_pdf_small_image(self, image2pdf_node, mock_image_bytes, monkeypatch):
        """Test _to_pdf with small image (no scaling needed)"""
        # Create small mock image
        mock_pil_image = MagicMock()
        mock_pil_image.width = 100
        mock_pil_image.height = 100
        mock_pil_image.save = MagicMock()
        
        # Mock PIL Image.open
        mock_image_open = MagicMock(return_value=mock_pil_image)
        monkeypatch.setattr('modules.flows.pretreat.image_handler.Image.open', mock_image_open)
        
        # Mock upload function
        def mock_upload_and_generate_url(file_path, file_bytes):
            return f"http://storage.test/{file_path}"
        
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        
        # Mock uuid
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "pdf_test_uuid"
            return mock_uuid
        
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Execute
        pdf_name, pdf_url = image2pdf_node._to_pdf(mock_image_bytes)
        
        # Verify
        assert pdf_name == "pdf_test_uuid.pdf"
        assert pdf_url == "http://storage.test/temp/pdf_test_uuid.pdf"
        
        # Verify image was not resized (small image)
        mock_pil_image.resize.assert_not_called() if hasattr(mock_pil_image, 'resize') else None
        mock_pil_image.save.assert_called()

    def test_to_pdf_large_image(self, image2pdf_node, mock_image_bytes, monkeypatch):
        """Test _to_pdf with large image (scaling needed)"""
        # Create large mock image
        mock_pil_image = MagicMock()
        mock_pil_image.width = 5000
        mock_pil_image.height = 5000  # Total pixels = 25M > MAX_IMAGE_PIXELS (10M)
        
        # Mock resized image
        mock_resized_image = MagicMock()
        mock_resized_image.save = MagicMock()
        mock_pil_image.resize.return_value = mock_resized_image
        
        # Mock PIL Image.open
        mock_image_open = MagicMock(return_value=mock_pil_image)
        monkeypatch.setattr('modules.flows.pretreat.image_handler.Image.open', mock_image_open)
        
        # Mock upload function
        def mock_upload_and_generate_url(file_path, file_bytes):
            return f"http://storage.test/{file_path}"
        
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        
        # Mock uuid
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "large_pdf_uuid"
            return mock_uuid
        
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Execute
        pdf_name, pdf_url = image2pdf_node._to_pdf(mock_image_bytes)
        
        # Verify
        assert pdf_name == "large_pdf_uuid.pdf"
        assert pdf_url == "http://storage.test/temp/large_pdf_uuid.pdf"
        
        # Verify image was resized
        expected_scale = (MAX_IMAGE_PIXELS / (5000 * 5000)) ** 0.5
        expected_width = int(5000 * expected_scale)
        expected_height = int(5000 * expected_scale)
        mock_pil_image.resize.assert_called_once_with((expected_width, expected_height))
        mock_resized_image.save.assert_called()

    def test_images_to_pdf_url_images(self, image2pdf_node, monkeypatch):
        """Test images_to_pdf with URL images"""
        # Mock image data
        mock_image_bytes = b"fake_image_data"
        mock_response = MagicMock()
        mock_response.content = mock_image_bytes
        mock_response.raise_for_status = MagicMock()
        
        # Mock PIL images
        mock_pil_image1 = MagicMock()
        mock_pil_image1.mode = "RGB"
        mock_pil_image2 = MagicMock()
        mock_pil_image2.mode = "RGBA"
        mock_pil_image2_converted = MagicMock()
        mock_pil_image2.convert.return_value = mock_pil_image2_converted
        
        # Mock PIL Image.open to return different images for different calls
        call_count = 0
        def mock_image_open(buffer):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return mock_pil_image1
            else:
                return mock_pil_image2
        
        # Mock first image save method
        mock_pil_image1.save = MagicMock()
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.requests.get', MagicMock(return_value=mock_response))
        monkeypatch.setattr('modules.flows.pretreat.image_handler.Image.open', mock_image_open)
        
        # Mock upload function
        def mock_upload_and_generate_url(file_path, file_bytes):
            return f"http://storage.test/{file_path}"
        
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        
        # Mock uuid
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "multi_pdf_uuid"
            return mock_uuid
        
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Test data
        image_list = ["http://test1.jpg", "http://test2.jpg"]
        
        # Execute
        pdf_name, pdf_url = image2pdf_node.images_to_pdf(image_list)
        
        # Verify
        assert pdf_name == "multi_pdf_uuid.pdf"
        assert pdf_url == "http://storage.test/temp/multi_pdf_uuid.pdf"
        
        # Verify image conversion to RGB
        mock_pil_image2.convert.assert_called_once_with("RGB")
        
        # Verify PDF save with multiple images
        mock_pil_image1.save.assert_called_once()

    def test_images_to_pdf_base64_images(self, image2pdf_node, monkeypatch):
        """Test images_to_pdf with base64 images"""
        # Create fake base64 image data
        fake_image_bytes = b"fake_image_data"
        fake_base64 = base64.b64encode(fake_image_bytes).decode()
        
        # Mock PIL image
        mock_pil_image = MagicMock()
        mock_pil_image.mode = "RGB"
        mock_pil_image.save = MagicMock()
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.Image.open', MagicMock(return_value=mock_pil_image))
        
        # Mock upload function
        def mock_upload_and_generate_url(file_path, file_bytes):
            return f"http://storage.test/{file_path}"
        
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        
        # Mock uuid
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "b64_pdf_uuid"
            return mock_uuid
        
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Test data
        image_list = [fake_base64]
        
        # Execute
        pdf_name, pdf_url = image2pdf_node.images_to_pdf(image_list)
        
        # Verify
        assert pdf_name == "b64_pdf_uuid.pdf"
        assert pdf_url == "http://storage.test/temp/b64_pdf_uuid.pdf"

    def test_images_to_pdf_bytes_images(self, image2pdf_node, monkeypatch):
        """Test images_to_pdf with raw bytes images"""
        fake_image_bytes = b"fake_image_data"
        
        # Mock PIL image
        mock_pil_image = MagicMock()
        mock_pil_image.mode = "RGB"
        mock_pil_image.save = MagicMock()
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.Image.open', MagicMock(return_value=mock_pil_image))
        
        # Mock upload function
        def mock_upload_and_generate_url(file_path, file_bytes):
            return f"http://storage.test/{file_path}"
        
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        
        # Mock uuid
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "bytes_pdf_uuid"
            return mock_uuid
        
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Test data
        image_list = [fake_image_bytes]
        
        # Execute
        pdf_name, pdf_url = image2pdf_node.images_to_pdf(image_list)
        
        # Verify
        assert pdf_name == "bytes_pdf_uuid.pdf"

    def test_images_to_pdf_unsupported_type(self, image2pdf_node, monkeypatch, capsys):
        """Test images_to_pdf with unsupported data type"""
        # Mock upload function (won't be called)
        def mock_upload_and_generate_url(file_path, file_bytes):
            return f"http://storage.test/{file_path}"
        
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        
        # Test data with unsupported type
        image_list = [123, "not_a_url_or_base64"]  # Integer and invalid string
        
        # Execute and expect exception
        with pytest.raises(ValueError, match="No valid images to convert to PDF"):
            image2pdf_node.images_to_pdf(image_list)
        
        # Verify error messages were printed
        captured = capsys.readouterr()
        assert "Error processing image" in captured.out

    def test_images_to_pdf_empty_list(self, image2pdf_node):
        """Test images_to_pdf with empty image list"""
        # Execute and expect exception
        with pytest.raises(ValueError, match="No valid images to convert to PDF"):
            image2pdf_node.images_to_pdf([])

    def test_images_to_pdf_save_error(self, image2pdf_node, monkeypatch, caplog):
        """Test images_to_pdf when PDF save fails"""
        fake_image_bytes = b"fake_image_data"
        
        # Mock PIL image that fails to save
        mock_pil_image = MagicMock()
        mock_pil_image.mode = "RGB"
        mock_pil_image.save.side_effect = Exception("Save error")
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.Image.open', MagicMock(return_value=mock_pil_image))
        
        # Test data
        image_list = [fake_image_bytes]
        
        # Execute and expect exception
        import logging
        with caplog.at_level(logging.ERROR):
            with pytest.raises(Exception):
                image2pdf_node.images_to_pdf(image_list)
        
        # Verify error was logged
        assert "Error saving images to PDF" in caplog.text

    def test_images_to_pdf_mixed_image_types(self, image2pdf_node, monkeypatch):
        """Test images_to_pdf with mixed image types"""
        # Mock different image responses
        fake_image_bytes = b"fake_image_data"
        fake_base64 = base64.b64encode(fake_image_bytes).decode()
        
        mock_response = MagicMock()
        mock_response.content = fake_image_bytes
        mock_response.raise_for_status = MagicMock()
        
        # Mock PIL images
        mock_pil_image1 = MagicMock()
        mock_pil_image1.mode = "RGB"
        mock_pil_image1.save = MagicMock()
        
        mock_pil_image2 = MagicMock()
        mock_pil_image2.mode = "RGB"
        
        mock_pil_image3 = MagicMock()
        mock_pil_image3.mode = "RGB"
        
        # Mock PIL Image.open to return different images
        call_count = 0
        def mock_image_open(buffer):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return mock_pil_image1
            elif call_count == 2:
                return mock_pil_image2
            else:
                return mock_pil_image3
        
        # Apply mocks
        monkeypatch.setattr('modules.flows.pretreat.image_handler.requests.get', MagicMock(return_value=mock_response))
        monkeypatch.setattr('modules.flows.pretreat.image_handler.Image.open', mock_image_open)
        
        # Mock upload function
        def mock_upload_and_generate_url(file_path, file_bytes):
            return f"http://storage.test/{file_path}"
        
        monkeypatch.setattr(image2pdf_node, '_upload_and_generate_url', mock_upload_and_generate_url)
        
        # Mock uuid
        def mock_uuid4():
            mock_uuid = MagicMock()
            mock_uuid.hex = "mixed_pdf_uuid"
            return mock_uuid
        
        monkeypatch.setattr('modules.flows.pretreat.image_handler.uuid.uuid4', mock_uuid4)
        
        # Test data - mix of URL, base64, and bytes
        image_list = ["http://test.jpg", fake_base64, fake_image_bytes]
        
        # Execute
        pdf_name, pdf_url = image2pdf_node.images_to_pdf(image_list)
        
        # Verify
        assert pdf_name == "mixed_pdf_uuid.pdf"
        assert pdf_url == "http://storage.test/temp/mixed_pdf_uuid.pdf"
        
        # Verify first image save method was called with multiple images
        mock_pil_image1.save.assert_called_once()