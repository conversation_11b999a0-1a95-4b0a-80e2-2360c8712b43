# Author: linqi
# Date: 2025/8/13
# Time: 10:55

import pytest

from modules.flows.chunk.et import ETChunk
from modules.pipeline.context import ChunkInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute


def _dst(id_, parent, level, page, text):
    return DST(id=id_, parent=parent, order=0, dst_type=DSTType.TEXT,
               attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
               content=[text])


def test_et_process_chunks_depth_branch():
    a = _dst("a", "-1", 10, 0, "A")
    b = _dst("b", "a", 10, 0, "B")
    chunks = ETChunk().process_chunks([a, b], page_size=1, chunks_info=ChunkInfo(chunk_size=100))
    assert chunks and hasattr(chunks[0], "page_num")


def test_et_process_chunks_non_depth_branch():
    # depth != 2 path: provide two root children => depth 1
    a = _dst("a", "-1", 10, 0, "A")
    c = _dst("c", "-1", 10, 0, "C")
    chunks = ETChunk().process_chunks([a, c], page_size=1, chunks_info=ChunkInfo(chunk_size=100))
    assert chunks and hasattr(chunks[0], "content")

