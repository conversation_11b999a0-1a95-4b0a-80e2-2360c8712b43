# Author: linqi
# Date: 2025/8/13
# Time: 10:55

import pytest
import modules

from modules.flows.chunk.common import (
    calculate_tree_depth,
    build_dst_tree,
    find_last_semantic_symbol,
    _update_outline,
    _build_text_content,
    _create_mergeable_chunk,
    _add_separator_to_last_chunk,
    _process_last_chunk,
    _find_last_semantic_ending,
    _find_semantic_split_point,
    _handle_title_dst,
    _handle_image_dst,
    _handle_special_dst,
    _handle_table_dst,
    build_chunk,
)
from modules.entity.dst_entity import DST, DSTType, DSTAttribute
from modules.entity.chunk_entity import Chunk, LabelType


def _make_dst(id_: str, parent: str, dst_type: DSTType, level: int, page: int, text: str, mark: str | None = None):
    return DST(
        id=id_,
        parent=parent,
        order=0,
        dst_type=dst_type,
        attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
        content=[text],
        mark=mark,
    )


def test_build_dst_tree_and_depth():
    a = _make_dst("a", "-1", DSTType.TEXT, 10, 0, "A")
    b = _make_dst("b", "a", DSTType.TEXT, 10, 0, "B")
    tree = build_dst_tree([a, b])
    assert calculate_tree_depth(tree) == 2


def test_find_last_semantic_symbol():
    assert find_last_semantic_symbol("你好。") == 2
    details = find_last_semantic_symbol("问号？感叹！", return_details=True)
    assert details["exists"] is True and details["symbol"] in {"？", "！"}
    assert find_last_semantic_symbol("") == -1

    # wrapper returns dict
    info = modules.flows.chunk.common.check_semantic_symbols("文本！")
    assert info["exists"] is True and info["position"] >= 0


def test_update_outline_truncation():
    long = "T" * 60
    first, outline = _update_outline(depth=2, level=1, content=long, first_outline="X", outline="X")
    assert len(outline) <= 50


def test_update_outline_levels():
    first, outline = _update_outline(depth=1, level=1, content="T1", first_outline="", outline="")
    assert first == "T1" and outline == "T1"
    first, outline = _update_outline(depth=2, level=1, content="T2", first_outline=first, outline=outline)
    assert outline.startswith("T1 ") and outline.endswith("T2")


def test_build_text_content_variants():
    out = _build_text_content("", "B", "Title", object())
    assert out == "Title B"
    out = _build_text_content("Title", "Title", "Title", None)
    assert out == "Title"
    out = _build_text_content("X", "Y", "Title", None)
    assert out == "X Y"


def test_create_mergeable_chunk_and_separator():
    d = _make_dst("d", "-1", DSTType.TEXT, 10, 0, "D")
    chunk = _create_mergeable_chunk("txt", [0], ["b"], [d], page_size=1, has_children=False)
    assert isinstance(chunk, Chunk)
    chunks = [chunk]
    _add_separator_to_last_chunk(chunks, has_children=True)
    assert chunks[-1].content.endswith("\n\n ")


def test_find_semantic_split_point_and_split_chunk():
    # last_chunk with two dsts: 'A。' and 'B'
    d1 = _make_dst("d1", "-1", DSTType.TEXT, 10, 0, "A。")
    d2 = _make_dst("d2", "-1", DSTType.TEXT, 10, 0, "B")
    last = Chunk(chunk_id="c", page_size=1, content="A。 B", label=LabelType.TEXT, page_num=[0], block=["d1","d2"], dsts=[d1, d2])
    idx, at_end = _find_semantic_split_point(last)
    assert idx == 0 and at_end is True

    chunks, remain, pages, blocks, dsts = modules.flows.chunk.common._split_chunk_semantically(last, 1, "Title")
    assert len(chunks) == 1 and chunks[0].content == "A。"
    assert remain == "Title B" and pages == [0] and blocks == ["d2"] and [x.id for x in dsts] == ["d2"]


def test_handle_title_dst_creates_new_chunk():
    # last chunk ends with level 10; current dst is title level 1
    last_dst = _make_dst("ld", "-1", DSTType.TEXT, 10, 0, "x")
    last_chunk = Chunk(chunk_id="lc", page_size=1, content="X", label=LabelType.TEXT, page_num=[0], block=["ld"], dsts=[last_dst])
    chunk_to_add, content, pages, blocks, dsts, lvl = _handle_title_dst(
        dst_level=1,
        current_chunk_content="Y",
        outline="T",
        last_chunk=last_chunk,
        current_chunk_pages=[0],
        current_chunk_blocks=["ld"],
        current_chunk_dsts=[last_dst],
        page_size=1,
    )
    assert chunk_to_add is not None
    assert content == "T" and pages == [] and blocks == [] and dsts == [] and lvl == 1


def test_handle_image_and_special_dst():
    # pure image
    img = _make_dst("i", "-1", DSTType.IMAGE, 10, 0, "")
    text, pages, blocks, dsts, cont = _handle_image_dst(img, [], [], [])
    assert text is None and cont is False and pages == [0] and blocks == ["i"] and [d.id for d in dsts] == ["i"]

    # special
    sp = _make_dst("s", "-1", DSTType.OTHER, 10, 1, "S")
    pages, blocks, dsts = _handle_special_dst(sp, [], [], [])
    assert pages == [1] and blocks == ["s"] and [d.id for d in dsts] == ["s"]


def test_handle_table_dst_finalize_current_and_create_table():
    table = _make_dst("t", "-1", DSTType.TABLE, 10, 2, "<table>")
    chunks, content, pages, blocks, dsts = _handle_table_dst(
        table, "<table>", current_chunk_content="C", outline="O", current_chunk_pages=[0], current_chunk_blocks=["x"], current_chunk_dsts=[_make_dst("d","-1",DSTType.TEXT,10,0,"D")], page_size=1
    )
    assert len(chunks) == 2
    assert chunks[-1].label == LabelType.TABLE
    assert content == "O" and pages == [] and blocks == [] and dsts == []


def test_create_mergeable_chunk_none_when_no_dsts():
    assert _create_mergeable_chunk("", [], [], [], 1, False) is None


def test_build_chunk_skips_marked_blocks_and_children_separator():
    from modules.entity.chunk_entity import DSTNode
    from modules.entity.dst_entity import MarkType
    header = _make_dst("h","-1", DSTType.TEXT, 10, 0, "H")
    header.mark = MarkType.HEADER
    child_text = _make_dst("c","-1", DSTType.TEXT, 10, 0, "C")
    root = DSTNode(blocks=[header], children=[DSTNode(blocks=[child_text], children=[])])
    from modules.pipeline.context import ChunkInfo
    chunks, mergeable = build_chunk(root, page_size=1, depth=0, first_outline="", outline="", chunks_info=ChunkInfo(chunk_size=100), last_chunk=None)
    assert mergeable is not None or chunks is not None


def test_process_last_chunk_image_last():
    img = _make_dst("i", "-1", DSTType.IMAGE, 10, 0, "")
    last = Chunk(chunk_id="c", page_size=1, content="abc", label=LabelType.IMAGE, page_num=[0], block=["i"], dsts=[img])
    chunks, content, pages, blocks, dsts = _process_last_chunk(last, block_content_len=500, chunk_size=3, page_size=1, outline="")
    assert chunks and chunks[0].chunk_id == "c"
    assert content == "" and pages == [] and blocks == [] and dsts == []


def test_process_last_chunk_merge_when_small():
    d = _make_dst("d", "-1", DSTType.TEXT, 10, 0, "d")
    last = Chunk(chunk_id="c", page_size=1, content="abc", label=LabelType.TEXT, page_num=[0], block=["d"], dsts=[d])
    chunks, content, pages, blocks, dsts = _process_last_chunk(last, block_content_len=2, chunk_size=10, page_size=1, outline="")
    assert chunks == [] and content == "abc" and pages == [0] and blocks == ["d"] and dsts == [d]

