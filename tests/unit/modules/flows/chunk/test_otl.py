# Author: linqi
# Date: 2025/8/13
# Time: 10:56

import pytest

from modules.flows.chunk.otl import OtlChunk
from modules.pipeline.context import ChunkInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute


def _dst(id_, parent, level, page, text):
    return DST(id=id_, parent=parent, order=0, dst_type=DSTType.TEXT,
               attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
               content=[text])


def test_otl_process_chunks_and_page_split():
    dsts = [_dst("a", "-1", 10, 0, "A" * 600), _dst("b", "a", 10, 0, "B" * 600)]
    chunks = OtlChunk().process_chunks(dsts, page_size=1, chunks_info=ChunkInfo(chunk_size=300))
    assert chunks and chunks[0].page_size >= 1


def test_otl_page_split_large_table_single_page(monkeypatch):
    from modules.entity.chunk_entity import Chunk, LabelType
    oc = OtlChunk()
    # fabricate a large table content > MAX_PAGE_SIZE after tag removal
    table_content = "<table>" + ("x" * 2500) + "</table>"
    table_chunk = Chunk(chunk_id="t", page_size=1, content=table_content, label=LabelType.TABLE, page_num=[0], block=[], dsts=[])
    out = oc.otl_page_split([table_chunk])
    assert out[0].page_num == [0]

def test_otl_page_split_large_chunk_single_page(monkeypatch):
    from modules.entity.chunk_entity import Chunk, LabelType
    oc = OtlChunk()
    content = "x"
    chunk1 = Chunk(chunk_id="t", page_size=1, content=content, label=LabelType.TEXT, page_num=[0], block=[], dsts=[])
    content = "x" * 2500
    chunk2 = Chunk(chunk_id="t", page_size=1, content=content, label=LabelType.TEXT, page_num=[0], block=[], dsts=[])
    out = oc.otl_page_split([chunk1, chunk2])
    assert out[0].page_num == [0]
