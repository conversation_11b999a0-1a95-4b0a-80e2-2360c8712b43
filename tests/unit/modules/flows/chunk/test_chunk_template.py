# Author: linqi
# Date: 2025/8/12
# Time: 16:37

import pytest

from modules.flows.chunk.chunk_template import Chunk<PERSON>emplate
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute
from modules.entity.kdc_enttiy import OutlineLevel


class _DummyChunkTemplate(ChunkTemplate):
    def process_chunks(self, dst_list, page_size, chunks_info):
        return []


def _make_dst(dst_id: str, level: int, content: str) -> DST:
    return DST(
        id=dst_id,
        parent="-1",
        order=0,
        dst_type=DSTType.TEXT,
        attributes=DSTAttribute(level=level, position="p", page=0, hash="h" * 32),
        content=[content],
    )


def _make_chunk(cid: str, label: LabelType, content: str, dsts=None, pages=None, blocks=None) -> Chunk:
    return Chunk(
        chunk_id=cid,
        page_size=1,
        content=content,
        label=label,
        page_num=pages or [0],
        block=blocks or [],
        dsts=dsts,
    )


def test_renumber_chunk_ids():
    tpl = _DummyChunkTemplate()
    chunks = [
        _make_chunk("10", LabelType.TEXT, "a"),
        _make_chunk("20", LabelType.TEXT, "b"),
        _make_chunk("30", LabelType.TEXT, "c"),
    ]
    out = tpl.renumber_chunk_ids(chunks)
    assert [c.chunk_id for c in out] == ["0", "1", "2"]


def test_merge_chunk_merges_when_title_then_text():
    tpl = _DummyChunkTemplate()
    # chunk1 ends with a title (level < 10)
    d1 = _make_dst("d1", OutlineLevel.l1, "A")
    c1 = _make_chunk("1", LabelType.TEXT, "A", dsts=[d1], pages=[0], blocks=["b1"]) 
    # chunk2 starts with text (level == 10)
    d2 = _make_dst("d2", OutlineLevel.l10, "B")
    c2 = _make_chunk("2", LabelType.TEXT, "B", dsts=[d2], pages=[1], blocks=["b2"]) 

    merged = tpl.merge_chunk(chunk_size=100, chunks=[c1, c2])
    assert len(merged) == 1
    m = merged[0]
    # content should append chunk2 dst content
    assert m.content == "A B"
    # page nums merged and sorted
    assert m.page_num == [0, 1]
    # blocks merged and deduped
    assert set(m.block) == {"b1", "b2"}
    # dsts union
    assert {d.id for d in (m.dsts or [])} == {"d1", "d2"}


def test_merge_chunk_does_not_merge_when_next_begins_with_title():
    tpl = _DummyChunkTemplate()
    # chunk1 has no title (only text level 10)
    c1 = _make_chunk("1", LabelType.TEXT, "A", dsts=[_make_dst("t1", OutlineLevel.l10, "A")])
    # chunk2 starts with a title (level < 10)
    c2 = _make_chunk("2", LabelType.TEXT, "B", dsts=[_make_dst("t2", OutlineLevel.l2, "B")])

    out = tpl.merge_chunk(chunk_size=100, chunks=[c1, c2])
    assert len(out) == 2
    assert [c.chunk_id for c in out] == ["1", "2"]


def test_merge_chunk_respects_table_label():
    tpl = _DummyChunkTemplate()
    c1 = _make_chunk("1", LabelType.TABLE, "A", dsts=[_make_dst("t1", OutlineLevel.l1, "A")])
    c2 = _make_chunk("2", LabelType.TEXT, "B", dsts=[_make_dst("t2", OutlineLevel.l10, "B")])
    out = tpl.merge_chunk(chunk_size=100, chunks=[c1, c2])
    assert len(out) == 2


def test_merge_chunk_respects_size_limit():
    tpl = _DummyChunkTemplate()
    c1 = _make_chunk("1", LabelType.TEXT, "AAAA", dsts=[_make_dst("t1", OutlineLevel.l1, "AAAA")])
    c2 = _make_chunk("2", LabelType.TEXT, "BBBB", dsts=[_make_dst("t2", OutlineLevel.l10, "BBBB")])
    # len("AAAA") + len("BBBB") = 8, use chunk_size <= 8 to prevent merge
    out = tpl.merge_chunk(chunk_size=8, chunks=[c1, c2])
    assert len(out) == 2


def test_title_hierarchy_controls_merge():
    tpl = _DummyChunkTemplate()
    # case 1: level2 then level3 -> can merge
    c1 = _make_chunk("1", LabelType.TEXT, "A", dsts=[_make_dst("a", OutlineLevel.l2, "A")])
    c2 = _make_chunk("2", LabelType.TEXT, "B", dsts=[_make_dst("b", OutlineLevel.l3, "B")])
    out1 = tpl.merge_chunk(chunk_size=100, chunks=[c1, c2])
    assert len(out1) == 1

    # case 2: level5 then level2 -> cannot merge
    c3 = _make_chunk("3", LabelType.TEXT, "C", dsts=[_make_dst("c", OutlineLevel.l5, "C")])
    c4 = _make_chunk("4", LabelType.TEXT, "D", dsts=[_make_dst("d", OutlineLevel.l2, "D")])
    out2 = tpl.merge_chunk(chunk_size=100, chunks=[c3, c4])
    assert len(out2) == 2


def test_merge_two_chunks_appends_remaining_content_for_same_dst():
    tpl = _DummyChunkTemplate()
    # same dst id appears in both chunks; only the remaining part should be appended
    dst1 = _make_dst("x", OutlineLevel.l1, "Hello world")
    dst2 = _make_dst("x", OutlineLevel.l1, "world!!!")
    c1 = _make_chunk("1", LabelType.TEXT, "Hello world", dsts=[dst1])
    c2 = _make_chunk("2", LabelType.TEXT, "ignored", dsts=[dst2])

    merged = tpl.merge_chunk(chunk_size=100, chunks=[c1, c2])
    assert len(merged) == 1
    assert merged[0].content == "Hello world !!!"

