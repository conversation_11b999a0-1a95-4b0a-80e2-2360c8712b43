# Author: linqi
# Date: 2025/8/13
# Time: 10:55

import pytest

from modules.flows.chunk.docx import DocxChunk
from modules.pipeline.context import ChunkInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute


def _dst(id_, parent, level, page, text):
    return DST(id=id_, parent=parent, order=0, dst_type=DSTType.TEXT,
               attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
               content=[text])


def test_docx_process_chunks_basic():
    dsts = [_dst("a", "-1", 10, 0, "A"), _dst("b", "a", 10, 0, "B")]
    chunks = DocxChunk().process_chunks(dsts, page_size=1, chunks_info=ChunkInfo(chunk_size=100))
    assert chunks and hasattr(chunks[0], "content")

