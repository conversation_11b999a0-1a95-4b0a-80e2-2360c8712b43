# Author: linqi
# Date: 2025/8/13
# Time: 10:55

import pytest

from modules.flows.chunk.image import ImageChunk
from modules.pipeline.context import ChunkInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute
from modules.entity.chunk_entity import LabelType


def _img(id_, parent, page):
    return DST(id=id_, parent=parent, order=0, dst_type=DSTType.IMAGE,
               attributes=DSTAttribute(level=10, position="p", page=page, hash="h" * 32),
               content=["url", "ocr"])


def test_image_process_chunks_builds_one_chunk():
    dsts = [_img("i1", "-1", 0)]
    chunks = ImageChunk().process_chunks(dsts, page_size=1, chunks_info=ChunkInfo(chunk_size=100))
    assert chunks and chunks[0].label in {LabelType.IMAGE, LabelType.TEXT}

