# Author: linqi
# Date: 2025/8/12
# Time: 18:53

import asyncio
import pytest

from modules.flows.chunk.chunk_handler import (
    _chunk_content_embedding_task,
    ChunkNode,
    ChunkFactory,
)
from modules.pipeline.context import PipelineContext, FileInfo, ChunkInfo, FileType
from modules.entity.chunk_entity import Chunk, LabelType


class DummyOCRModelClient:
    async def request_text_embedding(self, text: str):
        # return a simple numeric vector encoding the length
        return [len(text)]


class FakeMultiCoroutine:
    def __init__(self):
        self.tasks = {}

    def add_task(self, key, coro):
        self.tasks[key] = coro

    async def run_limit(self, limit: int):
        results = {}
        for k, c in self.tasks.items():
            results[k] = await c
        return results

    async def run(self):
        results = {}
        for k, c in self.tasks.items():
            results[k] = await c
        return results


class DummyChunkProcessor:
    def process_chunks(self, dst_list, page_size, chunks_info):
        return [
            Chunk(
                chunk_id="x",
                page_size=page_size or 1,
                content="Hello <b>world</b>",
                label=LabelType.TEXT,
                page_num=[0],
                block=["b1"],
            )
        ]

    def merge_chunk(self, chunk_size, chunks):
        return chunks

    def renumber_chunk_ids(self, chunks):
        for i, c in enumerate(chunks):
            c.chunk_id = str(i)
        return chunks


@pytest.mark.asyncio
async def test_chunk_content_embedding_task_cleans_html_and_sets_embeddings(monkeypatch):
    from modules.flows.chunk import chunk_handler as ch

    # Patch OCR client
    monkeypatch.setattr(ch, "OCRModelClient", lambda: DummyOCRModelClient())

    # prepare chunks: one table with html, one text
    table_chunk = Chunk(
        chunk_id="t1",
        page_size=1,
        content="<table><tr> X </tr></table>",
        label=LabelType.TABLE,
        page_num=[0],
        block=[],
    )
    text_chunk = Chunk(
        chunk_id="t2",
        page_size=1,
        content="abc",
        label=LabelType.TEXT,
        page_num=[0],
        block=[],
    )

    await _chunk_content_embedding_task([table_chunk, text_chunk])

    # After cleaning, "<table><tr> X </tr></table>" -> "X"
    assert table_chunk.content_embedding == [1]
    assert text_chunk.content_embedding == [3]


@pytest.mark.asyncio
async def test_chunk_node_process_happy_path(monkeypatch):
    # Patch MultiCoroutine to synchronous fake
    import modules.flows.chunk.chunk_handler as ch
    monkeypatch.setattr(ch, "MultiCoroutine", FakeMultiCoroutine)
    # Patch OCR client used inside _chunk_task
    monkeypatch.setattr(ch, "OCRModelClient", lambda: DummyOCRModelClient())

    # Patch TableProcessorContext to passthrough
    class DummyTPC:
        def __init__(self, *_args, **_kwargs):
            pass

        def process_chunks(self, chunks):
            return chunks

    monkeypatch.setattr(ch, "TableProcessorContext", DummyTPC)
    monkeypatch.setattr(ch, "TitleProcessor", lambda: None)

    # Patch ChunkFactory to return our dummy processor
    monkeypatch.setattr(ChunkFactory, "get_chunk_processor", staticmethod(lambda _ft: DummyChunkProcessor()))

    # Build context
    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=2),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=True,
    )

    node = ChunkNode("chunk")
    out_ctx = await node.process(ctx)

    # chunks produced and renumbered
    assert out_ctx.chunks and out_ctx.chunks[0].chunk_id == "0"
    # embeddings set by background task
    assert isinstance(out_ctx.chunks[0].content_embedding, list)
    # handler_results populated
    assert node.name in out_ctx.handler_results


def test_chunk_factory_raises_on_unknown_type(monkeypatch):
    # Temporarily clear mapping
    from modules.flows.chunk import chunk_handler as ch
    original = ch.ChunkFactory._chunk_processors.copy()
    try:
        ch.ChunkFactory._chunk_processors.clear()
        with pytest.raises(ValueError):
            ChunkFactory.get_chunk_processor(FileType.PDF)
    finally:
        ch.ChunkFactory._chunk_processors = original


@pytest.mark.asyncio
async def test_chunk_node_process_propagates_exceptions(monkeypatch):
    import modules.flows.chunk.chunk_handler as ch

    class BadProcessor(DummyChunkProcessor):
        def process_chunks(self, *_args, **_kwargs):
            raise RuntimeError("boom")

    # Patch dependencies to minimal
    monkeypatch.setattr(ch, "MultiCoroutine", FakeMultiCoroutine)
    monkeypatch.setattr(ch, "OCRModelClient", lambda: DummyOCRModelClient())
    monkeypatch.setattr(ChunkFactory, "get_chunk_processor", staticmethod(lambda _ft: BadProcessor()))
    monkeypatch.setattr(ch, "TableProcessorContext", lambda *a, **k: None)
    monkeypatch.setattr(ch, "TitleProcessor", lambda: None)

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=1),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=False,
    )

    node = ChunkNode("chunk")
    with pytest.raises(RuntimeError):
        await node.process(ctx)
