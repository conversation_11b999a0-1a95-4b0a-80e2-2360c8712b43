# Author: linqi
# Date: 2025/8/13
# Time: 10:56

import pytest

from modules.flows.chunk.pdf import merge_table_chunks, PDFChunk
from modules.entity.chunk_entity import Chunk, LabelType
from modules.pipeline.context import ChunkInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute


def _table_chunk(cid: str, page: int) -> Chunk:
    return Chunk(chunk_id=cid, page_size=1, content="<tr>x</tr>", label=LabelType.TABLE, page_num=[page], block=[], dsts=[])


def test_merge_table_chunks_sets_links():
    a = _table_chunk("a", 1)
    b = _table_chunk("b", 2)
    t = Chunk(chunk_id="t", page_size=1, content="txt", label=LabelType.TEXT, page_num=[3], block=[], dsts=[])
    out = merge_table_chunks([a, b, t])
    assert out[0].next_chunk == "b" and out[1].pre_chunk == "a"


def _dst(id_, parent, level, page, text):
    return DST(id=id_, parent=parent, order=0, dst_type=DSTType.TEXT,
               attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
               content=[text])


def test_pdf_process_chunks_monkeypatched(monkeypatch):
    import modules.flows.chunk.pdf as pdf_mod

    monkeypatch.setattr(pdf_mod, "cross_table_merge", lambda chunks: chunks)
    monkeypatch.setattr(pdf_mod, "merge_header_footer_into_chunk", lambda dsts, page_size: None)

    dsts = [_dst("a", "-1", 10, 0, "A"), _dst("b", "a", 10, 0, "B")]
    chunks = PDFChunk().process_chunks(dsts, page_size=1, chunks_info=ChunkInfo(chunk_size=100))
    assert chunks and hasattr(chunks[0], "content")

