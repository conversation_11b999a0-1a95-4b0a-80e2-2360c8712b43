# Author: linqi
# Date: 2025/8/14

import pytest
from abc import ABC

from modules.flows.dst_enhance.dst_enhance_template import DstEnhanceTemplate
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType


def _create_test_dst():
    """Create a test DST object"""
    return DST(
        id="test",
        parent="root",
        order=0,
        dst_type=DSTType.TEXT,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=1, y2=1)),
            page=0,
            hash="h" * 32
        ),
        content=["test content"]
    )


def test_dst_enhance_template_is_abstract():
    """Test that DstEnhanceTemplate is an abstract base class"""
    assert issubclass(DstEnhanceTemplate, ABC)
    
    # Cannot instantiate abstract class
    with pytest.raises(TypeError):
        DstEnhanceTemplate()


def test_dst_enhance_template_abstract_method():
    """Test that process_dst is an abstract method"""
    # Create a concrete implementation without the abstract method
    class IncompleteImplementation(DstEnhanceTemplate):
        pass
    
    # Should not be able to instantiate
    with pytest.raises(TypeError):
        IncompleteImplementation()


@pytest.mark.asyncio
async def test_dst_enhance_template_concrete_implementation():
    """Test a concrete implementation of DstEnhanceTemplate"""
    class ConcreteImplementation(DstEnhanceTemplate):
        async def process_dst(self, context, dst_list, page_size):
            # Simple implementation that returns modified DSTs
            enhanced_dsts = []
            for dst in dst_list:
                enhanced_dst = dst.model_copy()
                enhanced_dst.content = [f"enhanced: {dst.content[0]}"]
                enhanced_dsts.append(enhanced_dst)
            return enhanced_dsts
    
    # Should be able to instantiate
    implementation = ConcreteImplementation()
    assert isinstance(implementation, DstEnhanceTemplate)
    
    # Test the process_dst method
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    dst_list = [_create_test_dst()]
    page_size = 10
    
    result = await implementation.process_dst(ctx, dst_list, page_size)
    
    assert len(result) == 1
    assert result[0].content == ["enhanced: test content"]


@pytest.mark.asyncio
async def test_dst_enhance_template_method_signature():
    """Test that the abstract method has the correct signature"""
    class TestImplementation(DstEnhanceTemplate):
        async def process_dst(self, context: PipelineContext, dst_list, page_size: int):
            return dst_list
    
    implementation = TestImplementation()
    
    # Test method signature by calling it
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))
    dst_list = [_create_test_dst()]
    page_size = 20
    
    result = await implementation.process_dst(ctx, dst_list, page_size)
    assert result == dst_list


def test_dst_enhance_template_inheritance():
    """Test that concrete classes properly inherit from DstEnhanceTemplate"""
    class WorkingImplementation(DstEnhanceTemplate):
        async def process_dst(self, context, dst_list, page_size):
            return []
    
    implementation = WorkingImplementation()
    
    # Check inheritance
    assert isinstance(implementation, DstEnhanceTemplate)
    assert isinstance(implementation, ABC)
    
    # Check that the method exists
    assert hasattr(implementation, 'process_dst')
    assert callable(getattr(implementation, 'process_dst'))


@pytest.mark.asyncio 
async def test_dst_enhance_template_empty_dst_list():
    """Test concrete implementation with empty DST list"""
    class EmptyListImplementation(DstEnhanceTemplate):
        async def process_dst(self, context, dst_list, page_size):
            if not dst_list:
                return []
            return dst_list
    
    implementation = EmptyListImplementation()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.TXT))
    
    result = await implementation.process_dst(ctx, [], 10)
    assert result == []


@pytest.mark.asyncio
async def test_dst_enhance_template_multiple_dsts():
    """Test concrete implementation with multiple DSTs"""
    class MultiDstImplementation(DstEnhanceTemplate):
        async def process_dst(self, context, dst_list, page_size):
            # Add page number to each DST's content
            enhanced_dsts = []
            for i, dst in enumerate(dst_list):
                enhanced_dst = dst.model_copy()
                enhanced_dst.content = [f"Page {i+1}: {dst.content[0]}"]
                enhanced_dsts.append(enhanced_dst)
            return enhanced_dsts
    
    implementation = MultiDstImplementation()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    
    # Create multiple DSTs
    dst_list = []
    for i in range(3):
        dst = _create_test_dst()
        dst.id = f"test_{i}"
        dst.content = [f"content_{i}"]
        dst_list.append(dst)
    
    result = await implementation.process_dst(ctx, dst_list, 15)
    
    assert len(result) == 3
    assert result[0].content == ["Page 1: content_0"]
    assert result[1].content == ["Page 2: content_1"]
    assert result[2].content == ["Page 3: content_2"]