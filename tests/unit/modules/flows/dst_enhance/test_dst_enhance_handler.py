# Author: linqi
# Date: 2025/8/14

import pytest
import time
import async<PERSON>
from unittest.mock import AsyncMock

from modules.flows.dst_enhance.dst_enhance_handler import DstEnhanceFactory, DstEnhanceNode
from modules.flows.dst_enhance.common import CommonEnhance
from modules.pipeline.context import <PERSON>pelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from services.datamodel import FileType
from conf import ConfH<PERSON>lerName


def _create_test_dst():
    """Create a test DST object"""
    return DST(
        id="test",
        parent="root",
        order=0,
        dst_type=DSTType.TEXT,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=1, y2=1)),
            page=0,
            hash="h" * 32
        ),
        content=["test content"]
    )


def test_dst_enhance_factory_get_dst_processor():
    """Test DstEnhanceFactory.get_dst_processor for all supported file types"""
    # Test all supported file types
    supported_types = [
        FileType.OTL, FileType.PDF, FileType.DOC, FileType.DOCX, FileType.TXT,
        FileType.PPT, FileType.PPTX, FileType.XLSX, FileType.XLS,
        FileType.JPG, FileType.JPEG, FileType.PNG, FileType.WEBP
    ]
    
    for file_type in supported_types:
        processor = DstEnhanceFactory.get_dst_processor(file_type)
        assert isinstance(processor, CommonEnhance)


def test_dst_enhance_factory_unsupported_file_type():
    """Test DstEnhanceFactory.get_dst_processor with unsupported file type"""
    # Create a mock file type that doesn't exist in the factory
    class UnsupportedFileType:
        pass
    
    unsupported_type = UnsupportedFileType()
    with pytest.raises(ValueError, match="No parser available for file type"):
        DstEnhanceFactory.get_dst_processor(unsupported_type)


def test_dst_enhance_node_init():
    """Test DstEnhanceNode initialization"""
    node = DstEnhanceNode("test_node")
    assert node.name == "test_node"


@pytest.mark.asyncio
async def test_dst_enhance_node_process_success(monkeypatch):
    """Test DstEnhanceNode.process successful execution"""
    # Mock the processor
    mock_processor = AsyncMock()
    mock_dst_list = [_create_test_dst()]
    mock_processor.process_dst.return_value = mock_dst_list
    
    # Mock the factory
    monkeypatch.setattr(
        "modules.flows.dst_enhance.dst_enhance_handler.DstEnhanceFactory.get_dst_processor",
        lambda file_type: mock_processor
    )
    
    # Create context
    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=10),
        dst=[_create_test_dst()]
    )
    ctx.handler_results = {}
    
    # Create and process
    node = DstEnhanceNode("test_node")
    result_ctx = await node.process(ctx)
    
    # Verify results
    assert result_ctx.dst == mock_dst_list
    assert result_ctx.handler_results[ConfHandlerName.dst_handler] == mock_dst_list
    mock_processor.process_dst.assert_called_once()


@pytest.mark.asyncio
async def test_dst_enhance_node_process_exception(monkeypatch):
    """Test DstEnhanceNode.process with exception handling"""
    # Mock the processor to raise exception
    mock_processor = AsyncMock()
    mock_processor.process_dst.side_effect = ValueError("Test error")
    
    # Mock the factory
    monkeypatch.setattr(
        "modules.flows.dst_enhance.dst_enhance_handler.DstEnhanceFactory.get_dst_processor",
        lambda file_type: mock_processor
    )
    
    # Create context
    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=10),
        dst=[_create_test_dst()]
    )
    ctx.handler_results = {}
    
    # Create and process - should raise exception
    node = DstEnhanceNode("test_node")
    with pytest.raises(ValueError, match="Test error"):
        await node.process(ctx)


@pytest.mark.asyncio
async def test_dst_enhance_node_timing_measurement(monkeypatch):
    """Test that timing is properly measured and logged"""
    # Mock the processor with a simple fast process to avoid hanging
    mock_processor = AsyncMock()
    mock_processor.process_dst.return_value = [_create_test_dst()]
    
    # Mock the factory
    monkeypatch.setattr(
        "modules.flows.dst_enhance.dst_enhance_handler.DstEnhanceFactory.get_dst_processor",
        lambda file_type: mock_processor
    )
    
    # Create context
    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=10),
        dst=[_create_test_dst()]
    )
    ctx.handler_results = {}
    
    # Create and process
    node = DstEnhanceNode("test_node")
    start_time = time.time()
    result_ctx = await node.process(ctx)
    end_time = time.time()
    
    # Verify that it executed quickly and returned correct results
    assert end_time - start_time < 1.0  # Should be fast
    assert result_ctx is not None
    mock_processor.process_dst.assert_called_once()


def test_dst_enhance_factory_processors_mapping():
    """Test that all processors are correctly mapped to CommonEnhance"""
    for file_type, processor_class in DstEnhanceFactory._dst_processors.items():
        assert processor_class == CommonEnhance
        assert isinstance(file_type, FileType)


@pytest.mark.asyncio
async def test_dst_enhance_node_process_context_preservation(monkeypatch):
    """Test that context attributes are preserved during processing"""
    # Mock the processor
    mock_processor = AsyncMock()
    enhanced_dst = _create_test_dst()
    enhanced_dst.content = ["enhanced content"]
    mock_processor.process_dst.return_value = [enhanced_dst]
    
    # Mock the factory
    monkeypatch.setattr(
        "modules.flows.dst_enhance.dst_enhance_handler.DstEnhanceFactory.get_dst_processor",
        lambda file_type: mock_processor
    )
    
    # Create context with additional attributes
    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.DOCX, page_size=20),
        dst=[_create_test_dst()]
    )
    ctx.handler_results = {"existing": "data"}
    original_file_info = ctx.file_info
    
    # Create and process
    node = DstEnhanceNode("enhance_test")
    result_ctx = await node.process(ctx)
    
    # Verify context preservation
    assert result_ctx.file_info == original_file_info
    assert result_ctx.handler_results["existing"] == "data"
    assert result_ctx.handler_results[ConfHandlerName.dst_handler] == [enhanced_dst]
    assert result_ctx.dst == [enhanced_dst]