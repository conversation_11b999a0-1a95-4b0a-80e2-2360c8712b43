# Author: linqi
# Date: 2025/8/14

import pytest
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON>ck, patch
from collections import defaultdict

from modules.flows.dst_enhance.common import CommonEnhance
from modules.flows.dst_enhance.checkbox_text_correction import Replacement
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType, file_is_image


def _create_test_dst(dst_id="test", content=None, dst_type=DSTType.TEXT, page=0):
    """Create a test DST object"""
    if content is None:
        content = ["test content"]
    dst = DST(
        id=dst_id,
        parent="root",
        order=0,
        dst_type=dst_type,
        attributes=DSTAttribute(
            level=1,
            position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=1, y2=1)),
            page=page,
            hash="h" * 32
        ),
        content=content
    )
    # Add image_pixel attribute for IMAGE type DSTs
    if dst_type == DSTType.IMAGE:
        dst.image_pixel = [800, 600]  # width, height
    else:
        dst.image_pixel = None
    return dst


def _create_image_dst(dst_id="img", page=0):
    """Create a test image DST object"""
    return _create_test_dst(dst_id=dst_id, dst_type=DSTType.IMAGE, page=page)


def test_common_enhance_init():
    """Test CommonEnhance initialization"""
    enhance = CommonEnhance()
    assert enhance.enhance_task_ocr == "ocr"
    assert enhance.enhance_task_checkbox == "checkbox"


def test_check_dst_only_image_all_images():
    """Test _check_dst_only_image with all image DSTs"""
    enhance = CommonEnhance()
    dst_list = [
        _create_test_dst("root", dst_type=DSTType.ROOT),
        _create_image_dst("img1"),
        _create_image_dst("img2")
    ]
    
    result = enhance._check_dst_only_image(dst_list)
    assert result is True


def test_check_dst_only_image_mixed_types():
    """Test _check_dst_only_image with mixed DST types"""
    enhance = CommonEnhance()
    dst_list = [
        _create_test_dst("root", dst_type=DSTType.ROOT),
        _create_image_dst("img1"),
        _create_test_dst("text1", dst_type=DSTType.TEXT)
    ]
    
    result = enhance._check_dst_only_image(dst_list)
    assert result is False


def test_check_dst_only_image_empty_list():
    """Test _check_dst_only_image with empty list"""
    enhance = CommonEnhance()
    result = enhance._check_dst_only_image([])
    assert result is True  # all() returns True for empty iterable


def test_apply_replacements_basic():
    """Test apply_replacements with basic replacement"""
    enhance = CommonEnhance()
    dst_list = [_create_test_dst("test1", ["This is wrong text"], page=0)]
    
    replacements_by_page = {
        0: [Replacement(wrong="wrong", refined="correct", num=1, page=0)]
    }
    
    with patch.object(enhance, 'correct_checkbox') as mock_correct:
        result = enhance.apply_replacements(dst_list, replacements_by_page)
    
    assert result[0].content[0] == "This is correct text"
    mock_correct.assert_called_once_with(dst_list)


def test_apply_replacements_multiple_replacements():
    """Test apply_replacements with multiple replacements in same content"""
    enhance = CommonEnhance()
    dst_list = [_create_test_dst("test1", ["bad bad text"], page=0)]
    
    replacements_by_page = {
        0: [Replacement(wrong="bad", refined="good", num=2, page=0)]
    }
    
    with patch.object(enhance, 'correct_checkbox') as mock_correct:
        result = enhance.apply_replacements(dst_list, replacements_by_page)
    
    assert result[0].content[0] == "good good text"
    mock_correct.assert_called_once_with(dst_list)


def test_apply_replacements_skip_image_dst():
    """Test apply_replacements skips image DSTs"""
    enhance = CommonEnhance()
    dst_list = [
        _create_test_dst("text1", ["wrong text"], page=0),
        _create_image_dst("img1", page=0)
    ]
    
    replacements_by_page = {
        0: [Replacement(wrong="wrong", refined="correct", num=1, page=0)]
    }
    
    with patch.object(enhance, 'correct_checkbox') as mock_correct:
        result = enhance.apply_replacements(dst_list, replacements_by_page)
    
    # Only text DST should be modified
    assert result[0].content[0] == "correct text"
    assert result[1].dst_type == DSTType.IMAGE  # Image DST unchanged


def test_apply_replacements_different_pages():
    """Test apply_replacements with DSTs on different pages"""
    enhance = CommonEnhance()
    dst_list = [
        _create_test_dst("test1", ["wrong text"], page=0),
        _create_test_dst("test2", ["bad text"], page=1)
    ]
    
    replacements_by_page = {
        0: [Replacement(wrong="wrong", refined="correct", num=1, page=0)],
        1: [Replacement(wrong="bad", refined="good", num=1, page=1)]
    }
    
    with patch.object(enhance, 'correct_checkbox') as mock_correct:
        result = enhance.apply_replacements(dst_list, replacements_by_page)
    
    assert result[0].content[0] == "correct text"
    assert result[1].content[0] == "good text"


def test_correct_checkbox():
    """Test correct_checkbox method"""
    enhance = CommonEnhance()
    dst_list = [
        _create_test_dst("test1", ["☑ checked ☐ unchecked"], dst_type=DSTType.TEXT),
        _create_image_dst("img1")  # Should be skipped
    ]
    
    enhance.correct_checkbox(dst_list)
    
    # Text DST should have spaces added around checkboxes
    assert " ☑" in dst_list[0].content[0]
    assert " □" in dst_list[0].content[0]
    # Image DST should remain unchanged
    assert dst_list[1].dst_type == DSTType.IMAGE


@pytest.mark.asyncio
async def test_process_dst_only_images(monkeypatch):
    """Test process_dst when DST list contains only images"""
    enhance = CommonEnhance()
    
    # Mock get_checkbox_crop
    def mock_get_checkbox_crop(context, file_info, dst_list):
        return []
    monkeypatch.setattr("modules.flows.dst_enhance.common.get_checkbox_crop", mock_get_checkbox_crop)
    
    # Mock configs
    mock_conf_image_filter = Mock()
    mock_conf_image_filter.width = 100
    mock_conf_image_filter.height = 100
    monkeypatch.setattr("conf.ConfImageFilter", mock_conf_image_filter)
    
    mock_conf_use_ocr = Mock()
    mock_conf_use_ocr.use_ocr = "0"  # Disable OCR to avoid complexity
    monkeypatch.setattr("conf.ConfUseOcr", mock_conf_use_ocr)
    
    # Mock file_is_image to return True
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: True)
    
    # Mock MultiCoroutine to avoid hanging
    mock_pool = Mock()
    mock_pool.keys = []  # Empty keys to skip pool execution
    mock_pool.add_task = Mock()
    mock_pool.run = AsyncMock(return_value={})
    monkeypatch.setattr("modules.flows.dst_enhance.common.MultiCoroutine", lambda: mock_pool)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.JPG))
    ctx.need_solve_picture = False  # Disable to avoid OCR complexity
    ctx.raw_file_info = None
    dst_list = [_create_image_dst("img1")]
    
    result = await enhance.process_dst(ctx, dst_list, 10)
    assert result == dst_list


@pytest.mark.asyncio
async def test_process_dst_with_checkbox_enhancement(monkeypatch):
    """Test process_dst with checkbox enhancement enabled"""
    enhance = CommonEnhance()
    
    # Mock get_checkbox_crop to return checkbox data
    def mock_get_checkbox_crop(context, file_info, dst_list):
        from modules.entity.checkbox_entity import CheckBoxCorrect
        mock_checkbox = Mock(spec=CheckBoxCorrect)
        mock_checkbox.page = 0
        return [mock_checkbox]
    monkeypatch.setattr("modules.flows.dst_enhance.common.get_checkbox_crop", mock_get_checkbox_crop)
    
    # Mock dependencies and configs
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: False)
    
    # Mock config classes as Mock objects with required attributes
    mock_conf_image_filter = Mock()
    mock_conf_image_filter.width = 100
    mock_conf_image_filter.height = 100
    monkeypatch.setattr("conf.ConfImageFilter", mock_conf_image_filter)
    
    mock_conf_use_ocr = Mock()
    mock_conf_use_ocr.use_ocr = "0"  # Disable OCR to avoid complexity
    monkeypatch.setattr("conf.ConfUseOcr", mock_conf_use_ocr)
    
    # Mock MultiCoroutine to return checkbox results
    class MockPool:
        def __init__(self):
            self.keys = ["checkbox"]  # Include checkbox key to trigger processing
        
        def add_task(self, name, task):
            # If task is a coroutine, close it to avoid warnings
            if hasattr(task, 'close'):
                task.close()
        
        async def run(self):
            return {"checkbox": {0: [Replacement(wrong="test", refined="enhanced", num=1, page=0)]}}
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.MultiCoroutine", MockPool)
    
    # Mock correct_checkbox_marks (need to mock in the common module where it's imported)
    async def mock_correct_checkbox_marks(context, chunks):
        return {0: [Replacement(wrong="test", refined="enhanced", num=1, page=0)]}
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.correct_checkbox_marks", mock_correct_checkbox_marks)
    
    # Mock apply_replacements
    def mock_apply_replacements(dst_list, replacements):
        for dst in dst_list:
            if dst.dst_type != DSTType.IMAGE:
                dst.content = ["enhanced content"]
        return dst_list
    enhance.apply_replacements = mock_apply_replacements
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    ctx.need_solve_picture = False  # Disable to avoid OCR complexity
    ctx.raw_file_info = None
    dst_list = [_create_test_dst("test1", ["test content"])]
    
    result = await enhance.process_dst(ctx, dst_list, 10)
    assert result[0].content == ["enhanced content"]


@pytest.mark.asyncio
async def test_ocr_dst(monkeypatch):
    """Test ocr_dst method"""
    enhance = CommonEnhance()
    
    # Mock _generate_dsts_from_image_layout
    async def mock_generate_dsts(context, dst_map, ocr_only):
        ocr_dst = _create_test_dst("ocr_result")
        ocr_dst.content = ["http://test.jpg", "OCR result text"]
        return {"img1": [ocr_dst]}
    enhance._generate_dsts_from_image_layout = mock_generate_dsts
    
    # Mock insert_dst_map_into_list
    def mock_insert_dst_map(dst_list, res_map, ocr_only):
        ocr_inserted = _create_test_dst("ocr_inserted")
        ocr_inserted.content = ["http://test.jpg", "OCR inserted text"]
        return dst_list + [ocr_inserted]
    enhance.insert_dst_map_into_list = mock_insert_dst_map
    
    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, word_count=100),
        ocr_only=True
    )
    ctx.need_solve_picture = True
    ctx.raw_file_info = None
    dst_list = [_create_image_dst("img1")]
    dst_map = {"img1": dst_list[0]}
    
    result = await enhance.ocr_dst(ctx, dst_list, dst_map)
    assert len(result) == 2  # Original + inserted


@pytest.mark.asyncio 
async def test_generate_dsts_from_image_layout_empty_map():
    """Test _generate_dsts_from_image_layout with empty dst_map"""
    enhance = CommonEnhance()
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    ctx.need_solve_picture = True
    ctx.raw_file_info = None
    dst_map = {}
    
    result = await enhance._generate_dsts_from_image_layout(ctx, dst_map, ocr_only=True)
    assert result == {}


@pytest.mark.asyncio
async def test_process_dst_no_enhancement_needed(monkeypatch):
    """Test process_dst when no enhancement is needed"""
    enhance = CommonEnhance()
    
    # Mock get_checkbox_crop
    def mock_get_checkbox_crop(context, file_info, dst_list):
        return []
    monkeypatch.setattr("modules.flows.dst_enhance.common.get_checkbox_crop", mock_get_checkbox_crop)
    
    # Mock configs
    mock_conf_image_filter = Mock()
    mock_conf_image_filter.width = 100
    mock_conf_image_filter.height = 100
    monkeypatch.setattr("conf.ConfImageFilter", mock_conf_image_filter)
    
    mock_conf_use_ocr = Mock()
    mock_conf_use_ocr.use_ocr = "0"  # Disable OCR
    monkeypatch.setattr("conf.ConfUseOcr", mock_conf_use_ocr)
    
    # Mock MultiCoroutine to avoid hanging
    mock_pool = Mock()
    mock_pool.keys = []  # Empty keys to skip pool execution
    mock_pool.add_task = Mock()
    mock_pool.run = AsyncMock(return_value={})
    monkeypatch.setattr("modules.flows.dst_enhance.common.MultiCoroutine", lambda: mock_pool)
    
    # Mock to disable all enhancements
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: False)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.TXT))
    ctx.need_solve_picture = False
    ctx.raw_file_info = None
    dst_list = [_create_test_dst("test1")]
    
    result = await enhance.process_dst(ctx, dst_list, 10)
    assert result == dst_list


def test_apply_replacements_no_replacements():
    """Test apply_replacements with no matching replacements"""
    enhance = CommonEnhance()
    dst_list = [_create_test_dst("test1", ["no matches here"], page=0)]
    
    replacements_by_page = {
        0: [Replacement(wrong="nonexistent", refined="replacement", num=0, page=0)]
    }
    
    with patch.object(enhance, 'correct_checkbox') as mock_correct:
        result = enhance.apply_replacements(dst_list, replacements_by_page)
    
    assert result[0].content[0] == "no matches here"  # Unchanged
    mock_correct.assert_called_once_with(dst_list)


def test_apply_replacements_complex_content():
    """Test apply_replacements with multiple content items in DST"""
    enhance = CommonEnhance()
    dst_list = [_create_test_dst("test1", ["first wrong", "second wrong text"], page=0)]
    
    replacements_by_page = {
        0: [Replacement(wrong="wrong", refined="correct", num=2, page=0)]
    }
    
    with patch.object(enhance, 'correct_checkbox') as mock_correct:
        result = enhance.apply_replacements(dst_list, replacements_by_page)
    
    assert result[0].content[0] == "first correct"
    assert result[0].content[1] == "second correct text"


@pytest.mark.asyncio
async def test_process_dst_error_handling(monkeypatch):
    """Test process_dst handles errors gracefully"""
    enhance = CommonEnhance()
    
    # Mock get_checkbox_crop to raise an exception
    def mock_get_checkbox_crop(context, file_info, dst_list):
        raise ValueError("Checkbox crop error")
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.get_checkbox_crop", mock_get_checkbox_crop)
    
    # Mock configs
    mock_conf_image_filter = Mock()
    mock_conf_image_filter.width = 100
    mock_conf_image_filter.height = 100
    monkeypatch.setattr("conf.ConfImageFilter", mock_conf_image_filter)
    
    mock_conf_use_ocr = Mock()
    mock_conf_use_ocr.use_ocr = "0"  # Disable OCR
    monkeypatch.setattr("conf.ConfUseOcr", mock_conf_use_ocr)
    
    # Mock MultiCoroutine to avoid hanging
    mock_pool = Mock()
    mock_pool.keys = []  # Empty keys to skip pool execution
    mock_pool.add_task = Mock()
    mock_pool.run = AsyncMock(return_value={})
    monkeypatch.setattr("modules.flows.dst_enhance.common.MultiCoroutine", lambda: mock_pool)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    ctx.need_solve_picture = False  # Disable to avoid complexity 
    ctx.raw_file_info = None
    dst_list = [_create_test_dst("test1")]
    
    # Should raise exception due to get_checkbox_crop error
    with pytest.raises(ValueError, match="Checkbox crop error"):
        await enhance.process_dst(ctx, dst_list, 10)


@pytest.mark.asyncio
async def test_process_dst_with_ocr_enhancement_non_scan(monkeypatch):
    """Test process_dst with OCR enhancement for non-scan files"""
    enhance = CommonEnhance()
    
    # Mock get_checkbox_crop
    def mock_get_checkbox_crop(context, file_info, dst_list):
        return []
    monkeypatch.setattr("modules.flows.dst_enhance.common.get_checkbox_crop", mock_get_checkbox_crop)
    
    # Mock configs to enable OCR
    mock_conf_image_filter = Mock()
    mock_conf_image_filter.width = 100
    mock_conf_image_filter.height = 100
    monkeypatch.setattr("conf.ConfImageFilter", mock_conf_image_filter)
    
    mock_conf_use_ocr = Mock()
    mock_conf_use_ocr.use_ocr = "1"  # Enable OCR
    monkeypatch.setattr("conf.ConfUseOcr", mock_conf_use_ocr)
    
    # Mock file_is_image
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: False)
    
    # Mock MultiCoroutine to return OCR results
    class MockPool:
        def __init__(self):
            self.keys = ["ocr"]
        
        def add_task(self, name, task):
            # If task is a coroutine, close it to avoid warnings
            if hasattr(task, 'close'):
                task.close()
        
        async def run(self):
            # Create DST with proper OCR content format [image_url, ocr_text]
            ocr_dst = _create_test_dst("ocr_result")
            ocr_dst.content = ["http://test.jpg", "OCR result text"]
            return {"ocr": [ocr_dst]}
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.MultiCoroutine", MockPool)
    
    # Mock ocr_dst method to avoid creating unawaited coroutines
    async def mock_ocr_dst(context, dst_list, dst_map):
        ocr_dst = _create_test_dst("ocr_result")
        ocr_dst.content = ["http://test.jpg", "OCR result text"]
        return [ocr_dst]
    enhance.ocr_dst = mock_ocr_dst
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    ctx.need_solve_picture = True
    ctx.file_info.is_scan = False  # Non-scan file
    ctx.raw_file_info = None
    dst_list = [_create_image_dst("img1")]
    
    result = await enhance.process_dst(ctx, dst_list, 10)
    assert len(result) == 1  # Should return OCR result


@pytest.mark.asyncio
async def test_process_dst_with_ocr_enhancement_image_file(monkeypatch):
    """Test process_dst with OCR enhancement for image files"""
    enhance = CommonEnhance()
    
    # Mock get_checkbox_crop
    def mock_get_checkbox_crop(context, file_info, dst_list):
        return []
    monkeypatch.setattr("modules.flows.dst_enhance.common.get_checkbox_crop", mock_get_checkbox_crop)
    
    # Mock configs to enable OCR
    mock_conf_image_filter = Mock()
    mock_conf_image_filter.width = 100
    mock_conf_image_filter.height = 100
    monkeypatch.setattr("conf.ConfImageFilter", mock_conf_image_filter)
    
    mock_conf_use_ocr = Mock()
    mock_conf_use_ocr.use_ocr = "1"  # Enable OCR
    monkeypatch.setattr("conf.ConfUseOcr", mock_conf_use_ocr)
    
    # Mock file_is_image
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: True)
    
    # Mock MultiCoroutine to return OCR results
    class MockPool:
        def __init__(self):
            self.keys = ["ocr"]
        
        def add_task(self, name, task):
            # If task is a coroutine, close it to avoid warnings
            if hasattr(task, 'close'):
                task.close()
        
        async def run(self):
            # Create DST with proper OCR content format [image_url, ocr_text]
            ocr_dst = _create_test_dst("ocr_result")
            ocr_dst.content = ["http://test.jpg", "OCR result text"]
            return {"ocr": [ocr_dst]}
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.MultiCoroutine", MockPool)
    
    # Mock ocr_dst method to avoid creating unawaited coroutines
    async def mock_ocr_dst(context, dst_list, dst_map):
        ocr_dst = _create_test_dst("ocr_result")
        ocr_dst.content = ["http://test.jpg", "OCR result text"]
        return [ocr_dst]
    enhance.ocr_dst = mock_ocr_dst
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.JPG))
    ctx.need_solve_picture = True
    ctx.file_info.is_scan = True  # Scan file
    ctx.raw_file_info = FileInfo(file_type=FileType.JPG)  # Image file
    dst_list = [_create_image_dst("img1")]
    
    result = await enhance.process_dst(ctx, dst_list, 10)
    assert len(result) == 1  # Should return OCR result


@pytest.mark.asyncio
async def test_generate_dsts_from_image_layout_with_data(monkeypatch):
    """Test _generate_dsts_from_image_layout with actual data"""
    enhance = CommonEnhance()
    
    # Mock conf values
    mock_conf_coroutine = Mock()
    mock_conf_coroutine.image_ocr_limit = 5
    monkeypatch.setattr("conf.ConfCoroutine", mock_conf_coroutine)
    
    # Mock MultiCoroutine
    class MockMultiCoroutine:
        def __init__(self):
            pass
        
        def add_task(self, key, task):
            # If task is a coroutine, close it to avoid warnings
            if hasattr(task, 'close'):
                task.close()
        
        async def run_limit(self, limit):
            # Create DST with proper OCR content format [image_url, ocr_text]
            ocr_dst = _create_test_dst("ocr1")
            ocr_dst.content = ["http://test.jpg", "OCR result text"]
            return {
                "process_dst_img1": [ocr_dst],
                "process_dst_img2": Exception("OCR error")
            }
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.MultiCoroutine", MockMultiCoroutine)
    
    # Mock _process_single_dst method to avoid creating unawaited coroutines
    async def mock_process_single_dst(context, dst, ocr_only):
        ocr_dst = _create_test_dst("ocr_result")
        ocr_dst.content = ["http://test.jpg", "OCR result text"]
        return [ocr_dst]
    enhance._process_single_dst = mock_process_single_dst
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF, word_count=0))
    ctx.need_solve_picture = True
    ctx.raw_file_info = None
    
    dst_map = {
        "img1": _create_image_dst("img1"),
        "img2": _create_image_dst("img2")
    }
    
    result = await enhance._generate_dsts_from_image_layout(ctx, dst_map, ocr_only=True)
    
    # Should have result for img1, but not img2 (due to exception)
    assert "img1" in result
    assert "img2" not in result
    assert len(result["img1"]) == 1


@pytest.mark.asyncio
async def test_process_single_dst_success(monkeypatch):
    """Test _process_single_dst with successful processing"""
    enhance = CommonEnhance()
    
    # Mock file_is_image
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: False)
    
    # Mock MultipleParse config
    mock_multiple_parse = Mock()
    mock_multiple_parse.image_ocr_time_out = 30
    mock_multiple_parse.pic_input_ocr_time_out = 60
    monkeypatch.setattr("conf.MultipleParse", mock_multiple_parse)
    
    # Mock preprocess_ocrflux
    async def mock_preprocess_ocrflux(image_url, prompt, timeout):
        return "OCR result text", True
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.preprocess_ocrflux", mock_preprocess_ocrflux)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    dst = _create_image_dst("img1")
    dst.content = ["http://test.jpg"]
    
    result = await enhance._process_single_dst(ctx, dst, ocr_only=True)
    
    assert len(result) == 1
    assert result[0].content[1] == "OCR result text"


@pytest.mark.asyncio
async def test_process_single_dst_with_retry(monkeypatch):
    """Test _process_single_dst with retry for image files"""
    enhance = CommonEnhance()
    
    # Mock file_is_image to return True (image file)
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: True)
    
    # Mock MultipleParse config
    mock_multiple_parse = Mock()
    mock_multiple_parse.image_ocr_time_out = 30
    mock_multiple_parse.pic_input_ocr_time_out = 60
    monkeypatch.setattr("conf.MultipleParse", mock_multiple_parse)
    
    # Mock preprocess_ocrflux to fail first, succeed second
    call_count = 0
    async def mock_preprocess_ocrflux(image_url, prompt, timeout):
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            return "failed result", False
        else:
            return "retry success", True
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.preprocess_ocrflux", mock_preprocess_ocrflux)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.JPG))
    dst = _create_image_dst("img1")
    dst.content = ["http://test.jpg"]
    
    result = await enhance._process_single_dst(ctx, dst, ocr_only=True)
    
    assert len(result) == 1
    assert result[0].content[1] == "retry success"


@pytest.mark.asyncio
async def test_process_single_dst_invalid_content():
    """Test _process_single_dst with invalid dst content"""
    enhance = CommonEnhance()
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    dst = _create_image_dst("img1")
    dst.content = []  # Invalid empty content
    
    result = await enhance._process_single_dst(ctx, dst, ocr_only=True)
    
    assert len(result) == 0  # Should return empty list


@pytest.mark.asyncio
async def test_process_single_dst_request_exception(monkeypatch):
    """Test _process_single_dst with request exception"""
    enhance = CommonEnhance()
    
    # Mock file_is_image
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: False)
    
    # Mock MultipleParse config
    mock_multiple_parse = Mock()
    mock_multiple_parse.image_ocr_time_out = 30
    mock_multiple_parse.pic_input_ocr_time_out = 60
    monkeypatch.setattr("conf.MultipleParse", mock_multiple_parse)
    
    # Mock preprocess_ocrflux to raise RequestException
    async def mock_preprocess_ocrflux(image_url, prompt, timeout):
        import requests
        raise requests.RequestException("Network error")
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.preprocess_ocrflux", mock_preprocess_ocrflux)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    dst = _create_image_dst("img1")
    dst.content = ["http://test.jpg"]
    
    result = await enhance._process_single_dst(ctx, dst, ocr_only=True)
    
    assert len(result) == 0  # Should return empty list on exception


@pytest.mark.asyncio
async def test_process_single_dst_general_exception(monkeypatch):
    """Test _process_single_dst with general exception"""
    enhance = CommonEnhance()
    
    # Mock file_is_image
    monkeypatch.setattr("modules.flows.dst_enhance.common.file_is_image", lambda ft: False)
    
    # Mock MultipleParse config
    mock_multiple_parse = Mock()
    mock_multiple_parse.image_ocr_time_out = 30
    mock_multiple_parse.pic_input_ocr_time_out = 60
    monkeypatch.setattr("conf.MultipleParse", mock_multiple_parse)
    
    # Mock preprocess_ocrflux to raise general exception
    async def mock_preprocess_ocrflux(image_url, prompt, timeout):
        raise ValueError("Processing error")
    
    monkeypatch.setattr("modules.flows.dst_enhance.common.preprocess_ocrflux", mock_preprocess_ocrflux)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    dst = _create_image_dst("img1")
    dst.content = ["http://test.jpg"]
    
    result = await enhance._process_single_dst(ctx, dst, ocr_only=True)
    
    assert len(result) == 0  # Should return empty list on exception


def test_insert_dst_map_into_list_with_ocr_only():
    """Test insert_dst_map_into_list with ocr_only=True"""
    enhance = CommonEnhance()
    
    original_dst = _create_test_dst("dst1")
    original_dst.order = 0
    
    enhanced_dst = _create_test_dst("enhanced1")
    
    dst_list = [original_dst, _create_test_dst("dst2")]
    dst_map = {"dst1": [enhanced_dst]}
    
    result = enhance.insert_dst_map_into_list(dst_list, dst_map, ocr_only=True)
    
    # With ocr_only=True, original dst should be replaced
    assert len(result) == 2
    assert result[0].id == "enhanced1"  # Enhanced DST
    assert result[1].id == "dst2"  # Unchanged DST
    # Check order reassignment
    assert result[0].order == 0
    assert result[1].order == 1


def test_insert_dst_map_into_list_with_ocr_false():
    """Test insert_dst_map_into_list with ocr_only=False"""
    enhance = CommonEnhance()
    
    original_dst = _create_test_dst("dst1")
    enhanced_dst = _create_test_dst("enhanced1")
    
    dst_list = [original_dst, _create_test_dst("dst2")]
    dst_map = {"dst1": [enhanced_dst]}
    
    result = enhance.insert_dst_map_into_list(dst_list, dst_map, ocr_only=False)
    
    # With ocr_only=False, original dst should be kept alongside enhanced
    assert len(result) == 3
    assert result[0].id == "dst1"  # Original DST kept
    assert result[1].id == "enhanced1"  # Enhanced DST added
    assert result[2].id == "dst2"  # Unchanged DST


def test_insert_dst_map_into_list_no_matching_ids():
    """Test insert_dst_map_into_list with no matching IDs"""
    enhance = CommonEnhance()
    
    dst_list = [_create_test_dst("dst1"), _create_test_dst("dst2")]
    dst_map = {"dst3": [_create_test_dst("enhanced3")]}  # No matching IDs
    
    result = enhance.insert_dst_map_into_list(dst_list, dst_map, ocr_only=True)
    
    # Should return original list unchanged
    assert len(result) == 2
    assert result[0].id == "dst1"
    assert result[1].id == "dst2"