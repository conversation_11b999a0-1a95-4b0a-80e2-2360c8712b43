# Author: linqi
# Date: 2025/8/13
# Time: 12:20

import pytest

from modules.flows.chunk_recall.chunk_recall_handler import ChunkRecallNode
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute
from services.datamodel import ParseTarget, FileType
from conf import ConfHandlerName


def _dst(id_, parent, level, page, text, dst_type=DSTType.TEXT):
    return DST(
        id=id_, parent=parent, order=0, dst_type=dst_type,
        attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
        content=[text] if dst_type != DSTType.IMAGE else ["url", text],
    )


def _chunk(cid: str, text: str, pages, blocks, dsts):
    return Chunk(
        chunk_id=cid, page_size=1, content=text, label=LabelType.TEXT,
        page_num=pages, block=blocks, dsts=dsts,
    )


class DummyRecallOutput:
    def __init__(self, chunks, file_infos):
        self.chunks = chunks
        self.file_infos = file_infos


class DummyChunkInfo:
    def __init__(self, file_id, chunk):
        self.file_id = file_id
        self.chunk = chunk


@pytest.mark.asyncio
async def test_chunk_recall_fills_context_when_no_chunk_target(monkeypatch):
    # Prepare recall output
    img_dst = _dst("img1", "-1", 10, 0, "ocr", DSTType.IMAGE)
    ch = _chunk("0", "text", [0], ["img1"], [img_dst])

    async def _req(_self, r, header):
        return DummyRecallOutput(
            chunks=[DummyChunkInfo(file_id="fid", chunk=ch)],
            file_infos=[FileInfo(file_type=FileType.PDF, page_size=1)],
        )

    from modules.rpc.parse_res_rpc import RecallChunkClient
    monkeypatch.setattr(RecallChunkClient, "request_parse_result", _req)

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.DOCX, file_id="fid"),
        parse_target=[ParseTarget.summary],  # no ParseTarget.chunk
        file_drive_id="drv",
        recall_chunk_header="h",
    )

    out = await ChunkRecallNode("chunk_recall").process(ctx)

    # context updated with recalled chunks and dsts
    assert out.chunks and out.dst and out.file_info.page_size == 1
    # handler_results set
    assert ConfHandlerName.dst_handler in out.handler_results
    assert ConfHandlerName.chunk_handler in out.handler_results


@pytest.mark.asyncio
async def test_chunk_recall_skips_when_has_chunk_target(monkeypatch):
    called = {"n": 0}

    async def _req(_self, r, header):
        called["n"] += 1
        return None

    from modules.rpc.parse_res_rpc import RecallChunkClient
    monkeypatch.setattr(RecallChunkClient, "request_parse_result", _req)

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.DOCX, file_id="fid"),
        parse_target=[ParseTarget.chunk],  # has chunk target -> skip recall
        file_drive_id="drv",
        recall_chunk_header="h",
    )

    out = await ChunkRecallNode("chunk_recall").process(ctx)
    # request not called
    assert called["n"] == 0
    # context unchanged (no chunks assigned)
    assert out.chunks is None


@pytest.mark.asyncio
async def test_chunk_recall_raises_on_none_result(monkeypatch):
    from modules.rpc.parse_res_rpc import RecallChunkClient
    async def _req(_self, r, header):
        return None

    monkeypatch.setattr(RecallChunkClient, "request_parse_result", _req)

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.DOCX, file_id="fid"),
        parse_target=[ParseTarget.summary],
        file_drive_id="drv",
        recall_chunk_header="h",
    )

    with pytest.raises(ValueError):
        await ChunkRecallNode("chunk_recall").process(ctx)

