# Author: linqi
# Date: 2025/8/13

import pytest
import pandas as pd
import io

from modules.flows.dst_builder.xls import XlsParse, generate_html_table, extract_img_positions, num_to_col, row_process, extract_content_for_et
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo


def _create_workbook_doc_with_data():
    """Create workbook doc with sheet data for testing"""
    return {
        "doc": {
            "sheets": [
                {
                    "id": 1,
                    "name": "Sheet1",
                    "used_range": {"col_spans": {"from": 0, "to": 2}, "row_spans": {"from": 0, "to": 1}},
                    "data": [
                        {
                            "index": 0,
                            "spans": {"from": 0, "to": 2},
                            "cells": [
                                {"index": 0, "type": "text", "value": "Header1"},
                                {"index": 1, "type": "text", "value": "Header2"}
                            ]
                        },
                        {
                            "index": 1,
                            "spans": {"from": 0, "to": 2},
                            "cells": [
                                {"index": 0, "type": "text", "value": "Data1"},
                                {"index": 1, "type": "text", "value": "Data2"}
                            ]
                        }
                    ],
                    "merge_cells": []
                }
            ],
            "cell_images": [],
            "shared_strings": []
        }
    }


@pytest.mark.asyncio
async def test_xls_dst_generate(monkeypatch):
    # minimal workbook doc
    doc = {"doc": {"sheets": [], "cell_images": [], "shared_strings": []}}
    async def mock_build_media_map(*a, **k):
        return {}
    monkeypatch.setattr("modules.flows.dst_builder.xls.build_media_map", mock_build_media_map)
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLS))
    out = await XlsParse().dst_generate(ctx, [doc])
    assert 0 in out


def test_generate_html_table_empty_data():
    """Test lines 24-28: generate_html_table with empty data"""
    result = generate_html_table([], [])
    assert result == ""


def test_generate_html_table_with_data_no_spans():
    """Test lines 29-44: generate_html_table with data but no spans"""
    data = [["A1", "B1"], ["A2", "B2"]]
    result = generate_html_table(data, [])
    assert "<table border='1'>" in result
    assert "A1" in result and "B1" in result


def test_generate_html_table_with_spans():
    """Test lines 33-39: generate_html_table with spans"""
    from types import SimpleNamespace
    data = [["A1", "B1"], ["A2", "B2"]]
    spans = [SimpleNamespace(
        row_spans=SimpleNamespace(from_=0, to=0),
        col_spans=SimpleNamespace(from_=0, to=1)
    )]
    result = generate_html_table(data, spans)
    assert "colspan" in result and "rowspan" in result


@pytest.mark.asyncio
async def test_extract_content_for_et_with_sheet_data(monkeypatch):
    """Test lines 78-138: extract_content_for_et with sheet containing data"""
    from modules.entity.kdc_enttiy import WorkBook
    async def mock_build_media_map(*a, **k):
        return {}
    monkeypatch.setattr("modules.flows.dst_builder.xls.build_media_map", mock_build_media_map)
    
    # Create mock WorkBook with sheet data
    class MockCell:
        def __init__(self, index, cell_type, value, image_id=None):
            self.index = index
            self.type = cell_type
            self.value = value
            self.image_id = image_id
    
    class MockRow:
        def __init__(self, index, cells, spans=None):
            self.index = index
            self.cells = cells
            self.spans = spans
    
    class MockSheet:
        def __init__(self, name, data, used_range=None, merge_cells=None):
            self.name = name
            self.data = data
            self.used_range = used_range
            self.merge_cells = merge_cells
    
    class MockWorkBook:
        def __init__(self, sheets, cell_images=None, shared_strings=None):
            self.sheets = sheets
            self.cell_images = cell_images or []
            self.shared_strings = shared_strings or []
    
    # Create workbook with sheet data
    workbook = MockWorkBook([
        MockSheet("TestSheet", [
            MockRow(0, [MockCell(0, "string", "Test")], spans=type('', (), {'to': 1})())
        ], used_range=True)
    ])
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLS))
    result = await extract_content_for_et(ctx, [workbook])
    assert "1/1" in result
    assert result["1/1"]["title"] == "TestSheet"


@pytest.mark.asyncio
async def test_extract_content_for_et_shared_string_type(monkeypatch):
    """Test lines 110-112: extract_content_for_et with shared string cell type"""
    async def mock_build_media_map(*a, **k):
        return {}
    monkeypatch.setattr("modules.flows.dst_builder.xls.build_media_map", mock_build_media_map)
    from modules.entity.kdc_enttiy import CellDataType
    
    # Mock classes
    class MockCell:
        def __init__(self, index, cell_type, value):
            self.index = index
            self.type = cell_type
            self.value = value
            self.image_id = None
    
    class MockRow:
        def __init__(self, index, cells, spans=None):
            self.index = index
            self.cells = cells
            self.spans = spans or type('', (), {'to': 1})()
    
    class MockSheet:
        def __init__(self, name, data, used_range=True):
            self.name = name
            self.data = data
            self.used_range = used_range
            self.merge_cells = []
    
    class MockWorkBook:
        def __init__(self, sheets, shared_strings):
            self.sheets = sheets
            self.cell_images = []
            self.shared_strings = shared_strings
    
    class MockSharedString:
        def __init__(self, items):
            self.items = items
    
    class MockItem:
        def __init__(self, text):
            self.text = text
    
    # Create workbook with shared string
    shared_strings = [MockSharedString([MockItem("SharedText")])]
    workbook = MockWorkBook([
        MockSheet("TestSheet", [
            MockRow(0, [MockCell(0, CellDataType.shared, "0")])
        ])
    ], shared_strings)
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLS))
    result = await extract_content_for_et(ctx, [workbook])
    assert "SharedText" in result["1/1"]["content"]


@pytest.mark.asyncio
async def test_extract_content_for_et_cell_with_image(monkeypatch):
    """Test lines 116-123: extract_content_for_et with cell containing image"""
    # build_media_map returns dict mapping id to url directly (not a list)
    async def mock_build_media_map(*a, **k):
        return {"img1": "http://image.url"}
    monkeypatch.setattr("modules.flows.dst_builder.xls.build_media_map", mock_build_media_map)
    
    # Mock classes (same as above)
    class MockCell:
        def __init__(self, index, cell_type, value, image_id=None):
            self.index = index
            self.type = cell_type
            self.value = value
            self.image_id = image_id
    
    class MockRow:
        def __init__(self, index, cells, spans=None):
            self.index = index
            self.cells = cells
            self.spans = spans or type('', (), {'to': 1})()
    
    class MockSheet:
        def __init__(self, name, data, used_range=True):
            self.name = name
            self.data = data
            self.used_range = used_range
            self.merge_cells = []
    
    class MockWorkBook:
        def __init__(self, sheets):
            self.sheets = sheets
            self.cell_images = []
            self.shared_strings = []
    
    # Create workbook with image cell
    workbook = MockWorkBook([
        MockSheet("TestSheet", [
            MockRow(0, [MockCell(0, "string", "test", image_id="img1")])
        ])
    ])
    
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLS))
    result = await extract_content_for_et(ctx, [workbook])
    assert "<img src='http://image.url'/>" in result["1/1"]["content"]


def test_extract_img_positions():
    """Test lines 143-174: extract_img_positions function"""
    html_content = "<table><tr><td><img src='http://test.jpg'/></td></tr></table>"
    nodes = []
    from modules.common import build_root_dst
    root = build_root_dst()
    
    extract_img_positions(html_content, nodes, 0, root, "Sheet1", 1)
    assert len(nodes) == 1
    assert nodes[0].dst_type == DSTType.IMAGE
    assert nodes[0].content == ["http://test.jpg"]


def test_extract_img_positions_no_table():
    """Test lines 146-147: extract_img_positions with no table"""
    html_content = "<div>No table here</div>"
    nodes = []
    from modules.common import build_root_dst
    root = build_root_dst()
    
    extract_img_positions(html_content, nodes, 0, root, "Sheet1", 1)
    assert len(nodes) == 0  # Should return early


def test_num_to_col():
    """Test lines 179-183: num_to_col function"""
    assert num_to_col(1) == "A"
    assert num_to_col(26) == "Z"
    assert num_to_col(27) == "AA"


def test_row_process(monkeypatch):
    """Test lines 187-229: row_process function"""
    # Mock pandas read_html to return controlled data
    mock_df = pd.DataFrame([["Header1", "Header2"], ["Data1", "Data2"], ["Data3", "Data4"]])
    monkeypatch.setattr("pandas.read_html", lambda x: [mock_df])
    
    nodes = []
    from modules.common import build_root_dst
    root = build_root_dst()
    
    content = "<table><tr><td>Header1</td><td>Header2</td></tr><tr><td>Data1</td><td>Data2</td></tr></table>"
    row_process(content, nodes, 0, root, "Sheet1")
    assert len(nodes) > 0


def test_xls_kdc_validate_branches():
    """Test lines 242-246: _kdc_validate with invalid data"""
    parser = XlsParse()
    # The logic at line 244 has same error as pdf.py: should be "or" not "and"
    # We test line 242 (None) and successful validation
    parser.kdc_data = [None, {"doc": {"sheets": [], "cell_images": [], "shared_strings": []}}]
    result = parser._kdc_validate()
    assert len(result) == 1


@pytest.mark.asyncio
async def test_xls_dst_generate_empty_kdc_list():
    """Test lines 254-255: dst_generate with empty kdc_doc_list"""
    parser = XlsParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLS))
    result = await parser.dst_generate(ctx, [None])  # Will be filtered out
    assert result is None


@pytest.mark.asyncio
async def test_xls_dst_generate_value_error(monkeypatch):
    """Test lines 269-270: dst_generate with ValueError in row_process"""
    async def mock_build_media_map(*a, **k):
        return {}
    monkeypatch.setattr("modules.flows.dst_builder.xls.build_media_map", mock_build_media_map)
    # Mock row_process to raise ValueError
    monkeypatch.setattr("modules.flows.dst_builder.xls.row_process", 
                       lambda *args: (_ for _ in ()).throw(ValueError("Test error")))
    
    parser = XlsParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLS))
    doc = _create_workbook_doc_with_data()
    result = await parser.dst_generate(ctx, [doc])
    # Should still return result dict even with error
    assert 0 in result


def test_xls_dst_reprocess():
    """Test line 275: dst_reprocess method (empty implementation)"""
    parser = XlsParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLS))
    result = parser.dst_reprocess(ctx, [])
    assert result is None


def test_xls_get_res():
    """Test line 278: get_res method (empty implementation)"""
    parser = XlsParse()
    dst = DST(id="test", parent="root", order=0, dst_type=DSTType.TEXT,
              attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
              content=["test"])
    result = parser.get_res(dst)
    assert result is None

