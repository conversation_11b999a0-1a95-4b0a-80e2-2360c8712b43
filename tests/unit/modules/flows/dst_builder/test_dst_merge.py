# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.dst_merge import DSTMergeNode
from modules.flows.dst_builder.dst_handler import ParserFactory
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from services.datamodel import FileType
from conf import ConfHandlerName


def _dst(id_, parent, level, page, text, dst_type=DSTType.TEXT):
    return DST(id=id_, parent=parent, order=0, dst_type=dst_type,
               attributes=DSTAttribute(level=level, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=page, hash="h" * 32),
               content=[text])


@pytest.mark.asyncio
async def test_dst_merge_node(monkeypatch):
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))
    ctx.update_multiple_parse_dsts_unsafe(0, [
        _dst("root","-1", 1, 0, "root", DSTType.ROOT),
        _dst("t","root", 10, 0, "abc"),
        _dst("tbl","root", 10, 0, "<table>x</table>", DSTType.TABLE),
        _dst("img","root", 10, 0, "", DSTType.IMAGE),
        _dst("mind","root", 10, 0, "mind", DSTType.MINDMAP),
    ])

    class DummyParser:
        def dst_reprocess(self, context, dst):
            return dst

    monkeypatch.setattr(ParserFactory, "get_parser", staticmethod(lambda ft: DummyParser()))
    out = await DSTMergeNode("dst_merge").process(ctx)
    assert ConfHandlerName.dst_handler in out.handler_results and out.file_info.word_count >= 3


@pytest.mark.asyncio
async def test_dst_merge_none_reprocess_raises(monkeypatch):
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))
    ctx.update_multiple_parse_dsts_unsafe(0, [_dst("root","-1", 1, 0, "root", DSTType.ROOT)])

    class BadParser:
        def dst_reprocess(self, context, dst):
            return None

    monkeypatch.setattr(ParserFactory, "get_parser", staticmethod(lambda ft: BadParser()))
    with pytest.raises(ValueError):
        await DSTMergeNode("dst_merge").process(ctx)


@pytest.mark.asyncio
async def test_dst_merge_word_count_limit_exceeded(monkeypatch):
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX), word_count=1)
    # make computed word_count > 1
    ctx.update_multiple_parse_dsts_unsafe(0, [_dst("root","-1", 1, 0, "root", DSTType.ROOT), _dst("t","root", 10, 0, "abcd")])

    class OkParser:
        def dst_reprocess(self, context, dst):
            return dst

    monkeypatch.setattr(ParserFactory, "get_parser", staticmethod(lambda ft: OkParser()))
    with pytest.raises(ValueError):
        await DSTMergeNode("dst_merge").process(ctx)


@pytest.mark.asyncio
async def test_dst_merge_parser_factory_exception(monkeypatch):
    def _raise(*a, **k):
        raise RuntimeError("boom")
    monkeypatch.setattr(ParserFactory, "get_parser", staticmethod(lambda ft: _raise()))
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))
    with pytest.raises(RuntimeError):
        await DSTMergeNode("dst_merge").process(ctx)

