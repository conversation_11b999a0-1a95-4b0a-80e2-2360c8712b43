# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.docx import DocxParse
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from services.datamodel import FileType


def _dst_root():
    return DST(id="root", parent="-1", order=0, dst_type=DSTType.ROOT,
               attributes=DSTAttribute(level=0, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
               content=["root"]) 


@pytest.mark.asyncio
async def test_docx_dst_generate(monkeypatch):
    doc = {"doc": {"tree": {"blocks": []}, "hyperlinks": [], "medias": []}}
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", _dst_root)
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    out = await DocxParse().dst_generate(ctx, [doc])
    assert 0 in out and out[0][0].dst_type == DSTType.ROOT


def test_docx_dst_reprocess(monkeypatch):
    monkeypatch.setattr("modules.entity.dst_entity.get_page_dst", lambda lst: {0: lst})
    # Return non-empty temp list to exercise assign_order_to_dst
    monkeypatch.setattr("modules.layout.typesetting.typesetting_correct", lambda root, page_dst: ([root], "top-bottom"))
    monkeypatch.setattr("modules.entity.dst_entity.assign_order_to_dst", lambda lst: lst)
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))
    dsts = [_dst_root()]
    out = DocxParse().dst_reprocess(ctx, dsts)
    assert ctx.layout == "top-bottom" and out == dsts


@pytest.mark.asyncio
async def test_docx_traverse_blocks_and_children(monkeypatch):
    # Monkeypatch node processors to return new ids
    class Dummy:
        def __init__(self, *a, **k):
            pass
        def process(self, *a, **k):
            return "new_id"
    class AsyncDummy:
        def __init__(self, *a, **k):
            pass
        async def process(self, *a, **k):
            return "new_id"
    monkeypatch.setattr("modules.flows.dst_builder.docx.ParaNode", Dummy)
    monkeypatch.setattr("modules.flows.dst_builder.docx.TextboxNode", Dummy)
    monkeypatch.setattr("modules.flows.dst_builder.docx.ComponentNode", Dummy)
    monkeypatch.setattr("modules.flows.dst_builder.docx.TableNode", AsyncDummy)
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", _dst_root)

    # Build a tree with various block types (including an unknown to hit else: continue) and a child
    doc = {
        "doc": {
            "tree": {
                "outline_level": 1,
                "blocks": [
                    {"type": "para", "para": {"runs": [{"text": "t"}], "prop": {"outline_level": 1}}},
                    {"type": "table", "table": {}},
                    {"type": "textbox", "textbox": {"blocks": []}},
                    {"type": "component", "component": {"type": "image", "media_id": "m1"}},
                    {"type": "highlight", "highlight": {"blocks": []}},
                ],
                "children": [
                    {"outline_level": 1, "blocks": [], "children": []}
                ],
            },
            "hyperlinks": [],
            "medias": [],
        }
    }
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))
    out = await DocxParse().dst_generate(ctx, [doc])
    assert 0 in out and len(out[0]) >= 1


def test_docx_is_image_true_and_false():
    from modules.entity.kdc_enttiy import Node, Block, BlockType, Para, Run
    from modules.flows.dst_builder.docx import is_image
    # Node with no blocks/children should be considered image-only by definition here
    node = Node(blocks=[], children=[])
    assert is_image(node, []) is True
    # Node with para having runs should return False
    run = Run(text="t")
    para = Para(runs=[run])
    blk = Block(type=BlockType.para, para=para)
    node2 = Node(blocks=[blk], children=[])
    assert is_image(node2, []) is False
    # Parent with image-only child remains True
    parent = Node(blocks=[], children=[Node(blocks=[], children=[])])
    assert is_image(parent, []) is True


def test_docx_get_res_executes():
    # just call to execute pass path
    DocxParse().get_res(_dst_root())


@pytest.mark.asyncio
async def test_kdc_entity_parse_nodes(monkeypatch):
    # Import node classes and kdc entity helpers
    from modules.flows.dst_builder.kdc_entity_parse import (
        ComponentNode, TextboxNode, TableNode, ParaNode, HighLightNode
    )
    from modules.entity.kdc_enttiy import (
        Block, BlockType, ComponentType, Component, Rectangle, Para, Run, RunProp, ParaProp, TextBox, Table, TableRow, TableCell, Highlight
    )

    # Common mappings
    id2url = {"m1": ["http://u", [100, 100]]}
    id2text = {}
    ls = []
    parent_id = "pid"

    # ComponentNode with valid media and bbox
    comp = Component(type=ComponentType.image, media_id="m1")
    blk_comp = Block(type=BlockType.component, component=comp, page_index=1, id="bid_10_5", bounding_box=Rectangle(x1=0,y1=0,x2=10,y2=10))
    cid = ComponentNode(blk_comp, id2url, id2text).process(1, parent_id, 0, ls)
    assert cid != parent_id and ls[-1].dst_type.value in ("image", "other")

    # TextboxNode with inner para runs, run props, and bbox
    run = Run(text="Hello", prop=RunProp(size=12, bold=True))
    inner_para = Para(runs=[run], prop=ParaProp(outline_level=1, list_string="* "))
    inner_block = Block(type=BlockType.para, para=inner_para)
    textbox = TextBox(blocks=[inner_block])
    blk_tb = Block(type=BlockType.textbox, textbox=textbox, page_index=0, id="tbox_1_2", bounding_box=Rectangle(x1=1,y1=1,x2=5,y2=5))
    tid = TextboxNode(blk_tb, id2url, id2text).process(1, parent_id, 1, ls)
    assert tid != parent_id and "Hello" in "".join(ls[-1].content)

    # ParaNode with runs and props, id without underscore (numeric parse path)
    run2 = Run(text="World")
    para2 = Para(runs=[run2], prop=ParaProp(outline_level=2, list_string="- "))
    blk_para = Block(type=BlockType.para, para=para2, page_index=0, id="abc12d34", bounding_box=Rectangle(x1=2,y1=2,x2=6,y2=6))
    pid2 = ParaNode(blk_para, id2url, id2text).process(2, parent_id, 2, ls)
    assert pid2 != parent_id and "World" in ls[-1].content

    # TableNode with bbox and stubbed table_entity2html
    monkeypatch.setattr("modules.common.table_entity2html", lambda *a, **k: "<table><tr><td>x</td></tr></table>")
    table = Table(rows=[TableRow(cells=[TableCell()])])
    blk_table = Block(type=BlockType.table, table=table, page_index=0, id="tbl_3_2", bounding_box=Rectangle(x1=3,y1=3,x2=7,y2=7))
    tid3 = await TableNode(blk_table, id2url, id2text).process(3, parent_id, 3, ls)
    assert tid3 != parent_id and ls[-1].dst_type.value == "table"

    # HighLightNode with inner para
    h_run = Run(text="HL")
    h_para = Para(runs=[h_run])
    h_block = Block(type=BlockType.para, para=h_para)
    highlight = Highlight(blocks=[h_block])
    blk_hl = Block(type=BlockType.highlight, highlight=highlight, page_index=0, id="hl_1_1", bounding_box=Rectangle(x1=4,y1=4,x2=8,y2=8))
    hid = HighLightNode(blk_hl, id2url, id2text).process(1, parent_id, 4, ls)
    assert hid != parent_id and "HL" in "".join(ls[-1].content)

