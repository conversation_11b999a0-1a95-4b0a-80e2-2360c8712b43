# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.pptx import PptxParser
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType


def _ppt_doc():
    return {
        "doc": {
            "prop": {"slide_size": {"width": 1024, "height": 768}, "note_size": {"width": 1024, "height": 768}},
            "slide_containers": [
                {"category": "slides", "slides": [
                    {"id": 1, "name": "Title", "shape_tree": []}
                ]}
            ],
            "medias": [],
            "hyperlinks": []
        }
    }


@pytest.mark.asyncio
async def test_pptx_dst_generate(monkeypatch):
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPTX))
    out = await PptxParser().dst_generate(ctx, [_ppt_doc()])
    assert 0 in out


def test_pptx_dst_reprocess():
    """Test line 19: dst_reprocess method (empty implementation)"""
    parser = PptxParser()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPTX))
    result = parser.dst_reprocess(ctx, [])
    assert result is None  # Should return None (empty implementation)


def test_pptx_get_res():
    """Test line 22: get_res method (empty implementation)"""
    parser = PptxParser()
    from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
    dst = DST(id="test", parent="root", order=0, dst_type=DSTType.TEXT,
              attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
              content=["test"])
    result = parser.get_res(dst)
    assert result is None  # Should return None (empty implementation)

