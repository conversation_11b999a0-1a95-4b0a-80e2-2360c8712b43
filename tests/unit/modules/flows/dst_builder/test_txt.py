# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.txt import TXTParse
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType


@pytest.mark.asyncio
async def test_txt_dst_generate(monkeypatch):
    doc = {"doc": {"tree": {"blocks": []}, "hyperlinks": [], "medias": []}}
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.TXT))
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: None)
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    # build_root_dst 返回 None 会在 traverse 前 append(None)，不影响结构检验
    out = await TXTParse().dst_generate(ctx, [doc])
    assert 0 in out

