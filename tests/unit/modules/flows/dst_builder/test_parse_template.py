# Author: linqi
# Date: 2025/8/13
# Time: 19:54


def test_parse_template_kdc_parser_and_data_process(monkeypatch):
    # test kdc_parser calls KDCRpc.get_content_by_url_or_file with file_type='otl' enabling upload_medias
    from modules.flows.dst_builder.parse_template import kdc_parser, ParseTemplate
    class DummyReq:
        def __init__(self, text): self.text = text
        def raise_for_status(self): pass
    # mock KDCRpc
    class DummyKDCRpc:
        def get_content_by_url_or_file(self, **kwargs):
            # ensure convert_options contains flag
            assert kwargs.get("convert_options", {}).get("enable_upload_medias", False) is True
            return [{"doc": {}}]
    monkeypatch.setattr("modules.flows.dst_builder.parse_template.KDCRpc", lambda: DummyKDCRpc())
    res = kdc_parser("cid", b"bytes", "name", file_type="otl")
    assert isinstance(res, list)

    # data_process with kdc_ks3_url path (requests.get)
    import json
    class DummyRequests:
        def get(self, url): return DummyReq(json.dumps({"doc": {}}))
    monkeypatch.setattr("modules.flows.dst_builder.parse_template.requests", DummyRequests())
    class DummyPT(ParseTemplate):
        def __init__(self): pass
        async def dst_generate(self, *a, **k): pass
        def dst_reprocess(self, *a, **k): pass
        def get_res(self, *a, **k): pass
    pt = DummyPT()
    out = pt.data_process(kdc_ks3_url="http://x")
    assert isinstance(out, list)
    # cover run() and abstract methods in subclass
    pt.run()
    import asyncio
    asyncio.get_event_loop().run_until_complete(pt.dst_generate(None, None))
    pt.dst_reprocess(None, None)
    pt.get_res(None)
    # data_process else path using kdc_parser stub
    monkeypatch.setattr("modules.flows.dst_builder.parse_template.kdc_parser", lambda *a, **k: [1,2,3])
    out2 = pt.data_process(file_url_or_bytes=b"b", file_name="n")
    assert out2 == [1,2,3]
    # data_process exception branch
    import modules.flows.dst_builder.parse_template as pmod
    import requests as real_requests
    def _raise_get(url, *args, **kwargs):
        raise real_requests.exceptions.RequestException("boom")
    # restore real requests module so exceptions attribute exists
    monkeypatch.setattr(pmod, "requests", real_requests, raising=False)
    # stub get to raise
    monkeypatch.setattr(real_requests, "get", _raise_get)
    try:
        pt.data_process(kdc_ks3_url="http://bad")
        assert False, "ValueError expected"
    except ValueError:
        pass