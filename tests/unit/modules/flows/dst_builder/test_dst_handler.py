# Author: linqi
# Date: 2025/8/13
# Time: 13:15

import pytest

from modules.flows.dst_builder.dst_handler import DocumentParserNode, ParserFactory
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute


def _dst(id_, parent, level, page, text):
    return DST(id=id_, parent=parent, order=0, dst_type=DSTType.TEXT,
               attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
               content=[text])


class DummyParser:
    async def dst_generate(self, context, kdc_data):
        # simulate multi-page dst dict
        return {
            0: [_dst("a", "-1", 10, 0, "A")],
            2: [_dst("b", "-1", 10, 2, "B")],
        }


@pytest.mark.asyncio
async def test_document_parser_node_updates_multiple_parse_dsts(monkeypatch):
    # Patch parser factory to return dummy parser
    monkeypatch.setattr(ParserFactory, "get_parser", staticmethod(lambda ft: DummyParser()))

    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX), kdc_data=[{"doc": {}}])
    out = await DocumentParserNode("document_parser").process(ctx)
    # multiple_parse_dsts should be populated for keys 0 and 2
    assert 0 in out.multiple_parse_dsts and 2 in out.multiple_parse_dsts
    assert out.multiple_parse_dsts[0][0].content == ["A"]


@pytest.mark.asyncio
async def test_document_parser_node_no_kdc_data_returns_context(monkeypatch):
    monkeypatch.setattr(ParserFactory, "get_parser", staticmethod(lambda ft: DummyParser()))
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX), kdc_data=[])
    out = await DocumentParserNode("document_parser").process(ctx)
    assert out is ctx


def test_parser_factory_unknown_type_raises():
    with pytest.raises(ValueError):
        ParserFactory.get_parser("unknown")


def test_parser_factory_known_type_success():
    # Should return a parser instance for known file types (e.g., TXT)
    parser = ParserFactory.get_parser(FileType.TXT)
    assert parser is not None and hasattr(parser, "dst_generate")


@pytest.mark.asyncio
async def test_document_parser_node_error_path_logs_and_raises(monkeypatch):
    # Force factory to raise to exercise except block inside process
    monkeypatch.setattr(ParserFactory, "get_parser", staticmethod(lambda ft: (_ for _ in ()).throw(RuntimeError("boom"))))
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX), kdc_data=[{"doc": {}}])
    with pytest.raises(RuntimeError):
        await DocumentParserNode("document_parser").process(ctx)

