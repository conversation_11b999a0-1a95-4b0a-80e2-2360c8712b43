# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.doc import DocParse
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from services.datamodel import FileType


def _dst_root():
    return DST(id="root", parent="-1", order=0, dst_type=DSTType.ROOT,
               attributes=DSTAttribute(level=0, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
               content=["root"]) 


@pytest.mark.asyncio
async def test_doc_dst_generate(monkeypatch):
    doc = {"doc": {"tree": {"blocks": []}, "hyperlinks": [], "medias": []}}
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOC))
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", _dst_root)
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    out = await DocParse().dst_generate(ctx, [doc])
    assert 0 in out and out[0][0].dst_type == DSTType.ROOT


def test_doc_dst_reprocess(monkeypatch):
    from modules.flows.dst_builder.doc import DocParse
    monkeypatch.setattr("modules.entity.dst_entity.get_page_dst", lambda lst: {0: lst})
    # Return non-empty temp list to cover assign_order_to_dst branch
    monkeypatch.setattr("modules.layout.typesetting.typesetting_correct", lambda root, page_dst: ([root], "top-bottom"))
    monkeypatch.setattr("modules.entity.dst_entity.assign_order_to_dst", lambda lst: lst)
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.DOC))
    dsts = [_dst_root()]
    out = DocParse().dst_reprocess(ctx, dsts)
    assert ctx.layout == "top-bottom" and out == dsts


def test_is_all_images_true_and_false(monkeypatch):
    from modules.flows.dst_builder.doc import is_all_images
    # Case with medias containing data -> returns (is_image(tree), data_array)
    doc = {"doc": {"tree": {"blocks": []}, "medias": [{"id": "m1", "data": "xx"}], "hyperlinks": []}}
    is_img, data = is_all_images(PipelineContext(file_info=FileInfo(file_type=FileType.DOC)), [doc], images=[])
    assert is_img is True and data == ["xx"]
    # Case with no medias -> returns (False, images)
    doc2 = {"doc": {"tree": {"blocks": []}, "medias": [], "hyperlinks": []}}
    is_img2, data2 = is_all_images(PipelineContext(file_info=FileInfo(file_type=FileType.DOC)), [doc2], images=["a"])
    assert is_img2 is False and data2 == ["a"]

