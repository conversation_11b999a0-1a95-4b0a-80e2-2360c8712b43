# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.image import ImageParse
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo


def _dst_root():
    return DST(
        id="root",
        parent="-1",
        order=0,
        dst_type=DSTType.ROOT,
        attributes=DSTAttribute(level=0, position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=1, y2=1)), page=0, hash="h" * 32),
        content=["root"],
    )


def test_image_parse_placeholder():
    parser = ImageParse()
    assert parser is not None


@pytest.mark.asyncio
async def test_image_parse_dst_generate():
    parser = ImageParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PNG))
    # method is pass; just ensure it can be awaited without error
    res = await parser.dst_generate(ctx, [])
    assert res is None


def test_image_parse_reprocess_and_get_res():
    parser = ImageParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PNG))
    # pass methods; ensure callable
    parser.dst_reprocess(ctx, [])
    parser.get_res(_dst_root())

