"""
Unit tests for modules.utils module
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import unittest.mock
import requests
import aiohttp
import urllib3

from modules.utils import ConnPool


class TestConnPool:
    """Test cases for ConnPool class"""

    def test_connpool_singleton(self):
        """Test that ConnPool is a singleton"""
        pool1 = ConnPool()
        pool2 = ConnPool()
        
        assert pool1 is pool2

    @pytest.mark.asyncio
    async def test_connpool_init_default(self):
        """Test ConnPool initialization with default parameters"""
        pool = ConnPool()

        with patch('modules.utils.requests.Session') as mock_session:
            with patch('modules.utils.aiohttp.ClientSession') as mock_async_session:
                with patch('modules.utils.urllib3.disable_warnings') as mock_disable_warnings:
                    mock_session_instance = MagicMock()
                    mock_session.return_value = mock_session_instance

                    mock_async_session_instance = MagicMock()
                    mock_async_session.return_value = mock_async_session_instance

                    pool.init()

                    # Verify session creation
                    mock_session.assert_called_once()
                    mock_async_session.assert_called_once()
                    mock_disable_warnings.assert_called_once()

                    # Verify adapters are mounted
                    assert mock_session_instance.mount.call_count == 2
                    mock_session_instance.mount.assert_any_call('http://', unittest.mock.ANY)
                    mock_session_instance.mount.assert_any_call('https://', unittest.mock.ANY)

    def test_connpool_init_custom_pool_max(self):
        """Test ConnPool initialization with custom pool_max"""
        pool = ConnPool()
        
        with patch('modules.utils.requests.Session') as mock_session:
            with patch('modules.utils.aiohttp.ClientSession') as mock_async_session:
                with patch('modules.utils.HTTPAdapter') as mock_adapter:
                    with patch('modules.utils.aiohttp.TCPConnector') as mock_connector:
                        mock_session_instance = MagicMock()
                        mock_session.return_value = mock_session_instance
                        
                        mock_async_session_instance = MagicMock()
                        mock_async_session.return_value = mock_async_session_instance
                        
                        mock_adapter_instance = MagicMock()
                        mock_adapter.return_value = mock_adapter_instance
                        
                        mock_connector_instance = MagicMock()
                        mock_connector.return_value = mock_connector_instance
                        
                        pool.init(pool_max=20)
                        
                        # Verify HTTPAdapter is created with correct pool_maxsize
                        assert mock_adapter.call_count == 2
                        mock_adapter.assert_any_call(pool_maxsize=20)
                        
                        # Verify TCPConnector is created with correct limit
                        mock_connector.assert_called_once_with(limit=20)
                        mock_async_session.assert_called_once_with(connector=mock_connector_instance)

    @pytest.mark.asyncio
    async def test_connpool_close(self):
        """Test ConnPool close method"""
        pool = ConnPool()
        
        # Mock the session objects
        mock_async_session = AsyncMock()
        mock_sync_session = MagicMock()
        
        pool._afile_req_sess = mock_async_session
        pool._file_req_sess = mock_sync_session
        
        await pool.close()
        
        # Verify close methods are called
        mock_async_session.close.assert_called_once()
        mock_sync_session.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_connpool_close_none_sessions(self):
        """Test ConnPool close method when sessions are None"""
        pool = ConnPool()
        
        pool._afile_req_sess = None
        pool._file_req_sess = None
        
        # Should not raise any exception
        await pool.close()

    def test_get_file_req_before_init(self):
        """Test get_file_req before initialization"""
        pool = ConnPool()
        pool._file_req_sess = None
        
        result = pool.get_file_req()
        assert result is None

    @pytest.mark.asyncio
    async def test_get_file_req_after_init(self):
        """Test get_file_req after initialization"""
        pool = ConnPool()

        with patch('modules.utils.requests.Session') as mock_session:
            with patch('modules.utils.aiohttp.ClientSession'):
                with patch('modules.utils.urllib3.disable_warnings'):
                    mock_session_instance = MagicMock()
                    mock_session.return_value = mock_session_instance

                    pool.init()
                    result = pool.get_file_req()

                    assert result == mock_session_instance

    def test_get_afile_req_before_init(self):
        """Test get_afile_req before initialization"""
        pool = ConnPool()
        pool._afile_req_sess = None
        
        result = pool.get_afile_req()
        assert result is None

    @pytest.mark.asyncio
    async def test_get_afile_req_after_init(self):
        """Test get_afile_req after initialization"""
        pool = ConnPool()

        with patch('modules.utils.requests.Session'):
            with patch('modules.utils.aiohttp.ClientSession') as mock_async_session:
                with patch('modules.utils.urllib3.disable_warnings'):
                    mock_async_session_instance = MagicMock()
                    mock_async_session.return_value = mock_async_session_instance

                    pool.init()
                    result = pool.get_afile_req()

                    assert result == mock_async_session_instance

    @pytest.mark.asyncio
    async def test_connpool_multiple_init_calls(self):
        """Test that multiple init calls work correctly"""
        pool = ConnPool()

        with patch('modules.utils.requests.Session') as mock_session:
            with patch('modules.utils.aiohttp.ClientSession') as mock_async_session:
                with patch('modules.utils.urllib3.disable_warnings'):
                    mock_session_instance1 = MagicMock()
                    mock_session_instance2 = MagicMock()
                    mock_session.side_effect = [mock_session_instance1, mock_session_instance2]

                    mock_async_session_instance1 = MagicMock()
                    mock_async_session_instance2 = MagicMock()
                    mock_async_session.side_effect = [mock_async_session_instance1, mock_async_session_instance2]

                    # First init
                    pool.init(pool_max=10)
                    first_sync_session = pool.get_file_req()
                    first_async_session = pool.get_afile_req()

                    # Second init should replace sessions
                    pool.init(pool_max=15)
                    second_sync_session = pool.get_file_req()
                    second_async_session = pool.get_afile_req()

                    assert first_sync_session == mock_session_instance1
                    assert second_sync_session == mock_session_instance2
                    assert first_async_session == mock_async_session_instance1
                    assert second_async_session == mock_async_session_instance2

    @pytest.mark.asyncio
    async def test_connpool_state_persistence(self):
        """Test that ConnPool maintains state across multiple instances"""
        pool1 = ConnPool()

        with patch('modules.utils.requests.Session') as mock_session:
            with patch('modules.utils.aiohttp.ClientSession') as mock_async_session:
                with patch('modules.utils.urllib3.disable_warnings'):
                    mock_session_instance = MagicMock()
                    mock_session.return_value = mock_session_instance

                    mock_async_session_instance = MagicMock()
                    mock_async_session.return_value = mock_async_session_instance

                    pool1.init()

                    # Create another instance (should be same due to singleton)
                    pool2 = ConnPool()

                    # Both should return the same sessions
                    assert pool1.get_file_req() == pool2.get_file_req()
                    assert pool1.get_afile_req() == pool2.get_afile_req()
