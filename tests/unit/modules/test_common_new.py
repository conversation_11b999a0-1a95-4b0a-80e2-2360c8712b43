"""
modules.common 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import base64
import uuid
from io import BytesIO
from PIL import Image

from modules.common import (
    table2html, build_dst_id, build_root_dst, calculate_original_bbox,
    bytes_or_base64_image, upload_image, build_hyperlink_map
)
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox
from modules.entity.kdc_enttiy import HyperLink, Reference, ReferenctType


class TestTable2Html:
    """table2html函数的测试用例"""

    def test_table2html_empty_table(self):
        """测试table2html处理空表格"""
        result = table2html(None)
        assert result == ""

        result = table2html([])
        assert result == ""

    def test_table2html_simple_table(self):
        """测试table2html处理简单表格"""
        table = [
            [
                {'content': ['Cell 1'], 'row_span': 1, 'col_span': 1},
                {'content': ['Cell 2'], 'row_span': 1, 'col_span': 1}
            ],
            [
                {'content': ['Cell 3'], 'row_span': 1, 'col_span': 1},
                {'content': ['Cell 4'], 'row_span': 1, 'col_span': 1}
            ]
        ]
        
        result = table2html(table)
        
        assert '<table>' in result
        assert '</table>' in result
        assert '<tr>' in result
        assert '</tr>' in result
        assert '<td' in result  # 允许属性
        assert '</td>' in result
        assert 'Cell 1' in result
        assert 'Cell 2' in result
        assert 'Cell 3' in result
        assert 'Cell 4' in result

    def test_table2html_with_spans(self):
        """测试table2html处理行列跨度"""
        table = [
            [
                {'content': ['Header'], 'row_span': 1, 'col_span': 2}
            ],
            [
                {'content': ['Cell 1'], 'row_span': 2, 'col_span': 1},
                {'content': ['Cell 2'], 'row_span': 1, 'col_span': 1}
            ]
        ]
        
        result = table2html(table)
        
        assert 'colspan=2' in result  # 允许无引号
        assert 'rowspan=2' in result
        assert 'Header' in result
        assert 'Cell 1' in result
        assert 'Cell 2' in result

    def test_table2html_with_multiple_content(self):
        """测试table2html处理多个内容项"""
        table = [
            [
                {'content': ['Line 1', 'Line 2'], 'row_span': 1, 'col_span': 1}
            ]
        ]
        
        result = table2html(table)
        
        assert 'Line 1' in result
        assert 'Line 2' in result


class TestBuildDstId:
    """build_dst_id函数的测试用例"""

    def test_build_dst_id_returns_string(self):
        """测试build_dst_id返回字符串"""
        result = build_dst_id()
        assert isinstance(result, str)

    def test_build_dst_id_returns_hex_uuid(self):
        """测试build_dst_id返回有效的十六进制UUID"""
        result = build_dst_id()
        # 应该是32个字符（不带连字符的UUID）
        assert len(result) == 32
        # 应该是有效的十六进制
        int(result, 16)  # 如果不是有效的十六进制，这将引发ValueError

    def test_build_dst_id_unique(self):
        """测试build_dst_id返回唯一值"""
        id1 = build_dst_id()
        id2 = build_dst_id()
        assert id1 != id2


class TestBuildRootDst:
    """build_root_dst函数的测试用例"""

    def test_build_root_dst_structure(self):
        """测试build_root_dst返回正确的DST结构"""
        result = build_root_dst()
        
        assert isinstance(result, DST)
        assert result.parent == str(-1)
        assert result.dst_type == DSTType.ROOT
        assert isinstance(result.attributes, DSTAttribute)
        assert result.content == ['根节点']
        assert result.order == 0

    def test_build_root_dst_has_valid_id(self):
        """测试build_root_dst具有有效的ID"""
        result = build_root_dst()

        assert isinstance(result.id, str)
        assert len(result.id) == 32  # UUID十六进制格式
        # 应该是有效的十六进制
        int(result.id, 16)

    def test_build_root_dst_unique_ids(self):
        """测试多次调用返回不同的ID"""
        dst1 = build_root_dst()
        dst2 = build_root_dst()
        
        assert dst1.id != dst2.id


class TestCalculateOriginalBbox:
    """calculate_original_bbox函数的测试用例"""

    def test_calculate_original_bbox_no_rotation(self):
        """测试calculate_original_bbox无旋转情况"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        # 无旋转时，坐标应保持不变
        assert result == (10, 20, 30, 40)

    def test_calculate_original_bbox_with_rotation_90(self):
        """测试calculate_original_bbox 90度旋转情况"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40, rotation=90)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        # 应该返回转换后的坐标
        assert isinstance(result, tuple)
        assert len(result) == 4

    def test_calculate_original_bbox_with_rotation_180(self):
        """测试calculate_original_bbox 180度旋转情况"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40, rotation=180)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        # 应该返回转换后的坐标
        assert isinstance(result, tuple)
        assert len(result) == 4

    def test_calculate_original_bbox_with_rotation_270(self):
        """测试calculate_original_bbox 270度旋转情况"""
        bbox = BBox(x1=10, y1=20, x2=30, y2=40, rotation=270)
        width, height = 100, 200

        result = calculate_original_bbox(bbox, width, height)

        # 应该返回转换后的坐标
        assert isinstance(result, tuple)
        assert len(result) == 4


class TestBytesOrBase64Image:
    """bytes_or_base64_image函数的测试用例"""

    def test_bytes_or_base64_image_with_bytes(self):
        """测试bytes_or_base64_image处理字节输入"""
        # 创建一个简单的1x1像素图像
        img = Image.new('RGB', (1, 1), color='red')
        img_bytes = BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes = img_bytes.getvalue()

        result = bytes_or_base64_image(img_bytes)

        assert isinstance(result, Image.Image)
        assert result.size == (1, 1)

    def test_bytes_or_base64_image_with_base64(self):
        """测试bytes_or_base64_image处理base64字符串输入"""
        # 创建一个简单的1x1像素图像并编码为base64
        img = Image.new('RGB', (1, 1), color='blue')
        img_bytes = BytesIO()
        img.save(img_bytes, format='PNG')
        img_base64 = base64.b64encode(img_bytes.getvalue()).decode('utf-8')

        result = bytes_or_base64_image(img_base64)

        assert isinstance(result, Image.Image)
        assert result.size == (1, 1)


class TestUploadImage:
    """upload_image函数的测试用例"""

    @patch('modules.common.StoreDao')
    def test_upload_image_success(self, mock_store_dao):
        """测试成功的图像上传"""
        # 创建一个简单的base64图像
        img = Image.new('RGB', (1, 1), color='green')
        img_bytes = BytesIO()
        img.save(img_bytes, format='PNG')
        img_base64 = base64.b64encode(img_bytes.getvalue()).decode('utf-8')

        # 模拟StoreDao
        mock_store_instance = MagicMock()
        mock_store_dao.return_value = mock_store_instance
        mock_store_instance.upload_from_bytes.return_value = True
        mock_store_instance.generate_url.return_value = "http://example.com/image.png"

        result = upload_image(img_base64, public=True)

        assert result == "http://example.com/image.png"
        mock_store_instance.upload_from_bytes.assert_called_once()
        mock_store_instance.generate_url.assert_called_once()

    @patch('modules.common.StoreDao')
    def test_upload_image_with_internal_url(self, mock_store_dao):
        """测试图像上传并清理内部URL"""
        img = Image.new('RGB', (1, 1), color='yellow')
        img_bytes = BytesIO()
        img.save(img_bytes, format='PNG')
        img_base64 = base64.b64encode(img_bytes.getvalue()).decode('utf-8')

        # 模拟StoreDao返回内部URL
        mock_store_instance = MagicMock()
        mock_store_dao.return_value = mock_store_instance
        mock_store_instance.upload_from_bytes.return_value = True
        mock_store_instance.generate_url.return_value = "http://example-internal.com/image.png"

        result = upload_image(img_base64, public=True)

        # 应该从URL中移除-internal
        assert result == "http://example.com/image.png"


class TestBuildHyperlinkMap:
    """build_hyperlink_map函数的测试用例"""

    def test_build_hyperlink_map_empty_list(self):
        """测试build_hyperlink_map处理空列表"""
        result = build_hyperlink_map([])
        assert result == {}

        result = build_hyperlink_map(None)
        assert result == {}

    def test_build_hyperlink_map_with_links(self):
        """测试build_hyperlink_map处理超链接"""
        ref1 = Reference(id="ref1", type=ReferenctType.run)
        ref2 = Reference(id="ref2", type=ReferenctType.block)

        link1 = HyperLink(
            display_text="Link 1",
            target="http://example.com",
            references=[ref1, ref2]
        )

        ref3 = Reference(id="ref3", type=ReferenctType.cell)
        link2 = HyperLink(
            display_text="Link 2",
            target="http://example2.com",
            references=[ref3]
        )
        
        result = build_hyperlink_map([link1, link2])
        
        assert result["ref1"] == "Link 1"
        assert result["ref2"] == "Link 1"
        assert result["ref3"] == "Link 2"

    def test_build_hyperlink_map_with_special_chars(self):
        """测试build_hyperlink_map处理文本中的特殊字符"""
        ref1 = Reference(id="ref1", type=ReferenctType.run)

        # 包含应该被移除的特殊字符的文本
        link = HyperLink(
            display_text="Link with  special chars",
            target="http://example.com",
            references=[ref1]
        )

        result = build_hyperlink_map([link])

        # 特殊字符应该被移除
        assert result["ref1"] == "Link with  special chars"

    def test_build_hyperlink_map_no_references(self):
        """测试build_hyperlink_map处理没有引用的链接"""
        link = HyperLink(
            display_text="Link without refs",
            target="http://example.com",
            references=None
        )
        
        result = build_hyperlink_map([link])
        
        assert result == {}

    def test_build_hyperlink_map_empty_display_text(self):
        """测试build_hyperlink_map处理空显示文本的链接"""
        ref1 = Reference(id="ref1", type=ReferenctType.run)

        link = HyperLink(
            display_text="",
            target="http://example.com",
            references=[ref1]
        )

        result = build_hyperlink_map([link])

        # 空显示文本为假值，所以条件失败，不创建映射
        assert result == {}
