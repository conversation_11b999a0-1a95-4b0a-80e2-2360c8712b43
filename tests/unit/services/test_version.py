"""
services.version 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock

from services.version import get_version
from services.datamodel import RespVersionRes
from modules.entity.version_entity import RespVersionData
from routers.httpcode import HTTPCODE


class TestVersionService:
    """版本服务的测试用例"""

    @pytest.mark.asyncio
    async def test_get_version_success(self):
        """测试成功获取版本信息"""
        with patch('conf.ConfParseVersion.version', 'v1.0.0'):
            result = await get_version()
            
            assert isinstance(result, RespVersionRes)
            assert result.code == HTTPCODE.OK
            assert result.data.version == 'v1.0.0'

    @pytest.mark.asyncio
    async def test_get_version_with_different_version(self):
        """测试使用不同版本字符串获取版本"""
        with patch('conf.ConfParseVersion.version', 'v2.1.3-beta'):
            result = await get_version()
            
            assert isinstance(result, RespVersionRes)
            assert result.code == HTTPCODE.OK
            assert result.data.version == 'v2.1.3-beta'

    @pytest.mark.asyncio
    async def test_get_version_exception_handling(self):
        """测试版本服务异常处理"""
        with patch('conf.ConfParseVersion.version', side_effect=Exception("Config error")):
            with patch('services.version.error_trace') as mock_error_trace:
                result = await get_version()
                
                assert isinstance(result, RespVersionRes)
                assert result.code == HTTPCODE.ERROR
                mock_error_trace.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_version_empty_version(self):
        """测试版本服务处理空版本字符串"""
        with patch('conf.ConfParseVersion.version', ''):
            result = await get_version()
            
            assert isinstance(result, RespVersionRes)
            assert result.code == HTTPCODE.OK
            assert result.data.version == ''

    @pytest.mark.asyncio
    async def test_get_version_none_version(self):
        """测试版本服务处理None版本（应该引起错误）"""
        with patch('conf.ConfParseVersion.version', None):
            with patch('services.version.error_trace') as mock_error_trace:
                result = await get_version()

                assert isinstance(result, RespVersionRes)
                assert result.code == HTTPCODE.ERROR
                mock_error_trace.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_version_data_structure(self):
        """测试返回的数据具有正确的结构"""
        with patch('conf.ConfParseVersion.version', 'test-version'):
            result = await get_version()
            
            # Check response structure
            assert hasattr(result, 'code')
            assert hasattr(result, 'data')
            assert isinstance(result.data, RespVersionData)
            assert hasattr(result.data, 'version')

    @pytest.mark.asyncio
    async def test_get_version_trace_span_decorator(self):
        """测试async_trace_span装饰器被应用"""
        # This test verifies the function is decorated, actual tracing behavior
        # would require integration testing with the tracing system
        with patch('conf.ConfParseVersion.version', 'v1.0.0'):
            result = await get_version()
            assert result.code == HTTPCODE.OK
