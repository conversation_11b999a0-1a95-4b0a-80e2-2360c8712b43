# Author: linqi
# Date: 2025/8/19
# Time: 11:09

import pytest
import asyncio
import json
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from fastapi import Request, BackgroundTasks
from io import BytesIO

from services.parse import (
    ParseKafkaMsg, 
    parse_input_to_ks3, 
    background_general_parse,
    general_parse_service,
    convert_handler_results_to_resp,
    general_parse_res_service,
    merge_parse_res
)
from services.datamodel import ReqGeneralParse, ReqGeneralParseType, ReqGeneralParseRes, ParseTarget
from modules.pipeline.context import PipelineContext, KDCInput, FileInfo, ChunkInfo
from modules.entity.parse_entity import GeneralParseStatus, ParseRes, RespGeneralParseData, Image
from modules.entity.chunk_entity import Chunk, LabelType
from routers.httpcode import HTTPCODE
from commons.logger.logger import PassableLogContext
from fastapi import UploadFile


class TestParseKafkaMsg:
    """Test ParseKafkaMsg model"""
    
    def test_parse_kafka_msg_creation(self):
        """Test ParseKafkaMsg model creation"""
        body = ReqGeneralParse(
            file_name="test.pdf",
            file_type="pdf",
            wps_company_id="123"
        )
        
        msg = ParseKafkaMsg(
            file_url="http://test.com/file.pdf",
            body=body,
            token="test-token",
            wiki_branch="main"
        )
        
        assert msg.file_url == "http://test.com/file.pdf"
        assert msg.body == body
        assert msg.token == "test-token"
        assert msg.wiki_branch == "main"
        assert msg.log_context is None

    def test_parse_kafka_msg_with_log_context(self):
        """Test ParseKafkaMsg with log context"""
        body = ReqGeneralParse(
            file_name="test.pdf",
            file_type="pdf",
            wps_company_id="123"
        )
        log_context = PassableLogContext(
            ip="127.0.0.1", 
            req_type="test", 
            request_id="123", 
            local_id="456", 
            index=1, 
            uri="/test",
            trace_id="abc123",
            span_id="def456"
        )
        
        msg = ParseKafkaMsg(
            body=body,
            token="test-token",
            log_context=log_context
        )
        
        assert msg.body == body
        assert msg.token == "test-token"
        assert msg.log_context == log_context


class TestParseInputToKs3:
    """Test parse_input_to_ks3 function"""
    
    @pytest.mark.asyncio
    @patch('services.parse.StoreDao')
    @patch('services.parse.datetime')
    async def test_parse_input_to_ks3_success(self, mock_datetime, mock_store_dao):
        """Test successful file upload to ks3"""
        # Arrange
        mock_datetime.now.return_value.strftime.return_value = '20240819'
        mock_store_instance = AsyncMock()
        mock_store_dao.return_value = mock_store_instance
        mock_store_instance.async_upload_from_bytes.return_value = True
        mock_store_instance.async_generate_url.return_value = "http://ks3.com/file.pdf"
        
        # Act
        result = await parse_input_to_ks3(b"file content", "test-token")
        
        # Assert
        assert result == "http://ks3.com/file.pdf"
        mock_store_instance.async_upload_from_bytes.assert_called_once()
        mock_store_instance.async_generate_url.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.StoreDao')
    async def test_parse_input_to_ks3_upload_failure(self, mock_store_dao):
        """Test file upload failure"""
        # Arrange
        mock_store_instance = AsyncMock()
        mock_store_dao.return_value = mock_store_instance
        mock_store_instance.async_upload_from_bytes.return_value = False
        
        # Act & Assert
        with pytest.raises(Exception, match="文件bytes上传ks3失败"):
            await parse_input_to_ks3(b"file content", "test-token")


class TestBackgroundGeneralParse:
    """Test background_general_parse function"""
    
    @pytest.mark.asyncio
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.get_mcount')
    @patch('services.parse.set_parse_target_status')
    async def test_background_general_parse_success(self, mock_set_parse_target, mock_get_mcount, mock_redis, mock_pipeline_factory):
        """Test successful background parse"""
        # Arrange
        req = ReqGeneralParse(
            file_name="test.pdf",
            file_type="pdf",
            wps_company_id="123",
            wps_v5_file_id="file123",
            parse_target=[ParseTarget.chunk]
        )
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.return_value = None
        
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        
        mock_mc = MagicMock()
        mock_get_mcount.return_value = mock_mc
        
        # Act
        await background_general_parse(req, "http://test.com/file.pdf", "test-token", "main")
        
        # Assert
        mock_pipeline_factory.create_document_pipeline.assert_called_once()
        mock_pipeline.execute.assert_called_once()
        mock_redis_instance.set.assert_called_once()
        mock_set_parse_target.assert_called_once_with("test-token", [ParseTarget.chunk])

    @pytest.mark.asyncio
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.error_trace')
    @patch('services.parse.set_parse_target_status')
    async def test_background_general_parse_value_error_page_limit(self, mock_set_parse_target, mock_error_trace, mock_redis, mock_pipeline_factory):
        """Test background parse with page count limit error"""
        # Arrange
        req = ReqGeneralParse(
            file_name="test.pdf",
            file_type="pdf",
            wps_company_id="123",
            wps_v5_file_id="file123",
            parse_target=[ParseTarget.chunk]
        )
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.side_effect = ValueError("page count exceeds limit")
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        await background_general_parse(req, "http://test.com/file.pdf", "test-token", "main")
        
        # Assert
        mock_redis_instance.aset.assert_called_with(
            f"parse_background_status_test-token", 
            GeneralParseStatus.limit, 
            ex=604800  # DAY_7
        )
        mock_set_parse_target.assert_called_once_with("test-token", [ParseTarget.chunk])

    @pytest.mark.asyncio
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.error_trace')
    @patch('services.parse.set_parse_target_status')
    async def test_background_general_parse_value_error_word_limit(self, mock_set_parse_target, mock_error_trace, mock_redis, mock_pipeline_factory):
        """Test background parse with word count limit error"""
        # Arrange
        req = ReqGeneralParse(
            file_name="test.pdf",
            file_type="pdf",
            wps_company_id="123",
            wps_v5_file_id="file123",
            parse_target=[ParseTarget.chunk]
        )
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.side_effect = ValueError("word count exceeds limit")
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        await background_general_parse(req, "http://test.com/file.pdf", "test-token", "main")
        
        # Assert
        mock_redis_instance.aset.assert_called_with(
            f"parse_background_status_test-token", 
            GeneralParseStatus.limit, 
            ex=604800  # DAY_7
        )
        mock_set_parse_target.assert_called_once_with("test-token", [ParseTarget.chunk])
        
    @pytest.mark.asyncio
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.error_trace')
    @patch('services.parse.set_parse_target_status')
    async def test_background_general_parse_value_error_generic(self, mock_set_parse_target, mock_error_trace, mock_redis, mock_pipeline_factory):
        """Test background parse with generic ValueError"""
        # Arrange
        req = ReqGeneralParse(
            file_name="test.pdf",
            file_type="pdf",
            wps_company_id="123",
            wps_v5_file_id="file123",
            parse_target=[ParseTarget.chunk]
        )
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.side_effect = ValueError("Some other error")
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        await background_general_parse(req, "http://test.com/file.pdf", "test-token", "main")
        
        # Assert
        mock_redis_instance.aset.assert_called_with(
            f"parse_background_status_test-token", 
            GeneralParseStatus.fail, 
            ex=604800  # DAY_7
        )
        mock_set_parse_target.assert_called_once_with("test-token", [ParseTarget.chunk])

    @pytest.mark.asyncio
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.error_trace')
    @patch('services.parse.set_parse_target_status')
    async def test_background_general_parse_generic_exception(self, mock_set_parse_target, mock_error_trace, mock_redis, mock_pipeline_factory):
        """Test background parse with generic exception"""
        # Arrange
        req = ReqGeneralParse(
            file_name="test.pdf",
            file_type="pdf",
            wps_company_id="123",
            wps_v5_file_id="file123",
            parse_target=[ParseTarget.chunk]
        )
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.side_effect = Exception("Generic error")
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        await background_general_parse(req, "http://test.com/file.pdf", "test-token", "main")
        
        # Assert
        mock_redis_instance.aset.assert_called_with(
            f"parse_background_status_test-token", 
            GeneralParseStatus.fail, 
            ex=604800  # DAY_7
        )
        mock_set_parse_target.assert_called_once_with("test-token", [ParseTarget.chunk])


class TestConvertHandlerResultsToResp:
    """Test convert_handler_results_to_resp function"""
    
    def test_convert_handler_results_to_resp(self):
        """Test converting handler results to response"""
        # Arrange
        from modules.entity.chunk_entity import Chunk, LabelType
        from modules.entity.parse_entity import Image, ImageType
        
        chunk1 = Chunk(
            chunk_id="chunk1",
            page_size=5,
            content="chunk1",
            label=LabelType.TEXT,
            page_num=[1],
            block=["block1"]
        )
        chunk2 = Chunk(
            chunk_id="chunk2",
            page_size=5,
            content="chunk2",
            label=LabelType.TEXT,
            page_num=[1],
            block=["block2"]
        )
        
        image1 = Image(
            page_num=1,
            url="img1.png",
            image_type=ImageType.INPUT_IMAGE
        )
        image2 = Image(
            page_num=1,
            url="img2.png",
            image_type=ImageType.INPUT_IMAGE
        )
        
        handler_results = {
            "chunk": [chunk1, chunk2],
            "fake_title": {"fake_title": "Test Title", "fake_title_embedding": [0.1, 0.2]},
            "keywords": {"keywords": ["key1", "key2"], "keywords_embedding": [0.3, 0.4]},
            "summary": {"summary": "Test summary", "summary_embedding": [0.5, 0.6]},
            "screenshot": [image1, image2]
        }
        
        context = PipelineContext(
            file_info=FileInfo(file_id="123", file_name="test.pdf", file_type="pdf"),
            embed_enabled=True,
            kdc_input=KDCInput(company_id="123"),
            chunks_info=ChunkInfo(),
            token="test-token"
        )
        context.file_info.page_size = 10
        context.file_info.word_count = 1000
        context.file_info.width = 800
        context.file_info.height = 600
        context.file_info.is_scan = False
        context.file_info.rotate_page = {}
        context.parse_version = "1.0"
        
        parse_target = [ParseTarget.chunk, ParseTarget.fake_title]
        
        # Act
        result = convert_handler_results_to_resp(handler_results, context, parse_target)
        
        # Assert
        assert result.status == GeneralParseStatus.ok
        assert result.parse_res.chunks == [chunk1, chunk2]
        assert result.parse_res.fake_title == "Test Title"
        assert result.parse_res.page_size == 10
        assert result.parse_res.word_count == 1000
        assert result.parse_res.image == [image1, image2]
        assert result.parse_target == parse_target


class TestMergeParseRes:
    """Test merge_parse_res function"""
    
    def test_merge_parse_res_with_multiple_results(self):
        """Test merging multiple ParseRes objects"""
        # Arrange
        from modules.entity.chunk_entity import Chunk, LabelType
        from modules.entity.parse_entity import Image, ImageType
        
        chunk1 = Chunk(
            chunk_id="chunk1",
            page_size=5,
            content="chunk1",
            label=LabelType.TEXT,
            page_num=[1],
            block=["block1"]
        )
        
        image1 = Image(
            page_num=1,
            url="img1.png",
            image_type=ImageType.INPUT_IMAGE
        )
        
        parse_res1 = ParseRes(
            chunks=[chunk1],
            fake_title="Title 1",
            keywords=["key1"],
            page_size=5,
            word_count=500
        )
        
        parse_res2 = ParseRes(
            summary="Summary 2",
            image=[image1],
            width=800,
            height=600,
            is_scan=True
        )
        
        parse_res_list = [parse_res1, parse_res2]
        
        # Act
        result = merge_parse_res(parse_res_list)
        
        # Assert
        assert result.chunks == [chunk1]
        assert result.fake_title == "Title 1"
        assert result.keywords == ["key1"]
        assert result.summary == "Summary 2"
        assert result.image == [image1]
        assert result.page_size == 5
        assert result.word_count == 500
        assert result.width == 800
        assert result.height == 600
        assert result.is_scan == True

    def test_merge_parse_res_with_empty_and_none_results(self):
        """Test merging with empty and None results"""
        # Arrange
        parse_res1 = ParseRes(fake_title="Title")
        parse_res_list = [parse_res1, None]
        
        # Act
        result = merge_parse_res(parse_res_list)
        
        # Assert
        assert result.fake_title == "Title"
        assert result.chunks == []


class TestGeneralParseService:
    """Test general_parse_service function"""
    
    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.get_drive_rpc_client')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_json_normal_success(self, mock_redis, mock_drive_client, mock_pipeline_factory, mock_uuid):
        """Test successful JSON normal parse request"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "wps_v5_file_id": "file123",
            "req_type": "normal",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        # Mock Redis5Dao properly
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_result = MagicMock()
        mock_result.handler_results = {"chunk_handler": []}
        mock_pipeline.execute.return_value = mock_result
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.OK
        assert result.data is not None
        mock_pipeline_factory.create_document_pipeline.assert_called_once()
        mock_pipeline.execute.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_json_background_success(self, mock_redis, mock_uuid):
        """Test successful JSON background parse request"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "wps_v5_file_id": "file123",
            "req_type": "background",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.OK
        assert result.data.status == GeneralParseStatus.wait
        assert result.data.token == "test-token"
        mock_background_tasks.add_task.assert_called_once()
        mock_redis_instance.aset.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.KafkaProducerDao')
    @patch('services.parse.PassableLogContext')
    async def test_general_parse_service_json_queue_success(self, mock_log_context, mock_kafka, mock_redis, mock_uuid):
        """Test successful JSON queue parse request"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf", 
            "wps_company_id": "123",
            "wps_v5_file_id": "file123",
            "req_type": "queue",
            "req_level": "high",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        mock_kafka_instance = AsyncMock()
        mock_kafka.return_value = mock_kafka_instance
        mock_kafka_instance.asend_message.return_value = True
        
        # Mock PassableLogContext properly
        mock_log_context_instance = PassableLogContext(
            ip="127.0.0.1",
            req_type="test",
            request_id="123",
            local_id="456",
            index=1,
            uri="/test",
            trace_id="abc123",
            span_id="def456"
        )
        mock_log_context.return_value = mock_log_context_instance
        mock_log_context.dump.return_value = {
            "ip": "127.0.0.1",
            "req_type": "test",
            "request_id": "123",
            "local_id": "456",
            "index": 1,
            "uri": "/test",
            "trace_id": "abc123",
            "span_id": "def456"
        }
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.OK
        assert result.data.status == GeneralParseStatus.wait
        assert result.data.token == "test-token"
        mock_redis_instance.aset.assert_called_once()
        mock_kafka_instance.asend_message.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_multipart_form_data(self, mock_redis, mock_uuid):
        """Test multipart form data parse request"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        # Create a proper UploadFile mock
        mock_upload_file = MagicMock(spec=UploadFile)
        mock_file_obj = MagicMock()
        mock_file_obj.read.return_value = b"file content"
        mock_upload_file.file = mock_file_obj
        mock_upload_file.filename = "test.pdf"
        mock_upload_file.content_type = "application/pdf"
        
        mock_form = {
            "file_io": mock_upload_file,
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "normal",
            "parse_target": "chunk"
        }
        
        # Add get method to mock form
        def mock_get(key, default=None):
            return mock_form.get(key, default)
        
        mock_form_obj = MagicMock()
        mock_form_obj.get = mock_get
        mock_form_obj.__getitem__ = lambda self, key: mock_form[key]
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "multipart/form-data; boundary=xxx"}
        mock_request.form.return_value = mock_form_obj
        
        mock_background_tasks = MagicMock()
        
        # Mock Redis5Dao properly
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        with patch('services.parse.PipelineFactory') as mock_pipeline_factory:
            mock_pipeline = AsyncMock()
            mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
            mock_result = MagicMock()
            mock_result.handler_results = {"chunk_handler": []}
            mock_pipeline.execute.return_value = mock_result
            
            # Act
            result = await general_parse_service(mock_request, mock_background_tasks)
            
            # Assert
            assert result.code == HTTPCODE.OK
            mock_pipeline_factory.create_document_pipeline.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.get_drive_rpc_client')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_with_download_id(self, mock_redis, mock_drive_client, mock_uuid):
        """Test parse service with download_id"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf", 
            "file_type": "pdf",
            "wps_company_id": "123",
            "download_id": "download123",
            "req_type": "normal",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_drive_client_instance = AsyncMock()
        mock_drive_client.return_value = mock_drive_client_instance
        mock_download_response = MagicMock()
        mock_download_response.is_success = True
        mock_download_response.url = "http://test.com/download.pdf"
        mock_drive_client_instance.aget_download_url.return_value = mock_download_response
        
        # Mock Redis5Dao properly
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        with patch('services.parse.PipelineFactory') as mock_pipeline_factory:
            mock_pipeline = AsyncMock()
            mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
            mock_result = MagicMock()
            mock_result.handler_results = {"chunk_handler": []}
            mock_pipeline.execute.return_value = mock_result
            
            # Act
            result = await general_parse_service(mock_request, mock_background_tasks)
            
            # Assert
            assert result.code == HTTPCODE.OK
            mock_drive_client.assert_called_with("wpsv5")
            mock_drive_client_instance.aget_download_url.assert_called_with("download123")

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.get_drive_rpc_client')
    async def test_general_parse_service_download_failure(self, mock_drive_client, mock_uuid):
        """Test parse service when download fails"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf", 
            "wps_company_id": "123",
            "wps_v5_file_id": "file123",
            "req_type": "normal",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_drive_client_instance = AsyncMock()
        mock_drive_client.return_value = mock_drive_client_instance
        mock_download_response = MagicMock()
        mock_download_response.is_success = False
        mock_download_response.error_message = "Download failed"
        mock_drive_client_instance.aget_download_url.return_value = mock_download_response
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR_PARAMS

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_pipeline_value_error(self, mock_redis, mock_pipeline_factory, mock_uuid):
        """Test parse service with pipeline ValueError"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "normal",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        # Mock Redis5Dao properly
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.side_effect = ValueError("page count exceeds limit")
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR_PAGESIZE_LIMIT

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.error_trace')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_generic_exception(self, mock_redis, mock_error_trace, mock_uuid):
        """Test parse service with generic exception"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.side_effect = Exception("Request error")
        
        mock_background_tasks = MagicMock()
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR
        mock_error_trace.assert_called_once()
        mock_redis_instance.aset.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    async def test_general_parse_service_invalid_content_type(self, mock_uuid):
        """Test parse service with invalid content type"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "text/plain"}
        
        mock_background_tasks = MagicMock()
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR_PARAMS

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    async def test_general_parse_service_missing_file_params(self, mock_uuid):
        """Test parse service with missing file parameters"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "normal",
            "parse_target": ["chunk"]
            # Missing file_url, download_id, and wps_v5_file_id
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR_PARAMS

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.PipelineFactory')
    async def test_general_parse_service_pipeline_returns_none(self, mock_pipeline_factory, mock_uuid):
        """Test parse service when pipeline returns None"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "normal",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.return_value = None  # Pipeline returns None
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.PipelineFactory')
    async def test_general_parse_service_pipeline_word_count_error(self, mock_pipeline_factory, mock_uuid):
        """Test parse service with pipeline word count ValueError"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "normal",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.side_effect = ValueError("word count exceeds limit")
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR_PAGESIZE_LIMIT

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.PipelineFactory')
    async def test_general_parse_service_pipeline_generic_value_error(self, mock_pipeline_factory, mock_uuid):
        """Test parse service with generic ValueError"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "normal",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline
        mock_pipeline.execute.side_effect = ValueError("Some other validation error")
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR



    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.KafkaProducerDao')
    @patch('services.parse.PassableLogContext')
    async def test_general_parse_service_queue_kafka_fail(self, mock_log_context, mock_kafka, mock_redis, mock_uuid):
        """Test queue parse service when kafka message send fails"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "queue",
            "req_level": "high",
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        mock_kafka_instance = AsyncMock()
        mock_kafka.return_value = mock_kafka_instance
        mock_kafka_instance.asend_message.return_value = False  # Kafka send fails
        
        # Mock PassableLogContext properly
        mock_log_context_instance = PassableLogContext(
            ip="127.0.0.1",
            req_type="test",
            request_id="123",
            local_id="456",
            index=1,
            uri="/test",
            trace_id="abc123",
            span_id="def456"
        )
        mock_log_context.return_value = mock_log_context_instance
        mock_log_context.dump.return_value = {
            "ip": "127.0.0.1",
            "req_type": "test",
            "request_id": "123",
            "local_id": "456",
            "index": 1,
            "uri": "/test",
            "trace_id": "abc123",
            "span_id": "def456"
        }
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR_KAFKA_MSG_SEND

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_multipart_missing_file_io(self, mock_redis, mock_uuid):
        """Test multipart form data with missing file_io"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        mock_form = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "normal",
            "parse_target": "chunk"
            # Missing file_io
        }
        
        # Add get method to mock form
        def mock_get(key, default=None):
            return mock_form.get(key, default)
        
        def mock_getitem(key):
            if key in mock_form:
                return mock_form[key]
            else:
                raise KeyError(key)
        
        mock_form_obj = MagicMock()
        mock_form_obj.get = mock_get
        mock_form_obj.__getitem__ = mock_getitem
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "multipart/form-data; boundary=xxx"}
        mock_request.form.return_value = mock_form_obj
        
        mock_background_tasks = MagicMock()
        
        # Mock Redis5Dao
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR

    @pytest.mark.asyncio
    @patch('services.parse.uuid.uuid4')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_service_invalid_req_type(self, mock_redis, mock_uuid):
        """Test parse service with invalid req_type"""
        # Arrange
        mock_uuid.return_value = "test-token"
        
        request_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "wps_company_id": "123",
            "req_type": "invalid_type",  # Invalid req_type
            "file_url": "http://test.com/file.pdf",
            "parse_target": ["chunk"]
        }
        
        mock_request = AsyncMock()
        mock_request.headers = {"Content-Type": "application/json"}
        mock_request.body.return_value = json.dumps(request_data).encode()
        
        mock_background_tasks = MagicMock()
        
        # Mock Redis5Dao
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Act
        result = await general_parse_service(mock_request, mock_background_tasks)
        
        # Assert
        assert result.code == HTTPCODE.ERROR


class TestGeneralParseResService:
    """Test general_parse_res_service function"""
    
    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.requests')
    async def test_general_parse_res_service_success(self, mock_requests, mock_redis):
        """Test successful parse result service"""
        # Arrange
        req = ReqGeneralParseRes(
            token="test-token",
            parse_target=[ParseTarget.chunk],
            return_ks3_url=False
        )
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.agetstring.side_effect = [
            GeneralParseStatus.ok,  # status
            "http://ks3.com/chunk_result.json"  # content url
        ]
        
        from modules.entity.chunk_entity import Chunk, LabelType
        
        chunk_data = {
            "chunk_id": "chunk1",
            "page_size": 10,
            "content": "test chunk",
            "label": "text",
            "page_num": [1],
            "block": ["block1"],
            "content_embedding": []
        }
        
        mock_response = Mock()
        mock_response.json.return_value = {
            "chunks": [chunk_data],
            "page_size": 10,
            "word_count": 1000,
            "rotate_page": {}
        }
        mock_requests.get.return_value = mock_response
        
        # Act
        result = await general_parse_res_service(req)
        
        # Assert
        assert result.code == HTTPCODE.OK
        assert result.data.status == GeneralParseStatus.ok
        assert result.data.parse_res is not None

    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.get_parse_target_status')
    async def test_general_parse_res_service_wait_status(self, mock_get_parse_target, mock_redis):
        """Test parse result service with wait status"""
        # Arrange
        req = ReqGeneralParseRes(token="test-token")
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.agetstring.return_value = GeneralParseStatus.wait
        
        mock_get_parse_target.return_value = []
        
        # Act
        result = await general_parse_res_service(req)
        
        # Assert
        assert result.code == HTTPCODE.OK
        assert result.data.status == GeneralParseStatus.wait

    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.get_parse_target_status')
    async def test_general_parse_res_service_limit_status(self, mock_get_parse_target, mock_redis):
        """Test parse result service with limit status"""
        # Arrange
        req = ReqGeneralParseRes(token="test-token")
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.agetstring.return_value = GeneralParseStatus.limit
        
        mock_get_parse_target.return_value = []
        
        # Act
        result = await general_parse_res_service(req)
        
        # Assert
        assert result.code == HTTPCODE.ERROR_PAGESIZE_LIMIT
        assert result.data.status == GeneralParseStatus.limit

    @pytest.mark.asyncio
    @patch('services.parse.error_trace')
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_res_service_exception(self, mock_redis, mock_error_trace):
        """Test parse result service with exception"""
        # Arrange
        req = ReqGeneralParseRes(token="test-token")
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.agetstring.side_effect = Exception("Redis error")
        
        # Act
        result = await general_parse_res_service(req)
        
        # Assert
        assert result.code == HTTPCODE.ERROR
        mock_error_trace.assert_called_once()

    @pytest.mark.asyncio 
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.requests')
    async def test_general_parse_res_service_with_return_ks3_url(self, mock_requests, mock_redis):
        """Test parse result service with return_ks3_url=True"""
        # Arrange
        req = ReqGeneralParseRes(
            token="test-token",
            parse_target=[ParseTarget.chunk],
            return_ks3_url=True,
            use_external_link=True
        )
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.agetstring.side_effect = [
            GeneralParseStatus.ok,  # status
            "http://ks3-internal.com/chunk_result.json"  # content url
        ]
        
        chunk_data = {
            "chunk_id": "chunk1",
            "page_size": 10,
            "content": "test chunk",
            "label": "text",
            "page_num": [1],
            "block": ["block1"],
            "content_embedding": []
        }
        
        mock_response = Mock()
        mock_response.json.return_value = {
            "chunks": [chunk_data],
            "rotate_page": {}
        }
        mock_requests.get.return_value = mock_response
        
        # Act
        result = await general_parse_res_service(req)
        
        # Assert
        assert result.code == HTTPCODE.OK
        assert result.data.status == GeneralParseStatus.ok
        # Should have res_ks3_url without '-internal'
        assert result.data.res_ks3_url == "http://ks3.com/chunk_result.json"

    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.get_parse_target_status')
    async def test_general_parse_res_service_auto_discover_targets(self, mock_get_parse_target, mock_redis):
        """Test parse result service auto-discovering parse targets"""
        # Arrange
        req = ReqGeneralParseRes(
            token="test-token",
            return_ks3_url=False
        )  # No parse_target specified
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        # Mock get_parse_target_status to return empty list so it uses auto discovery
        mock_get_parse_target.return_value = []
        
        # Create side effect that returns different values for different calls
        def mock_agetstring(key, default=""):
            if "status" in key:
                return GeneralParseStatus.ok
            elif "_chunk_" in key:
                return "http://ks3.com/chunk_result.json"
            elif "_keywords_" in key:
                return "http://ks3.com/keywords_result.json"
            elif "_fake_title_" in key:
                return ""
            elif "_summary_" in key:
                return ""
            elif "_screenshot_" in key:
                return ""
            else:
                return default
        
        mock_redis_instance.agetstring.side_effect = mock_agetstring
        
        with patch('services.parse.requests') as mock_requests:
            chunk_data = {
                "chunk_id": "chunk1",
                "page_size": 10,
                "content": "test chunk",
                "label": "text",
                "page_num": [1],
                "block": ["block1"],
                "content_embedding": []
            }
            
            mock_chunk_response = Mock()
            mock_chunk_response.json.return_value = {
                "chunks": [chunk_data],
                "rotate_page": {}
            }
            
            mock_keywords_response = Mock()
            mock_keywords_response.json.return_value = {
                "keywords": ["key1", "key2"],
                "rotate_page": {}
            }
            
            mock_requests.get.side_effect = [mock_chunk_response, mock_keywords_response]
            
            # Act
            result = await general_parse_res_service(req)
            
            # Assert
            assert result.code == HTTPCODE.OK
            assert result.data.status == GeneralParseStatus.ok
            assert ParseTarget.chunk in result.data.parse_target
            assert ParseTarget.keywords in result.data.parse_target

    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.requests')
    async def test_general_parse_res_service_all_parse_targets(self, mock_requests, mock_redis):
        """Test parse result service covering all parse target handlers"""
        # Arrange
        req = ReqGeneralParseRes(
            token="test-token",
            parse_target=[ParseTarget.fake_title, ParseTarget.summary, ParseTarget.screenshot],
            return_ks3_url=False
        )
        
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        
        def mock_agetstring(key, default=""):
            if "status" in key:
                return GeneralParseStatus.ok
            elif "_fake_title_" in key:
                return "http://ks3.com/fake_title_result.json"
            elif "_summary_" in key:
                return "http://ks3.com/summary_result.json"
            elif "_screenshot_" in key:
                return "http://ks3.com/screenshot_result.json"
            else:
                return ""
        
        mock_redis_instance.agetstring.side_effect = mock_agetstring
        
        # Mock different responses for different handlers
        fake_title_response = Mock()
        fake_title_response.json.return_value = {
            "fake_title": "Test Title",
            "fake_title_embedding": [0.1, 0.2],
            "rotate_page": {}
        }
        
        summary_response = Mock()
        summary_response.json.return_value = {
            "summary": "Test Summary",
            "summary_embedding": [0.3, 0.4],
            "rotate_page": {}
        }
        
        screenshot_response = Mock()
        screenshot_response.json.return_value = {
            "image": [{
                "page_num": 1,
                "url": "img1.png",
                "image_type": "input_image"
            }],
            "rotate_page": {}
        }
        
        mock_requests.get.side_effect = [fake_title_response, summary_response, screenshot_response]
        
        # Act
        result = await general_parse_res_service(req)
        
        # Assert
        assert result.code == HTTPCODE.OK
        assert result.data.status == GeneralParseStatus.ok
        assert result.data.parse_res.fake_title == "Test Title"
        assert result.data.parse_res.summary == "Test Summary"
        assert len(result.data.parse_res.image) == 1


if __name__ == "__main__":
    pytest.main([__file__])