"""
Unit tests for services.table_merge module
"""
import pytest
from unittest.mock import patch, MagicMock

from services.table_merge import merge_table
from services.datamodel import ReqMergeTable, RespMergeTableRes
from modules.entity.parse_entity import RespMergeTableData
from routers.httpcode import HTTPCODE


class TestTableMergeService:
    """表格合并服务的测试用例"""

    @pytest.mark.asyncio
    async def test_merge_table_success(self):
        """测试成功的表格合并"""
        # Mock the merge_table_by_force function
        with patch('services.table_merge.merge_table_by_force') as mock_merge:
            mock_merge.return_value = "<table><tr><td>merged content</td></tr></table>"
            
            request = ReqMergeTable(content=["<table><tr><td>table1</td></tr></table>", 
                                           "<table><tr><td>table2</td></tr></table>"])
            result = await merge_table(request)
            
            assert isinstance(result, RespMergeTableRes)
            assert result.code == HTTPCODE.OK
            assert result.data.content == ["<table><tr><td>merged content</td></tr></table>"]
            mock_merge.assert_called_once_with(request.content)

    @pytest.mark.asyncio
    async def test_merge_table_invalid_content_none(self):
        """测试内容为None时的表格合并服务（通过补丁模拟）"""
        # Since ReqMergeTable doesn't accept None, we'll test the service logic directly
        # by creating a mock request object with None content
        mock_request = MagicMock()
        mock_request.content = None

        result = await merge_table(mock_request)

        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.ERROR
        assert "Invalid content provided for merging" in result.message

    @pytest.mark.asyncio
    async def test_merge_table_invalid_content_empty(self):
        """测试表格合并处理空内容"""
        request = ReqMergeTable(content=[])
        result = await merge_table(request)
        
        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.ERROR
        assert "Invalid content provided for merging" in result.message

    @pytest.mark.asyncio
    async def test_merge_table_invalid_content_single_item(self):
        """测试表格合并处理单个项目（少于2个）"""
        request = ReqMergeTable(content=["<table><tr><td>single table</td></tr></table>"])
        result = await merge_table(request)
        
        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.ERROR
        assert "Invalid content provided for merging" in result.message

    @pytest.mark.asyncio
    async def test_merge_table_exception_handling(self):
        """测试表格合并异常处理"""
        with patch('services.table_merge.merge_table_by_force', side_effect=Exception("Merge error")):
            with patch('services.table_merge.error_trace') as mock_error_trace:
                request = ReqMergeTable(content=["table1", "table2"])
                result = await merge_table(request)
                
                assert isinstance(result, RespMergeTableRes)
                assert result.code == HTTPCODE.ERROR
                mock_error_trace.assert_called_once()

    @pytest.mark.asyncio
    async def test_merge_table_multiple_tables(self):
        """测试合并多个表格"""
        with patch('services.table_merge.merge_table_by_force') as mock_merge:
            mock_merge.return_value = "<table><tr><td>merged multiple</td></tr></table>"
            
            request = ReqMergeTable(content=[
                "<table><tr><td>table1</td></tr></table>",
                "<table><tr><td>table2</td></tr></table>",
                "<table><tr><td>table3</td></tr></table>"
            ])
            result = await merge_table(request)
            
            assert isinstance(result, RespMergeTableRes)
            assert result.code == HTTPCODE.OK
            assert len(result.data.content) == 1
            mock_merge.assert_called_once_with(request.content)

    @pytest.mark.asyncio
    async def test_merge_table_data_structure(self):
        """测试返回的数据具有正确的结构"""
        with patch('services.table_merge.merge_table_by_force') as mock_merge:
            mock_merge.return_value = "merged_table_html"
            
            request = ReqMergeTable(content=["table1", "table2"])
            result = await merge_table(request)
            
            # Check response structure
            assert hasattr(result, 'code')
            assert hasattr(result, 'data')
            assert isinstance(result.data, RespMergeTableData)
            assert hasattr(result.data, 'content')
            assert isinstance(result.data.content, list)

    @pytest.mark.asyncio
    async def test_merge_table_trace_span_decorator(self):
        """测试async_trace_span装饰器被应用"""
        with patch('services.table_merge.merge_table_by_force') as mock_merge:
            mock_merge.return_value = "test_table"
            
            request = ReqMergeTable(content=["table1", "table2"])
            result = await merge_table(request)
            assert result.code == HTTPCODE.OK
