"""
services.kafka_service 模块的单元测试
"""
import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock

from services.kafka_service import (
    get_kafka_lag, get_kafka_throughput, fetch_and_store_throughput,
    KafkaMsg, TopicLagInfo, ThroughputInfo, RespKafkaMsg
)
from routers.httpcode import HTTPCODE


class TestKafkaService:
    """kafka服务的测试用例"""

    @pytest.mark.asyncio
    async def test_get_kafka_lag_success(self):
        """测试成功获取kafka延迟"""
        mock_config = [
            {"topic": "test-topic-1", "consumer_group_id": "test-group-1"},
            {"topic": "test-topic-2", "consumer_group_id": "test-group-2"}
        ]
        
        with patch('conf.ConfKafka.parse_config', mock_config):
            with patch('services.kafka_service.KafkaConsumerMetrics') as mock_metrics:
                mock_instance = mock_metrics.return_value
                mock_instance.get_lag = AsyncMock(side_effect=[100, 200])
                
                result = await get_kafka_lag()
                
                assert isinstance(result, RespKafkaMsg)
                assert result.code == HTTPCODE.OK
                assert len(result.data) == 2
                assert result.data[0].topic == "test-topic-1"
                assert result.data[0].group_id == "test-group-1"
                assert result.data[0].total_lag == 100
                assert result.data[1].topic == "test-topic-2"
                assert result.data[1].group_id == "test-group-2"
                assert result.data[1].total_lag == 200

    @pytest.mark.asyncio
    async def test_get_kafka_lag_exception(self):
        """测试获取kafka延迟时的异常情况"""
        with patch('conf.ConfKafka.parse_config', [{"topic": "test", "consumer_group_id": "group"}]):
            with patch('services.kafka_service.KafkaConsumerMetrics') as mock_metrics:
                mock_instance = mock_metrics.return_value
                mock_instance.get_lag = AsyncMock(side_effect=Exception("Connection error"))
                
                with patch('services.kafka_service.error_trace') as mock_error_trace:
                    result = await get_kafka_lag()
                    
                    assert isinstance(result, RespKafkaMsg)
                    assert result.code == HTTPCODE.ERROR
                    mock_error_trace.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_kafka_throughput_success(self):
        """测试成功获取kafka吞吐量"""
        mock_throughput_data = [
            {"topic": "test-topic-1", "group_id": "test-group-1", "throughput": 10.5},
            {"topic": "test-topic-2", "group_id": "test-group-2", "throughput": 20.3}
        ]
        
        with patch('services.kafka_service.Redis5Dao') as mock_redis:
            mock_instance = mock_redis.return_value
            mock_instance.agetstring = AsyncMock(return_value=json.dumps(mock_throughput_data))
            
            result = await get_kafka_throughput()
            
            assert isinstance(result, RespKafkaMsg)
            assert result.code == HTTPCODE.OK
            assert len(result.data) == 2
            assert result.data[0].topic == "test-topic-1"
            assert result.data[0].group_id == "test-group-1"
            assert result.data[0].throughput == 10.5

    @pytest.mark.asyncio
    async def test_get_kafka_throughput_no_data(self):
        """测试Redis中无数据时的kafka吞吐量检索"""
        with patch('services.kafka_service.Redis5Dao') as mock_redis:
            mock_instance = mock_redis.return_value
            mock_instance.agetstring = AsyncMock(return_value="")
            
            result = await get_kafka_throughput()
            
            assert isinstance(result, RespKafkaMsg)
            assert result.code == HTTPCODE.ERROR
            assert "No throughput data found in Redis" in result.message

    @pytest.mark.asyncio
    async def test_get_kafka_throughput_exception(self):
        """测试kafka吞吐量检索时异常的情况"""
        with patch('services.kafka_service.Redis5Dao') as mock_redis:
            mock_instance = mock_redis.return_value
            mock_instance.agetstring = AsyncMock(side_effect=Exception("Redis error"))
            
            with patch('services.kafka_service.error_trace') as mock_error_trace:
                result = await get_kafka_throughput()
                
                assert isinstance(result, RespKafkaMsg)
                assert result.code == HTTPCODE.ERROR
                mock_error_trace.assert_called_once()

    @pytest.mark.asyncio
    async def test_fetch_and_store_throughput_success(self):
        """测试成功的吞吐量获取和存储"""
        mock_config = [{"topic": "test", "consumer_group_id": "group"}]
        mock_results = [{"topic": "test", "group_id": "group", "throughput": 15.0}]
        
        with patch('conf.ConfKafka.parse_config', mock_config):
            with patch('conf.ConfKafka.re_throughput_lock_key', 'lock_key'):
                with patch('conf.ConfKafka.re_throughput_key', 'throughput_key'):
                    with patch('services.kafka_service.Redis5Dao') as mock_redis:
                        mock_instance = mock_redis.return_value
                        mock_instance.set = MagicMock(return_value=True)  # Lock acquired
                        mock_instance.aset = AsyncMock()
                        
                        with patch('services.kafka_service.KafkaConsumerMetrics') as mock_metrics:
                            mock_metrics_instance = mock_metrics.return_value
                            mock_metrics_instance.get_throughput = AsyncMock(return_value=mock_results)
                            
                            await fetch_and_store_throughput()
                            
                            mock_instance.set.assert_called_once_with('lock_key', '1', ex=60, nx=True)
                            mock_instance.aset.assert_called_once_with('throughput_key', json.dumps(mock_results), ex=600)

    @pytest.mark.asyncio
    async def test_fetch_and_store_throughput_lock_not_acquired(self):
        """测试未获取锁时的吞吐量获取"""
        with patch('conf.ConfKafka.re_throughput_lock_key', 'lock_key'):
            with patch('services.kafka_service.Redis5Dao') as mock_redis:
                mock_instance = mock_redis.return_value
                mock_instance.set = MagicMock(return_value=False)  # Lock not acquired
                
                await fetch_and_store_throughput()
                
                mock_instance.set.assert_called_once_with('lock_key', '1', ex=60, nx=True)
                # Should not call aset since lock was not acquired
                assert not hasattr(mock_instance, 'aset') or not mock_instance.aset.called

    @pytest.mark.asyncio
    async def test_fetch_and_store_throughput_exception(self):
        """测试吞吐量获取时异常的情况"""
        with patch('conf.ConfKafka.re_throughput_lock_key', 'lock_key'):
            with patch('services.kafka_service.Redis5Dao') as mock_redis:
                mock_instance = mock_redis.return_value
                mock_instance.set = MagicMock(side_effect=Exception("Redis error"))
                
                with patch('services.kafka_service.error_trace') as mock_error_trace:
                    await fetch_and_store_throughput()
                    mock_error_trace.assert_called_once()

    def test_kafka_msg_model(self):
        """测试KafkaMsg模型"""
        msg = KafkaMsg(topic_name="test-topic", group_id="test-group")
        assert msg.topic_name == "test-topic"
        assert msg.group_id == "test-group"

    def test_topic_lag_info_model(self):
        """测试TopicLagInfo模型"""
        lag_info = TopicLagInfo(topic="test-topic", group_id="test-group", total_lag=100)
        assert lag_info.topic == "test-topic"
        assert lag_info.group_id == "test-group"
        assert lag_info.total_lag == 100

    def test_throughput_info_model(self):
        """测试ThroughputInfo模型"""
        throughput_info = ThroughputInfo(topic="test-topic", group_id="test-group", throughput=15.5)
        assert throughput_info.topic == "test-topic"
        assert throughput_info.group_id == "test-group"
        assert throughput_info.throughput == 15.5
