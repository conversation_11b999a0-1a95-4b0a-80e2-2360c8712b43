# Author: linqi
# Date: 2025/8/19
# Time: 11:09

import pytest
import asyncio
import json
import time
import threading
import signal
import os
from unittest.mock import AsyncMock, MagicMock, patch, Mock, PropertyMock
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor

from services.parse_consumer_kafka import (
    KafkaConsumerProcess,
    ParseKafka,
    ParseReProduct,
    process_worker
)
from services.parse import ParseKafkaMsg
from commons.logger.logger import PassableLogContext


class TestKafkaConsumerProcess:
    """Test KafkaConsumerProcess class"""
    
    def test_init(self):
        """Test KafkaConsumerProcess initialization"""
        bootstrap_servers = ["localhost:9092"]
        topic = "test-topic"
        group_id = "test-group"
        worker_num = 2
        task_num = 3
        
        process = KafkaConsumerProcess(
            bootstrap_servers=bootstrap_servers,
            topic=topic,
            group_id=group_id,
            worker_num=worker_num,
            task_num=task_num
        )
        
        assert process.bootstrap_servers == bootstrap_servers
        assert process.topic == topic
        assert process.group_id == group_id
        assert process.worker_num == worker_num
        assert process.task_num == task_num
        assert process.consumer is None
        assert process.should_stop is False
        assert process.running_futures == []

    @patch('services.parse_consumer_kafka.ProcessPoolExecutor')
    @patch('services.parse_consumer_kafka.ThreadPoolExecutor')
    @patch('services.parse_consumer_kafka._hooked')
    def test_start_worker_process_with_hook(self, mock_hooked, mock_thread_pool, mock_process_pool):
        """Test _start_worker_process with hook enabled"""
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group",
            worker_num=2
        )
        
        mock_process_pool_instance = MagicMock()
        mock_process_pool.return_value = mock_process_pool_instance
        mock_thread_pool_instance = MagicMock()
        mock_thread_pool.return_value = mock_thread_pool_instance
        
        process._start_worker_process(hook=True)
        
        mock_hooked.assert_called_once()
        mock_process_pool.assert_called_once_with(2, initializer=None, initargs=())
        mock_thread_pool.assert_called_once_with(2)
        assert process.process_pool == mock_process_pool_instance
        assert process.thread_pool == mock_thread_pool_instance

    @patch('services.parse_consumer_kafka.ProcessPoolExecutor')
    @patch('services.parse_consumer_kafka.ThreadPoolExecutor')
    def test_start_worker_process_no_workers(self, mock_thread_pool, mock_process_pool):
        """Test _start_worker_process with no workers"""
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group",
            worker_num=0
        )
        
        process._start_worker_process(hook=False)
        
        mock_process_pool.assert_not_called()
        mock_thread_pool.assert_not_called()

    @patch('services.parse_consumer_kafka.asyncio.get_event_loop')
    @patch('services.parse_consumer_kafka.background_general_parse')
    @patch('services.parse_consumer_kafka.PassableLogContext')
    def test_consume_task_success(self, mock_log_context, mock_background_parse, mock_get_loop):
        """Test _consume_task successful execution"""
        # Arrange
        msg_value = {
            "file_url": "http://test.com/file.pdf",
            "body": {
                "file_name": "test.pdf",
                "file_type": "pdf",
                "wps_company_id": "123"
            },
            "token": "test-token",
            "wiki_branch": "main"
        }
        
        msg = MagicMock()
        msg.value = msg_value
        
        mock_parse_msg = MagicMock()
        mock_parse_msg.log_context = PassableLogContext(
            ip="127.0.0.1", 
            req_type="test", 
            request_id="123", 
            local_id="456", 
            index=1, 
            uri="/test",
            trace_id="abc123",
            span_id="def456"
        )
        mock_parse_msg.token = "test-token"
        mock_parse_msg.body = msg_value["body"]
        mock_parse_msg.file_url = msg_value["file_url"]
        mock_parse_msg.wiki_branch = msg_value["wiki_branch"]
        
        mock_loop = MagicMock()
        mock_get_loop.return_value = mock_loop
        
        with patch('services.parse_consumer_kafka.ParseKafkaMsg.model_validate', return_value=mock_parse_msg):
            # Act
            KafkaConsumerProcess._consume_task(msg, "test-group")
            
            # Assert
            mock_loop.run_until_complete.assert_called_once()
            mock_background_parse.assert_called_once()

    @patch('services.parse_consumer_kafka.asyncio.get_event_loop')
    @patch('services.parse_consumer_kafka.background_general_parse')
    @patch('services.parse_consumer_kafka.PassableLogContext')
    def test_consume_task_exception(self, mock_log_context, mock_background_parse, mock_get_loop):
        """Test _consume_task with exception"""
        # Arrange
        msg_value = {
            "token": "test-token"
        }
        
        msg = MagicMock()
        msg.value = msg_value
        
        mock_get_loop.side_effect = Exception("Loop error")
        
        with patch('services.parse_consumer_kafka.ParseKafkaMsg.model_validate') as mock_model_validate:
            mock_model_validate.side_effect = Exception("Parse error")
            
            # Act (should not raise exception)
            KafkaConsumerProcess._consume_task(msg, "test-group")
            
            # No assertion needed - just testing it doesn't crash

    @patch('services.parse_consumer_kafka.asyncio.get_event_loop')
    @patch('services.parse_consumer_kafka.background_general_parse')
    @patch('services.parse_consumer_kafka.PassableLogContext')
    def test_consume_task_no_log_context(self, mock_log_context, mock_background_parse, mock_get_loop):
        """Test _consume_task with no log context"""
        # Arrange
        msg_value = {
            "file_url": "http://test.com/file.pdf",
            "body": {
                "file_name": "test.pdf",
                "file_type": "pdf",
                "wps_company_id": "123"
            },
            "token": "test-token",
            "wiki_branch": "main"
        }
        
        msg = MagicMock()
        msg.value = msg_value
        
        mock_parse_msg = MagicMock()
        mock_parse_msg.log_context = None  # No log context
        mock_parse_msg.token = "test-token"
        mock_parse_msg.body = msg_value["body"]
        mock_parse_msg.file_url = msg_value["file_url"]
        mock_parse_msg.wiki_branch = msg_value["wiki_branch"]
        
        mock_loop = MagicMock()
        mock_get_loop.return_value = mock_loop
        
        mock_default_context = MagicMock()
        mock_default_context.export.return_value = None  # Ensure export returns None, not a coroutine
        mock_log_context.return_value = mock_default_context
        
        with patch('services.parse_consumer_kafka.ParseKafkaMsg.model_validate', return_value=mock_parse_msg):
            # Act
            KafkaConsumerProcess._consume_task(msg, "test-group")
            
            # Assert
            mock_log_context.assert_called_once_with(ip="", req_type="", request_id="", local_id="", index=0, uri="")
            mock_default_context.export.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse_consumer_kafka.KafkaConsumerDao')
    @patch('services.parse_consumer_kafka.asyncio.sleep', new_callable=AsyncMock)
    async def test_consume_success(self, mock_sleep, mock_kafka_dao):
        """Test consume method success"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group",
            worker_num=2,
            task_num=2
        )
        
        mock_consumer = AsyncMock()
        mock_kafka_dao_instance = AsyncMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        mock_kafka_dao_instance.get_consumer.return_value = mock_consumer
        
        # Mock sleep to stop after first call
        async def mock_sleep_side_effect(*args):
            process.should_stop = True
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act
        await process.consume()
        
        # Assert
        mock_kafka_dao.assert_called_once_with(
            ["localhost:9092"],
            ["test-topic"],
            "test-group"
        )
        mock_kafka_dao_instance.get_consumer.assert_called_once()
        mock_consumer.stop.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse_consumer_kafka.KafkaConsumerDao')
    @patch('services.parse_consumer_kafka.asyncio.sleep', new_callable=AsyncMock)
    async def test_consume_with_messages(self, mock_sleep, mock_kafka_dao):
        """Test consume method processing messages"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group",
            worker_num=2,
            task_num=2
        )
        
        # Mock process pool
        process.process_pool = MagicMock()
        mock_future = MagicMock()
        process.process_pool.submit.return_value = mock_future
        
        mock_msg = MagicMock()
        mock_msg.value = {"token": "test-token"}
        mock_msg.topic = "test-topic"
        mock_msg.partition = 0
        mock_msg.offset = 123
        
        mock_consumer = AsyncMock()
        
        # 创建一个计数器来控制迭代次数
        message_count = 0
        async def mock_aiter(self):
            nonlocal message_count
            message_count += 1
            if message_count == 1:
                yield mock_msg
            # 在第二次迭代时设置should_stop
            elif message_count == 2:
                process.should_stop = True
                # 结束迭代器，不再yield
                return
        
        mock_consumer.__aiter__ = mock_aiter
        mock_consumer.stop = AsyncMock()
        mock_consumer.commit = AsyncMock()
        
        mock_kafka_dao_instance = AsyncMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        mock_kafka_dao_instance.get_consumer.return_value = mock_consumer
        
        # Mock sleep to return immediately and set should_stop
        async def mock_sleep_side_effect(*args):
            process.should_stop = True
        mock_sleep.side_effect = mock_sleep_side_effect
        
        with patch.object(process, '_task_record', return_value=threading.Event()) as mock_task_record:
            # Act
            await process.consume()
            
            # Assert
            process.process_pool.submit.assert_called_once()
            mock_consumer.commit.assert_called_once()
            mock_task_record.assert_called_once_with("test-token", {"token": "test-token"})

    @pytest.mark.asyncio
    @patch('services.parse_consumer_kafka.KafkaConsumerDao')
    @patch('services.parse_consumer_kafka.asyncio.sleep', new_callable=AsyncMock)
    async def test_consume_with_exception_in_loop(self, mock_sleep, mock_kafka_dao):
        """Test consume method with exception in message processing loop"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group",
            worker_num=2,
            task_num=2
        )
        
        # Mock process pool
        process.process_pool = MagicMock()
        mock_future = MagicMock()
        process.process_pool.submit.return_value = mock_future
        
        mock_msg = MagicMock()
        mock_msg.value = {}  # Missing token to trigger exception
        mock_msg.topic = "test-topic"
        mock_msg.partition = 0
        mock_msg.offset = 123
        
        mock_consumer = AsyncMock()
        
        # 创建一个会触发异常然后停止的迭代器
        call_count = 0
        async def mock_aiter(self):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                yield mock_msg
            elif call_count == 2:
                process.should_stop = True
                return
        
        mock_consumer.__aiter__ = mock_aiter
        mock_consumer.stop = AsyncMock()
        mock_consumer.commit = AsyncMock()
        
        mock_kafka_dao_instance = AsyncMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        mock_kafka_dao_instance.get_consumer.return_value = mock_consumer
        
        # Mock sleep to return immediately
        async def mock_sleep_side_effect(*args):
            if args[0] == 5:  # The sleep in exception handling
                process.should_stop = True
        mock_sleep.side_effect = mock_sleep_side_effect
        
        with patch.object(process, '_task_record', return_value=threading.Event()) as mock_task_record:
            with patch.object(process, '_task_clear') as mock_task_clear:
                # Act
                await process.consume()
                
                # Assert - should handle exception gracefully
                # The exception in processing should be caught
                pass

    @pytest.mark.asyncio
    @patch('services.parse_consumer_kafka.KafkaConsumerDao')
    @patch('services.parse_consumer_kafka.asyncio.sleep', new_callable=AsyncMock)
    async def test_consume_kafka_exception(self, mock_sleep, mock_kafka_dao):
        """Test consume method with kafka consumer exception"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group",
            worker_num=2,
            task_num=2
        )
        
        mock_kafka_dao_instance = AsyncMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        mock_kafka_dao_instance.get_consumer.side_effect = Exception("Kafka connection error")
        
        # Mock sleep to stop after first exception
        call_count = 0
        async def mock_sleep_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count >= 1:  # Stop after first exception handling sleep
                process.should_stop = True
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act
        try:
            await process.consume()
        except Exception:
            # Expected to catch exception in some cases
            pass
        
        # Assert
        mock_kafka_dao.assert_called()

    @pytest.mark.asyncio
    async def test_future_done_roll_empty_futures(self):
        """Test _futrue_done_roll with empty futures list"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        process.running_futures = []  # Empty futures list
        
        # Stop after one iteration
        call_count = 0
        async def mock_sleep_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count >= 2:
                process.should_stop = True
        
        with patch('services.parse_consumer_kafka.asyncio.sleep', side_effect=mock_sleep_side_effect):
            # Act
            await process._futrue_done_roll()
        
        # Assert - should handle empty list gracefully
        pass

    @pytest.mark.asyncio
    async def test_future_done_roll(self):
        """Test _futrue_done_roll method"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        mock_future = MagicMock()
        mock_future.done.return_value = True
        mock_event = threading.Event()
        
        process.running_futures = [("test-token", mock_future, mock_event)]
        
        with patch.object(process, '_task_clear') as mock_task_clear:
            # Stop after one iteration
            async def side_effect_stop(*args):
                process.should_stop = True
            
            mock_task_clear.side_effect = side_effect_stop
            
            # Act
            await process._futrue_done_roll()
            
            # Assert
            mock_task_clear.assert_called_once_with("test-token", mock_event)
            assert process.running_futures == []

    @pytest.mark.asyncio
    @patch('services.parse_consumer_kafka.json.dumps')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao.add_record')
    async def test_task_record_success(self, mock_add_record, mock_sqlsession, mock_json_dumps):
        """Test _task_record successful execution"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        process.thread_pool = MagicMock()
        
        mock_json_dumps.return_value = '{"token": "test-token"}'
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Act
        result = await process._task_record("test-token", {"token": "test-token"})
        
        # Assert
        assert isinstance(result, threading.Event)
        mock_add_record.assert_called_once_with(mock_db, "test-token", '{"token": "test-token"}')
        process.thread_pool.submit.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse_consumer_kafka.json.dumps')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao.add_record')
    async def test_task_record_exception(self, mock_add_record, mock_sqlsession, mock_json_dumps):
        """Test _task_record with exception"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        mock_add_record.side_effect = Exception("DB error")
        mock_json_dumps.return_value = '{"token": "test-token"}'
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Act
        result = await process._task_record("test-token", {"token": "test-token"})
        
        # Assert
        assert isinstance(result, threading.Event)
        # Should not crash on exception

    @patch('services.parse_consumer_kafka.time.sleep')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao.record_heartbeat')
    def test_task_heartbeat(self, mock_record_heartbeat, mock_sqlsession, mock_sleep):
        """Test _task_heartbeat method"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        mock_event = threading.Event()
        mock_event.set()  # Set event immediately to exit loop before sleep
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Act
        process._task_heartbeat("test-token", mock_event)
        
        # Assert
        mock_record_heartbeat.assert_not_called()  # Should not be called if event is already set
        mock_sleep.assert_not_called()  # Should exit before sleep due to event being set

    @pytest.mark.asyncio
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao.del_record')
    async def test_task_clear(self, mock_del_record, mock_sqlsession):
        """Test _task_clear method"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        mock_event = MagicMock()
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Act
        await process._task_clear("test-token", mock_event)
        
        # Assert
        mock_event.set.assert_called_once()
        mock_del_record.assert_called_once_with(mock_db, "test-token")

    @patch('services.parse_consumer_kafka.signal.signal')
    def test_setup_signal_handlers(self, mock_signal):
        """Test _setup_signal_handlers method"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        # Act
        process._setup_signal_handlers()
        
        # Assert
        assert mock_signal.call_count == 2
        # Test that signal handler sets should_stop to True
        signal_handler = mock_signal.call_args_list[0][0][1]
        signal_handler(signal.SIGINT, None)
        assert process.should_stop is True

    @patch('services.parse_consumer_kafka.asyncio.get_event_loop')
    @patch.object(KafkaConsumerProcess, '_start_worker_process')
    def test_run_success(self, mock_start_worker, mock_get_loop):
        """Test run method success"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        mock_loop = MagicMock()
        mock_get_loop.return_value = mock_loop
        
        # Mock the async methods that get created as tasks
        mock_stop_consumer_task = MagicMock()
        mock_future_done_roll_task = MagicMock()
        
        def mock_create_task(coro):
            # Return a mock task instead of creating real coroutines
            return MagicMock()
        
        mock_loop.create_task.side_effect = mock_create_task
        
        # Stop immediately to avoid infinite loop
        async def mock_consume():
            process.should_stop = True
        
        with patch.object(process, 'consume', side_effect=mock_consume):
            # Act
            process.run(hook=True)
            
            # Assert
            mock_start_worker.assert_called_once_with(True)
            assert mock_loop.create_task.call_count == 2  # _stop_consumer and _futrue_done_roll
            mock_loop.run_until_complete.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_consumer(self):
        """Test _stop_consumer method"""
        # Arrange
        process = KafkaConsumerProcess(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group",
            worker_num=2
        )
        
        mock_consumer = AsyncMock()
        process.consumer = mock_consumer
        
        mock_process_pool = MagicMock()
        mock_thread_pool = MagicMock()
        process.process_pool = mock_process_pool
        process.thread_pool = mock_thread_pool
        
        process.should_stop = True  # Stop immediately
        
        # Act
        await process._stop_consumer()
        
        # Assert
        mock_consumer.stop.assert_called_once()
        mock_process_pool.shutdown.assert_called_once()
        mock_thread_pool.shutdown.assert_called_once()


class TestProcessWorker:
    """Test process_worker function"""
    
    def test_process_worker(self):
        """Test process_worker function"""
        # Arrange
        mock_task = MagicMock()
        
        # Act
        process_worker(mock_task, hook=True)
        
        # Assert
        mock_task.run.assert_called_once_with(True)


class TestParseKafka:
    """Test ParseKafka class"""
    
    def test_init(self):
        """Test ParseKafka initialization"""
        bootstrap_servers = ["localhost:9092"]
        topic = "test-topic"
        group_id = "test-group"
        
        kafka = ParseKafka(
            bootstrap_servers=bootstrap_servers,
            topic=topic,
            group_id=group_id
        )
        
        assert kafka.bootstrap_servers == bootstrap_servers
        assert kafka.topic == topic
        assert kafka.group_id == group_id
        assert kafka.initializer is None

    @patch('services.parse_consumer_kafka.MultiProcess')
    def test_consumer_by_multiprocess(self, mock_multi_process):
        """Test consumer_by_multiprocess method"""
        # Arrange
        kafka = ParseKafka(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        mock_multi_process_instance = MagicMock()
        mock_multi_process.return_value = mock_multi_process_instance
        
        # Act
        kafka.consumer_by_multiprocess(
            worker_num=2,
            task_num=3,
            hook=True,
            initializer=lambda: None,
            initargs=("arg1",)
        )
        
        # Assert
        mock_multi_process_instance.submit.assert_called_once()
        args = mock_multi_process_instance.submit.call_args[0]
        assert args[0] == process_worker
        assert isinstance(args[1], KafkaConsumerProcess)
        assert args[2] is True

    @patch('services.parse_consumer_kafka.MultiProcess')
    def test_consumer_by_multiprocess_no_workers(self, mock_multi_process):
        """Test consumer_by_multiprocess with no workers"""
        # Arrange
        kafka = ParseKafka(
            bootstrap_servers=["localhost:9092"],
            topic="test-topic",
            group_id="test-group"
        )
        
        mock_multi_process_instance = MagicMock()
        mock_multi_process.return_value = mock_multi_process_instance
        
        # Act
        kafka.consumer_by_multiprocess(worker_num=0)
        
        # Assert
        mock_multi_process_instance.submit.assert_not_called()


class TestParseReProduct:
    """Test ParseReProduct singleton class"""
    
    def test_singleton(self):
        """Test ParseReProduct is a singleton"""
        instance1 = ParseReProduct()
        instance2 = ParseReProduct()
        assert instance1 is instance2

    def test_init(self):
        """Test ParseReProduct initialization"""
        instance = ParseReProduct()
        assert instance.running is False
        assert instance.dist_lock_key == ""

    def test_init_with_params(self):
        """Test ParseReProduct init method"""
        instance = ParseReProduct()
        instance.init("test_lock_key")
        
        assert instance.running is False
        assert instance.dist_lock_key == "test_lock_key"

    @patch('services.parse_consumer_kafka.threading.Thread')
    def test_re_product_start(self, mock_thread):
        """Test re_product_start method"""
        # Arrange
        instance = ParseReProduct()
        mock_thread_instance = MagicMock()
        mock_thread.return_value = mock_thread_instance
        
        # Act
        instance.re_product_start()
        
        # Assert
        mock_thread.assert_called_once()
        mock_thread_instance.start.assert_called_once()

    @patch('services.parse_consumer_kafka.time.sleep')
    @patch('services.parse_consumer_kafka.Redis5Dao')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao')
    @patch('services.parse_consumer_kafka.KafkaProducerDao')
    def test_re_product_success(self, mock_kafka_dao, mock_parse_dao, mock_sqlsession, mock_redis, mock_sleep):
        """Test _re_product successful execution"""
        # Arrange
        instance = ParseReProduct()
        instance.dist_lock_key = "test_lock_key"
        
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        # First call returns True (acquired lock), second call returns False (exit loop)
        mock_redis_instance.set.side_effect = [True, False]
        
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Create a mock record
        mock_record = MagicMock()
        mock_record.token = "test-token"
        mock_record.record_json = '{"test": "data"}'
        mock_record.key_id = 1
        mock_record.mtime = datetime.now() - timedelta(minutes=10)  # Expired record
        
        mock_parse_dao_instance = MagicMock()
        mock_parse_dao.return_value = mock_parse_dao_instance
        mock_parse_dao_instance.scan_records.return_value = [mock_record]
        
        mock_kafka_dao_instance = MagicMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        mock_kafka_dao_instance.send_message.return_value = True
        
        # Mock sleep to exit the infinite loop after first call
        call_count = 0
        def mock_sleep_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count >= 1:
                # Exit after the first sleep call by raising KeyboardInterrupt
                raise KeyboardInterrupt("Test timeout - stopping infinite loop")
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act & Assert
        try:
            instance._re_product()
        except KeyboardInterrupt:
            # Expected behavior - the infinite loop was stopped
            pass
        
        # Assert
        mock_redis_instance.set.assert_called_with("test_lock_key", "1", ex=600, nx=True)
        mock_parse_dao_instance.scan_records.assert_called()
        mock_parse_dao_instance.del_record.assert_called_with(mock_db, "test-token")
        mock_kafka_dao_instance.send_message.assert_called_with("high", {"test": "data"})

    @patch('services.parse_consumer_kafka.time.sleep')
    @patch('services.parse_consumer_kafka.Redis5Dao')
    def test_re_product_no_lock(self, mock_redis, mock_sleep):
        """Test _re_product when lock is not acquired"""
        # Arrange
        instance = ParseReProduct()
        instance.dist_lock_key = "test_lock_key"
        
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.set.return_value = False  # Lock not acquired
        
        # Stop after first sleep call by raising an exception
        def mock_sleep_side_effect(*args):
            # Simulate stopping the infinite loop by raising StopIteration
            raise KeyboardInterrupt("Test timeout - stopping infinite loop")
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act & Assert
        try:
            instance._re_product()
        except KeyboardInterrupt:
            # Expected behavior - the infinite loop was stopped
            pass
        
        # Assert
        mock_redis_instance.set.assert_called_with("test_lock_key", "1", ex=600, nx=True)
        assert not instance.running

    @patch('services.parse_consumer_kafka.time.sleep')
    @patch('services.parse_consumer_kafka.Redis5Dao')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao')
    def test_re_product_exception_handling(self, mock_parse_dao, mock_sqlsession, mock_redis, mock_sleep):
        """Test _re_product exception handling"""
        # Arrange
        instance = ParseReProduct()
        instance.dist_lock_key = "test_lock_key"
        
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        # First call returns True (acquired lock), second call should exit loop
        mock_redis_instance.set.side_effect = [True, False]
        
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        mock_parse_dao_instance = MagicMock()
        mock_parse_dao.return_value = mock_parse_dao_instance
        mock_parse_dao_instance.scan_records.side_effect = Exception("DB error")
        
        # Mock sleep to exit the infinite loop
        call_count = 0
        def mock_sleep_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count >= 1:
                # Exit after the first sleep call by raising KeyboardInterrupt
                raise KeyboardInterrupt("Test timeout - stopping infinite loop")
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act (should not crash)
        try:
            instance._re_product()
        except KeyboardInterrupt:
            # Expected behavior - the infinite loop was stopped
            pass
        
        # Assert
        mock_redis_instance.remove.assert_called_with("test_lock_key")

    @patch('services.parse_consumer_kafka.time.sleep')
    @patch('services.parse_consumer_kafka.Redis5Dao')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao')
    @patch('services.parse_consumer_kafka.KafkaProducerDao')
    def test_re_product_recent_record(self, mock_kafka_dao, mock_parse_dao, mock_sqlsession, mock_redis, mock_sleep):
        """Test _re_product with recent records that shouldn't be reprocessed"""
        # Arrange
        instance = ParseReProduct()
        instance.dist_lock_key = "test_lock_key"
        
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        # First call returns True (acquired lock)
        mock_redis_instance.set.return_value = True
        
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Create a mock record that's recent (should be skipped)
        mock_record = MagicMock()
        mock_record.token = "test-token"
        mock_record.record_json = '{"test": "data"}'
        mock_record.key_id = 1
        mock_record.mtime = datetime.now() - timedelta(minutes=2)  # Recent record (< 5 min)
        
        mock_parse_dao_instance = MagicMock()
        mock_parse_dao.return_value = mock_parse_dao_instance
        mock_parse_dao_instance.scan_records.return_value = [mock_record]
        
        mock_kafka_dao_instance = MagicMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        
        # Mock sleep to exit after processing by raising KeyboardInterrupt
        call_count = 0
        def mock_sleep_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count >= 1:
                raise KeyboardInterrupt("Test exit")
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act
        try:
            instance._re_product()
        except KeyboardInterrupt:
            pass
        
        # Assert
        mock_parse_dao_instance.del_record.assert_not_called()  # Should not delete recent record
        mock_kafka_dao_instance.send_message.assert_not_called()  # Should not resend recent record

    @patch('services.parse_consumer_kafka.time.sleep')
    @patch('services.parse_consumer_kafka.Redis5Dao')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao')
    @patch('services.parse_consumer_kafka.KafkaProducerDao')
    def test_re_product_kafka_send_failure(self, mock_kafka_dao, mock_parse_dao, mock_sqlsession, mock_redis, mock_sleep):
        """Test _re_product when kafka send fails"""
        # Arrange
        instance = ParseReProduct()
        instance.dist_lock_key = "test_lock_key"
        
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        # First call returns True (acquired lock)
        mock_redis_instance.set.return_value = True
        
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Create a mock record
        mock_record = MagicMock()
        mock_record.token = "test-token"
        mock_record.record_json = '{"test": "data"}'
        mock_record.key_id = 1
        mock_record.mtime = datetime.now() - timedelta(minutes=10)  # Expired record
        
        mock_parse_dao_instance = MagicMock()
        mock_parse_dao.return_value = mock_parse_dao_instance
        mock_parse_dao_instance.scan_records.return_value = [mock_record]
        mock_parse_dao_instance.exist_record.return_value = False
        
        mock_kafka_dao_instance = MagicMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        mock_kafka_dao_instance.send_message.return_value = False  # Kafka send fails
        
        # Mock sleep to exit after processing by raising KeyboardInterrupt
        call_count = 0
        def mock_sleep_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count >= 1:
                raise KeyboardInterrupt("Test exit")
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act
        try:
            instance._re_product()
        except KeyboardInterrupt:
            pass
        
        # Assert
        mock_parse_dao_instance.add_record.assert_called()  # Should re-add record on kafka failure

    @patch('services.parse_consumer_kafka.time.sleep')
    @patch('services.parse_consumer_kafka.Redis5Dao')
    @patch('services.parse_consumer_kafka.sqlsession')
    @patch('services.parse_consumer_kafka.ParseRecordDao')
    @patch('services.parse_consumer_kafka.KafkaProducerDao')
    def test_re_product_record_processing_exception(self, mock_kafka_dao, mock_parse_dao, mock_sqlsession, mock_redis, mock_sleep):
        """Test _re_product when individual record processing fails"""
        # Arrange
        instance = ParseReProduct()
        instance.dist_lock_key = "test_lock_key"
        
        mock_redis_instance = MagicMock()
        mock_redis.return_value = mock_redis_instance
        # First call returns True (acquired lock)
        mock_redis_instance.set.return_value = True
        
        mock_db = MagicMock()
        mock_sqlsession.return_value.__enter__ = Mock(return_value=mock_db)
        mock_sqlsession.return_value.__exit__ = Mock(return_value=None)
        
        # Create a mock record
        mock_record = MagicMock()
        mock_record.token = "test-token"
        mock_record.record_json = '{"test": "data"}'
        mock_record.key_id = 1
        mock_record.mtime = datetime.now() - timedelta(minutes=10)  # Expired record
        
        mock_parse_dao_instance = MagicMock()
        mock_parse_dao.return_value = mock_parse_dao_instance
        mock_parse_dao_instance.scan_records.return_value = [mock_record]
        mock_parse_dao_instance.del_record.side_effect = Exception("Delete error")  # Exception in processing
        mock_parse_dao_instance.exist_record.return_value = False
        
        mock_kafka_dao_instance = MagicMock()
        mock_kafka_dao.return_value = mock_kafka_dao_instance
        
        # Mock sleep to exit after processing by raising KeyboardInterrupt
        call_count = 0
        def mock_sleep_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count >= 1:
                raise KeyboardInterrupt("Test exit")
        mock_sleep.side_effect = mock_sleep_side_effect
        
        # Act
        try:
            instance._re_product()
        except KeyboardInterrupt:
            pass
        
        # Assert
        mock_parse_dao_instance.add_record.assert_called()  # Should re-add record on exception


if __name__ == "__main__":
    pytest.main([__file__])