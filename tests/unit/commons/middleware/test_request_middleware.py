"""
commons.middleware.request_middleware 模块的测试
"""
import pytest
import secrets
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request
from starlette.responses import Response
from starlette.datastructures import Headers, State

from commons.middleware.request_middleware import RequestIDMiddleware


class TestRequestIDMiddleware:
    """测试请求ID中间件"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.mock_app = Mock()
        self.middleware = RequestIDMiddleware(self.mock_app)

    def test_init(self):
        """测试中间件初始化"""
        assert self.middleware.app == self.mock_app

    @pytest.mark.asyncio
    async def test_dispatch_with_existing_request_id(self):
        """测试处理已有请求ID的请求"""
        # 创建模拟请求，包含现有的 request ID
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({"x-request-id": "existing-request-id-123"})
        mock_request.state = State()
        
        # 创建模拟响应
        mock_response = Mock(spec=Response)
        mock_response.headers = {}
        
        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response
        
        # 执行中间件
        result = await self.middleware.dispatch(mock_request, mock_call_next)
        
        # 验证结果
        assert result == mock_response
        assert mock_request.state.request_id == "existing-request-id-123"
        assert mock_response.headers["x-request-id"] == "existing-request-id-123"

    @pytest.mark.asyncio
    async def test_dispatch_without_request_id(self):
        """测试处理没有请求ID的请求"""
        # 创建模拟请求，不包含 request ID
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({})
        mock_request.state = State()
        
        # 创建模拟响应
        mock_response = Mock(spec=Response)
        mock_response.headers = {}
        
        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response
        
        # 模拟 secrets.token_hex 生成固定ID
        with patch('commons.middleware.request_middleware.secrets.token_hex', return_value="generated-id-123456789abcdef"):
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            # 验证结果
            assert result == mock_response
            assert mock_request.state.request_id == "generated-id-123456789abcdef"
            assert mock_response.headers["x-request-id"] == "generated-id-123456789abcdef"

    @pytest.mark.asyncio
    async def test_dispatch_with_empty_request_id(self):
        """测试处理空请求ID的请求"""
        # 创建模拟请求，包含空的 request ID
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({"x-request-id": ""})
        mock_request.state = State()
        
        # 创建模拟响应
        mock_response = Mock(spec=Response)
        mock_response.headers = {}
        
        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response
        
        # 模拟 secrets.token_hex 生成固定ID
        with patch('commons.middleware.request_middleware.secrets.token_hex', return_value="empty-replacement-id"):
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            # 验证空ID被替换
            assert result == mock_response
            assert mock_request.state.request_id == "empty-replacement-id"
            assert mock_response.headers["x-request-id"] == "empty-replacement-id"

    @pytest.mark.asyncio
    async def test_dispatch_with_none_request_id(self):
        """测试处理 None 请求ID的请求"""
        # 创建模拟请求，不包含 x-request-id 头部（模拟 None 情况）
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({})  # 空头部，get 会返回 None
        mock_request.state = State()

        # 创建模拟响应
        mock_response = Mock(spec=Response)
        mock_response.headers = {}

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response

        # 模拟 secrets.token_hex 生成固定ID
        with patch('commons.middleware.request_middleware.secrets.token_hex', return_value="none-replacement-id"):
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # 验证 None ID 被替换
            assert result == mock_response
            assert mock_request.state.request_id == "none-replacement-id"
            assert mock_response.headers["x-request-id"] == "none-replacement-id"

    @pytest.mark.asyncio
    async def test_dispatch_case_insensitive_header(self):
        """测试请求头大小写不敏感"""
        # 测试不同大小写的请求头
        test_cases = [
            "x-request-id",
            "X-Request-Id", 
            "X-REQUEST-ID",
            "x-Request-ID"
        ]
        
        for header_name in test_cases:
            mock_request = Mock(spec=Request)
            mock_request.headers = Headers({header_name: f"case-test-{header_name}"})
            mock_request.state = State()
            
            mock_response = Mock(spec=Response)
            mock_response.headers = {}
            
            async def mock_call_next(request):
                return mock_response
            
            result = await self.middleware.dispatch(mock_request, mock_call_next)
            
            # 验证请求ID被正确识别（headers.get 应该是大小写不敏感的）
            expected_id = f"case-test-{header_name}"
            assert mock_request.state.request_id == expected_id
            assert mock_response.headers["x-request-id"] == expected_id

    @pytest.mark.asyncio
    async def test_dispatch_preserves_call_next_response(self):
        """测试中间件保持下一个处理器的响应"""
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({"x-request-id": "preserve-test"})
        mock_request.state = State()

        # 创建模拟响应对象
        custom_response = Mock(spec=Response)
        custom_response.status_code = 201
        custom_response.headers = {"custom-header": "custom-value"}

        async def mock_call_next(request):
            return custom_response

        result = await self.middleware.dispatch(mock_request, mock_call_next)

        # 验证响应被保持，只添加了 x-request-id
        assert result == custom_response
        assert result.status_code == 201
        assert result.headers["custom-header"] == "custom-value"
        assert result.headers["x-request-id"] == "preserve-test"

    @pytest.mark.asyncio
    async def test_dispatch_exception_propagation(self):
        """测试异常传播"""
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({"x-request-id": "exception-test"})
        mock_request.state = State()
        
        # 创建抛出异常的下一个处理器
        async def mock_call_next(request):
            raise ValueError("测试异常")
        
        # 执行中间件，应该传播异常
        with pytest.raises(ValueError, match="测试异常"):
            await self.middleware.dispatch(mock_request, mock_call_next)
        
        # 验证请求状态仍然被设置
        assert mock_request.state.request_id == "exception-test"

    @pytest.mark.asyncio
    async def test_dispatch_token_hex_length(self):
        """测试生成的令牌长度"""
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({})
        mock_request.state = State()
        
        mock_response = Mock(spec=Response)
        mock_response.headers = {}
        
        async def mock_call_next(request):
            return mock_response
        
        # 不模拟 token_hex，使用真实的生成
        result = await self.middleware.dispatch(mock_request, mock_call_next)
        
        # 验证生成的ID长度（16字节 = 32个十六进制字符）
        generated_id = mock_request.state.request_id
        assert len(generated_id) == 32
        assert all(c in "0123456789abcdef" for c in generated_id)

    @pytest.mark.asyncio
    async def test_dispatch_multiple_requests_different_ids(self):
        """测试多个请求生成不同的ID"""
        generated_ids = []
        
        for i in range(3):
            mock_request = Mock(spec=Request)
            mock_request.headers = Headers({})
            mock_request.state = State()
            
            mock_response = Mock(spec=Response)
            mock_response.headers = {}
            
            async def mock_call_next(request):
                return mock_response
            
            await self.middleware.dispatch(mock_request, mock_call_next)
            generated_ids.append(mock_request.state.request_id)
        
        # 验证生成的ID都不相同
        assert len(set(generated_ids)) == 3  # 所有ID都应该是唯一的
