"""
commons.db.kafkadao 模块的测试
"""
import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from kafka import KafkaProducer

from commons.db.kafkadao import KafkaProducerDao, KafkaConsumerDao, KafkaAdminClient


class TestKafkaProducerDao:
    """测试 KafkaProducerDao 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        KafkaProducerDao._instances = {}
        self.producer_dao = KafkaProducerDao()

    def test_singleton_pattern(self):
        """测试单例模式"""
        dao1 = KafkaProducerDao()
        dao2 = KafkaProducerDao()
        assert dao1 is dao2

    def test_init_default_values(self):
        """测试初始化默认值"""
        assert self.producer_dao.aproducer is None
        assert self.producer_dao.producer is None
        assert self.producer_dao.level_topic_dict == {}

    @patch('commons.db.kafkadao.AIOKafkaProducer')
    @patch('commons.db.kafkadao.KafkaProducer')
    @pytest.mark.asyncio
    async def test_init_success(self, mock_kafka_producer_class, mock_aio_producer_class):
        """测试成功初始化"""
        # 模拟异步生产者
        mock_aio_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_aio_producer.start = AsyncMock()  # Mock start方法
        mock_aio_producer_class.return_value = mock_aio_producer

        # 模拟同步生产者
        mock_kafka_producer = Mock(spec=KafkaProducer)
        mock_kafka_producer_class.return_value = mock_kafka_producer
        
        level_topic_dict = {
            "info": "info_topic",
            "error": "error_topic",
            "debug": "debug_topic"
        }
        bootstrap_servers = ["localhost:9092", "localhost:9093"]
        
        await self.producer_dao.init(level_topic_dict, bootstrap_servers)
        
        # 验证初始化结果
        assert self.producer_dao.level_topic_dict == level_topic_dict
        assert self.producer_dao.aproducer == mock_aio_producer
        assert self.producer_dao.producer == mock_kafka_producer
        
        # 验证异步生产者配置
        mock_aio_producer_class.assert_called_once()
        call_args = mock_aio_producer_class.call_args
        assert call_args[1]['bootstrap_servers'] == bootstrap_servers
        assert call_args[1]['request_timeout_ms'] == 30000
        assert call_args[1]['retry_backoff_ms'] == 1000
        assert 'value_serializer' in call_args[1]
        mock_aio_producer.start.assert_called_once()
        
        # 验证同步生产者配置
        mock_kafka_producer_class.assert_called_once()
        call_args = mock_kafka_producer_class.call_args
        assert call_args[1]['bootstrap_servers'] == bootstrap_servers
        assert call_args[1]['request_timeout_ms'] == 30000
        assert call_args[1]['retry_backoff_ms'] == 1000
        assert 'value_serializer' in call_args[1]

    @pytest.mark.asyncio
    async def test_close(self):
        """测试关闭连接"""
        # 模拟生产者实例
        mock_aio_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_kafka_producer = Mock(spec=KafkaProducer)
        
        self.producer_dao.aproducer = mock_aio_producer
        self.producer_dao.producer = mock_kafka_producer
        
        await self.producer_dao.close()
        
        # 验证关闭方法被调用
        mock_aio_producer.stop.assert_called_once()
        mock_kafka_producer.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_asend_message_success(self):
        """测试异步发送消息成功"""
        # 模拟异步生产者
        mock_aio_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_aio_producer._closed = False
        mock_aio_producer.send_and_wait.return_value = None
        
        self.producer_dao.aproducer = mock_aio_producer
        self.producer_dao.level_topic_dict = {"info": "info_topic"}
        
        test_message = {"message": "测试消息", "timestamp": "2024-01-01T00:00:00Z"}
        
        result = await self.producer_dao.asend_message("info", test_message)
        
        assert result is True
        mock_aio_producer.send_and_wait.assert_called_once_with("info_topic", value=test_message)

    @pytest.mark.asyncio
    async def test_asend_message_producer_closed(self):
        """测试异步发送消息 - 生产者已关闭"""
        # 模拟异步生产者（已关闭）
        mock_aio_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_aio_producer._closed = True
        mock_aio_producer.send_and_wait.return_value = None
        
        self.producer_dao.aproducer = mock_aio_producer
        self.producer_dao.level_topic_dict = {"info": "info_topic"}
        
        test_message = {"message": "测试消息"}
        
        result = await self.producer_dao.asend_message("info", test_message)
        
        assert result is True
        # 验证生产者被重新启动
        mock_aio_producer.start.assert_called_once()
        mock_aio_producer.send_and_wait.assert_called_once_with("info_topic", value=test_message)

    @pytest.mark.asyncio
    async def test_asend_message_invalid_level(self):
        """测试异步发送消息 - 无效的级别"""
        mock_aio_producer = AsyncMock(spec=AIOKafkaProducer)
        
        self.producer_dao.aproducer = mock_aio_producer
        self.producer_dao.level_topic_dict = {"info": "info_topic"}
        
        test_message = {"message": "测试消息"}
        
        with patch('commons.db.kafkadao.logger') as mock_logger:
            result = await self.producer_dao.asend_message("invalid_level", test_message)

            assert result is False
            mock_logger.error.assert_called_once()
            mock_aio_producer.send_and_wait.assert_not_called()

    @pytest.mark.asyncio
    async def test_asend_message_exception(self):
        """测试异步发送消息异常"""
        # 模拟异步生产者抛出异常
        mock_aio_producer = AsyncMock(spec=AIOKafkaProducer)
        mock_aio_producer._closed = False
        mock_aio_producer.send_and_wait.side_effect = Exception("发送失败")
        
        self.producer_dao.aproducer = mock_aio_producer
        self.producer_dao.level_topic_dict = {"error": "error_topic"}
        
        test_message = {"message": "错误消息"}
        
        with patch('commons.db.kafkadao.logger') as mock_logger:
            result = await self.producer_dao.asend_message("error", test_message)

            assert result is False
            mock_logger.error.assert_called_once()

    def test_send_message_success(self):
        """测试同步发送消息成功"""
        # 模拟同步生产者
        mock_kafka_producer = Mock(spec=KafkaProducer)
        mock_future = Mock()
        mock_future.get.return_value = None
        mock_kafka_producer.send.return_value = mock_future
        
        self.producer_dao.producer = mock_kafka_producer
        self.producer_dao.level_topic_dict = {"info": "info_topic"}
        
        test_message = {"message": "同步测试消息"}
        
        result = self.producer_dao.send_message("info", test_message)
        
        assert result is True
        mock_kafka_producer.send.assert_called_once_with("info_topic", value=test_message)
        mock_future.get.assert_called_once_with(timeout=10)

    def test_send_message_invalid_level(self):
        """测试同步发送消息 - 无效的级别"""
        mock_kafka_producer = Mock(spec=KafkaProducer)
        
        self.producer_dao.producer = mock_kafka_producer
        self.producer_dao.level_topic_dict = {"info": "info_topic"}
        
        test_message = {"message": "测试消息"}
        
        with patch('commons.db.kafkadao.logger') as mock_logger:
            result = self.producer_dao.send_message("invalid_level", test_message)

            assert result is False
            mock_logger.error.assert_called_once()
            mock_kafka_producer.send.assert_not_called()

    def test_send_message_exception(self):
        """测试同步发送消息异常"""
        # 模拟同步生产者抛出异常
        mock_kafka_producer = Mock(spec=KafkaProducer)
        mock_kafka_producer.send.side_effect = Exception("同步发送失败")
        
        self.producer_dao.producer = mock_kafka_producer
        self.producer_dao.level_topic_dict = {"error": "error_topic"}
        
        test_message = {"message": "错误消息"}
        
        with patch('commons.db.kafkadao.logger') as mock_logger:
            result = self.producer_dao.send_message("error", test_message)

            assert result is False
            mock_logger.error.assert_called_once()


class TestKafkaConsumerDao:
    """测试 KafkaConsumerDao 类"""

    def test_init_default_values(self):
        """测试初始化默认值"""
        bootstrap_servers = ["localhost:9092"]
        topics = ["test_topic"]
        group_id = "test_group"
        
        consumer_dao = KafkaConsumerDao(bootstrap_servers, topics, group_id)
        
        assert consumer_dao.bootstrap_servers == bootstrap_servers
        assert consumer_dao.topics == topics
        assert consumer_dao.group_id == group_id
        assert consumer_dao.auto_offset_reset == "latest"

    def test_init_with_custom_offset_reset(self):
        """测试使用自定义偏移重置初始化"""
        bootstrap_servers = ["localhost:9092"]
        topics = ["test_topic"]
        group_id = "test_group"
        auto_offset_reset = "earliest"
        
        consumer_dao = KafkaConsumerDao(bootstrap_servers, topics, group_id, auto_offset_reset)
        
        assert consumer_dao.auto_offset_reset == auto_offset_reset

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @pytest.mark.asyncio
    async def test_get_consumer(self, mock_aio_consumer_class):
        """测试获取消费者实例"""
        mock_consumer = AsyncMock(spec=AIOKafkaConsumer)
        mock_consumer.start = AsyncMock()  # Mock start方法
        mock_aio_consumer_class.return_value = mock_consumer
        
        bootstrap_servers = ["localhost:9092", "localhost:9093"]
        topics = ["topic1", "topic2"]
        group_id = "test_consumer_group"
        
        consumer_dao = KafkaConsumerDao(bootstrap_servers, topics, group_id)
        
        result = await consumer_dao.get_consumer()
        
        assert result == mock_consumer
        
        # 验证消费者配置
        # 验证消费者配置
        mock_aio_consumer_class.assert_called_once()
        call_args = mock_aio_consumer_class.call_args
        # 验证位置参数（topics）
        assert call_args[0] == tuple(topics)
        # 验证关键字参数
        kwargs = call_args[1]
        assert kwargs['bootstrap_servers'] == bootstrap_servers
        assert kwargs['group_id'] == group_id
        assert kwargs['enable_auto_commit'] == False
        assert kwargs['auto_offset_reset'] == 'latest'
        assert kwargs['max_poll_interval_ms'] == 600000
        assert kwargs['max_poll_records'] == 1
        assert kwargs['session_timeout_ms'] == 60000
        assert 'value_deserializer' in kwargs


class TestKafkaAdminClient:
    """测试 KafkaAdminClient 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        KafkaAdminClient._instances = {}
        self.admin_client = KafkaAdminClient()

    def test_singleton_pattern(self):
        """测试单例模式"""
        client1 = KafkaAdminClient()
        client2 = KafkaAdminClient()
        assert client1 is client2

    def test_init_default_values(self):
        """测试初始化默认值"""
        assert self.admin_client.client is None

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_init_success(self, mock_admin_client_class):
        """测试成功初始化"""
        mock_admin_instance = AsyncMock()
        mock_admin_instance.start = AsyncMock()  # Mock start方法
        mock_admin_client_class.return_value = mock_admin_instance
        
        bootstrap_servers = ["localhost:9092", "localhost:9093"]
        
        await self.admin_client.init(bootstrap_servers)
        
        # 验证初始化结果
        assert self.admin_client.client == mock_admin_instance
        
        # 验证管理客户端配置
        mock_admin_client_class.assert_called_once_with(
            bootstrap_servers=bootstrap_servers
        )
        mock_admin_instance.start.assert_called_once()

    @pytest.mark.asyncio
    async def test_close(self):
        """测试关闭连接"""
        mock_admin_instance = AsyncMock()
        self.admin_client.client = mock_admin_instance
        
        await self.admin_client.close()
        
        mock_admin_instance.close.assert_called_once()

    def test_value_serializer_function(self):
        """测试值序列化函数"""
        # 测试生产者的值序列化函数
        test_dict = {"key": "value", "number": 123, "boolean": True}
        
        # 模拟序列化函数
        serializer = lambda v: json.dumps(v).encode('utf-8')
        
        result = serializer(test_dict)
        expected = json.dumps(test_dict).encode('utf-8')
        
        assert result == expected
        assert isinstance(result, bytes)

    def test_value_deserializer_function(self):
        """测试值反序列化函数"""
        # 测试消费者的值反序列化函数
        test_dict = {"key": "value", "number": 123, "boolean": True}
        serialized_data = json.dumps(test_dict).encode('utf-8')
        
        # 模拟反序列化函数
        deserializer = lambda x: json.loads(x.decode('utf-8'))
        
        result = deserializer(serialized_data)
        
        assert result == test_dict
        assert isinstance(result, dict)

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @pytest.mark.asyncio
    async def test_simple_consume_messages_success(self, mock_consumer_class):
        """测试简单消费消息成功"""
        # 模拟消费者
        mock_consumer = AsyncMock()
        mock_consumer_class.return_value = mock_consumer

        # 模拟消息
        mock_messages = [
            Mock(value=b'{"message": "test1"}'),
            Mock(value=b'{"message": "test2"}'),
        ]

        # 模拟异步迭代器
        async def mock_async_iter():
            for msg in mock_messages:
                yield msg

        mock_consumer.__aiter__ = mock_async_iter
        mock_consumer.commit = AsyncMock()
        mock_consumer.stop = AsyncMock()

        # 初始化消费者
        await self.consumer_dao.init(["localhost:9092"], "test_group", ["test_topic"])

        # 模拟 get_consumer 返回消费者
        with patch.object(self.consumer_dao, 'get_consumer', return_value=mock_consumer):
            messages = []
            async for message in self.consumer_dao.simple_consume_messages():
                messages.append(message)
                if len(messages) >= 2:  # 限制消息数量以避免无限循环
                    break

            # 验证消息被正确消费
            assert len(messages) == 2
            assert messages[0].value == b'{"message": "test1"}'
            assert messages[1].value == b'{"message": "test2"}'

            # 验证 commit 被调用
            assert mock_consumer.commit.call_count == 2

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @patch('commons.db.kafkadao.logger')
    @pytest.mark.asyncio
    async def test_simple_consume_messages_exception(self, mock_logger, mock_consumer_class):
        """测试简单消费消息异常处理"""
        # 模拟消费者
        mock_consumer = AsyncMock()
        mock_consumer_class.return_value = mock_consumer

        # 模拟异步迭代器抛出异常
        async def mock_async_iter():
            raise Exception("消费异常")

        mock_consumer.__aiter__ = mock_async_iter
        mock_consumer.stop = AsyncMock()

        # 初始化消费者
        await self.consumer_dao.init(["localhost:9092"], "test_group", ["test_topic"])

        # 模拟 get_consumer 返回消费者
        with patch.object(self.consumer_dao, 'get_consumer', return_value=mock_consumer):
            messages = []
            async for message in self.consumer_dao.simple_consume_messages():
                messages.append(message)
                break  # 应该不会执行到这里

            # 验证异常被记录
            mock_logger.error.assert_called()
            # 验证 stop 被调用
            mock_consumer.stop.assert_called_once()

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @pytest.mark.asyncio
    async def test_simple_consume_messages_finally_block(self, mock_consumer_class):
        """测试简单消费消息的 finally 块"""
        # 模拟消费者
        mock_consumer = AsyncMock()
        mock_consumer_class.return_value = mock_consumer

        # 模拟空的异步迭代器
        async def mock_async_iter():
            return
            yield  # 这行不会执行，但保持生成器语法

        mock_consumer.__aiter__ = mock_async_iter
        mock_consumer.stop = AsyncMock()

        # 初始化消费者
        await self.consumer_dao.init(["localhost:9092"], "test_group", ["test_topic"])

        # 模拟 get_consumer 返回消费者
        with patch.object(self.consumer_dao, 'get_consumer', return_value=mock_consumer):
            messages = []
            async for message in self.consumer_dao.simple_consume_messages():
                messages.append(message)

            # 验证 stop 被调用（finally 块）
            mock_consumer.stop.assert_called_once()


class TestKafkaAdminClient:
    """测试 KafkaAdminClient 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        KafkaAdminClient._instances = {}
        self.admin_client = KafkaAdminClient()

    def test_singleton_pattern(self):
        """测试单例模式"""
        client1 = KafkaAdminClient()
        client2 = KafkaAdminClient()
        assert client1 is client2

    def test_init_default_values(self):
        """测试初始化默认值"""
        assert self.admin_client.client is None

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_init_success(self, mock_admin_client_class):
        """测试成功初始化管理客户端"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 执行初始化
        await self.admin_client.init(["localhost:9092"])

        # 验证客户端被创建和启动
        mock_admin_client_class.assert_called_once_with(bootstrap_servers=["localhost:9092"])
        mock_client.start.assert_called_once()
        assert self.admin_client.client == mock_client

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_create_topic_single_success(self, mock_admin_client_class):
        """测试成功创建单个主题"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_client.describe_topics = AsyncMock(return_value=[{"partitions": []}])  # 主题不存在
        mock_client.create_topics = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 初始化客户端
        await self.admin_client.init(["localhost:9092"])

        # 执行创建单个主题
        result = await self.admin_client.create_topic("test_topic", 3, 1)

        # 验证结果
        assert result is True

        # 验证相关方法被调用
        mock_client.describe_topics.assert_called_once_with(["test_topic"])
        mock_client.create_topics.assert_called_once()

    @pytest.mark.asyncio
    async def test_operations_without_init(self):
        """测试未初始化时的操作"""
        # 尝试在未初始化时执行操作，应该返回 False 而不是抛出异常
        result = await self.admin_client.create_topic("test_topic", 1, 1)
        assert result is False

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_close_client(self, mock_admin_client_class):
        """测试关闭客户端"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_client.close = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 初始化客户端
        await self.admin_client.init(["localhost:9092"])

        # 执行关闭
        await self.admin_client.close()

        # 验证关闭被调用
        mock_client.close.assert_called_once()

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_config_success(self, mock_admin_client_class):
        """测试成功更新主题配置"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_client.describe_topics = AsyncMock(return_value=[{"partitions": [{"partition": 0}]}])  # 主题存在
        mock_client.alter_configs = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 初始化客户端
        await self.admin_client.init(["localhost:9092"])

        # 执行更新主题配置
        topic_config = {"retention.ms": "86400000"}
        result = await self.admin_client.update_topic_config("test_topic", topic_config)

        # 验证结果
        assert result is True

        # 验证相关方法被调用
        mock_client.describe_topics.assert_called_once_with(["test_topic"])
        mock_client.alter_configs.assert_called_once()

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_config_topic_not_exists(self, mock_admin_client_class):
        """测试更新不存在主题的配置"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_client.describe_topics = AsyncMock(return_value=[{"partitions": []}])  # 主题不存在
        mock_admin_client_class.return_value = mock_client

        # 初始化客户端
        await self.admin_client.init(["localhost:9092"])

        # 执行更新主题配置
        topic_config = {"retention.ms": "86400000"}
        result = await self.admin_client.update_topic_config("nonexistent_topic", topic_config)

        # 验证结果
        assert result is False

        # 验证 describe_topics 被调用，但 alter_configs 不被调用
        mock_client.describe_topics.assert_called_once_with(["nonexistent_topic"])
        mock_client.alter_configs.assert_not_called()

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_partitions_success(self, mock_admin_client_class):
        """测试成功更新主题分区数"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_client.describe_topics = AsyncMock(return_value=[{"partitions": [{"partition": 0}]}])  # 当前1个分区
        mock_client.create_partitions = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 初始化客户端
        await self.admin_client.init(["localhost:9092"])

        # 执行更新主题分区数（增加到3个分区）
        result = await self.admin_client.update_topic_partitions("test_topic", 3)

        # 验证结果
        assert result is True

        # 验证相关方法被调用
        mock_client.describe_topics.assert_called_once_with(["test_topic"])
        mock_client.create_partitions.assert_called_once()

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_partitions_no_change_needed(self, mock_admin_client_class):
        """测试更新主题分区数时无需更改"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        # 模拟当前有3个分区
        mock_client.describe_topics = AsyncMock(return_value=[{
            "partitions": [{"partition": 0}, {"partition": 1}, {"partition": 2}]
        }])
        mock_admin_client_class.return_value = mock_client

        # 初始化客户端
        await self.admin_client.init(["localhost:9092"])

        # 执行更新主题分区数（保持3个分区）
        result = await self.admin_client.update_topic_partitions("test_topic", 3)

        # 验证结果
        assert result is True

        # 验证 describe_topics 被调用，但 create_partitions 不被调用
        mock_client.describe_topics.assert_called_once_with(["test_topic"])
        mock_client.create_partitions.assert_not_called()

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_partitions_decrease_attempt(self, mock_admin_client_class):
        """测试尝试减少主题分区数（应该不变）"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        # 模拟当前有3个分区
        mock_client.describe_topics = AsyncMock(return_value=[{
            "partitions": [{"partition": 0}, {"partition": 1}, {"partition": 2}]
        }])
        mock_admin_client_class.return_value = mock_client

        # 初始化客户端
        await self.admin_client.init(["localhost:9092"])

        # 执行更新主题分区数（尝试减少到1个分区）
        result = await self.admin_client.update_topic_partitions("test_topic", 1)

        # 验证结果
        assert result is True

        # 验证 describe_topics 被调用，但 create_partitions 不被调用（因为不能减少分区）
        mock_client.describe_topics.assert_called_once_with(["test_topic"])
        mock_client.create_partitions.assert_not_called()

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_create_topic_existing_topic_with_config_update(self, mock_admin_client_class):
        """测试创建已存在主题时更新配置"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_client.describe_topics = AsyncMock(return_value=[{"partitions": [{"partition": 0}]}])  # 主题存在
        mock_admin_client_class.return_value = mock_client

        # 模拟 update_topic_config 和 update_topic_partitions 方法
        with patch.object(self.admin_client, 'update_topic_config', return_value=True) as mock_update_config, \
             patch.object(self.admin_client, 'update_topic_partitions', return_value=True) as mock_update_partitions:

            # 初始化客户端
            await self.admin_client.init(["localhost:9092"])

            # 执行创建主题（主题已存在，应该更新配置和分区）
            topic_config = {"retention.ms": "86400000"}
            result = await self.admin_client.create_topic("existing_topic", 3, 1, topic_config)

            # 验证结果
            assert result is True

            # 验证更新方法被调用
            mock_update_config.assert_called_once_with("existing_topic", topic_config)
            mock_update_partitions.assert_called_once_with("existing_topic", 3)

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_create_topic_existing_topic_update_failure(self, mock_admin_client_class):
        """测试创建已存在主题时更新失败"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_client.start = AsyncMock()
        mock_client.describe_topics = AsyncMock(return_value=[{"partitions": [{"partition": 0}]}])  # 主题存在
        mock_admin_client_class.return_value = mock_client

        # 模拟 update_topic_config 失败
        with patch.object(self.admin_client, 'update_topic_config', return_value=False) as mock_update_config:

            # 初始化客户端
            await self.admin_client.init(["localhost:9092"])

            # 执行创建主题（配置更新失败）
            topic_config = {"retention.ms": "86400000"}
            result = await self.admin_client.create_topic("existing_topic", 3, 1, topic_config)

            # 验证结果
            assert result is False

            # 验证更新方法被调用
            mock_update_config.assert_called_once_with("existing_topic", topic_config)

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_create_topic_update_config_failure(self, mock_admin_client_class):
        """测试创建主题时更新配置失败"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 模拟主题已存在
        mock_client.describe_topics.return_value = [{"partitions": [{"partition": 0}]}]

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        # 模拟更新配置失败
        with patch.object(self.admin_client, 'update_topic_config', return_value=False):
            result = await self.admin_client.create_topic(
                "test_topic",
                partitions=1,
                topic_config={"retention.ms": "86400000"}
            )

            assert result is False

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_create_topic_update_partitions_failure(self, mock_admin_client_class):
        """测试创建主题时更新分区失败"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 模拟主题已存在
        mock_client.describe_topics.return_value = [{"partitions": [{"partition": 0}]}]

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        # 模拟更新分区失败
        with patch.object(self.admin_client, 'update_topic_partitions', return_value=False):
            result = await self.admin_client.create_topic("test_topic", partitions=3)

            assert result is False

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_config_exception(self, mock_admin_client_class):
        """测试更新主题配置异常"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 模拟异常
        mock_client.alter_configs.side_effect = Exception("配置更新失败")

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        result = await self.admin_client.update_topic_config(
            "test_topic",
            {"retention.ms": "86400000"}
        )

        assert result is False

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_partitions_topic_not_exist(self, mock_admin_client_class):
        """测试更新分区时主题不存在"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 模拟主题不存在（空分区列表）
        mock_client.describe_topics.return_value = [{"partitions": []}]

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        result = await self.admin_client.update_topic_partitions("test_topic", 3)

        assert result is False

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_update_topic_partitions_exception(self, mock_admin_client_class):
        """测试更新主题分区异常"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 模拟异常
        mock_client.describe_topics.side_effect = Exception("分区更新失败")

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        result = await self.admin_client.update_topic_partitions("test_topic", 3)

        assert result is False

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_del_topic_success(self, mock_admin_client_class):
        """测试删除主题成功"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 模拟删除成功
        mock_client.delete_topics.return_value = None

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        result = await self.admin_client.del_topic("test_topic")

        assert result is True
        mock_client.delete_topics.assert_called_once_with(["test_topic"])

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_del_topic_exception(self, mock_admin_client_class):
        """测试删除主题异常"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 模拟异常
        mock_client.delete_topics.side_effect = Exception("删除失败")

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        result = await self.admin_client.del_topic("test_topic")

        assert result is False

    @patch('commons.db.kafkadao.AIOKafkaAdminClient')
    @pytest.mark.asyncio
    async def test_get_client(self, mock_admin_client_class):
        """测试获取客户端"""
        # 模拟管理客户端
        mock_client = AsyncMock()
        mock_admin_client_class.return_value = mock_client

        # 初始化管理客户端
        await self.admin_client.init(["localhost:9092"])

        client = self.admin_client.get_client()

        assert client == mock_client


class TestKafkaConsumerDao:
    """测试 Kafka 消费者 DAO"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.consumer_dao = KafkaConsumerDao(
            bootstrap_servers=["localhost:9092"],
            topics=["test_topic"],
            group_id="test_group",
            auto_offset_reset="latest"
        )

    def test_init_with_default_params(self):
        """测试使用默认参数初始化"""
        consumer = KafkaConsumerDao(
            bootstrap_servers=["localhost:9092"],
            topics=["topic1", "topic2"],
            group_id="group1"
        )

        assert consumer.bootstrap_servers == ["localhost:9092"]
        assert consumer.topics == ["topic1", "topic2"]
        assert consumer.group_id == "group1"
        assert consumer.auto_offset_reset == "latest"

    def test_init_with_custom_params(self):
        """测试使用自定义参数初始化"""
        consumer = KafkaConsumerDao(
            bootstrap_servers=["broker1:9092", "broker2:9092"],
            topics=["custom_topic"],
            group_id="custom_group",
            auto_offset_reset="earliest"
        )

        assert consumer.bootstrap_servers == ["broker1:9092", "broker2:9092"]
        assert consumer.topics == ["custom_topic"]
        assert consumer.group_id == "custom_group"
        assert consumer.auto_offset_reset == "earliest"

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @pytest.mark.asyncio
    async def test_get_consumer_success(self, mock_consumer_class):
        """测试成功获取消费者"""
        # 模拟消费者
        mock_consumer = AsyncMock()
        mock_consumer.start = AsyncMock()
        mock_consumer_class.return_value = mock_consumer

        # 执行获取消费者
        result = await self.consumer_dao.get_consumer()

        # 验证结果
        assert result == mock_consumer

        # 验证消费者创建参数
        mock_consumer_class.assert_called_once_with(
            "test_topic",
            bootstrap_servers=["localhost:9092"],
            group_id="test_group",
            value_deserializer=mock_consumer_class.call_args[1]["value_deserializer"],
            enable_auto_commit=False,
            auto_offset_reset="latest",
            max_poll_interval_ms=600000,
            max_poll_records=1,
            session_timeout_ms=60000
        )

        # 验证消费者启动
        mock_consumer.start.assert_called_once()

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @pytest.mark.asyncio
    async def test_get_consumer_with_different_offset_reset(self, mock_consumer_class):
        """测试使用不同偏移重置策略获取消费者"""
        # 创建使用 earliest 策略的消费者 DAO
        consumer_dao = KafkaConsumerDao(
            bootstrap_servers=["localhost:9092"],
            topics=["test_topic"],
            group_id="test_group",
            auto_offset_reset="earliest"
        )

        # 模拟消费者
        mock_consumer = AsyncMock()
        mock_consumer.start = AsyncMock()
        mock_consumer_class.return_value = mock_consumer

        # 执行获取消费者
        result = await consumer_dao.get_consumer()

        # 验证 auto_offset_reset 参数
        call_args = mock_consumer_class.call_args[1]
        assert call_args["auto_offset_reset"] == "earliest"

    def test_value_deserializer_function(self):
        """测试值反序列化函数"""
        # 模拟 JSON 数据
        test_data = {"key": "value", "number": 123}
        json_bytes = json.dumps(test_data).encode('utf-8')

        # 创建消费者以获取反序列化函数
        with patch('commons.db.kafkadao.AIOKafkaConsumer') as mock_consumer_class:
            mock_consumer = AsyncMock()
            mock_consumer_class.return_value = mock_consumer

            # 触发消费者创建以获取反序列化函数
            import asyncio
            async def get_deserializer():
                await self.consumer_dao.get_consumer()
                return mock_consumer_class.call_args[1]["value_deserializer"]

            # 在事件循环中运行
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                deserializer = loop.run_until_complete(get_deserializer())

                # 测试反序列化
                result = deserializer(json_bytes)
                assert result == test_data
            finally:
                loop.close()

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @patch('commons.db.kafkadao.logger')
    @pytest.mark.asyncio
    async def test_test_consume_messages_success(self, mock_logger, mock_consumer_class):
        """测试成功消费消息"""
        # 模拟消费者和消息
        mock_consumer = AsyncMock()
        mock_consumer.start = AsyncMock()
        mock_consumer.stop = AsyncMock()
        mock_consumer.commit = AsyncMock()

        # 模拟消息
        mock_message1 = Mock()
        mock_message1.value = {"id": 1, "content": "message1"}
        mock_message2 = Mock()
        mock_message2.value = {"id": 2, "content": "message2"}

        # 模拟异步迭代器
        async def async_iter():
            yield mock_message1
            yield mock_message2

        mock_consumer.__aiter__ = lambda self: async_iter()
        mock_consumer_class.return_value = mock_consumer

        # 执行消费消息
        messages = []
        async for message in self.consumer_dao.test_consume_messages():
            messages.append(message)
            if len(messages) >= 2:  # 限制消息数量以避免无限循环
                break

        # 验证结果
        assert len(messages) == 2
        assert messages[0] == mock_message1
        assert messages[1] == mock_message2

        # 验证消费者操作
        mock_consumer.start.assert_called_once()
        # stop 可能在 finally 块中被调用，但由于异步生成器的特性，可能不会立即调用
        assert mock_consumer.commit.call_count >= 1  # 至少提交一次

    @patch('commons.db.kafkadao.AIOKafkaConsumer')
    @patch('commons.db.kafkadao.logger')
    @pytest.mark.asyncio
    async def test_test_consume_messages_exception(self, mock_logger, mock_consumer_class):
        """测试消费消息时异常处理"""
        # 模拟消费者
        mock_consumer = AsyncMock()
        mock_consumer.start = AsyncMock()
        mock_consumer.stop = AsyncMock()

        # 模拟异步迭代器抛出异常
        async def async_iter_with_exception():
            raise Exception("消费异常")

        mock_consumer.__aiter__ = lambda self: async_iter_with_exception()
        mock_consumer_class.return_value = mock_consumer

        # 执行消费消息
        messages = []
        async for message in self.consumer_dao.test_consume_messages():
            messages.append(message)

        # 验证异常被记录
        mock_logger.error.assert_called()

        # 验证消费者被正确停止
        mock_consumer.stop.assert_called_once()

    def test_consumer_configuration_parameters(self):
        """测试消费者配置参数"""
        # 验证各种配置参数的设置
        configs = [
            {
                "bootstrap_servers": ["server1:9092"],
                "topics": ["topic1"],
                "group_id": "group1",
                "auto_offset_reset": "earliest"
            },
            {
                "bootstrap_servers": ["server1:9092", "server2:9092"],
                "topics": ["topic1", "topic2", "topic3"],
                "group_id": "group2",
                "auto_offset_reset": "latest"
            }
        ]

        for config in configs:
            consumer = KafkaConsumerDao(**config)
            assert consumer.bootstrap_servers == config["bootstrap_servers"]
            assert consumer.topics == config["topics"]
            assert consumer.group_id == config["group_id"]
            assert consumer.auto_offset_reset == config["auto_offset_reset"]

    def test_consumer_dao_attributes(self):
        """测试消费者 DAO 属性"""
        # 验证所有必要属性存在
        required_attributes = [
            "bootstrap_servers",
            "topics",
            "group_id",
            "auto_offset_reset"
        ]

        for attr in required_attributes:
            assert hasattr(self.consumer_dao, attr)

        # 验证方法存在
        required_methods = ["get_consumer", "test_consume_messages"]

        for method in required_methods:
            assert hasattr(self.consumer_dao, method)
            assert callable(getattr(self.consumer_dao, method))

    def test_kafka_admin_client_basic_functionality(self):
        """测试 Kafka 管理客户端基本功能"""
        # 创建管理客户端实例
        admin_client = KafkaAdminClient()

        # 验证实例创建
        assert admin_client is not None
        assert hasattr(admin_client, 'init')
        assert hasattr(admin_client, 'create_topic')


        # 验证方法可调用
        assert callable(admin_client.init)
        assert callable(admin_client.create_topic)


    def test_kafka_admin_client_configuration(self):
        """测试 Kafka 管理客户端配置"""
        # 测试不同的配置参数
        configs = [
            {"bootstrap_servers": ["localhost:9092"]},
            {"bootstrap_servers": ["broker1:9092", "broker2:9092"]},
            {"bootstrap_servers": ["kafka.example.com:9092"]},
        ]

        for config in configs:
            admin_client = KafkaAdminClient()
            # 验证可以创建实例
            assert admin_client is not None
            assert hasattr(admin_client, 'init')

    def test_kafka_consumer_dao_edge_cases(self):
        """测试 Kafka 消费者 DAO 边界情况"""
        # 测试边界情况
        edge_cases = [
            {
                "bootstrap_servers": [],  # 空服务器列表
                "topics": ["test_topic"],
                "group_id": "test_group"
            },
            {
                "bootstrap_servers": ["localhost:9092"],
                "topics": [],  # 空主题列表
                "group_id": "test_group"
            },
            {
                "bootstrap_servers": ["localhost:9092"],
                "topics": ["test_topic"],
                "group_id": ""  # 空组ID
            }
        ]

        for case in edge_cases:
            try:
                consumer = KafkaConsumerDao(**case)
                # 验证可以创建实例（即使参数可能无效）
                assert consumer is not None
            except Exception:
                # 如果抛出异常，也是可接受的行为
                assert True

    def test_kafka_topic_configuration_validation(self):
        """测试 Kafka 主题配置验证"""
        # 模拟主题配置验证
        def validate_topic_config(config):
            required_fields = ["name"]
            optional_fields = ["num_partitions", "replication_factor", "topic_config"]

            # 检查必需字段
            for field in required_fields:
                if field not in config:
                    return False, f"Missing required field: {field}"

            # 检查分区数
            if "num_partitions" in config and config["num_partitions"] <= 0:
                return False, "num_partitions must be positive"

            # 检查复制因子
            if "replication_factor" in config and config["replication_factor"] <= 0:
                return False, "replication_factor must be positive"

            return True, "Valid configuration"

        # 测试有效配置
        valid_configs = [
            {"name": "valid_topic1", "num_partitions": 3, "replication_factor": 1},
            {"name": "valid_topic2"},
            {"name": "valid_topic3", "topic_config": {"retention.ms": "86400000"}}
        ]

        for config in valid_configs:
            is_valid, message = validate_topic_config(config)
            assert is_valid is True

        # 测试无效配置
        invalid_configs = [
            {},  # 缺少名称
            {"name": "invalid_topic", "num_partitions": 0},  # 无效分区数
            {"name": "invalid_topic", "replication_factor": -1}  # 无效复制因子
        ]

        for config in invalid_configs:
            is_valid, message = validate_topic_config(config)
            assert is_valid is False

    def test_kafka_performance_monitoring_simulation(self):
        """测试 Kafka 性能监控模拟"""
        # 模拟性能监控指标
        performance_metrics = {
            "producer_metrics": {
                "messages_sent_per_sec": 1000,
                "bytes_sent_per_sec": 1024000,
                "average_latency_ms": 5.2,
                "error_rate": 0.001
            },
            "consumer_metrics": {
                "messages_consumed_per_sec": 950,
                "bytes_consumed_per_sec": 972800,
                "lag_messages": 50,
                "processing_time_ms": 2.1
            },
            "broker_metrics": {
                "cpu_usage": 0.65,
                "memory_usage": 0.70,
                "disk_usage": 0.45,
                "network_io_mbps": 100
            }
        }

        # 验证性能指标
        assert performance_metrics["producer_metrics"]["messages_sent_per_sec"] > 0
        assert performance_metrics["consumer_metrics"]["lag_messages"] >= 0
        assert 0 <= performance_metrics["broker_metrics"]["cpu_usage"] <= 1


