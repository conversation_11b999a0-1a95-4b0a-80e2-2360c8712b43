"""
commons.db.mysqldao 模块的测试
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Session
from sqlalchemy.engine import Engine

from commons.db.mysqldao import <PERSON>sql<PERSON><PERSON>, SQLBase, sqlsession


class TestModel(SQLBase):
    """测试用的数据模型"""
    __tablename__ = "test_table"
    id = Column(Integer, primary_key=True)
    name = Column(String(50))


class TestMysqlDao:
    """测试 MysqlDao 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        MysqlDao._instances = {}
        self.mysql_dao = MysqlDao()

    def test_singleton_pattern(self):
        """测试单例模式"""
        dao1 = MysqlDao()
        dao2 = MysqlDao()
        assert dao1 is dao2

    def test_init_not_initialized(self):
        """测试初始化状态检查 - 未初始化"""
        assert not self.mysql_dao.is_init()

    @patch('commons.db.mysqldao.create_engine')
    @patch('commons.db.mysqldao.sessionmaker')
    def test_init_success(self, mock_sessionmaker, mock_create_engine):
        """测试成功初始化"""
        # 模拟引擎和会话
        mock_engine = Mock(spec=Engine)
        mock_engine.connect.return_value = Mock()
        mock_create_engine.return_value = mock_engine
        
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        
        # 执行初始化
        self.mysql_dao.init(
            host="localhost",
            port=3306,
            user="test_user",
            pwd="test_password",
            db="test_db",
            pools=10,
            env="test",
            is_debug=False
        )
        
        # 验证初始化结果
        assert self.mysql_dao.is_init()
        assert self.mysql_dao.env == "test"
        assert self.mysql_dao.engine == mock_engine
        assert self.mysql_dao.sess == mock_session_class
        
        # 验证 create_engine 被正确调用
        mock_create_engine.assert_called_once()
        call_args = mock_create_engine.call_args
        conn_sql = call_args[0][0]
        assert "mysql+pymysql://test_user:test_password@localhost:3306/test_db" in conn_sql
        assert "charset=utf8mb4" in conn_sql
        
        # 验证引擎配置
        engine_kwargs = call_args[1]
        assert engine_kwargs['pool_size'] == 10
        assert engine_kwargs['max_overflow'] == 10
        assert engine_kwargs['pool_pre_ping'] is True
        assert engine_kwargs['echo'] is False
        
        # 验证 sessionmaker 被正确调用
        mock_sessionmaker.assert_called_once_with(
            bind=mock_engine, 
            autocommit=False, 
            autoflush=True
        )

    @patch('commons.db.mysqldao.create_engine')
    @patch('commons.db.mysqldao.sessionmaker')
    def test_init_with_debug(self, mock_sessionmaker, mock_create_engine):
        """测试带调试模式的初始化"""
        mock_engine = Mock(spec=Engine)
        mock_engine.connect.return_value = Mock()
        mock_create_engine.return_value = mock_engine
        
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        
        # 执行带调试模式的初始化
        self.mysql_dao.init(
            host="localhost",
            port=3306,
            user="test_user",
            pwd="test_password",
            db="test_db",
            pools=5,
            env="dev",
            is_debug=True
        )
        
        # 验证调试模式被启用
        call_args = mock_create_engine.call_args
        engine_kwargs = call_args[1]
        assert engine_kwargs['echo'] is True

    @patch('commons.db.mysqldao.create_engine')
    @patch('commons.db.mysqldao.sessionmaker')
    def test_init_with_special_password(self, mock_sessionmaker, mock_create_engine):
        """测试包含特殊字符的密码初始化"""
        mock_engine = Mock(spec=Engine)
        mock_engine.connect.return_value = Mock()
        mock_create_engine.return_value = mock_engine
        
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        
        # 使用包含特殊字符的密码
        special_password = "test@password#123"
        
        self.mysql_dao.init(
            host="localhost",
            port=3306,
            user="test_user",
            pwd=special_password,
            db="test_db",
            pools=5,
            env="test"
        )
        
        # 验证密码被正确编码
        call_args = mock_create_engine.call_args
        conn_sql = call_args[0][0]
        # 特殊字符应该被URL编码
        assert "test%40password%23123" in conn_sql

    def test_sess_not_initialized(self):
        """测试未初始化时获取会话"""
        with pytest.raises(TypeError):
            self.mysql_dao.sess()

    @patch('commons.db.mysqldao.create_engine')
    @patch('commons.db.mysqldao.sessionmaker')
    def test_sess_initialized(self, mock_sessionmaker, mock_create_engine):
        """测试已初始化时获取会话"""
        # 模拟初始化
        mock_engine = Mock(spec=Engine)
        mock_engine.connect.return_value = Mock()
        mock_create_engine.return_value = mock_engine
        
        mock_session_instance = Mock(spec=Session)
        mock_session_class = Mock(return_value=mock_session_instance)
        mock_sessionmaker.return_value = mock_session_class
        
        self.mysql_dao.init(
            host="localhost",
            port=3306,
            user="test_user",
            pwd="test_password",
            db="test_db",
            pools=5,
            env="test"
        )
        
        # 获取会话
        session = self.mysql_dao.sess()
        
        # 验证返回的是会话实例
        assert session == mock_session_instance
        mock_session_class.assert_called_once()

    @patch('commons.db.mysqldao.create_engine')
    @patch('commons.db.mysqldao.sessionmaker')
    def test_close_with_engine(self, mock_sessionmaker, mock_create_engine):
        """测试关闭连接 - 有引擎"""
        # 模拟初始化
        mock_engine = Mock(spec=Engine)
        mock_engine.connect.return_value = Mock()
        mock_create_engine.return_value = mock_engine
        
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        
        self.mysql_dao.init(
            host="localhost",
            port=3306,
            user="test_user",
            pwd="test_password",
            db="test_db",
            pools=5,
            env="test"
        )
        
        # 关闭连接
        self.mysql_dao.close()
        
        # 验证引擎被释放
        mock_engine.dispose.assert_called_once()

    def test_close_without_engine(self):
        """测试关闭连接 - 无引擎"""
        # 直接关闭，不应该抛出异常
        self.mysql_dao.close()
        # 测试通过即表示没有异常


class TestSqlSession:
    """测试 sqlsession 上下文管理器"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        MysqlDao._instances = {}

    @patch('commons.db.mysqldao.create_engine')
    @patch('commons.db.mysqldao.sessionmaker')
    def test_sqlsession_context_manager_success(self, mock_sessionmaker, mock_create_engine):
        """测试 sqlsession 上下文管理器成功场景"""
        # 模拟初始化
        mock_engine = Mock(spec=Engine)
        mock_engine.connect.return_value = Mock()
        mock_create_engine.return_value = mock_engine
        
        mock_session_instance = Mock(spec=Session)
        mock_session_class = Mock(return_value=mock_session_instance)
        mock_sessionmaker.return_value = mock_session_class
        
        mysql_dao = MysqlDao()
        mysql_dao.init(
            host="localhost",
            port=3306,
            user="test_user",
            pwd="test_password",
            db="test_db",
            pools=5,
            env="test"
        )
        
        # 使用上下文管理器
        with sqlsession() as session:
            assert session == mock_session_instance
            # 模拟一些数据库操作
            session.query(TestModel).all()
        
        # 验证会话被正确提交和关闭
        mock_session_instance.commit.assert_called_once()
        mock_session_instance.close.assert_called_once()

    @patch('commons.db.mysqldao.create_engine')
    @patch('commons.db.mysqldao.sessionmaker')
    def test_sqlsession_context_manager_exception(self, mock_sessionmaker, mock_create_engine):
        """测试 sqlsession 上下文管理器异常场景"""
        # 模拟初始化
        mock_engine = Mock(spec=Engine)
        mock_engine.connect.return_value = Mock()
        mock_create_engine.return_value = mock_engine
        
        mock_session_instance = Mock(spec=Session)
        mock_session_class = Mock(return_value=mock_session_instance)
        mock_sessionmaker.return_value = mock_session_class
        
        mysql_dao = MysqlDao()
        mysql_dao.init(
            host="localhost",
            port=3306,
            user="test_user",
            pwd="test_password",
            db="test_db",
            pools=5,
            env="test"
        )
        
        # 使用上下文管理器并抛出异常
        with pytest.raises(ValueError):
            with sqlsession() as session:
                assert session == mock_session_instance
                # 模拟异常
                raise ValueError("Test exception")
        
        # 验证会话被回滚和关闭，但没有提交
        mock_session_instance.rollback.assert_called_once()
        mock_session_instance.close.assert_called_once()
        mock_session_instance.commit.assert_not_called()


class TestSQLBase:
    """测试 SQLBase 基类"""

    def test_sqlbase_is_declarative_base(self):
        """测试 SQLBase 是声明式基类"""
        # 验证 SQLBase 具有声明式基类的特征
        assert hasattr(SQLBase, 'metadata')
        assert hasattr(SQLBase, 'registry')

    def test_model_inheritance(self):
        """测试模型继承"""
        # 验证测试模型正确继承了 SQLBase
        assert issubclass(TestModel, SQLBase)
        assert TestModel.__tablename__ == "test_table"
        assert hasattr(TestModel, 'id')
        assert hasattr(TestModel, 'name')
