"""
commons.db.redis5dao 模块的测试
"""
import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
import redis
from redis import asyncio as aredis

from commons.db.redis5dao import Redis5Dao, CommonData, CommonResp, RespCode


class TestCommonModels:
    """测试通用数据模型"""

    def test_common_data_init(self):
        """测试 CommonData 初始化"""
        data = CommonData()
        assert data.val is None
        
        data_with_val = CommonData(val="test_value")
        assert data_with_val.val == "test_value"

    def test_common_resp_init(self):
        """测试 CommonResp 初始化"""
        resp = CommonResp(code=0, msg="success")
        assert resp.code == 0
        assert resp.msg == "success"
        assert resp.data is None
        
        data = CommonData(val="test")
        resp_with_data = CommonResp(code=0, msg="success", data=data)
        assert resp_with_data.data == data

    def test_resp_code_enum(self):
        """测试响应码枚举"""
        assert RespCode.OK == 0
        assert isinstance(RespCode.OK, int)


class TestRedis5Dao:
    """测试 Redis5Dao 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        Redis5Dao._instances = {}
        self.redis_dao = Redis5Dao()

    def test_singleton_pattern(self):
        """测试单例模式"""
        dao1 = Redis5Dao()
        dao2 = Redis5Dao()
        assert dao1 is dao2

    def test_init_default_values(self):
        """测试初始化默认值"""
        assert self.redis_dao._hosts == []
        assert self.redis_dao._password is None
        assert self.redis_dao.rd is None
        assert self.redis_dao.ard is None
        assert self.redis_dao._prefix == ""
        assert self.redis_dao._cluster is False
        assert self.redis_dao._cams_ak is None
        assert self.redis_dao._cams_sk is None
        assert self.redis_dao._cams_host is None
        assert self.redis_dao.is_init is False

    @patch('commons.db.redis5dao.Redis5Dao._connect')
    @patch('commons.db.redis5dao.Redis5Dao._aconnect')
    @pytest.mark.asyncio
    async def test_init_single_node(self, mock_aconnect, mock_connect):
        """测试单节点初始化"""
        # 模拟连接
        mock_redis = Mock(spec=redis.Redis)
        mock_aredis = Mock(spec=aredis.Redis)
        mock_connect.return_value = mock_redis
        mock_aconnect.return_value = mock_aredis
        
        hosts = ["127.0.0.1:6379"]
        password = "test_password"
        prefix = "test_"
        cluster = False
        
        await self.redis_dao.init(hosts, password, prefix, cluster)
        
        # 验证初始化结果
        assert self.redis_dao._hosts == hosts
        assert self.redis_dao._password == password
        assert self.redis_dao._prefix == prefix
        assert self.redis_dao._cluster == cluster
        assert self.redis_dao.rd == mock_redis
        assert self.redis_dao.ard == mock_aredis
        assert self.redis_dao.is_init is True
        
        # 验证连接方法被调用
        mock_connect.assert_called_once()
        mock_aconnect.assert_called_once()

    @patch('commons.db.redis5dao.Redis5Dao._connect')
    @patch('commons.db.redis5dao.Redis5Dao._aconnect')
    @pytest.mark.asyncio
    async def test_init_cluster(self, mock_aconnect, mock_connect):
        """测试集群初始化"""
        # 模拟连接
        mock_redis_cluster = Mock(spec=redis.RedisCluster)
        mock_aredis_cluster = Mock(spec=aredis.RedisCluster)
        mock_connect.return_value = mock_redis_cluster
        mock_aconnect.return_value = mock_aredis_cluster
        
        hosts = ["127.0.0.1:6379", "127.0.0.1:6380", "127.0.0.1:6381"]
        password = "cluster_password"
        prefix = "cluster_"
        cluster = True
        
        await self.redis_dao.init(hosts, password, prefix, cluster)
        
        # 验证集群配置
        assert self.redis_dao._cluster is True
        assert self.redis_dao.rd == mock_redis_cluster
        assert self.redis_dao.ard == mock_aredis_cluster

    def test_init_cams(self):
        """测试 CAMS 初始化"""
        ak = "test_ak"
        sk = "test_sk"
        host = "http://cams-host"
        prefix = "test_prefix:"

        self.redis_dao.init_cams(ak, sk, host, prefix)

        assert self.redis_dao._cams_ak == ak
        assert self.redis_dao._cams_sk == sk
        assert self.redis_dao._cams_host == host
        assert self.redis_dao._prefix == prefix

    @patch('redis.ConnectionPool')
    @patch('redis.Redis')
    def test_connect_single_node(self, mock_redis_class, mock_pool_class):
        """测试单节点连接"""
        mock_pool_instance = Mock()
        mock_pool_class.return_value = mock_pool_instance
        mock_redis_instance = Mock(spec=redis.Redis)
        mock_redis_class.return_value = mock_redis_instance

        self.redis_dao._hosts = ["127.0.0.1:6379"]
        self.redis_dao._password = "test_password"
        self.redis_dao._cluster = False

        result = self.redis_dao._connect()

        assert result == mock_redis_instance
        mock_pool_class.assert_called_once_with(
            host="127.0.0.1",
            port=6379,
            password="test_password"
        )
        mock_redis_class.assert_called_once_with(connection_pool=mock_pool_instance)

    @patch('redis.RedisCluster')
    def test_connect_cluster(self, mock_redis_cluster_class):
        """测试集群连接"""
        mock_cluster_instance = Mock(spec=redis.RedisCluster)
        mock_redis_cluster_class.return_value = mock_cluster_instance
        
        self.redis_dao._hosts = ["127.0.0.1:6379", "127.0.0.1:6380"]
        self.redis_dao._password = "cluster_password"
        self.redis_dao._cluster = True
        
        result = self.redis_dao._connect()
        
        assert result == mock_cluster_instance
        # 验证集群节点配置
        mock_redis_cluster_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_aconnect_single_node(self):
        """测试异步单节点连接"""
        with patch('redis.asyncio.ConnectionPool') as mock_pool_class, \
             patch('redis.asyncio.Redis') as mock_aredis_class:

            mock_pool_instance = Mock()
            mock_pool_class.return_value = mock_pool_instance

            # 创建一个异步mock，但不能直接await
            mock_aredis_instance = Mock(spec=aredis.Redis)
            # 让Redis构造函数返回一个awaitable对象
            async def mock_redis_constructor(*args, **kwargs):
                return mock_aredis_instance
            mock_aredis_class.side_effect = mock_redis_constructor

            self.redis_dao._hosts = ["127.0.0.1:6379"]
            self.redis_dao._password = "test_password"
            self.redis_dao._cluster = False

            result = await self.redis_dao._aconnect()

            assert result == mock_aredis_instance
            mock_pool_class.assert_called_once_with(
                host="127.0.0.1",
                port=6379,
                password="test_password"
            )
            mock_aredis_class.assert_called_once_with(connection_pool=mock_pool_instance)

    def test_set_get_operations(self):
        """测试基本的设置和获取操作"""
        # 模拟 Redis 实例
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.set.return_value = True
        mock_redis.get.return_value = "test_value"
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None  # 不使用 CAMS
        
        # 测试设置
        result = self.redis_dao.set("key1", "test_value")
        assert result is True
        mock_redis.set.assert_called_once_with("test_key1", "test_value", ex=None, nx=False)
        
        # 测试获取
        value = self.redis_dao.get("key1")
        assert value == "test_value"
        mock_redis.get.assert_called_once_with("test_key1")

    def test_set_with_expire(self):
        """测试带过期时间的设置"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.set.return_value = True
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        result = self.redis_dao.set("key1", "test_value", 3600)
        assert result is True
        mock_redis.set.assert_called_once_with("test_key1", "test_value", ex=3600, nx=False)

    @pytest.mark.asyncio
    async def test_async_set_get_operations(self):
        """测试异步设置和获取操作"""
        # 模拟异步 Redis 实例
        mock_aredis = AsyncMock(spec=aredis.Redis)
        # 异步方法需要返回awaitable对象
        mock_aredis.set = AsyncMock(return_value=True)
        mock_aredis.get = AsyncMock(return_value="async_test_value")
        
        self.redis_dao.ard = mock_aredis
        self.redis_dao._prefix = "async_"
        self.redis_dao._cams_host = None
        
        # 测试异步设置
        result = await self.redis_dao.aset("key1", "async_test_value")
        assert result is True
        mock_aredis.set.assert_called_once_with("async_key1", "async_test_value", ex=None, nx=False)
        
        # 测试异步获取
        value = await self.redis_dao.aget("key1")
        assert value == "async_test_value"
        mock_aredis.get.assert_called_once_with("async_key1")

    def test_expire_operations(self):
        """测试过期时间操作"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.expire.return_value = True
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        result = self.redis_dao.expire("key1", 3600)
        assert result is True
        mock_redis.expire.assert_called_once_with("test_key1", 3600)

    def test_remove_operations(self):
        """测试删除操作"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.delete.return_value = 1
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        result = self.redis_dao.remove("key1")
        assert result == 1  # Redis delete返回删除的键数量
        mock_redis.delete.assert_called_once_with("test_key1")

    def test_incrby_operations(self):
        """测试递增操作"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.incrby.return_value = 5
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        result = self.redis_dao.incrby("counter", 3)
        assert result == 5
        mock_redis.incrby.assert_called_once_with("test_counter", 3)

    def test_zadd_operations(self):
        """测试有序集合添加操作"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.zadd.return_value = 2
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        mapping = {"member1": 1.0, "member2": 2.0}
        self.redis_dao.zadd("zset_key", mapping)
        mock_redis.zadd.assert_called_once_with("test_zset_key", mapping=mapping, nx=False)

    def test_zrange_operations(self):
        """测试有序集合范围查询操作"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.zrange.return_value = ["member1", "member2"]
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        result = self.redis_dao.zrang("zset_key", 0, -1)
        assert result == ["member1", "member2"]
        mock_redis.zrange.assert_called_once_with(name="test_zset_key", start=0, end=-1, withscores=False)

    def test_zrange_with_scores(self):
        """测试带分数的有序集合范围查询"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.zrange.return_value = [("member1", 1.0), ("member2", 2.0)]
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        result = self.redis_dao.zrang("zset_key", 0, -1, withscores=True)
        assert result == [("member1", 1.0), ("member2", 2.0)]
        mock_redis.zrange.assert_called_once_with(name="test_zset_key", start=0, end=-1, withscores=True)

    @patch('commons.db.redis5dao.Redis5Dao._get_cams')
    def test_get_with_cams(self, mock_get_cams):
        """测试使用 CAMS 的获取操作"""
        mock_get_cams.return_value = "cams_value"
        
        self.redis_dao._cams_host = "http://cams-host"
        self.redis_dao._prefix = "cams_"
        
        result = self.redis_dao.get("key1")
        assert result == "cams_value"
        mock_get_cams.assert_called_once_with("key1", None)

    @patch('commons.db.redis5dao.Redis5Dao._set_cams')
    def test_set_with_cams(self, mock_set_cams):
        """测试使用 CAMS 的设置操作"""
        mock_set_cams.return_value = True
        
        self.redis_dao._cams_host = "http://cams-host"
        self.redis_dao._prefix = "cams_"
        
        result = self.redis_dao.set("key1", "cams_value")
        assert result is True
        mock_set_cams.assert_called_once_with("key1", "cams_value", None)

    def test_get_default_value(self):
        """测试获取默认值"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.get.return_value = None
        
        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None
        
        result = self.redis_dao.get("nonexistent_key", "default_value")
        assert result == "default_value"

    def test_error_handling(self):
        """测试错误处理"""
        mock_redis = Mock(spec=redis.Redis)
        mock_redis.get.side_effect = Exception("Redis connection error")

        self.redis_dao.rd = mock_redis
        self.redis_dao._prefix = "test_"
        self.redis_dao._cams_host = None

        # 应该重新抛出异常
        with pytest.raises(Exception, match="Redis connection error"):
            self.redis_dao.get("key1", "default")

    def test_init_parameters_setting(self):
        """测试初始化参数设置"""
        # 直接测试参数设置，不涉及复杂的连接逻辑
        self.redis_dao._hosts = ["localhost:6379"]
        self.redis_dao._password = "test_password"
        self.redis_dao._prefix = "test_prefix"
        self.redis_dao._cluster = False

        # 验证参数设置
        assert self.redis_dao._hosts == ["localhost:6379"]
        assert self.redis_dao._password == "test_password"
        assert self.redis_dao._prefix == "test_prefix"
        assert self.redis_dao._cluster is False

    def test_init_cams_configuration(self):
        """测试 CAMS 配置初始化"""
        # 执行 CAMS 配置初始化
        self.redis_dao.init_cams(
            ak="test_ak",
            sk="test_sk",
            host="test_host",
            prefix="test_prefix"
        )

        # 验证 CAMS 配置
        assert self.redis_dao._cams_ak == "test_ak"
        assert self.redis_dao._cams_sk == "test_sk"
        assert self.redis_dao._cams_host == "test_host"
        assert self.redis_dao._prefix == "test_prefix"

    def test_basic_attributes(self):
        """测试基本属性"""
        # 验证初始属性
        assert hasattr(self.redis_dao, '_hosts')
        assert hasattr(self.redis_dao, '_password')
        assert hasattr(self.redis_dao, 'rd')
        assert hasattr(self.redis_dao, 'ard')
        assert hasattr(self.redis_dao, '_prefix')
        assert hasattr(self.redis_dao, '_cluster')

        # 验证初始值
        assert self.redis_dao._hosts == []
        assert self.redis_dao._password is None
        assert self.redis_dao.rd is None
        assert self.redis_dao.ard is None
        assert self.redis_dao._prefix == ""
        assert self.redis_dao._cluster is False

    def test_cams_attributes(self):
        """测试 CAMS 相关属性"""
        # 验证 CAMS 属性存在
        assert hasattr(self.redis_dao, '_cams_ak')
        assert hasattr(self.redis_dao, '_cams_sk')
        assert hasattr(self.redis_dao, '_cams_host')

        # 验证初始值
        assert self.redis_dao._cams_ak is None
        assert self.redis_dao._cams_sk is None
        assert self.redis_dao._cams_host is None

    @patch('commons.db.redis5dao.requests.post')
    def test_call_cams_success(self, mock_post):
        """测试 CAMS 调用成功"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"code": 0, "msg": "success", "data": {"val": "test_value"}}'
        mock_post.return_value = mock_response

        # 执行 CAMS 调用
        with patch('commons.db.redis5dao.sig_wps4', return_value="test_signature"):
            result = self.redis_dao._call_cams("/test/uri", {"key": "test_key"})

        # 验证结果
        assert result.code == 0
        assert result.msg == "success"
        assert result.data.val == "test_value"

        # 验证请求被正确调用
        mock_post.assert_called_once()

    @patch('commons.db.redis5dao.aiohttp.ClientSession')
    @pytest.mark.asyncio
    async def test_acall_cams_success(self, mock_session_class):
        """测试异步 CAMS 调用成功"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟异步响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value='{"code": 0, "msg": "success", "data": {"val": "async_test_value"}}')

        # 创建模拟的请求上下文管理器
        mock_request_cm = AsyncMock()
        mock_request_cm.__aenter__ = AsyncMock(return_value=mock_response)
        mock_request_cm.__aexit__ = AsyncMock(return_value=None)

        # 创建模拟的会话
        mock_session = AsyncMock()
        mock_session.post = Mock(return_value=mock_request_cm)

        # 创建模拟的会话上下文管理器
        mock_session_cm = AsyncMock()
        mock_session_cm.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_cm.__aexit__ = AsyncMock(return_value=None)

        mock_session_class.return_value = mock_session_cm

        # 执行异步 CAMS 调用
        with patch('commons.db.redis5dao.sig_wps4', return_value="test_signature"):
            result = await self.redis_dao._acall_cams("/test/uri", {"key": "test_key"})

        # 验证结果
        assert result.code == 0
        assert result.msg == "success"
        assert result.data.val == "async_test_value"

    def test_get_cams_success(self):
        """测试 CAMS get 操作成功"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _call_cams 返回成功结果
        mock_result = Mock()
        mock_result.data = Mock()
        mock_result.data.val = "cached_value"

        with patch.object(self.redis_dao, '_call_cams', return_value=mock_result):
            result = self.redis_dao._get_cams("test_key", "default_value")

        # 验证结果
        assert result == "cached_value"

    def test_get_cams_no_data(self):
        """测试 CAMS get 操作无数据"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _call_cams 返回无数据结果
        mock_result = Mock()
        mock_result.data = None

        with patch.object(self.redis_dao, '_call_cams', return_value=mock_result):
            result = self.redis_dao._get_cams("test_key", "default_value")

        # 验证返回默认值
        assert result == "default_value"

    def test_get_cams_empty_val(self):
        """测试 CAMS get 操作空值"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _call_cams 返回空值结果
        mock_result = Mock()
        mock_result.data = Mock()
        mock_result.data.val = None

        with patch.object(self.redis_dao, '_call_cams', return_value=mock_result):
            result = self.redis_dao._get_cams("test_key", "default_value")

        # 验证返回默认值
        assert result == "default_value"

    @pytest.mark.asyncio
    async def test_aget_cams_success(self):
        """测试异步 CAMS get 操作成功"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _acall_cams 返回成功结果
        mock_result = Mock()
        mock_result.data = Mock()
        mock_result.data.val = "async_cached_value"

        with patch.object(self.redis_dao, '_acall_cams', return_value=mock_result):
            result = await self.redis_dao._aget_cams("test_key", "default_value")

        # 验证结果
        assert result == "async_cached_value"

    @pytest.mark.asyncio
    async def test_aget_cams_no_data(self):
        """测试异步 CAMS get 操作无数据"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _acall_cams 返回无数据结果
        mock_result = Mock()
        mock_result.data = None

        with patch.object(self.redis_dao, '_acall_cams', return_value=mock_result):
            result = await self.redis_dao._aget_cams("test_key", "default_value")

        # 验证返回默认值
        assert result == "default_value"

    def test_set_cams_success(self):
        """测试 CAMS set 操作成功"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _call_cams 返回成功结果
        mock_result = Mock()
        mock_result.code = 0

        with patch.object(self.redis_dao, '_call_cams', return_value=mock_result):
            result = self.redis_dao._set_cams("test_key", "test_value", 3600)

        # 验证结果
        assert result is True

    def test_set_cams_failure(self):
        """测试 CAMS set 操作失败"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _call_cams 返回失败结果
        mock_result = Mock()
        mock_result.code = 1

        with patch.object(self.redis_dao, '_call_cams', return_value=mock_result):
            result = self.redis_dao._set_cams("test_key", "test_value", 3600)

        # 验证结果
        assert result is False

    @pytest.mark.asyncio
    async def test_aset_cams_success(self):
        """测试异步 CAMS set 操作成功"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _acall_cams 返回成功结果
        mock_result = Mock()
        mock_result.code = 0

        with patch.object(self.redis_dao, '_acall_cams', return_value=mock_result):
            result = await self.redis_dao._aset_cams("test_key", "test_value", 3600)

        # 验证结果
        assert result is True

    def test_get_with_cams_host(self):
        """测试带 CAMS 主机的 get 操作"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _get_cams 返回结果
        with patch.object(self.redis_dao, '_get_cams', return_value="cams_value") as mock_get_cams:
            result = self.redis_dao.get("test_key", "default_value")

        # 验证使用 CAMS 获取
        assert result == "cams_value"
        mock_get_cams.assert_called_once_with("test_key", "default_value")

    @pytest.mark.asyncio
    async def test_aget_with_cams_host(self):
        """测试带 CAMS 主机的异步 get 操作"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _aget_cams 返回结果
        with patch.object(self.redis_dao, '_aget_cams', return_value="async_cams_value") as mock_aget_cams:
            result = await self.redis_dao.aget("test_key", "default_value")

        # 验证使用 CAMS 获取
        assert result == "async_cams_value"
        mock_aget_cams.assert_called_once_with("test_key", "default_value")

    def test_set_with_cams_host(self):
        """测试带 CAMS 主机的 set 操作"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _set_cams 返回结果
        with patch.object(self.redis_dao, '_set_cams', return_value=True) as mock_set_cams:
            result = self.redis_dao.set("test_key", "test_value", 3600)

        # 验证使用 CAMS 设置
        assert result is True
        mock_set_cams.assert_called_once_with("test_key", "test_value", 3600)

    @pytest.mark.asyncio
    async def test_aset_with_cams_host(self):
        """测试带 CAMS 主机的异步 set 操作"""
        # 设置 CAMS 配置
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 _aset_cams 返回结果
        with patch.object(self.redis_dao, '_aset_cams', return_value=True) as mock_aset_cams:
            result = await self.redis_dao.aset("test_key", "test_value", 3600)

        # 验证使用 CAMS 设置
        assert result is True
        mock_aset_cams.assert_called_once_with("test_key", "test_value", 3600)

    def test_getint_success(self):
        """测试成功获取整数"""
        # 模拟 get 方法返回字符串数字
        with patch.object(self.redis_dao, 'get', return_value="123"):
            result = self.redis_dao.getint("test_key", 0)
            assert result == 123

    def test_getint_default_value(self):
        """测试获取整数默认值"""
        # 模拟 get 方法返回默认值
        with patch.object(self.redis_dao, 'get', return_value=0):
            result = self.redis_dao.getint("nonexistent_key", 42)
            assert result == 0

    @pytest.mark.asyncio
    async def test_agetint_success(self):
        """测试异步成功获取整数"""
        # 模拟 aget 方法返回字符串数字
        with patch.object(self.redis_dao, 'aget', return_value="456"):
            result = await self.redis_dao.agetint("test_key", 0)
            assert result == 456

    def test_getstring_bytes_conversion(self):
        """测试获取字符串时字节转换"""
        # 模拟 get 方法返回字节
        with patch.object(self.redis_dao, 'get', return_value=b"test_string"):
            result = self.redis_dao.getstring("test_key", "")
            assert result == "test_string"

    def test_getstring_already_string(self):
        """测试获取已经是字符串的值"""
        # 模拟 get 方法返回字符串
        with patch.object(self.redis_dao, 'get', return_value="already_string"):
            result = self.redis_dao.getstring("test_key", "")
            assert result == "already_string"

    @pytest.mark.asyncio
    async def test_agetstring_bytes_conversion(self):
        """测试异步获取字符串时字节转换"""
        # 模拟 aget 方法返回字节
        with patch.object(self.redis_dao, 'aget', return_value=b"async_string"):
            result = await self.redis_dao.agetstring("test_key", "")
            assert result == "async_string"

    def test_getbool_truthy_values(self):
        """测试获取布尔值的真值"""
        truthy_values = [1, "1", "true", "True", b"1", "yes"]

        for value in truthy_values:
            with patch.object(self.redis_dao, 'get', return_value=value):
                result = self.redis_dao.getbool("test_key", False)
                assert result is True

    def test_getbool_falsy_values(self):
        """测试获取布尔值的假值"""
        falsy_values = [0, "", None, b"", "false", "False"]

        for value in falsy_values:
            with patch.object(self.redis_dao, 'get', return_value=value):
                result = self.redis_dao.getbool("test_key", True)
                # bool() 转换的结果
                expected = bool(value)
                assert result == expected

    @pytest.mark.asyncio
    async def test_agetbool_success(self):
        """测试异步获取布尔值成功"""
        # 模拟 aget 方法返回真值
        with patch.object(self.redis_dao, 'aget', return_value=1):
            result = await self.redis_dao.agetbool("test_key", False)
            assert result is True

    def test_redis_operations_without_cams(self):
        """测试不使用 CAMS 的 Redis 操作"""
        # 确保没有设置 CAMS 主机
        self.redis_dao._cams_host = None

        # 模拟 _get 方法
        with patch.object(self.redis_dao, '_get', return_value="redis_value") as mock_get:
            result = self.redis_dao.get("test_key", "default")
            assert result == "redis_value"
            mock_get.assert_called_once_with("test_key", "default")

    @pytest.mark.asyncio
    async def test_redis_async_operations_without_cams(self):
        """测试不使用 CAMS 的异步 Redis 操作"""
        # 确保没有设置 CAMS 主机
        self.redis_dao._cams_host = None

        # 模拟 _aget 方法
        with patch.object(self.redis_dao, '_aget', return_value="async_redis_value") as mock_aget:
            result = await self.redis_dao.aget("test_key", "default")
            assert result == "async_redis_value"
            mock_aget.assert_called_once_with("test_key", "default")

    def test_redis_error_handling_simulation(self):
        """测试 Redis 错误处理模拟"""
        # 模拟各种错误场景
        error_scenarios = [
            {"error": "ConnectionError", "description": "连接失败"},
            {"error": "TimeoutError", "description": "超时"},
            {"error": "AuthenticationError", "description": "认证失败"},
        ]

        # 验证错误场景结构
        for scenario in error_scenarios:
            assert "error" in scenario
            assert "description" in scenario
            assert isinstance(scenario["error"], str)
            assert isinstance(scenario["description"], str)

    def test_redis_general_exception_handling(self):
        """测试 Redis 一般异常处理"""
        # 确保没有设置 CAMS 主机
        self.redis_dao._cams_host = None

        # 模拟一般异常
        with patch.object(self.redis_dao, '_get', side_effect=Exception("一般错误")):
            with patch('commons.db.redis5dao.logging.exception') as mock_exception:
                with pytest.raises(Exception):
                    self.redis_dao.get("test_key", "default")

    def test_type_conversion_edge_cases(self):
        """测试类型转换边界情况"""
        # 测试整数转换边界情况
        edge_cases = [
            ("0", 0),
            ("-1", -1),
            ("999999", 999999),
            (b"123", 123),
        ]

        for input_value, expected in edge_cases:
            with patch.object(self.redis_dao, 'get', return_value=input_value):
                result = self.redis_dao.getint("test_key", 0)
                assert result == expected

    def test_string_encoding_edge_cases(self):
        """测试字符串编码边界情况"""
        # 测试各种编码情况
        test_cases = [
            (b"\xe4\xb8\xad\xe6\x96\x87", "中文"),  # UTF-8 中文
            (b"english", "english"),  # 英文
            ("already_string", "already_string"),  # 已经是字符串
            (b"", ""),  # 空字节
            ("", ""),  # 空字符串
        ]

        for input_value, expected in test_cases:
            with patch.object(self.redis_dao, 'get', return_value=input_value):
                result = self.redis_dao.getstring("test_key", "")
                assert result == expected

    def test_prefix_handling(self):
        """测试前缀处理"""
        # 设置前缀
        self.redis_dao._prefix = "test_prefix:"

        # 模拟 _get 方法，验证前缀被正确添加
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.get.return_value = "value"

            result = self.redis_dao._get("test_key", "default")

            # 验证调用时使用了前缀
            mock_rd.get.assert_called_once_with("test_prefix:test_key")

    @pytest.mark.asyncio
    async def test_async_prefix_handling(self):
        """测试异步前缀处理"""
        # 设置前缀
        self.redis_dao._prefix = "async_prefix:"

        # 模拟 _aget 方法，验证前缀被正确添加
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.get = AsyncMock(return_value="async_value")

            result = await self.redis_dao._aget("test_key", "default")

            # 验证调用时使用了前缀
            mock_ard.get.assert_called_once_with("async_prefix:test_key")

    @patch('commons.db.redis5dao.redis.Redis')
    @patch('commons.db.redis5dao.aredis.Redis')
    @pytest.mark.asyncio
    async def test_close_single_node(self, mock_aredis_class, mock_redis_class):
        """测试关闭单节点连接"""
        # 模拟连接
        mock_redis = Mock()
        mock_aredis = AsyncMock()
        mock_redis.close = Mock()
        mock_aredis.aclose = AsyncMock()

        mock_redis_class.return_value = mock_redis
        mock_aredis_class.return_value = mock_aredis

        # 设置连接
        self.redis_dao.rd = mock_redis
        self.redis_dao.ard = mock_aredis
        self.redis_dao._cluster = False

        # 执行关闭
        await self.redis_dao.close()

        # 验证关闭操作
        mock_redis.close.assert_called_once()
        mock_aredis.aclose.assert_called_once_with(True)

    @patch('commons.db.redis5dao.redis.RedisCluster')
    @patch('commons.db.redis5dao.aredis.RedisCluster')
    @pytest.mark.asyncio
    async def test_close_cluster_mode(self, mock_aredis_cluster_class, mock_redis_cluster_class):
        """测试关闭集群模式连接"""
        # 模拟集群连接
        mock_redis_cluster = Mock()
        mock_aredis_cluster = AsyncMock()
        mock_redis_cluster.close = Mock()
        mock_aredis_cluster.aclose = AsyncMock()

        mock_redis_cluster_class.return_value = mock_redis_cluster
        mock_aredis_cluster_class.return_value = mock_aredis_cluster

        # 设置连接
        self.redis_dao.rd = mock_redis_cluster
        self.redis_dao.ard = mock_aredis_cluster
        self.redis_dao._cluster = True

        # 执行关闭
        await self.redis_dao.close()

        # 验证关闭操作
        mock_redis_cluster.close.assert_called_once()
        mock_aredis_cluster.aclose.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_no_connections(self):
        """测试关闭时没有连接"""
        # 确保没有连接
        self.redis_dao.rd = None
        self.redis_dao.ard = None

        # 执行关闭（不应该抛出异常）
        await self.redis_dao.close()

        # 验证没有异常
        assert True

    def test_redis_operations_with_prefix(self):
        """测试带前缀的 Redis 操作"""
        # 设置前缀
        self.redis_dao._prefix = "app:"

        # 模拟 Redis 连接
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.get.return_value = "test_value"
            mock_rd.set.return_value = True
            mock_rd.delete.return_value = 1
            mock_rd.exists.return_value = 1
            mock_rd.expire.return_value = True

            # 测试各种操作
            result = self.redis_dao._get("test_key", "default")
            assert result == "test_value"
            mock_rd.get.assert_called_with("app:test_key")

            result = self.redis_dao._set("test_key", "test_value", 3600)
            assert result is True
            # 验证调用（可能包含额外参数）
            call_args = mock_rd.set.call_args
            assert call_args[0] == ("app:test_key", "test_value")
            assert call_args[1]["ex"] == 3600

            # 测试其他操作（如果方法存在）
            if hasattr(self.redis_dao, '_delete'):
                result = self.redis_dao._delete("test_key")
                assert result == 1
                mock_rd.delete.assert_called_with("app:test_key")

            if hasattr(self.redis_dao, '_exists'):
                result = self.redis_dao._exists("test_key")
                assert result == 1
                mock_rd.exists.assert_called_with("app:test_key")

            if hasattr(self.redis_dao, '_expire'):
                result = self.redis_dao._expire("test_key", 7200)
                assert result is True
                mock_rd.expire.assert_called_with("app:test_key", 7200)

    @pytest.mark.asyncio
    async def test_async_redis_operations_with_prefix(self):
        """测试带前缀的异步 Redis 操作"""
        # 设置前缀
        self.redis_dao._prefix = "async_app:"

        # 模拟异步 Redis 连接
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.get = AsyncMock(return_value="async_value")
            mock_ard.set = AsyncMock(return_value=True)
            mock_ard.delete = AsyncMock(return_value=1)
            mock_ard.exists = AsyncMock(return_value=1)
            mock_ard.expire = AsyncMock(return_value=True)

            # 测试各种异步操作
            result = await self.redis_dao._aget("test_key", "default")
            assert result == "async_value"
            mock_ard.get.assert_called_with("async_app:test_key")

            result = await self.redis_dao._aset("test_key", "test_value", 3600)
            assert result is True
            # 验证调用（可能包含额外参数）
            call_args = mock_ard.set.call_args
            assert call_args[0] == ("async_app:test_key", "test_value")
            assert call_args[1]["ex"] == 3600

            # 测试其他异步操作（如果方法存在）
            if hasattr(self.redis_dao, '_adelete'):
                result = await self.redis_dao._adelete("test_key")
                assert result == 1
                mock_ard.delete.assert_called_with("async_app:test_key")

            if hasattr(self.redis_dao, '_aexists'):
                result = await self.redis_dao._aexists("test_key")
                assert result == 1
                mock_ard.exists.assert_called_with("async_app:test_key")

            if hasattr(self.redis_dao, '_aexpire'):
                result = await self.redis_dao._aexpire("test_key", 7200)
                assert result is True
                mock_ard.expire.assert_called_with("async_app:test_key", 7200)

    def test_redis_operations_without_prefix(self):
        """测试不带前缀的 Redis 操作"""
        # 不设置前缀
        self.redis_dao._prefix = ""

        # 模拟 Redis 连接
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.get.return_value = "no_prefix_value"

            result = self.redis_dao._get("test_key", "default")
            assert result == "no_prefix_value"
            mock_rd.get.assert_called_with("test_key")  # 没有前缀

    def test_redis_operations_error_handling(self):
        """测试 Redis 操作错误处理"""
        # 确保没有设置 CAMS 主机
        self.redis_dao._cams_host = None

        # 模拟 Redis 连接错误
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.get.side_effect = Exception("Redis 连接错误")

            # 测试错误处理
            with pytest.raises(Exception):
                self.redis_dao._get("test_key", "default")

    @pytest.mark.asyncio
    async def test_async_redis_operations_error_handling(self):
        """测试异步 Redis 操作错误处理"""
        # 确保没有设置 CAMS 主机
        self.redis_dao._cams_host = None

        # 模拟异步 Redis 连接错误
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.get = AsyncMock(side_effect=Exception("异步 Redis 连接错误"))

            # 测试错误处理
            with pytest.raises(Exception):
                await self.redis_dao._aget("test_key", "default")

    def test_redis_data_type_operations(self):
        """测试 Redis 数据类型操作"""
        # 模拟 Redis 连接
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            # 测试列表操作
            mock_rd.lpush.return_value = 1
            mock_rd.rpop.return_value = "list_item"
            mock_rd.llen.return_value = 5

            # 测试哈希操作
            mock_rd.hset.return_value = 1
            mock_rd.hget.return_value = "hash_value"
            mock_rd.hdel.return_value = 1

            # 测试集合操作
            mock_rd.sadd.return_value = 1
            mock_rd.smembers.return_value = {"member1", "member2"}
            mock_rd.srem.return_value = 1

            # 验证操作可以调用（如果方法存在）
            if hasattr(self.redis_dao, '_lpush'):
                result = self.redis_dao._lpush("list_key", "item")
                assert result == 1

            if hasattr(self.redis_dao, '_hset'):
                result = self.redis_dao._hset("hash_key", "field", "value")
                assert result == 1

            if hasattr(self.redis_dao, '_sadd'):
                result = self.redis_dao._sadd("set_key", "member")
                assert result == 1

    def test_redis_transaction_operations(self):
        """测试 Redis 事务操作"""
        # 模拟 Redis 连接
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            # 模拟事务
            mock_pipeline = Mock()
            mock_pipeline.multi.return_value = None
            mock_pipeline.set.return_value = None
            mock_pipeline.expire.return_value = None
            mock_pipeline.execute.return_value = [True, True]

            mock_rd.pipeline.return_value = mock_pipeline

            # 验证事务操作可以调用（如果方法存在）
            if hasattr(self.redis_dao, '_pipeline'):
                pipeline = self.redis_dao._pipeline()
                assert pipeline is not None

    def test_init_cams_configuration(self):
        """测试 CAMS 配置初始化"""
        ak = "test_ak"
        sk = "test_sk"
        host = "http://test-cams.com"
        prefix = "test_prefix_"

        self.redis_dao.init_cams(ak, sk, host, prefix)

        assert self.redis_dao._cams_ak == ak
        assert self.redis_dao._cams_sk == sk
        assert self.redis_dao._cams_host == host
        assert self.redis_dao._prefix == prefix

    @patch('commons.db.redis5dao.sig_wps4')
    @patch('commons.db.redis5dao.time.strftime')
    def test_sig_cams_without_body(self, mock_strftime, mock_sig_wps4):
        """测试 CAMS 签名生成（无请求体）"""
        mock_strftime.return_value = "Mon, 01 Jan 2024 00:00:00 GMT"
        mock_sig_wps4.return_value = {"Authorization": "test_auth"}

        self.redis_dao._cams_ak = "test_ak"
        self.redis_dao._cams_sk = "test_sk"

        result = self.redis_dao._sig_cams("GET", "/test/uri")

        mock_sig_wps4.assert_called_once_with(
            "GET", "/test/uri", None, "test_ak", "test_sk", "Mon, 01 Jan 2024 00:00:00 GMT"
        )
        assert result == {"Authorization": "test_auth"}

    @patch('commons.db.redis5dao.sig_wps4')
    @patch('commons.db.redis5dao.time.strftime')
    @patch('commons.db.redis5dao.json.dumps')
    def test_sig_cams_with_body(self, mock_dumps, mock_strftime, mock_sig_wps4):
        """测试 CAMS 签名生成（有请求体）"""
        mock_strftime.return_value = "Mon, 01 Jan 2024 00:00:00 GMT"
        mock_dumps.return_value.encode.return_value = b'{"key": "value"}'
        mock_sig_wps4.return_value = {"Authorization": "test_auth"}

        self.redis_dao._cams_ak = "test_ak"
        self.redis_dao._cams_sk = "test_sk"

        body = {"key": "value"}
        result = self.redis_dao._sig_cams("POST", "/test/uri", body)

        mock_dumps.assert_called_once_with(body)
        mock_sig_wps4.assert_called_once_with(
            "POST", "/test/uri", b'{"key": "value"}', "test_ak", "test_sk", "Mon, 01 Jan 2024 00:00:00 GMT"
        )
        assert result == {"Authorization": "test_auth"}

    @patch('commons.db.redis5dao.requests.post')
    def test_call_cams_success(self, mock_post):
        """测试 CAMS 调用成功"""
        # 设置 CAMS 配置
        self.redis_dao._cams_host = "http://test-cams.com"

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"code": 0, "msg": "success", "data": {"val": "test_value"}}'
        mock_post.return_value = mock_response

        with patch.object(self.redis_dao, '_sig_cams') as mock_sig:
            mock_sig.return_value = {"Authorization": "test_auth"}

            result = self.redis_dao._call_cams("/test/uri", {"key": "value"})

            assert result.code == 0
            assert result.msg == "success"
            assert result.data.val == "test_value"

            mock_post.assert_called_once_with(
                "http://test-cams.com/test/uri",
                json={"key": "value"},
                headers={"Authorization": "test_auth"}
            )

    @patch('commons.db.redis5dao.requests.post')
    def test_call_cams_http_error(self, mock_post):
        """测试 CAMS 调用 HTTP 错误"""
        self.redis_dao._cams_host = "http://test-cams.com"

        # 模拟 HTTP 错误
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response

        with patch.object(self.redis_dao, '_sig_cams') as mock_sig:
            mock_sig.return_value = {"Authorization": "test_auth"}

            with pytest.raises(Exception) as exc_info:
                self.redis_dao._call_cams("/test/uri", {"key": "value"})

            assert "cams redis rpc request fail" in str(exc_info.value)
            assert "status_code: 500" in str(exc_info.value)

    @patch('commons.db.redis5dao.requests.post')
    def test_call_cams_business_error(self, mock_post):
        """测试 CAMS 调用业务错误"""
        self.redis_dao._cams_host = "http://test-cams.com"

        # 模拟业务错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"code": 1, "msg": "business error"}'
        mock_post.return_value = mock_response

        with patch.object(self.redis_dao, '_sig_cams') as mock_sig:
            mock_sig.return_value = {"Authorization": "test_auth"}

            with pytest.raises(Exception) as exc_info:
                self.redis_dao._call_cams("/test/uri", {"key": "value"})

            assert "cams redis rpc request fail" in str(exc_info.value)



    def test_set_cams_success(self):
        """测试 CAMS 设置操作成功"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_call.return_value = mock_resp

            result = self.redis_dao._set_cams("test_key", "test_value", 3600)

            assert result is True
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/set",
                {
                    "key": f"{self.redis_dao._prefix}test_key",
                    "val": "test_value",
                    "expire": 3600
                }
            )

    def test_set_cams_without_expire(self):
        """测试 CAMS 设置操作（无过期时间）"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_call.return_value = mock_resp

            result = self.redis_dao._set_cams("test_key", "test_value")

            assert result is True
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/set",
                {
                    "key": f"{self.redis_dao._prefix}test_key",
                    "val": "test_value"
                }
            )

    def test_set_cams_failure(self):
        """测试 CAMS 设置操作失败"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = 1  # 非 OK 状态
            mock_call.return_value = mock_resp

            result = self.redis_dao._set_cams("test_key", "test_value")

            assert result is False

    @pytest.mark.asyncio
    async def test_aset_cams_success(self):
        """测试异步 CAMS 设置操作成功"""
        with patch.object(self.redis_dao, '_acall_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_call.return_value = mock_resp

            result = await self.redis_dao._aset_cams("test_key", "test_value", 3600)

            assert result is True
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/set",
                {
                    "key": f"{self.redis_dao._prefix}test_key",
                    "val": "test_value",
                    "expire": 3600
                }
            )

    @pytest.mark.asyncio
    async def test_aset_cams_failure(self):
        """测试异步 CAMS 设置操作失败"""
        with patch.object(self.redis_dao, '_acall_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = 1  # 非 OK 状态
            mock_call.return_value = mock_resp

            result = await self.redis_dao._aset_cams("test_key", "test_value")

            assert result is False

    def test_set_without_cams_success(self):
        """测试不使用 CAMS 的设置操作成功"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_set') as mock_set:
            mock_set.return_value = True

            result = self.redis_dao.set("test_key", "test_value", ex=3600, nx=True)

            assert result is True
            mock_set.assert_called_once_with("test_key", "test_value", 3600, True)



    def test_get_cams_success(self):
        """测试 CAMS 获取操作成功"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_resp.data = Mock()
            mock_resp.data.val = "cached_value"
            mock_call.return_value = mock_resp

            result = self.redis_dao._get_cams("test_key", "default_value")

            assert result == "cached_value"
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/get",
                {"key": f"{self.redis_dao._prefix}test_key"}
            )

    def test_get_cams_no_data(self):
        """测试 CAMS 获取操作无数据"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_resp.data = None
            mock_call.return_value = mock_resp

            result = self.redis_dao._get_cams("test_key", "default_value")

            assert result == "default_value"

    def test_get_cams_empty_val(self):
        """测试 CAMS 获取操作空值"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_resp.data = Mock()
            mock_resp.data.val = None
            mock_call.return_value = mock_resp

            result = self.redis_dao._get_cams("test_key", "default_value")

            assert result == "default_value"

    def test_set_connection_error_recovery(self):
        """测试设置操作连接错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_set') as mock_set, \
             patch.object(self.redis_dao, '_connect') as mock_connect:

            # 第一次调用抛出连接错误，第二次成功
            mock_set.side_effect = [ConnectionError("连接失败"), True]
            mock_connect.return_value = Mock()

            result = self.redis_dao.set("test_key", "test_value")

            assert result is True
            assert mock_set.call_count == 2
            mock_connect.assert_called_once()

    def test_set_timeout_error_recovery(self):
        """测试设置操作超时错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_set') as mock_set, \
             patch.object(self.redis_dao, '_connect') as mock_connect:

            # 第一次调用抛出超时错误，第二次成功
            mock_set.side_effect = [TimeoutError("超时"), True]
            mock_connect.return_value = Mock()

            result = self.redis_dao.set("test_key", "test_value")

            assert result is True
            assert mock_set.call_count == 2
            mock_connect.assert_called_once()

    def test_set_connection_error_then_exception(self):
        """测试设置操作连接错误后再次异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_set') as mock_set, \
             patch.object(self.redis_dao, '_connect') as mock_connect:

            # 第一次连接错误，第二次其他异常
            mock_set.side_effect = [ConnectionError("连接失败"), Exception("其他错误")]
            mock_connect.return_value = Mock()

            with pytest.raises(Exception, match="其他错误"):
                self.redis_dao.set("test_key", "test_value")

            assert mock_set.call_count == 2
            mock_connect.assert_called_once()

    def test_set_general_exception(self):
        """测试设置操作一般异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_set') as mock_set:
            mock_set.side_effect = Exception("一般错误")

            with pytest.raises(Exception, match="一般错误"):
                self.redis_dao.set("test_key", "test_value")

    @pytest.mark.asyncio
    async def test_aset_connection_error_recovery(self):
        """测试异步设置操作连接错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aset') as mock_aset, \
             patch.object(self.redis_dao, '_aconnect') as mock_aconnect:

            # 第一次调用抛出连接错误，第二次成功
            mock_aset.side_effect = [ConnectionError("连接失败"), True]
            mock_aconnect.return_value = Mock()

            result = await self.redis_dao.aset("test_key", "test_value")

            assert result is True
            assert mock_aset.call_count == 2
            mock_aconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_aset_timeout_error_recovery(self):
        """测试异步设置操作超时错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aset') as mock_aset, \
             patch.object(self.redis_dao, '_aconnect') as mock_aconnect:

            # 第一次调用抛出超时错误，第二次成功
            mock_aset.side_effect = [TimeoutError("超时"), True]
            mock_aconnect.return_value = Mock()

            result = await self.redis_dao.aset("test_key", "test_value")

            assert result is True
            assert mock_aset.call_count == 2
            mock_aconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_aset_connection_error_then_exception(self):
        """测试异步设置操作连接错误后再次异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aset') as mock_aset, \
             patch.object(self.redis_dao, '_aconnect') as mock_aconnect:

            # 第一次连接错误，第二次其他异常
            mock_aset.side_effect = [ConnectionError("连接失败"), Exception("其他错误")]
            mock_aconnect.return_value = Mock()

            with pytest.raises(Exception, match="其他错误"):
                await self.redis_dao.aset("test_key", "test_value")

            assert mock_aset.call_count == 2
            mock_aconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_aset_general_exception(self):
        """测试异步设置操作一般异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aset') as mock_aset:
            mock_aset.side_effect = Exception("一般错误")

            with pytest.raises(Exception, match="一般错误"):
                await self.redis_dao.aset("test_key", "test_value")

    def test_get_connection_error_recovery(self):
        """测试获取操作连接错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_get') as mock_get, \
             patch.object(self.redis_dao, '_connect') as mock_connect:

            # 第一次调用抛出连接错误，第二次成功
            mock_get.side_effect = [ConnectionError("连接失败"), "recovered_value"]
            mock_connect.return_value = Mock()

            result = self.redis_dao.get("test_key", "default")

            assert result == "recovered_value"
            assert mock_get.call_count == 2
            mock_connect.assert_called_once()

    def test_get_timeout_error_recovery(self):
        """测试获取操作超时错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_get') as mock_get, \
             patch.object(self.redis_dao, '_connect') as mock_connect:

            # 第一次调用抛出超时错误，第二次成功
            mock_get.side_effect = [TimeoutError("超时"), "recovered_value"]
            mock_connect.return_value = Mock()

            result = self.redis_dao.get("test_key", "default")

            assert result == "recovered_value"
            assert mock_get.call_count == 2
            mock_connect.assert_called_once()

    def test_get_connection_error_then_exception(self):
        """测试获取操作连接错误后再次异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_get') as mock_get, \
             patch.object(self.redis_dao, '_connect') as mock_connect:

            # 第一次连接错误，第二次其他异常
            mock_get.side_effect = [ConnectionError("连接失败"), Exception("其他错误")]
            mock_connect.return_value = Mock()

            with pytest.raises(Exception, match="其他错误"):
                self.redis_dao.get("test_key", "default")

            assert mock_get.call_count == 2
            mock_connect.assert_called_once()

    def test_get_general_exception(self):
        """测试获取操作一般异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_get') as mock_get:
            mock_get.side_effect = Exception("一般错误")

            with pytest.raises(Exception, match="一般错误"):
                self.redis_dao.get("test_key", "default")

    @pytest.mark.asyncio
    async def test_aget_connection_error_recovery(self):
        """测试异步获取操作连接错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aget') as mock_aget, \
             patch.object(self.redis_dao, '_aconnect') as mock_aconnect:

            # 第一次调用抛出连接错误，第二次成功
            mock_aget.side_effect = [ConnectionError("连接失败"), "recovered_value"]
            mock_aconnect.return_value = Mock()

            result = await self.redis_dao.aget("test_key", "default")

            assert result == "recovered_value"
            assert mock_aget.call_count == 2
            mock_aconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_aget_timeout_error_recovery(self):
        """测试异步获取操作超时错误恢复"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aget') as mock_aget, \
             patch.object(self.redis_dao, '_aconnect') as mock_aconnect:

            # 第一次调用抛出超时错误，第二次成功
            mock_aget.side_effect = [TimeoutError("超时"), "recovered_value"]
            mock_aconnect.return_value = Mock()

            result = await self.redis_dao.aget("test_key", "default")

            assert result == "recovered_value"
            assert mock_aget.call_count == 2
            mock_aconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_aget_connection_error_then_exception(self):
        """测试异步获取操作连接错误后再次异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aget') as mock_aget, \
             patch.object(self.redis_dao, '_aconnect') as mock_aconnect:

            # 第一次连接错误，第二次其他异常
            mock_aget.side_effect = [ConnectionError("连接失败"), Exception("其他错误")]
            mock_aconnect.return_value = Mock()

            with pytest.raises(Exception, match="其他错误"):
                await self.redis_dao.aget("test_key", "default")

            assert mock_aget.call_count == 2
            mock_aconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_aget_general_exception(self):
        """测试异步获取操作一般异常"""
        self.redis_dao._cams_host = None  # 不使用 CAMS

        with patch.object(self.redis_dao, '_aget') as mock_aget:
            mock_aget.side_effect = Exception("一般错误")

            with pytest.raises(Exception, match="一般错误"):
                await self.redis_dao.aget("test_key", "default")

    @patch('commons.db.redis5dao.aiohttp.ClientSession')
    @pytest.mark.asyncio
    async def test_acall_cams_http_error(self, mock_session_class):
        """测试异步 CAMS 调用 HTTP 错误"""
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟 HTTP 错误响应
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text = AsyncMock(return_value="Internal Server Error")

        # 创建模拟的请求上下文管理器
        mock_request_cm = AsyncMock()
        mock_request_cm.__aenter__ = AsyncMock(return_value=mock_response)
        mock_request_cm.__aexit__ = AsyncMock(return_value=None)

        # 创建模拟的会话
        mock_session = AsyncMock()
        mock_session.post = Mock(return_value=mock_request_cm)

        # 创建模拟的会话上下文管理器
        mock_session_cm = AsyncMock()
        mock_session_cm.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_cm.__aexit__ = AsyncMock(return_value=None)

        mock_session_class.return_value = mock_session_cm

        # 执行异步 CAMS 调用并期望异常
        with patch('commons.db.redis5dao.sig_wps4', return_value="test_signature"):
            with pytest.raises(Exception) as exc_info:
                await self.redis_dao._acall_cams("/test/uri", {"key": "test_key"})

            assert "cams redis rpc request fail" in str(exc_info.value)
            assert "status_code: 500" in str(exc_info.value)

    @patch('commons.db.redis5dao.aiohttp.ClientSession')
    @pytest.mark.asyncio
    async def test_acall_cams_business_error(self, mock_session_class):
        """测试异步 CAMS 调用业务错误"""
        self.redis_dao.init_cams("test_ak", "test_sk", "http://test-host", "test_")

        # 模拟业务错误响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value='{"code": 1, "msg": "business error"}')

        # 创建模拟的请求上下文管理器
        mock_request_cm = AsyncMock()
        mock_request_cm.__aenter__ = AsyncMock(return_value=mock_response)
        mock_request_cm.__aexit__ = AsyncMock(return_value=None)

        # 创建模拟的会话
        mock_session = AsyncMock()
        mock_session.post = Mock(return_value=mock_request_cm)

        # 创建模拟的会话上下文管理器
        mock_session_cm = AsyncMock()
        mock_session_cm.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_cm.__aexit__ = AsyncMock(return_value=None)

        mock_session_class.return_value = mock_session_cm

        # 执行异步 CAMS 调用并期望异常
        with patch('commons.db.redis5dao.sig_wps4', return_value="test_signature"):
            with pytest.raises(Exception) as exc_info:
                await self.redis_dao._acall_cams("/test/uri", {"key": "test_key"})

            assert "cams redis rpc request fail" in str(exc_info.value)

    def test_remove_without_cams(self):
        """测试不使用 CAMS 的删除操作"""
        self.redis_dao._cams_host = None

        with patch.object(self.redis_dao, '_remove') as mock_remove:
            mock_remove.return_value = 1

            result = self.redis_dao.remove("test_key")

            assert result == 1
            mock_remove.assert_called_once_with("test_key")

    def test_remove_with_cams(self):
        """测试使用 CAMS 的删除操作"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_remove_cams') as mock_remove_cams:
            mock_remove_cams.return_value = True

            result = self.redis_dao.remove("test_key")

            assert result is True
            mock_remove_cams.assert_called_once_with("test_key")

    @pytest.mark.asyncio
    async def test_aremove_without_cams(self):
        """测试不使用 CAMS 的异步删除操作"""
        self.redis_dao._cams_host = None

        with patch.object(self.redis_dao, '_aremove') as mock_aremove:
            mock_aremove.return_value = 1

            result = await self.redis_dao.aremove("test_key")

            assert result == 1
            mock_aremove.assert_called_once_with("test_key")

    @pytest.mark.asyncio
    async def test_aremove_with_cams(self):
        """测试使用 CAMS 的异步删除操作"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_aremove_cams') as mock_aremove_cams:
            mock_aremove_cams.return_value = True

            result = await self.redis_dao.aremove("test_key")

            assert result is True
            mock_aremove_cams.assert_called_once_with("test_key")

    def test_remove_cams_success(self):
        """测试 CAMS 删除操作成功"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_call.return_value = mock_resp

            result = self.redis_dao._remove_cams("test_key")

            assert result is True
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/del",
                {"key": f"{self.redis_dao._prefix}test_key"}
            )

    def test_remove_cams_failure(self):
        """测试 CAMS 删除操作失败"""
        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = 1  # 非 OK 状态
            mock_call.return_value = mock_resp

            result = self.redis_dao._remove_cams("test_key")

            assert result is False

    @pytest.mark.asyncio
    async def test_aremove_cams_success(self):
        """测试异步 CAMS 删除操作成功"""
        with patch.object(self.redis_dao, '_acall_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_call.return_value = mock_resp

            result = await self.redis_dao._aremove_cams("test_key")

            assert result is True
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/del",
                {"key": f"{self.redis_dao._prefix}test_key"}
            )

    @pytest.mark.asyncio
    async def test_aremove_cams_failure(self):
        """测试异步 CAMS 删除操作失败"""
        with patch.object(self.redis_dao, '_acall_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = 1  # 非 OK 状态
            mock_call.return_value = mock_resp

            result = await self.redis_dao._aremove_cams("test_key")

            assert result is False

    def test_incrby_without_cams(self):
        """测试不使用 CAMS 的递增操作"""
        self.redis_dao._cams_host = None

        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.incrby.return_value = 5

            result = self.redis_dao.incrby("counter", 3)

            assert result == 5
            mock_rd.incrby.assert_called_once_with(f"{self.redis_dao._prefix}counter", 3)

    def test_incrby_with_cams(self):
        """测试使用 CAMS 的递增操作"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_get_cams') as mock_get_cams, \
             patch.object(self.redis_dao, '_set_cams') as mock_set_cams:

            mock_get_cams.return_value = "2"  # 当前值
            mock_set_cams.return_value = True

            result = self.redis_dao.incrby("counter", 3)

            assert result == 5  # 2 + 3
            mock_get_cams.assert_called_once_with("counter", 0)
            mock_set_cams.assert_called_once_with("counter", 5)

    def test_incrby_with_cams_set_failure(self):
        """测试使用 CAMS 的递增操作设置失败"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_get_cams') as mock_get_cams, \
             patch.object(self.redis_dao, '_set_cams') as mock_set_cams:

            mock_get_cams.return_value = "2"  # 当前值
            mock_set_cams.return_value = False  # 设置失败

            result = self.redis_dao.incrby("counter", 3)

            assert result == 0  # 设置失败返回 0
            mock_get_cams.assert_called_once_with("counter", 0)
            mock_set_cams.assert_called_once_with("counter", 5)

    @pytest.mark.asyncio
    async def test_aincrby_without_cams(self):
        """测试不使用 CAMS 的异步递增操作"""
        self.redis_dao._cams_host = None

        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.incrby = AsyncMock(return_value=8)

            result = await self.redis_dao.aincrby("counter", 5)

            assert result == 8
            mock_ard.incrby.assert_called_once_with(f"{self.redis_dao._prefix}counter", 5)

    @pytest.mark.asyncio
    async def test_aincrby_with_cams(self):
        """测试使用 CAMS 的异步递增操作"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_aget_cams') as mock_aget_cams, \
             patch.object(self.redis_dao, '_aset_cams') as mock_aset_cams:

            mock_aget_cams.return_value = "7"  # 当前值
            mock_aset_cams.return_value = True

            result = await self.redis_dao.aincrby("counter", 3)

            assert result == 10  # 7 + 3
            mock_aget_cams.assert_called_once_with("counter", 0)
            mock_aset_cams.assert_called_once_with("counter", 10)

    @pytest.mark.asyncio
    async def test_aincrby_with_cams_set_failure(self):
        """测试使用 CAMS 的异步递增操作设置失败"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_aget_cams') as mock_aget_cams, \
             patch.object(self.redis_dao, '_aset_cams') as mock_aset_cams:

            mock_aget_cams.return_value = "7"  # 当前值
            mock_aset_cams.return_value = False  # 设置失败

            result = await self.redis_dao.aincrby("counter", 3)

            assert result == 0  # 设置失败返回 0
            mock_aget_cams.assert_called_once_with("counter", 0)
            mock_aset_cams.assert_called_once_with("counter", 10)

    def test_expire_without_cams(self):
        """测试不使用 CAMS 的过期时间设置"""
        self.redis_dao._cams_host = None

        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.expire.return_value = True

            result = self.redis_dao.expire("test_key", 3600)

            assert result is True
            mock_rd.expire.assert_called_once_with(f"{self.redis_dao._prefix}test_key", 3600)

    def test_expire_with_cams(self):
        """测试使用 CAMS 的过期时间设置"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_get_cams') as mock_get_cams, \
             patch.object(self.redis_dao, '_set_cams') as mock_set_cams:

            mock_get_cams.return_value = "123"  # 返回可以转换为整数的字符串
            mock_set_cams.return_value = True

            result = self.redis_dao.expire("test_key", 3600)

            assert result is True
            mock_get_cams.assert_called_once_with("test_key", 0)
            mock_set_cams.assert_called_once_with("test_key", 123, ex=3600)  # 转换为int后的值

    @pytest.mark.asyncio
    async def test_aexpire_without_cams(self):
        """测试不使用 CAMS 的异步过期时间设置"""
        self.redis_dao._cams_host = None

        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.expire = AsyncMock(return_value=True)

            result = await self.redis_dao.aexpire("test_key", 3600)

            assert result is True
            mock_ard.expire.assert_called_once_with(f"{self.redis_dao._prefix}test_key", 3600)

    @pytest.mark.asyncio
    async def test_aexpire_with_cams(self):
        """测试使用 CAMS 的异步过期时间设置"""
        self.redis_dao._cams_host = "http://test-host"

        with patch.object(self.redis_dao, '_aget_cams') as mock_aget_cams, \
             patch.object(self.redis_dao, '_aset_cams') as mock_aset_cams:

            mock_aget_cams.return_value = "456"  # 返回可以转换为整数的字符串
            mock_aset_cams.return_value = True

            result = await self.redis_dao.aexpire("test_key", 3600)

            assert result is True
            mock_aget_cams.assert_called_once_with("test_key", 0)
            mock_aset_cams.assert_called_once_with("test_key", 456, ex=3600)  # 转换为int后的值

    def test_zadd_operation(self):
        """测试有序集合添加操作"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.zadd.return_value = 2

            mapping = {"member1": 1.0, "member2": 2.0}
            self.redis_dao.zadd("zset_key", mapping, nx=True)

            mock_rd.zadd.assert_called_once_with(f"{self.redis_dao._prefix}zset_key", mapping=mapping, nx=True)

    @pytest.mark.asyncio
    async def test_azadd_operation(self):
        """测试异步有序集合添加操作"""
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.zadd = AsyncMock(return_value=2)

            mapping = {"member1": 1.0, "member2": 2.0}
            await self.redis_dao.azadd("zset_key", mapping, nx=True)

            mock_ard.zadd.assert_called_once_with(f"{self.redis_dao._prefix}zset_key", mapping=mapping, nx=True)

    def test_zrem_operation(self):
        """测试有序集合删除成员操作"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.zrem.return_value = 1

            self.redis_dao.zrem("zset_key", "member1")

            mock_rd.zrem.assert_called_once_with(f"{self.redis_dao._prefix}zset_key", "member1")

    @pytest.mark.asyncio
    async def test_azrem_operation(self):
        """测试异步有序集合删除成员操作"""
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.zrem = AsyncMock(return_value=1)

            await self.redis_dao.azrem("zset_key", "member1")

            mock_ard.zrem.assert_called_once_with(f"{self.redis_dao._prefix}zset_key", "member1")

    def test_zpopmin_operation(self):
        """测试有序集合弹出最小值操作"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.zpopmin.return_value = [(b"member1", 1.0)]

            result = self.redis_dao.zpopmin("zset_key", 1)

            assert result == [(b"member1", 1.0)]
            mock_rd.zpopmin.assert_called_once_with(f"{self.redis_dao._prefix}zset_key", 1)

    @pytest.mark.asyncio
    async def test_azpopmin_operation(self):
        """测试异步有序集合弹出最小值操作"""
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.zpopmin = AsyncMock(return_value=[(b"member1", 1.0)])

            result = await self.redis_dao.azpopmin("zset_key", 1)

            assert result == [(b"member1", 1.0)]
            mock_ard.zpopmin.assert_called_once_with(f"{self.redis_dao._prefix}zset_key", 1)

    def test_zrang_normal_order(self):
        """测试有序集合正序范围查询"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.zrange.return_value = [b"member1", b"member2"]

            result = self.redis_dao.zrang("zset_key", 0, -1, withscores=False, rev=False)

            assert result == [b"member1", b"member2"]
            mock_rd.zrange.assert_called_once_with(name=f"{self.redis_dao._prefix}zset_key", start=0, end=-1, withscores=False)

    def test_zrang_reverse_order(self):
        """测试有序集合倒序范围查询"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.zrevrange.return_value = [b"member2", b"member1"]

            result = self.redis_dao.zrang("zset_key", 0, -1, withscores=False, rev=True)

            assert result == [b"member2", b"member1"]
            mock_rd.zrevrange.assert_called_once_with(name=f"{self.redis_dao._prefix}zset_key", start=0, end=-1, withscores=False)

    def test_zrang_with_scores(self):
        """测试有序集合带分数的范围查询"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.zrange.return_value = [(b"member1", 1.0), (b"member2", 2.0)]

            result = self.redis_dao.zrang("zset_key", 0, -1, withscores=True, rev=False)

            assert result == [(b"member1", 1.0), (b"member2", 2.0)]
            mock_rd.zrange.assert_called_once_with(name=f"{self.redis_dao._prefix}zset_key", start=0, end=-1, withscores=True)

    @pytest.mark.asyncio
    async def test_azrang_normal_order(self):
        """测试异步有序集合正序范围查询"""
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.zrange = AsyncMock(return_value=[b"member1", b"member2"])

            result = await self.redis_dao.azrang("zset_key", 0, -1, withscores=False, rev=False)

            assert result == [b"member1", b"member2"]
            mock_ard.zrange.assert_called_once_with(name=f"{self.redis_dao._prefix}zset_key", start=0, end=-1, withscores=False)

    @pytest.mark.asyncio
    async def test_azrang_reverse_order(self):
        """测试异步有序集合倒序范围查询"""
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.zrevrange = AsyncMock(return_value=[b"member2", b"member1"])

            result = await self.redis_dao.azrang("zset_key", 0, -1, withscores=False, rev=True)

            assert result == [b"member2", b"member1"]
            mock_ard.zrevrange.assert_called_once_with(name=f"{self.redis_dao._prefix}zset_key", start=0, end=-1, withscores=False)

    def test_zcard_operation(self):
        """测试有序集合基数操作"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.zcard.return_value = 5

            result = self.redis_dao.zcard("zset_key")

            assert result == 5
            mock_rd.zcard.assert_called_once_with(f"{self.redis_dao._prefix}zset_key")

    @pytest.mark.asyncio
    async def test_azcard_operation(self):
        """测试异步有序集合基数操作"""
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.zcard = AsyncMock(return_value=5)

            result = await self.redis_dao.azcard("zset_key")

            assert result == 5
            mock_ard.zcard.assert_called_once_with(f"{self.redis_dao._prefix}zset_key")

    @patch('redis.RedisCluster')
    def test_connect_ipv6_cluster(self, mock_redis_cluster_class):
        """测试 IPv6 集群连接"""
        mock_cluster_instance = Mock(spec=redis.RedisCluster)
        mock_redis_cluster_class.return_value = mock_cluster_instance

        # IPv6 地址格式
        self.redis_dao._hosts = ["[2001:db8::1]:6379", "[2001:db8::2]:6380"]
        self.redis_dao._password = "cluster_password"
        self.redis_dao._cluster = True

        result = self.redis_dao._connect()

        assert result == mock_cluster_instance
        # 验证集群节点配置被调用
        mock_redis_cluster_class.assert_called_once()

        # 验证调用参数中的节点配置
        call_args = mock_redis_cluster_class.call_args
        startup_nodes = call_args[1]['startup_nodes']

        # 验证 IPv6 地址被正确解析
        assert len(startup_nodes) == 2
        assert startup_nodes[0].host == "2001:db8::1"
        assert startup_nodes[0].port == 6379
        assert startup_nodes[1].host == "2001:db8::2"
        assert startup_nodes[1].port == 6380

    @patch('redis.ConnectionPool')
    @patch('redis.Redis')
    def test_connect_ipv6_single_node(self, mock_redis_class, mock_pool_class):
        """测试 IPv6 单节点连接"""
        mock_pool_instance = Mock()
        mock_pool_class.return_value = mock_pool_instance
        mock_redis_instance = Mock(spec=redis.Redis)
        mock_redis_class.return_value = mock_redis_instance

        # IPv6 地址格式
        self.redis_dao._hosts = ["[2001:db8::1]:6379"]
        self.redis_dao._password = "test_password"
        self.redis_dao._cluster = False

        result = self.redis_dao._connect()

        assert result == mock_redis_instance
        mock_pool_class.assert_called_once_with(
            host="2001:db8::1",  # IPv6 地址去掉方括号
            port=6379,
            password="test_password"
        )
        mock_redis_class.assert_called_once_with(connection_pool=mock_pool_instance)

    @pytest.mark.asyncio
    async def test_aconnect_cluster_mode(self):
        """测试异步集群模式连接"""
        with patch('redis.asyncio.RedisCluster') as mock_aredis_cluster_class:
            # 创建一个异步mock
            mock_cluster_instance = Mock(spec=aredis.RedisCluster)

            # 让RedisCluster构造函数返回一个awaitable对象
            async def mock_cluster_constructor(*args, **kwargs):
                return mock_cluster_instance
            mock_aredis_cluster_class.side_effect = mock_cluster_constructor

            self.redis_dao._hosts = ["127.0.0.1:6379", "127.0.0.1:6380"]
            self.redis_dao._password = "cluster_password"
            self.redis_dao._cluster = True

            result = await self.redis_dao._aconnect()

            assert result == mock_cluster_instance
            mock_aredis_cluster_class.assert_called_once()

            # 验证调用参数
            call_args = mock_aredis_cluster_class.call_args
            assert 'startup_nodes' in call_args[1]
            assert call_args[1]['password'] == "cluster_password"

    @pytest.mark.asyncio
    async def test_aconnect_ipv6_cluster(self):
        """测试异步 IPv6 集群连接"""
        with patch('redis.asyncio.RedisCluster') as mock_aredis_cluster_class:
            # 创建一个异步mock
            mock_cluster_instance = Mock(spec=aredis.RedisCluster)

            # 让RedisCluster构造函数返回一个awaitable对象
            async def mock_cluster_constructor(*args, **kwargs):
                return mock_cluster_instance
            mock_aredis_cluster_class.side_effect = mock_cluster_constructor

            # IPv6 地址格式
            self.redis_dao._hosts = ["[2001:db8::1]:6379", "[2001:db8::2]:6380"]
            self.redis_dao._password = "cluster_password"
            self.redis_dao._cluster = True

            result = await self.redis_dao._aconnect()

            assert result == mock_cluster_instance
            mock_aredis_cluster_class.assert_called_once()

            # 验证调用参数中的节点配置
            call_args = mock_aredis_cluster_class.call_args
            startup_nodes = call_args[1]['startup_nodes']

            # 验证 IPv6 地址被正确解析
            assert len(startup_nodes) == 2
            assert startup_nodes[0].host == "2001:db8::1"
            assert startup_nodes[0].port == 6379
            assert startup_nodes[1].host == "2001:db8::2"
            assert startup_nodes[1].port == 6380

    def test_host_parsing_edge_cases(self):
        """测试主机地址解析边界情况"""
        test_cases = [
            # (输入地址, 期望的host, 期望的port)
            ("127.0.0.1:6379", "127.0.0.1", 6379),
            ("[::1]:6379", "::1", 6379),
            ("[2001:db8::1]:6379", "2001:db8::1", 6379),
            ("localhost:6379", "localhost", 6379),
            ("redis.example.com:6379", "redis.example.com", 6379),
        ]

        for host_str, expected_host, expected_port in test_cases:
            # 模拟地址解析逻辑
            node = host_str.rsplit(":", 1)
            host = node[0].replace("[", "").replace("]", "")
            port = int(node[1])

            assert host == expected_host, f"Host parsing failed for {host_str}"
            assert port == expected_port, f"Port parsing failed for {host_str}"

    def test_multiple_host_parsing(self):
        """测试多主机地址解析"""
        hosts = [
            "127.0.0.1:6379",
            "[::1]:6380",
            "[2001:db8::1]:6381",
            "redis.example.com:6382"
        ]

        expected_results = [
            ("127.0.0.1", 6379),
            ("::1", 6380),
            ("2001:db8::1", 6381),
            ("redis.example.com", 6382)
        ]

        for i, host_str in enumerate(hosts):
            node = host_str.rsplit(":", 1)
            host = node[0].replace("[", "").replace("]", "")
            port = int(node[1])

            expected_host, expected_port = expected_results[i]
            assert host == expected_host
            assert port == expected_port

    def test_remove_direct_method(self):
        """测试直接删除方法"""
        with patch.object(self.redis_dao, 'rd') as mock_rd:
            mock_rd.delete.return_value = 1

            result = self.redis_dao._remove("test_key")

            assert result == 1
            mock_rd.delete.assert_called_once_with(f"{self.redis_dao._prefix}test_key")

    @pytest.mark.asyncio
    async def test_aremove_direct_method(self):
        """测试直接异步删除方法"""
        with patch.object(self.redis_dao, 'ard') as mock_ard:
            mock_ard.delete = AsyncMock(return_value=1)

            result = await self.redis_dao._aremove("test_key")

            assert result == 1
            mock_ard.delete.assert_called_once_with(f"{self.redis_dao._prefix}test_key")


class TestRedis5DaoAdditionalMethods:
    """测试 Redis5Dao 的额外方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        Redis5Dao._instances = {}
        self.redis_dao = Redis5Dao()

    def test_getint_success(self):
        """测试获取整数值成功"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = "123"

            result = self.redis_dao.getint("test_key", 0)

            assert result == 123
            mock_get.assert_called_once_with("test_key", 0)

    def test_getint_with_default(self):
        """测试获取整数值使用默认值"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = 42  # 返回默认值而不是 None

            result = self.redis_dao.getint("test_key", 42)

            assert result == 42
            mock_get.assert_called_once_with("test_key", 42)

    @pytest.mark.asyncio
    async def test_agetint_success(self):
        """测试异步获取整数值成功"""
        with patch.object(self.redis_dao, 'aget') as mock_aget:
            mock_aget.return_value = "456"

            result = await self.redis_dao.agetint("test_key", 0)

            assert result == 456
            mock_aget.assert_called_once_with("test_key", 0)

    def test_getstring_success(self):
        """测试获取字符串值成功"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = b"test_bytes"

            result = self.redis_dao.getstring("test_key", "")

            assert result == "test_bytes"
            mock_get.assert_called_once_with("test_key", "")

    def test_getstring_with_string_value(self):
        """测试获取字符串值（已经是字符串）"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = "test_string"

            result = self.redis_dao.getstring("test_key", "")

            assert result == "test_string"
            mock_get.assert_called_once_with("test_key", "")

    @pytest.mark.asyncio
    async def test_agetstring_success(self):
        """测试异步获取字符串值成功"""
        with patch.object(self.redis_dao, 'aget') as mock_aget:
            mock_aget.return_value = b"async_bytes"

            result = await self.redis_dao.agetstring("test_key", "")

            assert result == "async_bytes"
            mock_aget.assert_called_once_with("test_key", "")

    def test_getbool_success(self):
        """测试获取布尔值成功"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = "1"

            result = self.redis_dao.getbool("test_key", False)

            assert result is True
            mock_get.assert_called_once_with("test_key", False)

    def test_getbool_false_value(self):
        """测试获取布尔值为假"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = ""

            result = self.redis_dao.getbool("test_key", False)

            assert result is False
            mock_get.assert_called_once_with("test_key", False)

    @pytest.mark.asyncio
    async def test_agetbool_success(self):
        """测试异步获取布尔值成功"""
        with patch.object(self.redis_dao, 'aget') as mock_aget:
            mock_aget.return_value = "true"

            result = await self.redis_dao.agetbool("test_key", False)

            assert result is True
            mock_aget.assert_called_once_with("test_key", False)

    def test_redis_dao_methods_exist(self):
        """测试 Redis5Dao 方法存在性"""
        # 验证基本方法存在
        assert hasattr(self.redis_dao, 'get')
        assert hasattr(self.redis_dao, 'set')
        assert hasattr(self.redis_dao, 'aget')
        assert hasattr(self.redis_dao, 'aset')
        assert hasattr(self.redis_dao, 'getint')
        assert hasattr(self.redis_dao, 'agetint')
        assert hasattr(self.redis_dao, 'getstring')
        assert hasattr(self.redis_dao, 'agetstring')
        assert hasattr(self.redis_dao, 'getbool')
        assert hasattr(self.redis_dao, 'agetbool')

    def test_redis_dao_singleton(self):
        """测试 Redis5Dao 单例模式"""
        dao1 = Redis5Dao()
        dao2 = Redis5Dao()
        assert dao1 is dao2

    def test_redis_dao_configuration(self):
        """测试 Redis5Dao 配置"""
        # 验证基本属性存在
        assert hasattr(self.redis_dao, 'get')
        assert hasattr(self.redis_dao, 'set')
        assert hasattr(self.redis_dao, 'aget')
        assert hasattr(self.redis_dao, 'aset')

        # 验证单例模式
        dao1 = Redis5Dao()
        dao2 = Redis5Dao()
        assert dao1 is dao2


class TestRedis5DaoCAMSMethods:
    """测试 Redis5Dao 的 CAMS 相关方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        Redis5Dao._instances = {}
        self.redis_dao = Redis5Dao()

    def test_init_cams(self):
        """测试初始化 CAMS 配置"""
        ak = "test_access_key"
        sk = "test_secret_key"
        host = "http://test-cams.com"
        prefix = "test_prefix_"

        self.redis_dao.init_cams(ak, sk, host, prefix)

        assert self.redis_dao._cams_ak == ak
        assert self.redis_dao._cams_sk == sk
        assert self.redis_dao._cams_host == host
        assert self.redis_dao._prefix == prefix

    def test_sig_cams_without_body(self):
        """测试 CAMS 签名生成（无请求体）"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        with patch('commons.db.redis5dao.sig_wps4') as mock_sig:
            mock_sig.return_value = {"Authorization": "test_auth"}

            result = self.redis_dao._sig_cams("GET", "/test/uri")

            mock_sig.assert_called_once()
            assert result == {"Authorization": "test_auth"}

    def test_sig_cams_with_body(self):
        """测试 CAMS 签名生成（有请求体）"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        with patch('commons.db.redis5dao.sig_wps4') as mock_sig:
            mock_sig.return_value = {"Authorization": "test_auth"}

            body = {"key": "value"}
            result = self.redis_dao._sig_cams("POST", "/test/uri", body)

            mock_sig.assert_called_once()
            # 验证 body 被转换为 JSON bytes
            call_args = mock_sig.call_args[0]
            assert call_args[2] == json.dumps(body).encode("utf-8")

    def test_call_cams_success(self):
        """测试 CAMS 调用成功"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = json.dumps({
            "code": 0,
            "msg": "success",
            "data": {"result": "test"}
        })

        with patch('commons.db.redis5dao.requests.post') as mock_post, \
             patch.object(self.redis_dao, '_sig_cams') as mock_sig:

            mock_post.return_value = mock_response
            mock_sig.return_value = {"Authorization": "test_auth"}

            result = self.redis_dao._call_cams("/test/uri", {"key": "value"})

            assert result.code == 0
            assert result.msg == "success"
            mock_post.assert_called_once()

    def test_call_cams_http_error(self):
        """测试 CAMS 调用 HTTP 错误"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟 HTTP 错误响应
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"

        with patch('commons.db.redis5dao.requests.post') as mock_post, \
             patch.object(self.redis_dao, '_sig_cams') as mock_sig:

            mock_post.return_value = mock_response
            mock_sig.return_value = {"Authorization": "test_auth"}

            with pytest.raises(Exception) as exc_info:
                self.redis_dao._call_cams("/test/uri")

            assert "cams redis rpc request fail" in str(exc_info.value)
            assert "500" in str(exc_info.value)

    def test_call_cams_response_error(self):
        """测试 CAMS 调用响应错误"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟响应错误
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = json.dumps({
            "code": 1,
            "msg": "error",
            "data": None
        })

        with patch('commons.db.redis5dao.requests.post') as mock_post, \
             patch.object(self.redis_dao, '_sig_cams') as mock_sig:

            mock_post.return_value = mock_response
            mock_sig.return_value = {"Authorization": "test_auth"}

            with pytest.raises(Exception) as exc_info:
                self.redis_dao._call_cams("/test/uri")

            assert "cams redis rpc request fail" in str(exc_info.value)

    def test_acall_cams_method_exists(self):
        """测试异步 CAMS 调用方法存在"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 验证方法存在
        assert hasattr(self.redis_dao, '_acall_cams')
        assert callable(getattr(self.redis_dao, '_acall_cams'))

    def test_acall_cams_signature(self):
        """测试异步 CAMS 调用方法签名"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 验证方法可以被调用（不实际执行）
        import inspect
        sig = inspect.signature(self.redis_dao._acall_cams)
        params = list(sig.parameters.keys())

        assert 'uri' in params
        assert 'body' in params

    def test_set_cams_success(self):
        """测试 CAMS 设置成功"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟成功响应
        mock_resp = Mock()
        mock_resp.code = 0  # RespCode.OK

        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_call.return_value = mock_resp

            result = self.redis_dao._set_cams("test_key", "test_value", ex=3600)

            assert result is True
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/set",
                {
                    "key": "prefix_test_key",
                    "val": "test_value",
                    "expire": 3600
                }
            )

    def test_set_cams_failure(self):
        """测试 CAMS 设置失败"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟失败响应
        mock_resp = Mock()
        mock_resp.code = 1  # 非 RespCode.OK

        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_call.return_value = mock_resp

            result = self.redis_dao._set_cams("test_key", "test_value")

            assert result is False

    @pytest.mark.asyncio
    async def test_aset_cams_success(self):
        """测试异步 CAMS 设置成功"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟成功响应
        mock_resp = Mock()
        mock_resp.code = 0  # RespCode.OK

        with patch.object(self.redis_dao, '_acall_cams') as mock_acall:
            mock_acall.return_value = mock_resp

            result = await self.redis_dao._aset_cams("test_key", "test_value", ex=7200)

            assert result is True
            mock_acall.assert_called_once_with(
                "/sdk/api/v1/cache/set",
                {
                    "key": "prefix_test_key",
                    "val": "test_value",
                    "expire": 7200
                }
            )

    def test_get_cams_success(self):
        """测试 CAMS 获取成功"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟成功响应
        mock_resp = Mock()
        mock_resp.data = Mock()
        mock_resp.data.val = "cached_value"

        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_call.return_value = mock_resp

            result = self.redis_dao._get_cams("test_key", "default")

            assert result == "cached_value"
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/get",
                {"key": "prefix_test_key"}
            )

    def test_get_cams_not_found(self):
        """测试 CAMS 获取未找到"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟未找到响应
        mock_resp = Mock()
        mock_resp.data = None

        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_call.return_value = mock_resp

            result = self.redis_dao._get_cams("test_key", "default_value")

            assert result == "default_value"

    def test_get_cams_empty_value(self):
        """测试 CAMS 获取空值"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟空值响应
        mock_resp = Mock()
        mock_resp.data = Mock()
        mock_resp.data.val = None

        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_call.return_value = mock_resp

            result = self.redis_dao._get_cams("test_key", "default_value")

            assert result == "default_value"

    @pytest.mark.asyncio
    async def test_aget_cams_success(self):
        """测试异步 CAMS 获取成功"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟成功响应
        mock_resp = Mock()
        mock_resp.data = Mock()
        mock_resp.data.val = "async_cached_value"

        with patch.object(self.redis_dao, '_acall_cams') as mock_acall:
            mock_acall.return_value = mock_resp

            result = await self.redis_dao._aget_cams("test_key", "default")

            assert result == "async_cached_value"
            mock_acall.assert_called_once_with(
                "/sdk/api/v1/cache/get",
                {"key": "prefix_test_key"}
            )

    def test_remove_cams_success(self):
        """测试 CAMS 删除成功"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟成功响应
        mock_resp = Mock()
        mock_resp.code = 0  # RespCode.OK

        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_call.return_value = mock_resp

            result = self.redis_dao._remove_cams("test_key")

            assert result is True
            mock_call.assert_called_once_with(
                "/sdk/api/v1/cache/del",
                {"key": "prefix_test_key"}
            )

    def test_remove_cams_failure(self):
        """测试 CAMS 删除失败"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟失败响应
        mock_resp = Mock()
        mock_resp.code = 1  # 非 RespCode.OK

        with patch.object(self.redis_dao, '_call_cams') as mock_call:
            mock_call.return_value = mock_resp

            result = self.redis_dao._remove_cams("test_key")

            assert result is False

    @pytest.mark.asyncio
    async def test_aremove_cams_success(self):
        """测试异步 CAMS 删除成功"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        # 模拟成功响应
        mock_resp = Mock()
        mock_resp.code = 0  # RespCode.OK

        with patch.object(self.redis_dao, '_acall_cams') as mock_acall:
            mock_acall.return_value = mock_resp

            result = await self.redis_dao._aremove_cams("test_key")

            assert result is True
            mock_acall.assert_called_once_with(
                "/sdk/api/v1/cache/del",
                {"key": "prefix_test_key"}
            )


class TestRedis5DaoErrorHandling:
    """测试 Redis5Dao 的错误处理"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        Redis5Dao._instances = {}
        self.redis_dao = Redis5Dao()

    def test_error_handling_logic_exists(self):
        """测试错误处理逻辑存在"""
        # 验证错误处理相关的方法存在
        assert hasattr(self.redis_dao, 'set')
        assert hasattr(self.redis_dao, 'get')
        assert hasattr(self.redis_dao, 'aset')
        assert hasattr(self.redis_dao, 'aget')

    def test_cams_vs_redis_logic(self):
        """测试 CAMS 与 Redis 的选择逻辑"""
        # 测试没有 CAMS 主机时的逻辑
        self.redis_dao._cams_host = None
        assert self.redis_dao._cams_host is None

        # 测试有 CAMS 主机时的逻辑
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")
        assert self.redis_dao._cams_host == "http://test.com"

    def test_connection_error_types(self):
        """测试连接错误类型"""
        # 验证错误类型可以被正确识别
        connection_error = ConnectionError("Connection failed")
        timeout_error = TimeoutError("Timeout")
        value_error = ValueError("Invalid value")

        assert isinstance(connection_error, ConnectionError)
        assert isinstance(timeout_error, TimeoutError)
        assert not isinstance(value_error, (ConnectionError, TimeoutError))

    def test_logging_functionality(self):
        """测试日志功能"""
        # 验证日志模块可以被正确导入和使用
        import commons.db.redis5dao as redis_module
        assert hasattr(redis_module, 'logging')

        # 验证日志方法存在
        assert hasattr(redis_module.logging, 'warning')
        assert hasattr(redis_module.logging, 'exception')

    def test_set_with_cams_host(self):
        """测试使用 CAMS 主机的 set 方法"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        with patch.object(self.redis_dao, '_set_cams') as mock_set_cams:
            mock_set_cams.return_value = True

            result = self.redis_dao.set("test_key", "test_value", ex=3600)

            assert result is True
            mock_set_cams.assert_called_once_with("test_key", "test_value", 3600)

    @pytest.mark.asyncio
    async def test_aset_with_cams_host(self):
        """测试使用 CAMS 主机的异步 set 方法"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        with patch.object(self.redis_dao, '_aset_cams') as mock_aset_cams:
            mock_aset_cams.return_value = True

            result = await self.redis_dao.aset("test_key", "test_value", ex=7200)

            assert result is True
            mock_aset_cams.assert_called_once_with("test_key", "test_value", 7200)

    def test_get_with_cams_host(self):
        """测试使用 CAMS 主机的 get 方法"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        with patch.object(self.redis_dao, '_get_cams') as mock_get_cams:
            mock_get_cams.return_value = "cams_value"

            result = self.redis_dao.get("test_key", "default")

            assert result == "cams_value"
            mock_get_cams.assert_called_once_with("test_key", "default")

    @pytest.mark.asyncio
    async def test_aget_with_cams_host(self):
        """测试使用 CAMS 主机的异步 get 方法"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        with patch.object(self.redis_dao, '_aget_cams') as mock_aget_cams:
            mock_aget_cams.return_value = "async_cams_value"

            result = await self.redis_dao.aget("test_key", "default")

            assert result == "async_cams_value"
            mock_aget_cams.assert_called_once_with("test_key", "default")


class TestRedis5DaoUtilityMethods:
    """测试 Redis5Dao 的实用方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        Redis5Dao._instances = {}
        self.redis_dao = Redis5Dao()

    def test_getint_functionality(self):
        """测试获取整数功能"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = "123"

            result = self.redis_dao.getint("test_key", 0)

            assert result == 123
            mock_get.assert_called_once_with("test_key", 0)

    def test_getint_with_default(self):
        """测试获取整数带默认值"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = 42  # 返回默认值而不是 None

            result = self.redis_dao.getint("test_key", 42)

            assert result == 42
            mock_get.assert_called_once_with("test_key", 42)

    @pytest.mark.asyncio
    async def test_agetint_functionality(self):
        """测试异步获取整数功能"""
        with patch.object(self.redis_dao, 'aget') as mock_aget:
            mock_aget.return_value = "456"

            result = await self.redis_dao.agetint("test_key", 0)

            assert result == 456
            mock_aget.assert_called_once_with("test_key", 0)

    def test_getstring_functionality(self):
        """测试获取字符串功能"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = b"test_bytes"

            result = self.redis_dao.getstring("test_key", "")

            assert result == "test_bytes"
            mock_get.assert_called_once_with("test_key", "")

    def test_getstring_with_string_value(self):
        """测试获取字符串（已是字符串）"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = "test_string"

            result = self.redis_dao.getstring("test_key", "")

            assert result == "test_string"
            mock_get.assert_called_once_with("test_key", "")

    @pytest.mark.asyncio
    async def test_agetstring_functionality(self):
        """测试异步获取字符串功能"""
        with patch.object(self.redis_dao, 'aget') as mock_aget:
            mock_aget.return_value = b"async_bytes"

            result = await self.redis_dao.agetstring("test_key", "")

            assert result == "async_bytes"
            mock_aget.assert_called_once_with("test_key", "")

    @pytest.mark.asyncio
    async def test_agetstring_with_string_value(self):
        """测试异步获取字符串（已是字符串）"""
        with patch.object(self.redis_dao, 'aget') as mock_aget:
            mock_aget.return_value = "async_string"

            result = await self.redis_dao.agetstring("test_key", "")

            assert result == "async_string"
            mock_aget.assert_called_once_with("test_key", "")

    def test_getbool_functionality(self):
        """测试获取布尔值功能"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = "1"

            result = self.redis_dao.getbool("test_key", False)

            assert result is True
            mock_get.assert_called_once_with("test_key", False)

    def test_getbool_false_value(self):
        """测试获取布尔值（假值）"""
        with patch.object(self.redis_dao, 'get') as mock_get:
            mock_get.return_value = ""

            result = self.redis_dao.getbool("test_key", True)

            assert result is False
            mock_get.assert_called_once_with("test_key", True)

    @pytest.mark.asyncio
    async def test_agetbool_functionality(self):
        """测试异步获取布尔值功能"""
        with patch.object(self.redis_dao, 'aget') as mock_aget:
            mock_aget.return_value = "true"

            result = await self.redis_dao.agetbool("test_key", False)

            assert result is True
            mock_aget.assert_called_once_with("test_key", False)

    def test_remove_without_cams(self):
        """测试删除（不使用 CAMS）"""
        self.redis_dao._cams_host = None

        with patch.object(self.redis_dao, '_remove') as mock_remove:
            mock_remove.return_value = 1

            result = self.redis_dao.remove("test_key")

            assert result == 1
            mock_remove.assert_called_once_with("test_key")

    def test_remove_with_cams(self):
        """测试删除（使用 CAMS）"""
        self.redis_dao.init_cams("ak", "sk", "http://test.com", "prefix_")

        with patch.object(self.redis_dao, '_remove_cams') as mock_remove_cams:
            mock_remove_cams.return_value = True

            result = self.redis_dao.remove("test_key")

            assert result is True
            mock_remove_cams.assert_called_once_with("test_key")

    def test_redis_dao_methods_exist(self):
        """测试 Redis DAO 方法存在"""
        # 验证核心方法存在
        assert hasattr(self.redis_dao, 'set')
        assert callable(getattr(self.redis_dao, 'set'))

        assert hasattr(self.redis_dao, 'get')
        assert callable(getattr(self.redis_dao, 'get'))

        assert hasattr(self.redis_dao, 'remove')
        assert callable(getattr(self.redis_dao, 'remove'))

    def test_redis_dao_utility_methods(self):
        """测试 Redis DAO 实用方法"""
        # 验证实用方法存在
        assert hasattr(self.redis_dao, 'getint')
        assert callable(getattr(self.redis_dao, 'getint'))

        assert hasattr(self.redis_dao, 'getstring')
        assert callable(getattr(self.redis_dao, 'getstring'))

        assert hasattr(self.redis_dao, 'getbool')
        assert callable(getattr(self.redis_dao, 'getbool'))


