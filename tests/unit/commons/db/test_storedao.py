"""
commons.db.storedao 模块的测试
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock

from commons.db.storedao import StoreDao, StoreType


class TestStoreType:
    """测试 StoreType 枚举"""

    def test_store_type_values(self):
        """测试存储类型枚举值"""
        assert StoreType.Ks3 == "ks3"
        assert StoreType.Minio == "minio"
        assert isinstance(StoreType.Ks3, str)
        assert isinstance(StoreType.Minio, str)


class TestStoreDao:
    """测试 StoreDao 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        StoreDao._instances = {}
        self.store_dao = StoreDao()

    def test_singleton_pattern(self):
        """测试单例模式"""
        dao1 = StoreDao()
        dao2 = StoreDao()
        assert dao1 is dao2

    def test_init_default_values(self):
        """测试初始化默认值"""
        assert self.store_dao.store_type is None

    @patch('commons.db.storedao.KS3Tool')
    def test_init_with_ks3(self, mock_ks3_tool_class):
        """测试使用 KS3 初始化"""
        mock_ks3_instance = Mock()
        mock_ks3_tool_class.return_value = mock_ks3_instance
        
        host = "ks3-host.com"
        ak = "ks3_access_key"
        sk = "ks3_secret_key"
        bucket = "ks3_bucket"
        
        self.store_dao.init(host, ak, sk, bucket, StoreType.Ks3)
        
        # 验证初始化结果
        assert self.store_dao.store_type == StoreType.Ks3
        mock_ks3_instance.init.assert_called_once_with(host, ak, sk, bucket)

    @patch('commons.db.storedao.MinioDao')
    def test_init_with_minio(self, mock_minio_dao_class):
        """测试使用 Minio 初始化"""
        mock_minio_instance = Mock()
        mock_minio_dao_class.return_value = mock_minio_instance
        
        host = "127.0.0.1:9000"
        ak = "minio_access_key"
        sk = "minio_secret_key"
        bucket = "minio_bucket"
        
        self.store_dao.init(host, ak, sk, bucket, StoreType.Minio)
        
        # 验证初始化结果
        assert self.store_dao.store_type == StoreType.Minio
        mock_minio_instance.init.assert_called_once_with(host, ak, sk, bucket)

    def test_init_with_unsupported_type(self):
        """测试使用不支持的存储类型初始化"""
        with pytest.raises(Exception, match="store_type: unsupported not support"):
            self.store_dao.init("host", "ak", "sk", "bucket", "unsupported")

    @patch('commons.db.storedao.KS3Tool')
    def test_get_tool_ks3(self, mock_ks3_tool_class):
        """测试获取 KS3 工具"""
        mock_ks3_instance = Mock()
        mock_ks3_tool_class.return_value = mock_ks3_instance
        
        self.store_dao.store_type = StoreType.Ks3
        
        tool = self.store_dao._get_tool()
        assert tool == mock_ks3_instance

    @patch('commons.db.storedao.MinioDao')
    def test_get_tool_minio(self, mock_minio_dao_class):
        """测试获取 Minio 工具"""
        mock_minio_instance = Mock()
        mock_minio_dao_class.return_value = mock_minio_instance
        
        self.store_dao.store_type = StoreType.Minio
        
        tool = self.store_dao._get_tool()
        assert tool == mock_minio_instance

    def test_get_tool_unsupported_type(self):
        """测试获取不支持的工具类型"""
        self.store_dao.store_type = "unsupported"
        
        with pytest.raises(Exception, match="store_type: unsupported not support"):
            self.store_dao._get_tool()

    def test_upload_from_text_success(self):
        """测试从文本上传成功"""
        mock_tool = Mock()
        mock_tool.upload_from_bytes.return_value = True

        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            text = "测试文本内容"

            result = self.store_dao.upload_from_text(store_path, text)

            assert result is True
            mock_tool.upload_from_bytes.assert_called_once_with(store_path, text.encode("utf-8"))

    def test_upload_from_text_failure(self):
        """测试从文本上传失败"""
        mock_tool = Mock()
        mock_tool.upload_from_bytes.side_effect = Exception("Upload failed")
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            text = "测试文本内容"
            
            result = self.store_dao.upload_from_text(store_path, text)
            
            assert result is False

    def test_upload_from_file_success(self):
        """测试从文件上传成功"""
        mock_tool = Mock()
        mock_tool.upload_from_file.return_value = True
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/uploaded_file.txt"
            fpath = "/local/path/source_file.txt"
            
            result = self.store_dao.upload_from_file(store_path, fpath)
            
            assert result is True
            mock_tool.upload_from_file.assert_called_once_with(store_path, fpath)

    def test_upload_from_file_failure(self):
        """测试从文件上传失败"""
        mock_tool = Mock()
        mock_tool.upload_from_file.side_effect = Exception("File upload failed")
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/uploaded_file.txt"
            fpath = "/local/path/source_file.txt"
            
            result = self.store_dao.upload_from_file(store_path, fpath)
            
            assert result is False

    def test_download_to_text_success(self):
        """测试下载为文本成功"""
        mock_tool = Mock()
        mock_tool.download_to_text.return_value = "下载的文本内容"
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            
            result = self.store_dao.download_to_text(store_path)
            
            assert result == "下载的文本内容"
            mock_tool.download_to_text.assert_called_once_with(store_path)

    def test_download_to_text_failure(self):
        """测试下载为文本失败"""
        mock_tool = Mock()
        mock_tool.download_to_text.side_effect = Exception("Download failed")
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            
            result = self.store_dao.download_to_text(store_path)
            
            assert result == (False, "")

    def test_download_to_file_success(self):
        """测试下载到文件成功"""
        mock_tool = Mock()
        mock_tool.download_to_file.return_value = True
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/remote_file.txt"
            fpath = "/local/path/downloaded_file.txt"
            
            result = self.store_dao.download_to_file(store_path, fpath)
            
            assert result is True
            mock_tool.download_to_file.assert_called_once_with(store_path, fpath)

    def test_download_to_file_failure(self):
        """测试下载到文件失败"""
        mock_tool = Mock()
        mock_tool.download_to_file.side_effect = Exception("File download failed")
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/remote_file.txt"
            fpath = "/local/path/downloaded_file.txt"
            
            result = self.store_dao.download_to_file(store_path, fpath)
            
            assert result is False

    def test_generate_url_success(self):
        """测试生成URL成功"""
        mock_tool = Mock()
        expected_url = "http://example.com/presigned-url"
        mock_tool.generate_url.return_value = expected_url
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            timeout = 3600
            
            result = self.store_dao.generate_url(store_path, timeout)
            
            assert result == expected_url
            mock_tool.generate_url.assert_called_once_with(store_path, timeout)

    def test_generate_url_failure(self):
        """测试生成URL失败"""
        mock_tool = Mock()
        mock_tool.generate_url.side_effect = Exception("URL generation failed")
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            timeout = 3600
            
            result = self.store_dao.generate_url(store_path, timeout)
            
            assert result == ""

    @pytest.mark.asyncio
    async def test_async_generate_url_with_async_tool(self):
        """测试异步生成URL - 工具支持异步"""
        mock_tool = Mock()
        expected_url = "http://example.com/async-presigned-url"
        mock_tool.async_generate_url = AsyncMock(return_value=expected_url)
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            timeout = 3600
            
            result = await self.store_dao.async_generate_url(store_path, timeout)
            
            assert result == expected_url
            mock_tool.async_generate_url.assert_called_once_with(store_path, timeout)

    @pytest.mark.asyncio
    async def test_async_generate_url_with_sync_tool(self):
        """测试异步生成URL - 工具只支持同步"""
        mock_tool = Mock()
        expected_url = "http://example.com/sync-presigned-url"
        mock_tool.generate_url.return_value = expected_url

        # 确保mock对象没有async_generate_url属性
        del mock_tool.async_generate_url

        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            timeout = 3600

            result = await self.store_dao.async_generate_url(store_path, timeout)

            assert result == expected_url
            mock_tool.generate_url.assert_called_once_with(store_path, timeout)

    @pytest.mark.asyncio
    async def test_async_generate_url_failure(self):
        """测试异步生成URL失败"""
        mock_tool = Mock()
        mock_tool.async_generate_url = AsyncMock(side_effect=Exception("Async URL generation failed"))
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file.txt"
            timeout = 3600
            
            result = await self.store_dao.async_generate_url(store_path, timeout)
            
            assert result == ""

    def test_delete_by_key_success(self):
        """测试删除文件成功"""
        mock_tool = Mock()
        mock_tool.delete_by_key.return_value = True
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file_to_delete.txt"
            
            result = self.store_dao.delete_by_key(store_path)
            
            assert result is True
            mock_tool.delete_by_key.assert_called_once_with(store_path)

    def test_delete_by_key_failure(self):
        """测试删除文件失败"""
        mock_tool = Mock()
        mock_tool.delete_by_key.side_effect = Exception("Delete failed")
        
        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            store_path = "test/path/file_to_delete.txt"
            
            result = self.store_dao.delete_by_key(store_path)
            
            assert result is False

    @patch('commons.db.storedao.KS3Tool')
    def test_list_dirs_with_ks3(self, mock_ks3_tool_class):
        """测试使用 KS3 列出目录"""
        mock_ks3_instance = Mock()
        expected_dirs = ["dir1", "dir2", "dir3"]
        mock_ks3_instance.list_dir.return_value = expected_dirs
        mock_ks3_tool_class.return_value = mock_ks3_instance
        
        self.store_dao.store_type = StoreType.Ks3
        
        result = self.store_dao.list_dirs("test/path/")
        
        assert result == expected_dirs
        mock_ks3_instance.list_dir.assert_called_once_with("test/path/")

    def test_list_dirs_with_minio(self):
        """测试使用 Minio 列出目录（不支持）"""
        self.store_dao.store_type = StoreType.Minio
        
        result = self.store_dao.list_dirs("test/path/")
        
        assert result == []

    def test_list_dirs_failure(self):
        """测试列出目录失败"""
        self.store_dao.store_type = StoreType.Ks3
        
        with patch('commons.db.storedao.KS3Tool') as mock_ks3_tool_class:
            mock_ks3_instance = Mock()
            mock_ks3_instance.list_dir.side_effect = Exception("List dirs failed")
            mock_ks3_tool_class.return_value = mock_ks3_instance
            
            result = self.store_dao.list_dirs("test/path/")
            
            assert result == []

    @pytest.mark.asyncio
    async def test_async_upload_from_bytes_with_async_tool(self):
        """测试异步上传字节数据（工具支持异步）"""
        # 模拟支持异步的工具
        mock_tool = Mock()
        mock_tool.async_upload_from_bytes = AsyncMock(return_value=True)

        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            result = await self.store_dao.async_upload_from_bytes("test/path", b"test data")

            # 验证结果
            assert result is True

            # 验证异步方法被调用
            mock_tool.async_upload_from_bytes.assert_called_once_with("test/path", b"test data")

    @pytest.mark.asyncio
    async def test_async_upload_from_bytes_with_sync_tool(self):
        """测试异步上传字节数据（工具不支持异步，回退到同步）"""
        # 模拟不支持异步的工具
        mock_tool = Mock()
        mock_tool.upload_from_bytes = Mock(return_value=True)
        # 确保没有 async_upload_from_bytes 属性
        if hasattr(mock_tool, 'async_upload_from_bytes'):
            delattr(mock_tool, 'async_upload_from_bytes')

        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            result = await self.store_dao.async_upload_from_bytes("test/path", b"test data")

            # 验证结果（可能是 True 或 False，取决于实现）
            assert result in [True, False]

            # 如果成功，验证同步方法被调用
            if result:
                mock_tool.upload_from_bytes.assert_called_once_with("test/path", b"test data")

    @pytest.mark.asyncio
    async def test_async_upload_from_bytes_exception(self):
        """测试异步上传字节数据异常"""
        # 模拟工具异常
        mock_tool = Mock()
        mock_tool.async_upload_from_bytes = AsyncMock(side_effect=Exception("异步上传异常"))

        with patch.object(self.store_dao, '_get_tool', return_value=mock_tool):
            result = await self.store_dao.async_upload_from_bytes("test/path", b"test data")

            # 验证结果
            assert result is False

    def test_upload_from_text_encoding_variations(self):
        """测试文本上传的编码变化"""
        # 测试不同编码的文本
        test_texts = [
            "English text",
            "中文文本测试",
            "日本語のテスト",
            "Русский текст",
            "🚀 Emoji test 🎉",
            "Mixed 中文 and English",
            ""  # 空字符串
        ]

        for text in test_texts:
            with patch.object(self.store_dao, 'upload_from_bytes', return_value=True) as mock_upload:
                result = self.store_dao.upload_from_text(f"test/{len(text)}", text)
                assert result is True
                mock_upload.assert_called_once_with(f"test/{len(text)}", text.encode("utf-8"))

    def test_store_dao_error_resilience(self):
        """测试存储 DAO 错误恢复能力"""
        # 测试各种错误场景
        error_scenarios = [
            {"operation": "upload_bytes", "error": "网络连接失败"},
            {"operation": "upload_text", "error": "编码错误"},
            {"operation": "upload_file", "error": "文件不存在"},
            {"operation": "async_upload", "error": "异步操作失败"}
        ]

        # 验证错误场景结构
        for scenario in error_scenarios:
            assert "operation" in scenario
            assert "error" in scenario
            assert isinstance(scenario["operation"], str)
            assert isinstance(scenario["error"], str)

    def test_store_dao_performance_monitoring(self):
        """测试存储 DAO 性能监控"""
        import time

        # 模拟性能监控
        def monitor_upload_performance(upload_func, *args, **kwargs):
            start_time = time.time()
            result = upload_func(*args, **kwargs)
            end_time = time.time()

            performance_data = {
                "duration": end_time - start_time,
                "success": result,
                "operation": getattr(upload_func, '__name__', 'unknown_function')
            }

            return result, performance_data

        # 测试性能监控
        with patch.object(self.store_dao, 'upload_from_bytes', return_value=True) as mock_upload:
            result, perf_data = monitor_upload_performance(
                self.store_dao.upload_from_bytes,
                "test/path",
                b"test data"
            )

            assert result is True
            assert "duration" in perf_data
            assert "success" in perf_data
            assert "operation" in perf_data
            assert perf_data["duration"] >= 0

    def test_store_dao_configuration_management(self):
        """测试存储 DAO 配置管理"""
        # 模拟配置管理
        config_scenarios = [
            {"store_type": "ks3", "config": {"host": "ks3.example.com", "bucket": "test"}},
            {"store_type": "minio", "config": {"host": "localhost:9000", "bucket": "test"}}
        ]

        for scenario in config_scenarios:
            # 验证配置结构
            assert "store_type" in scenario
            assert "config" in scenario
            assert isinstance(scenario["config"], dict)

            # 模拟配置应用
            store_dao = StoreDao()
            store_dao.init(
                scenario["config"]["host"],
                "ak",
                "sk",
                scenario["config"]["bucket"],
                scenario["store_type"]
            )

            # 验证初始化
            assert store_dao.store_type == scenario["store_type"]

    def test_upload_from_text_with_ks3(self):
        """测试使用 KS3 上传文本"""
        with patch.object(self.store_dao, '_get_tool') as mock_get_tool:
            mock_tool = Mock()
            mock_tool.upload_from_bytes.return_value = True
            mock_get_tool.return_value = mock_tool

            result = self.store_dao.upload_from_text("test/path", "test content")

            assert result is True
            mock_tool.upload_from_bytes.assert_called_once_with("test/path", "test content".encode("utf-8"))

    def test_upload_from_text_with_minio(self):
        """测试使用 Minio 上传文本"""
        with patch.object(self.store_dao, '_get_tool') as mock_get_tool:
            mock_tool = Mock()
            mock_tool.upload_from_bytes.return_value = True
            mock_get_tool.return_value = mock_tool

            result = self.store_dao.upload_from_text("test/path", "test content")

            assert result is True
            mock_tool.upload_from_bytes.assert_called_once_with("test/path", "test content".encode("utf-8"))

    def test_upload_from_text_exception(self):
        """测试上传文本异常处理"""
        with patch.object(self.store_dao, '_get_tool') as mock_get_tool:
            mock_tool = Mock()
            mock_tool.upload_from_bytes.side_effect = Exception("上传失败")
            mock_get_tool.return_value = mock_tool

            result = self.store_dao.upload_from_text("test/path", "test content")

            assert result is False

    def test_list_dirs_with_ks3_tool(self):
        """测试使用 KS3 列出目录"""
        self.store_dao.store_type = StoreType.Ks3

        with patch('commons.db.storedao.KS3Tool') as mock_ks3_class:
            mock_ks3_instance = Mock()
            mock_ks3_instance.list_dir.return_value = ["dir1/", "dir2/"]
            mock_ks3_class.return_value = mock_ks3_instance

            result = self.store_dao.list_dirs("test/path")

            assert result == ["dir1/", "dir2/"]
            mock_ks3_instance.list_dir.assert_called_once_with("test/path")

    def test_list_dirs_with_minio_tool(self):
        """测试使用 Minio 列出目录（不支持）"""
        self.store_dao.store_type = StoreType.Minio

        with patch('commons.db.storedao.MinioDao') as mock_minio_class:
            mock_minio_instance = Mock()
            # Minio 没有 list_dir 方法
            mock_minio_class.return_value = mock_minio_instance

            result = self.store_dao.list_dirs("test/path")

            assert result == []

    def test_list_dirs_exception(self):
        """测试列出目录异常处理"""
        self.store_dao.store_type = StoreType.Ks3

        with patch('commons.db.storedao.KS3Tool') as mock_ks3_class:
            mock_ks3_instance = Mock()
            mock_ks3_instance.list_dir.side_effect = Exception("列出目录失败")
            mock_ks3_class.return_value = mock_ks3_instance

            result = self.store_dao.list_dirs("test/path")

            assert result == []

    @pytest.mark.asyncio
    async def test_async_upload_from_bytes_with_ks3_async_method(self):
        """测试使用 KS3 异步上传字节（有异步方法）"""
        self.store_dao.store_type = StoreType.Ks3

        with patch('commons.db.storedao.KS3Tool') as mock_ks3_class:
            mock_ks3_instance = Mock()
            mock_ks3_instance.async_upload_from_bytes = AsyncMock(return_value=True)
            mock_ks3_class.return_value = mock_ks3_instance

            result = await self.store_dao.async_upload_from_bytes("test/path", b"test data")

            assert result is True
            mock_ks3_instance.async_upload_from_bytes.assert_called_once_with("test/path", b"test data")

    @pytest.mark.asyncio
    async def test_async_upload_from_bytes_with_sync_fallback(self):
        """测试异步上传字节回退到同步方法"""
        with patch.object(self.store_dao, '_get_tool') as mock_get_tool:
            mock_tool = Mock()
            # 模拟没有异步方法，只有同步方法
            mock_tool.upload_from_bytes.return_value = True
            # 确保没有 async_upload_from_bytes 属性
            if hasattr(mock_tool, 'async_upload_from_bytes'):
                delattr(mock_tool, 'async_upload_from_bytes')
            mock_get_tool.return_value = mock_tool

            result = await self.store_dao.async_upload_from_bytes("test/path", b"test data")

            assert result is True
            mock_tool.upload_from_bytes.assert_called_once_with("test/path", b"test data")

    @pytest.mark.asyncio
    async def test_async_upload_from_bytes_exception(self):
        """测试异步上传字节异常处理"""
        self.store_dao.store_type = StoreType.Ks3

        with patch('commons.db.storedao.KS3Tool') as mock_ks3_class:
            mock_ks3_instance = Mock()
            mock_ks3_instance.async_upload_from_bytes = AsyncMock(side_effect=Exception("异步上传失败"))
            mock_ks3_class.return_value = mock_ks3_instance

            result = await self.store_dao.async_upload_from_bytes("test/path", b"test data")

            assert result is False

    @pytest.mark.asyncio
    async def test_async_generate_url_with_ks3_async_method(self):
        """测试使用 KS3 异步生成 URL（有异步方法）"""
        self.store_dao.store_type = StoreType.Ks3

        with patch('commons.db.storedao.KS3Tool') as mock_ks3_class:
            mock_ks3_instance = Mock()
            mock_ks3_instance.async_generate_url = AsyncMock(return_value="http://async-url.com")
            mock_ks3_class.return_value = mock_ks3_instance

            result = await self.store_dao.async_generate_url("test/path", 3600)

            assert result == "http://async-url.com"
            mock_ks3_instance.async_generate_url.assert_called_once_with("test/path", 3600)

    @pytest.mark.asyncio
    async def test_async_generate_url_with_sync_fallback(self):
        """测试异步生成 URL 回退到同步方法"""
        with patch.object(self.store_dao, '_get_tool') as mock_get_tool:
            mock_tool = Mock()
            # 模拟没有异步方法，只有同步方法
            mock_tool.generate_url.return_value = "http://sync-url.com"
            # 确保没有 async_generate_url 属性
            if hasattr(mock_tool, 'async_generate_url'):
                delattr(mock_tool, 'async_generate_url')
            mock_get_tool.return_value = mock_tool

            result = await self.store_dao.async_generate_url("test/path", 3600)

            assert result == "http://sync-url.com"
            mock_tool.generate_url.assert_called_once_with("test/path", 3600)

    @pytest.mark.asyncio
    async def test_async_generate_url_exception(self):
        """测试异步生成 URL 异常处理"""
        self.store_dao.store_type = StoreType.Ks3

        with patch('commons.db.storedao.KS3Tool') as mock_ks3_class:
            mock_ks3_instance = Mock()
            mock_ks3_instance.async_generate_url = AsyncMock(side_effect=Exception("异步生成 URL 失败"))
            mock_ks3_class.return_value = mock_ks3_instance

            result = await self.store_dao.async_generate_url("test/path", 3600)

            assert result == ""
