"""
commons.logger.business_log 模块的测试
"""
import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

from commons.logger.business_log import BusinessLogger, logger


class TestBusinessLogger:
    """测试 BusinessLogger 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建测试用的 BusinessLogger 实例
        self.test_logger = BusinessLogger("test_logger")

    def test_init(self):
        """测试初始化"""
        # 验证 logger 被正确创建
        assert self.test_logger.logger.name == "test_logger"
        assert self.test_logger._business_fields == {}
        
        # 验证 handler 被正确设置
        assert len(self.test_logger.logger.handlers) == 1
        handler = self.test_logger.logger.handlers[0]
        assert isinstance(handler, logging.StreamHandler)

    def test_init_clears_existing_handlers(self):
        """测试初始化时清除现有的处理器"""
        # 创建一个已有处理器的 logger
        existing_logger = logging.getLogger("existing_test")
        existing_handler = logging.StreamHandler()
        existing_logger.addHandler(existing_handler)
        
        # 验证已有处理器
        assert len(existing_logger.handlers) == 1
        
        # 创建 BusinessLogger，应该清除现有处理器
        business_logger = BusinessLogger("existing_test")
        
        # 验证处理器被清除并重新设置
        assert len(business_logger.logger.handlers) == 1
        assert business_logger.logger.handlers[0] != existing_handler

    def test_add_business_field(self):
        """测试添加业务字段"""
        # 添加单个字段
        self.test_logger.add_business_field("user_id", "12345")
        assert self.test_logger._business_fields["user_id"] == "12345"
        
        # 添加多个字段
        self.test_logger.add_business_field("request_id", "req_789")
        self.test_logger.add_business_field("action", "test_action")
        
        expected_fields = {
            "user_id": "12345",
            "request_id": "req_789",
            "action": "test_action"
        }
        assert self.test_logger._business_fields == expected_fields

    def test_add_business_field_overwrite(self):
        """测试覆盖业务字段"""
        # 添加字段
        self.test_logger.add_business_field("key", "value1")
        assert self.test_logger._business_fields["key"] == "value1"
        
        # 覆盖字段
        self.test_logger.add_business_field("key", "value2")
        assert self.test_logger._business_fields["key"] == "value2"

    def test_get_extra(self):
        """测试获取 extra 字典"""
        # 空字段时
        extra = self.test_logger._get_extra()
        assert extra == {"business_fields": {}}
        
        # 有字段时
        self.test_logger.add_business_field("test_key", "test_value")
        extra = self.test_logger._get_extra()
        expected_extra = {"business_fields": {"test_key": "test_value"}}
        assert extra == expected_extra

    def test_info_logging(self):
        """测试 info 级别日志"""
        with patch.object(self.test_logger.logger, 'info') as mock_info:
            self.test_logger.add_business_field("test_field", "test_value")
            self.test_logger.info("测试信息日志")
            
            mock_info.assert_called_once_with(
                "测试信息日志",
                extra={"business_fields": {"test_field": "test_value"}}
            )

    def test_debug_logging(self):
        """测试 debug 级别日志"""
        with patch.object(self.test_logger.logger, 'debug') as mock_debug:
            self.test_logger.add_business_field("debug_field", "debug_value")
            self.test_logger.debug("测试调试日志")
            
            mock_debug.assert_called_once_with(
                "测试调试日志",
                extra={"business_fields": {"debug_field": "debug_value"}}
            )

    def test_warning_logging(self):
        """测试 warning 级别日志"""
        with patch.object(self.test_logger.logger, 'warning') as mock_warning:
            self.test_logger.add_business_field("warn_field", "warn_value")
            self.test_logger.warning("测试警告日志")
            
            mock_warning.assert_called_once_with(
                "测试警告日志",
                extra={"business_fields": {"warn_field": "warn_value"}}
            )

    def test_error_logging(self):
        """测试 error 级别日志"""
        with patch.object(self.test_logger.logger, 'error') as mock_error:
            self.test_logger.add_business_field("error_field", "error_value")
            self.test_logger.error("测试错误日志")
            
            mock_error.assert_called_once_with(
                "测试错误日志",
                extra={"business_fields": {"error_field": "error_value"}}
            )

    def test_critical_logging(self):
        """测试 critical 级别日志"""
        with patch.object(self.test_logger.logger, 'critical') as mock_critical:
            self.test_logger.add_business_field("critical_field", "critical_value")
            self.test_logger.critical("测试严重错误日志")
            
            mock_critical.assert_called_once_with(
                "测试严重错误日志",
                extra={"business_fields": {"critical_field": "critical_value"}}
            )

    def test_exception_logging(self):
        """测试 exception 级别日志"""
        with patch.object(self.test_logger.logger, 'exception') as mock_exception:
            self.test_logger.add_business_field("exception_field", "exception_value")
            self.test_logger.exception("测试异常日志")
            
            mock_exception.assert_called_once_with(
                "测试异常日志",
                extra={"business_fields": {"exception_field": "exception_value"}}
            )

    def test_multiple_log_calls_with_different_fields(self):
        """测试多次日志调用使用不同字段"""
        with patch.object(self.test_logger.logger, 'info') as mock_info:
            # 第一次调用
            self.test_logger.add_business_field("field1", "value1")
            self.test_logger.info("第一条日志")

            # 验证第一次调用
            first_call = mock_info.call_args_list[0]
            assert first_call[0][0] == "第一条日志"
            assert first_call[1]["extra"]["business_fields"] == {"field1": "value1"}

            # 第二次调用，添加更多字段
            self.test_logger.add_business_field("field2", "value2")
            self.test_logger.info("第二条日志")

            # 验证两次调用
            assert mock_info.call_count == 2

            # 验证第二次调用（包含累积的字段）
            second_call = mock_info.call_args_list[1]
            assert second_call[0][0] == "第二条日志"
            assert second_call[1]["extra"]["business_fields"] == {"field1": "value1", "field2": "value2"}

    def test_logging_without_business_fields(self):
        """测试没有业务字段时的日志记录"""
        with patch.object(self.test_logger.logger, 'info') as mock_info:
            self.test_logger.info("无业务字段的日志")
            
            mock_info.assert_called_once_with(
                "无业务字段的日志",
                extra={"business_fields": {}}
            )

    @patch('commons.logger.business_log.ConfLogger')
    def test_logger_level_configuration(self, mock_conf_logger):
        """测试日志级别配置"""
        mock_conf_logger.log_level = logging.WARNING
        
        test_logger = BusinessLogger("level_test")
        assert test_logger.logger.level == logging.WARNING

    def test_handler_configuration(self):
        """测试处理器配置"""
        # 验证处理器类型
        handler = self.test_logger.logger.handlers[0]
        assert isinstance(handler, logging.StreamHandler)
        
        # 验证格式化器
        from commons.logger.logger import BaseFormatter
        assert isinstance(handler.formatter, BaseFormatter)
        
        # 验证过滤器
        from commons.logger.logger import LoggerFilter
        assert len(handler.filters) == 1
        assert isinstance(handler.filters[0], LoggerFilter)


class TestGlobalLogger:
    """测试全局 logger 实例"""

    def test_global_logger_instance(self):
        """测试全局 logger 实例"""
        assert isinstance(logger, BusinessLogger)
        # 全局 logger 的名称可能是空字符串或 root
        assert logger.logger.name in ["", "root"]

    def test_global_logger_functionality(self):
        """测试全局 logger 功能"""
        with patch.object(logger.logger, 'info') as mock_info:
            logger.add_business_field("global_test", "global_value")
            logger.info("全局日志测试")
            
            mock_info.assert_called_once_with(
                "全局日志测试",
                extra={"business_fields": {"global_test": "global_value"}}
            )

    def test_global_logger_isolation(self):
        """测试全局 logger 与其他实例的隔离"""
        # 清空全局 logger 的现有字段
        logger._business_fields.clear()

        # 创建新的 BusinessLogger 实例
        new_logger = BusinessLogger("isolated_test")

        # 在全局 logger 中添加字段
        logger.add_business_field("global_field", "global_value")

        # 在新实例中添加字段
        new_logger.add_business_field("isolated_field", "isolated_value")

        # 验证字段隔离
        assert logger._business_fields == {"global_field": "global_value"}
        assert new_logger._business_fields == {"isolated_field": "isolated_value"}

    def test_global_logger_persistence(self):
        """测试全局 logger 字段持久性"""
        # 清空现有字段
        logger._business_fields.clear()
        
        # 添加字段
        logger.add_business_field("persistent_field", "persistent_value")
        
        # 多次使用应该保持字段
        with patch.object(logger.logger, 'info') as mock_info:
            logger.info("第一次使用")
            logger.info("第二次使用")
            
            assert mock_info.call_count == 2
            for call in mock_info.call_args_list:
                assert call[1]["extra"]["business_fields"] == {"persistent_field": "persistent_value"}
