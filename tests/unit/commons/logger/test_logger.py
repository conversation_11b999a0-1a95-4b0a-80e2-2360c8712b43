"""
commons.logger.logger 模块的测试
"""
import pytest
import logging
import json
import contextvars
import sys
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from io import StringIO

from commons.logger.logger import (
    ip_context, type_context, request_id_context, local_id_context,
    index_context, uri_context, trace_id_context, span_id_context,
    PassableLogContext, LoggerFilter, ELKLoggerItem,
    common_format, _get_record_attr, BaseFormatter, ColorFormatter,
    init_logger, error_monitor_name, colors_code
)


class TestContextVars:
    """测试上下文变量"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置所有上下文变量
        ip_context.set("no ip")
        type_context.set("no type")
        request_id_context.set("")
        local_id_context.set("no Local-Id")
        index_context.set(0)
        uri_context.set("")
        trace_id_context.set("")
        span_id_context.set("")

    def test_context_vars_default_values(self):
        """测试上下文变量的默认值"""
        assert ip_context.get() == "no ip"
        assert type_context.get() == "no type"
        assert request_id_context.get() == ""
        assert local_id_context.get() == "no Local-Id"
        assert index_context.get() == 0
        assert uri_context.get() == ""
        assert trace_id_context.get() == ""
        assert span_id_context.get() == ""

    def test_context_vars_set_and_get(self):
        """测试上下文变量的设置和获取"""
        ip_context.set("***********")
        type_context.set("API")
        request_id_context.set("req-123")
        local_id_context.set("local-456")
        index_context.set(5)
        uri_context.set("/api/test")
        trace_id_context.set("trace-789")
        span_id_context.set("span-abc")

        assert ip_context.get() == "***********"
        assert type_context.get() == "API"
        assert request_id_context.get() == "req-123"
        assert local_id_context.get() == "local-456"
        assert index_context.get() == 5
        assert uri_context.get() == "/api/test"
        assert trace_id_context.get() == "trace-789"
        assert span_id_context.get() == "span-abc"


class TestPassableLogContext:
    """测试可传递日志上下文"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置所有上下文变量
        ip_context.set("test_ip")
        type_context.set("test_type")
        request_id_context.set("test_request_id")
        local_id_context.set("test_local_id")
        index_context.set(10)
        uri_context.set("test_uri")
        trace_id_context.set("test_trace_id")
        span_id_context.set("test_span_id")

    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        context = PassableLogContext(
            ip="***********",
            req_type="API",
            request_id="req-123",
            local_id="local-456",
            index=5,
            uri="/api/test",
            trace_id="trace-789",
            span_id="span-abc"
        )
        assert context.ip == "***********"
        assert context.req_type == "API"
        assert context.request_id == "req-123"
        assert context.local_id == "local-456"
        assert context.index == 5
        assert context.uri == "/api/test"
        assert context.trace_id == "trace-789"
        assert context.span_id == "span-abc"

    def test_dump_with_default_values(self):
        """测试使用默认值的 dump 方法"""
        result = PassableLogContext.dump()
        
        expected = {
            "ip": "test_ip",
            "req_type": "test_type",
            "request_id": "test_request_id",
            "local_id": "test_local_id",
            "index": 10,
            "uri": "test_uri",
            "trace_id": "test_trace_id",
            "span_id": "test_span_id"
        }
        assert result == expected

    def test_dump_with_custom_values(self):
        """测试使用自定义值的 dump 方法"""
        result = PassableLogContext.dump(
            ip="custom_ip",
            req_type="custom_type",
            request_id="custom_request",
            local_id="custom_local",
            index=99,
            uri="custom_uri"
        )
        
        expected = {
            "ip": "custom_ip",
            "req_type": "custom_type",
            "request_id": "custom_request",
            "local_id": "custom_local",
            "index": 99,
            "uri": "custom_uri",
            "trace_id": "test_trace_id",  # 这些仍然来自上下文
            "span_id": "test_span_id"
        }
        assert result == expected

    def test_dump_with_partial_custom_values(self):
        """测试使用部分自定义值的 dump 方法"""
        result = PassableLogContext.dump(
            ip="partial_ip",
            index=50
        )
        
        expected = {
            "ip": "partial_ip",
            "req_type": "test_type",
            "request_id": "test_request_id",
            "local_id": "test_local_id",
            "index": 50,
            "uri": "test_uri",
            "trace_id": "test_trace_id",
            "span_id": "test_span_id"
        }
        assert result == expected

    def test_export(self):
        """测试导出上下文到上下文变量"""
        context = PassableLogContext(
            ip="export_ip",
            req_type="export_type",
            request_id="export_request",
            local_id="export_local",
            index=100,
            uri="export_uri",
            trace_id="export_trace",
            span_id="export_span"
        )
        
        context.export()
        
        # 验证上下文变量被正确设置
        assert ip_context.get() == "export_ip"
        assert type_context.get() == "export_type"
        assert request_id_context.get() == "export_request"
        assert local_id_context.get() == "export_local"
        assert index_context.get() == 100
        assert uri_context.get() == "export_uri"
        assert trace_id_context.get() == "export_trace"
        assert span_id_context.get() == "export_span"

    def test_context_manager_enter_exit(self):
        """测试上下文管理器的进入和退出"""
        # 设置初始上下文
        ip_context.set("initial_ip")
        request_id_context.set("initial_request")
        
        context = PassableLogContext(
            ip="context_ip",
            req_type="context_type",
            request_id="context_request",
            local_id="context_local",
            index=200,
            uri="context_uri",
            trace_id="context_trace",
            span_id="context_span"
        )
        
        # 使用上下文管理器
        with context:
            # 在上下文内部，值应该被交换
            assert ip_context.get() == "context_ip"
            assert request_id_context.get() == "context_request"
        
        # 退出上下文后，值应该被恢复
        assert ip_context.get() == "initial_ip"
        assert request_id_context.get() == "initial_request"

    def test_swap_method(self):
        """测试 swap 方法"""
        # 设置初始上下文
        ip_context.set("original_ip")
        type_context.set("original_type")
        
        context = PassableLogContext(
            ip="new_ip",
            req_type="new_type",
            request_id="new_request",
            local_id="new_local",
            index=300,
            uri="new_uri",
            trace_id="new_trace",
            span_id="new_span"
        )
        
        # 第一次交换
        context.swap()
        assert ip_context.get() == "new_ip"
        assert type_context.get() == "new_type"
        assert context.ip == "original_ip"
        assert context.req_type == "original_type"
        
        # 第二次交换（恢复）
        context.swap()
        assert ip_context.get() == "original_ip"
        assert type_context.get() == "original_type"
        assert context.ip == "new_ip"
        assert context.req_type == "new_type"


class TestLoggerFilter:
    """测试日志过滤器"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.filter = LoggerFilter()
        # 重置上下文变量
        ip_context.set("filter_test_ip")
        type_context.set("filter_test_type")
        request_id_context.set("filter_test_request")
        local_id_context.set("filter_test_local")
        index_context.set(0)
        uri_context.set("filter_test_uri")

    def test_filter_adds_context_to_record(self):
        """测试过滤器向记录添加上下文信息"""
        # 创建模拟日志记录
        record = Mock()

        # 执行过滤
        result = self.filter.filter(record)

        # 验证返回值
        assert result is True

        # 验证记录被添加了上下文属性
        assert record.ip_context == "filter_test_ip"
        assert record.type_context == "filter_test_type"
        assert record.request_id == "filter_test_request"
        assert record.local_id == "filter_test_local"
        assert record.uri == "filter_test_uri"
        assert record.index == 0

    def test_filter_increments_index(self):
        """测试过滤器递增索引"""
        record1 = Mock()
        record2 = Mock()
        record3 = Mock()

        # 多次调用过滤器
        self.filter.filter(record1)
        self.filter.filter(record2)
        self.filter.filter(record3)

        # 验证索引递增
        assert record1.index == 0
        assert record2.index == 1
        assert record3.index == 2

        # 验证上下文变量也被更新
        assert index_context.get() == 3

    def test_filter_with_different_context_values(self):
        """测试过滤器在不同上下文值下的行为"""
        # 更改上下文值
        ip_context.set("new_ip")
        type_context.set("new_type")
        request_id_context.set("new_request")

        record = Mock()
        self.filter.filter(record)

        # 验证记录获得了新的上下文值
        assert record.ip_context == "new_ip"
        assert record.type_context == "new_type"
        assert record.request_id == "new_request"


class TestELKLoggerItem:
    """测试 ELK 日志项模型"""

    def test_init_with_required_fields(self):
        """测试使用必需字段初始化"""
        item = ELKLoggerItem(
            message="测试消息",
            level="INFO"
        )
        assert item.message == "测试消息"
        assert item.level == "INFO"
        # 验证默认值
        assert item.request_id == ""
        assert item.local_id == ""
        assert item.uri == ""
        assert item.file_path == ""
        assert item.log_time == ""
        assert item.ip == ""
        assert item.error_trace == ""

    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        item = ELKLoggerItem(
            message="完整测试消息",
            level="ERROR",
            request_id="req-123",
            local_id="local-456",
            uri="/api/test",
            file_path="/path/to/file.py",
            log_time="2024-01-01 12:00:00.000",
            ip="***********",
            error_trace="错误堆栈信息"
        )
        assert item.message == "完整测试消息"
        assert item.level == "ERROR"
        assert item.request_id == "req-123"
        assert item.local_id == "local-456"
        assert item.uri == "/api/test"
        assert item.file_path == "/path/to/file.py"
        assert item.log_time == "2024-01-01 12:00:00.000"
        assert item.ip == "***********"
        assert item.error_trace == "错误堆栈信息"


class TestGetRecordAttr:
    """测试 _get_record_attr 函数"""

    def test_get_existing_attribute(self):
        """测试获取存在的属性"""
        record = Mock()
        record.test_attr = "test_value"

        result = _get_record_attr(record, "test_attr", "default")
        assert result == "test_value"

    def test_get_non_existing_attribute_with_default(self):
        """测试获取不存在的属性，使用默认值"""
        record = Mock()
        # 确保属性不存在
        if hasattr(record, "non_existing"):
            delattr(record, "non_existing")

        result = _get_record_attr(record, "non_existing", "default_value")
        assert result == "default_value"

    def test_get_non_existing_attribute_without_default(self):
        """测试获取不存在的属性，不提供默认值"""
        record = Mock()
        # 确保属性不存在
        del record.non_existing  # 删除 Mock 自动创建的属性

        result = _get_record_attr(record, "non_existing")
        assert result is None

    def test_get_attribute_with_none_value(self):
        """测试获取值为 None 的属性"""
        record = Mock()
        record.none_attr = None

        result = _get_record_attr(record, "none_attr", "default")
        assert result is None  # 应该返回实际的 None 值，而不是默认值


class TestCommonFormat:
    """测试通用格式化函数"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 设置上下文变量
        request_id_context.set("test_request_123")
        local_id_context.set("test_local_456")
        ip_context.set("*************")
        uri_context.set("/api/format_test")

    def test_common_format_non_elk(self):
        """测试非 ELK 格式"""
        # 创建模拟日志记录
        record = Mock()
        record.asctime = "2024-01-01 12:00:00,000"
        record.levelname = "INFO"
        record.request_id = "req-123"
        record.index = 5
        record.message = "测试消息"
        record.getMessage = Mock(return_value="测试消息")

        with patch('logging.Formatter.format') as mock_format:
            mock_format.return_value = "[2024-01-01 12:00:00,000] [INFO] [req-123] [5] 测试消息"

            result = common_format(record, elk_format=False)
            assert result == "[2024-01-01 12:00:00,000] [INFO] [req-123] [5] 测试消息"

    def test_common_format_elk_with_basic_fields(self):
        """测试 ELK 格式的基本字段"""
        # 创建真实的日志记录对象
        import logging
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="/path/to/test.py",
            lineno=42,
            msg="测试消息",
            args=(),
            exc_info=None,
            func="test_function"
        )
        record.created = 1704110400.123  # 2024-01-01 12:00:00.123
        record.request_id = "req-123"
        record.index = 5

        with patch('logging.Formatter.format') as mock_format:
            mock_format.return_value = "[2024-01-01 12:00:00,000] [INFO] [req-123] [5] 测试消息"

            result = common_format(record, elk_format=True)

            # 解析 JSON 结果
            parsed_result = json.loads(result)

            # 验证基本字段
            assert parsed_result["level"] == "INFO"
            assert "测试消息" in parsed_result["message"]
            assert parsed_result["request_id"] == "req-123"
            assert parsed_result["file_path"] == "File：/path/to/test.py, line：42, func：test_function"
            assert parsed_result["log_time"] == "2024-01-01 12:00:00.123"

    def test_common_format_elk_with_business_fields(self):
        """测试 ELK 格式包含业务字段"""
        import logging
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="/path/to/test.py",
            lineno=42,
            msg="测试消息",
            args=(),
            exc_info=None,
            func="test_function"
        )
        record.created = 1704110400.123
        record.request_id = "req-123"
        record.index = 5
        record.business_fields = {
            "user_id": "user_123",
            "action": "test_action",
            "request_id": "should_be_ignored",  # 应该被忽略
            "custom_field": "custom_value"
        }

        with patch('logging.Formatter.format') as mock_format:
            mock_format.return_value = "[2024-01-01 12:00:00,000] [INFO] [req-123] [5] 测试消息"

            result = common_format(record, elk_format=True)
            parsed_result = json.loads(result)

            # 验证业务字段被包含，但保留字段被过滤
            assert parsed_result["user_id"] == "user_123"
            assert parsed_result["action"] == "test_action"
            assert parsed_result["custom_field"] == "custom_value"
            # request_id 应该来自记录本身，而不是业务字段
            assert parsed_result["request_id"] == "req-123"
