"""
commons.llm_gateway.models.public_model_gateway 模块的测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

from commons.llm_gateway.models.public_model_gateway import (
    PublicModelGateway,
    ChatParams,
    LLMParam,
    PublicCode,
    Choice,
    ChatResponse,
    SecText,
    RetryStrategy,
    ERROR_AUDIT_ARR,
    ERROR_PRIVILEGE_ARR,
    ERROR_LIMIT_ARR
)
from commons.llm_gateway.models.chat_data import Message, ChatType, Usage, TraceInfo
from commons.llm_gateway.llm import LLMChatStatus, LLMStreamItem


class TestChatParams:
    """测试聊天参数类"""

    def test_chat_params_creation(self):
        """测试聊天参数创建"""
        # 添加必需的 messages 参数
        messages = [Message(role="user", content="Hello")]

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        assert params.stream is False
        assert params.provider == "test_provider"
        assert params.model == "test_model"
        assert params.messages == messages

    def test_chat_params_with_optional_fields(self):
        """测试带可选字段的聊天参数"""
        # 添加必需的 messages 参数
        messages = [Message(role="user", content="Hello")]

        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            version="v1.0",
            messages=messages
        )

        assert params.stream is True
        assert params.version == "v1.0"
        assert params.messages == messages


class TestLLMParam:
    """测试 LLM 参数类"""

    def test_llm_param_creation(self):
        """测试 LLM 参数创建"""
        param = LLMParam(
            temperature=0.7,
            max_tokens=1000,
            top_p=0.9,
            stop=["<|endoftext|>"]
        )
        
        assert param.temperature == 0.7
        assert param.max_tokens == 1000
        assert param.top_p == 0.9
        assert param.stop == ["<|endoftext|>"]

    def test_llm_param_with_defaults(self):
        """测试带默认值的 LLM 参数"""
        param = LLMParam()
        
        # 验证默认值（根据实际实现调整）
        assert hasattr(param, 'temperature')
        assert hasattr(param, 'max_tokens')
        assert hasattr(param, 'top_p')
        assert hasattr(param, 'stop')


class TestPublicModelGatewayMethods:
    """测试公共模型网关方法"""

    def test_gateway_initialization(self):
        """测试网关初始化"""
        from unittest.mock import Mock

        # 创建模拟配置
        conf = Mock()
        conf.host = "http://public-api.com"
        conf.api = "/v1/public/chat"
        conf.token = "public_token"
        conf.uid = "public_uid"
        conf.product_name = "public_product"
        conf.intention_code = "public_intention"
        conf.record_token = False
        conf.prom_token = False

        # 模拟 requests.Session
        with patch('commons.llm_gateway.models.public_model_gateway.requests.Session') as mock_session:
            mock_session_instance = Mock()
            mock_session.return_value = mock_session_instance

            gateway = PublicModelGateway(conf, pool_max=5)

            # 验证初始化
            assert gateway.get_conf() == conf
            assert gateway._pool_max == 5

    def test_get_httpcall_datas(self):
        """测试获取 HTTP 调用数据"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://public-api.com"
        conf.api = "/v1/public/chat"
        conf.token = "public_token"
        conf.uid = "public_uid"
        conf.product_name = "public_product"
        conf.intention_code = "public_intention"
        conf.record_token = False
        conf.prom_token = False

        with patch('commons.llm_gateway.models.public_model_gateway.requests.Session'):
            gateway = PublicModelGateway(conf)

            # 创建测试参数
            params = Mock()
            params.company_id = "test_company"
            params.ai_token = "test_ai_token"
            params.ai_product_name = "test_product"
            params.ai_intention_code = "test_intention"
            params.user_id = "test_user"

            with patch('commons.llm_gateway.models.public_model_gateway.request_id_context') as mock_context:
                mock_context.get.return_value = "test_request_id"

                headers, jdata = gateway._get_httpcall_datas(params)

                # 验证头部设置
                assert headers["Client-Request-Id"] == "test_request_id"
                assert headers["AI-Gateway-Company-Id"] == "test_company"
                assert headers["AI-Gateway-Token"] == "test_ai_token"
                assert headers["AI-Gateway-Product-Name"] == "test_product"
                assert headers["AI-Gateway-Intention-Code"] == "test_intention"
                assert headers["AI-Gateway-User-Id"] == "test_user"

    def test_get_tpm_functionality(self):
        """测试获取 TPM 功能"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://public-api.com"
        conf.api = "/v1/public/chat"
        conf.token = "public_token"
        conf.uid = "public_uid"
        conf.product_name = "public_product"
        conf.intention_code = "public_intention"
        conf.record_token = True
        conf.record_token_key = "public_tpm"
        conf.get_tpm_callback = Mock(return_value=1000)
        conf.prom_token = False

        with patch('commons.llm_gateway.models.public_model_gateway.requests.Session'):
            gateway = PublicModelGateway(conf)

            # 调用获取 TPM 方法
            result = gateway.get_tpm("test_key")

            # 验证回调被调用并返回正确值
            assert result == 1000
            conf.get_tpm_callback.assert_called_once_with("public_tpm_test_key")

    def test_gateway_without_record_token(self):
        """测试不记录令牌的网关"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://public-api.com"
        conf.api = "/v1/public/chat"
        conf.token = "public_token"
        conf.uid = "public_uid"
        conf.product_name = "public_product"
        conf.intention_code = "public_intention"
        conf.record_token = False
        conf.prom_token = False

        with patch('commons.llm_gateway.models.public_model_gateway.requests.Session'):
            gateway = PublicModelGateway(conf)

            # 调用获取 TPM 方法（应该返回 0）
            result = gateway.get_tpm("test_key")
            assert result == 0


class TestPublicModelGateway:
    """测试公共模型网关类"""

    def test_class_exists(self):
        """测试类存在性"""
        # 简单测试类是否存在
        assert PublicModelGateway is not None

        # 测试类有必要的方法
        assert hasattr(PublicModelGateway, '__init__')
        assert hasattr(PublicModelGateway, 'get_conf')
        assert hasattr(PublicModelGateway, 'chat')
        assert hasattr(PublicModelGateway, 'close')

    def test_chat_params_with_messages(self):
        """测试带消息的聊天参数"""
        messages = [Message(role="user", content="Test message")]

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        assert params.messages == messages
        assert len(params.messages) == 1
        assert params.messages[0].role == "user"
        assert params.messages[0].content == "Test message"

    def test_llm_param_integration(self):
        """测试 LLM 参数集成"""
        param = LLMParam(
            temperature=0.5,
            max_tokens=500
        )

        # 验证参数设置
        assert param.temperature == 0.5
        assert param.max_tokens == 500


class TestPublicModelGatewayMethods:
    """测试公共模型网关方法"""

    def test_gateway_initialization(self):
        """测试网关初始化"""
        from unittest.mock import Mock

        # 创建模拟配置
        conf = Mock()
        conf.host = "http://test-api.com"
        conf.api = "/v1/chat"
        conf.multimodal_api = "/v1/multimodal"
        conf.token = "test_token"
        conf.uid = "test_uid"
        conf.product_name = "test_product"
        conf.intention_code = "test_intention"
        conf.prom_token = False
        conf.record_token = False

        # 模拟 aiohttp.ClientSession 和 TCPConnector
        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession') as mock_session, \
             patch('commons.llm_gateway.models.public_model_gateway.aiohttp.TCPConnector') as mock_connector:

            mock_session_instance = Mock()
            mock_session.return_value = mock_session_instance
            mock_connector_instance = Mock()
            mock_connector.return_value = mock_connector_instance

            gateway = PublicModelGateway(conf, pool_max=5)

            # 验证初始化
            assert gateway.get_conf() == conf
            assert gateway._chat_url == "http://test-api.com/v1/chat"
            assert gateway._multimodal_url == "http://test-api.com/v1/multimodal"
            assert gateway._pool_max == 5

    @pytest.mark.asyncio
    async def test_gateway_close(self):
        """测试网关关闭"""
        from unittest.mock import Mock, AsyncMock

        conf = Mock()
        conf.host = "http://test-api.com"
        conf.api = "/v1/chat"
        conf.multimodal_api = "/v1/multimodal"
        conf.token = "test_token"
        conf.uid = "test_uid"
        conf.product_name = "test_product"
        conf.intention_code = "test_intention"
        conf.prom_token = False
        conf.record_token = False

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value = mock_session_instance

            gateway = PublicModelGateway(conf)

            # 调用关闭方法
            await gateway.close()

            # 验证会话被关闭
            mock_session_instance.close.assert_called_once()

    def test_record_token_functionality(self):
        """测试令牌记录功能"""
        from unittest.mock import Mock

        # 创建带回调的配置
        conf = Mock()
        conf.host = "http://test-api.com"
        conf.api = "/v1/chat"
        conf.multimodal_api = "/v1/multimodal"
        conf.token = "test_token"
        conf.uid = "test_uid"
        conf.product_name = "test_product"
        conf.intention_code = "test_intention"
        conf.prom_token = False
        conf.record_token = True
        conf.record_token_key = "test_token"
        conf.record_token_callback = Mock()

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            # 调用记录令牌方法
            gateway._record_token("test_key", 100)

            # 验证回调被调用
            conf.record_token_callback.assert_called_once_with("test_token_test_key", 100)

    @pytest.mark.asyncio
    async def test_async_record_token_functionality(self):
        """测试异步令牌记录功能"""
        from unittest.mock import Mock, AsyncMock

        conf = Mock()
        conf.host = "http://test-api.com"
        conf.api = "/v1/chat"
        conf.multimodal_api = "/v1/multimodal"
        conf.token = "test_token"
        conf.uid = "test_uid"
        conf.product_name = "test_product"
        conf.intention_code = "test_intention"
        conf.prom_token = False
        conf.record_token = True
        conf.record_token_key = "async_token"
        conf.async_record_token_callback = AsyncMock()

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            # 调用异步记录令牌方法
            await gateway._async_record_token("async_key", 200)

            # 验证异步回调被调用
            conf.async_record_token_callback.assert_called_once_with("async_token_async_key", 200)

    def test_get_tpm_functionality(self):
        """测试获取 TPM 功能"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://test-api.com"
        conf.api = "/v1/chat"
        conf.multimodal_api = "/v1/multimodal"
        conf.token = "test_token"
        conf.uid = "test_uid"
        conf.product_name = "test_product"
        conf.intention_code = "test_intention"
        conf.prom_token = False
        conf.record_token = True
        conf.record_token_key = "tpm_token"
        conf.get_tpm_callback = Mock(return_value=1000)

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            # 调用获取 TPM 方法
            result = gateway.get_tpm("tpm_key")

            # 验证回调被调用并返回正确值
            assert result == 1000
            conf.get_tpm_callback.assert_called_once_with("tpm_token_tpm_key")

    @pytest.mark.asyncio
    async def test_async_get_tpm_functionality(self):
        """测试异步获取 TPM 功能"""
        from unittest.mock import Mock, AsyncMock

        conf = Mock()
        conf.host = "http://test-api.com"
        conf.api = "/v1/chat"
        conf.multimodal_api = "/v1/multimodal"
        conf.token = "test_token"
        conf.uid = "test_uid"
        conf.product_name = "test_product"
        conf.intention_code = "test_intention"
        conf.prom_token = False
        conf.record_token = True
        conf.record_token_key = "async_tpm_token"
        conf.async_get_tpm_callback = AsyncMock(return_value=2000)

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            # 调用异步获取 TPM 方法
            result = await gateway.async_get_tpm("async_tpm_key")

            # 验证异步回调被调用并返回正确值
            assert result == 2000
            conf.async_get_tpm_callback.assert_called_once_with("async_tpm_token_async_tpm_key")

    def test_gateway_without_record_token(self):
        """测试不记录令牌的网关"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://test-api.com"
        conf.api = "/v1/chat"
        conf.multimodal_api = "/v1/multimodal"
        conf.token = "test_token"
        conf.uid = "test_uid"
        conf.product_name = "test_product"
        conf.intention_code = "test_intention"
        conf.prom_token = False
        conf.record_token = False

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            # 调用记录令牌方法（应该不执行任何操作）
            gateway._record_token("test_key", 100)

            # 调用获取 TPM 方法（应该返回 0）
            result = gateway.get_tpm("test_key")
            assert result == 0


class TestPublicModelClasses:
    """测试公共模型相关的数据类"""

    def test_public_code_enum(self):
        """测试公共代码枚举"""
        assert PublicCode.OK == "Success"
        assert PublicCode.FAILED == "Failed"

    def test_choice_model(self):
        """测试选择模型"""
        choice = Choice(
            index=0,
            finish_reason="stop",
            logprobs=0.5,
            text="Test response"
        )

        assert choice.index == 0
        assert choice.finish_reason == "stop"
        assert choice.logprobs == 0.5
        assert choice.text == "Test response"

    def test_sec_text_model(self):
        """测试安全文本模型"""
        # 使用别名 "from" 来设置 from_ 字段
        sec_text = SecText(
            scene="test_scene",
            **{"from": "test_from"},  # 使用别名
            answer_flag=1,
            extra_text=["extra content"]
        )

        assert sec_text.scene == "test_scene"
        assert sec_text.from_ == "test_from"
        assert sec_text.answer_flag == 1
        assert sec_text.extra_text == ["extra content"]

    def test_retry_strategy_model(self):
        """测试重试策略模型"""
        retry_strategy = RetryStrategy(
            retry_count=3,  # 正确的字段名
            timeout=30      # 正确的字段名
        )

        assert retry_strategy.retry_count == 3
        assert retry_strategy.timeout == 30

    def test_chat_response_model(self):
        """测试聊天响应模型"""
        usage = Usage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        choice = Choice(index=0, finish_reason="stop", logprobs=0.0, text="Response")

        response = ChatResponse(
            code=PublicCode.OK,
            message="success",
            created=**********,
            usage=usage,
            choices=[choice]
        )

        assert response.code == PublicCode.OK
        assert response.message == "success"
        assert response.created == **********
        assert response.usage.total_tokens == 30
        assert len(response.choices) == 1
        assert response.choices[0].text == "Response"

    def test_error_arrays(self):
        """测试错误数组常量"""
        assert "AuditError.InternalInputNoPass" in ERROR_AUDIT_ARR
        assert "Privilege.CheckFailed" in ERROR_PRIVILEGE_ARR
        assert "GateWayLimitError" in ERROR_LIMIT_ARR


class TestPublicModelGatewayConfiguration:
    """测试公共模型网关配置"""

    def test_conf_creation(self):
        """测试配置创建"""
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            api="/custom/api",
            multimodal_api="/custom/multimodal",
            provider="test_provider",
            model="test_model",
            version="v1.0",
            multimodal_provider="multimodal_provider",
            multimodal_model="multimodal_model",
            multimodal_version="v2.0",
            sec_scene="test_scene",
            sec_from="test_from",
            multimodal_sec_scene="multimodal_scene",
            multimodal_sec_from="multimodal_from",
            prom_token=True,
            record_token=True,
            record_token_key="custom_key"
        )

        assert conf.host == "http://test-public.com"
        assert conf.token == "test_token"
        assert conf.uid == "test_uid"
        assert conf.product_name == "test_product"
        assert conf.intention_code == "test_intention"
        assert conf.api == "/custom/api"
        assert conf.multimodal_api == "/custom/multimodal"
        assert conf.provider == "test_provider"
        assert conf.model == "test_model"
        assert conf.version == "v1.0"
        assert conf.multimodal_provider == "multimodal_provider"
        assert conf.multimodal_model == "multimodal_model"
        assert conf.multimodal_version == "v2.0"
        assert conf.sec_scene == "test_scene"
        assert conf.sec_from == "test_from"
        assert conf.multimodal_sec_scene == "multimodal_scene"
        assert conf.multimodal_sec_from == "multimodal_from"
        assert conf.prom_token is True
        assert conf.record_token is True
        assert conf.record_token_key == "custom_key"

    def test_conf_defaults(self):
        """测试配置默认值"""
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention"
        )

        assert conf.host == "http://test-public.com"
        assert conf.api == "/api/v2/llm/chat"
        assert conf.multimodal_api == "/api/v2/llm/multimodal"
        assert conf.prom_token is False
        assert conf.record_token is False
        assert conf.record_token_key == "public_token"


class TestPublicModelGatewayCore:
    """测试公共模型网关核心功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            prom_token=False,
            record_token=False
        )

    def test_gateway_initialization_with_pool(self):
        """测试带连接池的网关初始化"""
        with patch('commons.llm_gateway.models.public_model_gateway.requests.Session') as mock_session, \
             patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession') as mock_async_session, \
             patch('commons.llm_gateway.models.public_model_gateway.aiohttp.TCPConnector') as mock_connector:

            mock_session_instance = Mock()
            mock_session.return_value = mock_session_instance
            mock_async_session_instance = Mock()
            mock_async_session.return_value = mock_async_session_instance
            mock_connector_instance = Mock()
            mock_connector.return_value = mock_connector_instance

            gateway = PublicModelGateway(self.conf, pool_max=10)

            # 验证初始化
            assert gateway.get_conf() == self.conf
            assert gateway._chat_url == "http://test-public.com/api/v2/llm/chat"
            assert gateway._multimodal_url == "http://test-public.com/api/v2/llm/multimodal"
            assert gateway._pool_max == 10
            assert gateway._sess == mock_session_instance

            # 验证 Session 配置
            mock_session.assert_called_once()
            mock_session_instance.mount.assert_called_once()

            # 验证异步 Session 配置
            mock_async_session.assert_called_once()
            mock_connector.assert_called_once_with(limit=10)

    def test_gateway_initialization_without_pool(self):
        """测试不带连接池的网关初始化"""
        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession') as mock_async_session:
            mock_async_session_instance = Mock()
            mock_async_session.return_value = mock_async_session_instance

            gateway = PublicModelGateway(self.conf, pool_max=-1)

            assert gateway._pool_max == -1
            assert gateway._sess is None

            # 验证异步 Session 仍然被创建
            mock_async_session.assert_called_once()

    def test_gateway_initialization_with_prom_token(self):
        """测试启用 Prometheus 令牌的网关初始化"""
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            prom_token=True
        )

        with patch('commons.llm_gateway.models.public_model_gateway.get_mcount', return_value=Mock()) as mock_get_mcount, \
             patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            assert gateway._prom_endpoint == "public_model_gateway"
            assert gateway._prom_multimodal_endpoint == "public_model_multimodal_gateway"
            mock_get_mcount.assert_called()

    def test_gateway_initialization_prom_token_assertion(self):
        """测试 Prometheus 令牌断言失败"""
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            prom_token=True
        )

        with patch('commons.llm_gateway.models.public_model_gateway.get_mcount', return_value=None), \
             patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            with pytest.raises(AssertionError):
                PublicModelGateway(conf)

    @pytest.mark.asyncio
    async def test_gateway_close(self):
        """测试网关关闭"""
        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession') as mock_async_session:
            mock_async_session_instance = AsyncMock()
            mock_async_session.return_value = mock_async_session_instance

            gateway = PublicModelGateway(self.conf)
            await gateway.close()

            mock_async_session_instance.close.assert_called_once()

    def test_record_token_with_callback(self):
        """测试记录令牌带回调"""
        mock_callback = Mock()
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            record_token=True,
            record_token_key="test_key",
            record_token_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)
            gateway._record_token("suffix", 100)

            mock_callback.assert_called_once_with("test_key_suffix", 100)

    def test_record_token_without_callback(self):
        """测试记录令牌无回调"""
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            record_token=False
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)
            # 不应该抛出异常
            gateway._record_token("suffix", 100)

    @pytest.mark.asyncio
    async def test_async_record_token_with_callback(self):
        """测试异步记录令牌带回调"""
        mock_callback = AsyncMock()
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            record_token=True,
            record_token_key="async_key",
            async_record_token_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)
            await gateway._async_record_token("async_suffix", 200)

            mock_callback.assert_called_once_with("async_key_async_suffix", 200)

    @pytest.mark.asyncio
    async def test_async_record_token_without_callback(self):
        """测试异步记录令牌无回调"""
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            record_token=False
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)
            # 不应该抛出异常
            await gateway._async_record_token("async_suffix", 200)


class TestPublicModelGatewayParams:
    """测试公共模型网关参数处理"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="default_provider",
            model="default_model",
            version="v1.0",
            multimodal_provider="multimodal_provider",
            multimodal_model="multimodal_model",
            multimodal_version="v2.0",
            sec_scene="default_scene",
            sec_from="default_from",
            multimodal_sec_scene="multimodal_scene",
            multimodal_sec_from="multimodal_from"
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            self.gateway = PublicModelGateway(self.conf)

    def test_get_dict_params_chat_with_defaults(self):
        """测试获取聊天字典参数使用默认值"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="",  # 空值，应该被默认值覆盖
            model="",     # 空值，应该被默认值覆盖
            messages=messages,
            chat_type=ChatType.chat
        )

        with patch('commons.llm_gateway.models.public_model_gateway.ParameterProcessor') as mock_processor:
            mock_processor_instance = Mock()
            mock_processor.return_value = mock_processor_instance
            mock_processor_instance.process_params.return_value = Mock()
            mock_processor_instance.process_params.return_value.dict.return_value = {}

            result = self.gateway._get_dict_params(params)

            # 验证默认值被设置
            assert params.provider == "default_provider"
            assert params.model == "default_model"
            assert params.version == "v1.0"
            assert params.sec_text.scene == "default_scene"
            assert params.sec_text.from_ == "default_from"

    def test_get_dict_params_multimodal_with_defaults(self):
        """测试获取多模态字典参数使用默认值"""
        messages = [Message(role="user", content="Describe this image")]
        params = ChatParams(
            stream=False,
            provider="",  # 空值，应该被默认值覆盖
            model="",     # 空值，应该被默认值覆盖
            messages=messages,
            chat_type=ChatType.multimodal
        )

        with patch('commons.llm_gateway.models.public_model_gateway.ParameterProcessor') as mock_processor:
            mock_processor_instance = Mock()
            mock_processor.return_value = mock_processor_instance
            mock_processor_instance.process_params.return_value = Mock()
            mock_processor_instance.process_params.return_value.dict.return_value = {}

            result = self.gateway._get_dict_params(params)

            # 验证多模态默认值被设置
            assert params.provider == "multimodal_provider"
            assert params.model == "multimodal_model"
            assert params.version == "v2.0"
            assert params.sec_text.scene == "multimodal_scene"
            assert params.sec_text.from_ == "multimodal_from"

    def test_get_dict_params_with_existing_values(self):
        """测试获取字典参数已有值不被覆盖"""
        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="custom_scene")
        sec_text.from_ = "custom_from"  # 设置 from_ 属性
        params = ChatParams(
            stream=False,
            provider="custom_provider",
            model="custom_model",
            messages=messages,
            sec_text=sec_text,
            chat_type=ChatType.chat
        )

        with patch('commons.llm_gateway.models.public_model_gateway.ParameterProcessor') as mock_processor:
            mock_processor_instance = Mock()
            mock_processor.return_value = mock_processor_instance
            mock_processor_instance.process_params.return_value = Mock()
            mock_processor_instance.process_params.return_value.dict.return_value = {}

            result = self.gateway._get_dict_params(params)

            # 验证自定义值被保留
            assert params.provider == "custom_provider"
            assert params.model == "custom_model"
            assert params.sec_text.scene == "custom_scene"
            assert params.sec_text.from_ == "custom_from"

    def test_get_httpcall_datas_basic(self):
        """测试获取 HTTP 调用数据基础功能"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.request_id_context') as mock_context, \
             patch.object(self.gateway, '_get_dict_params', return_value={"test": "data"}):
            mock_context.get.return_value = "test_request_id"

            headers, jdata = self.gateway._get_httpcall_datas(params)

            # 验证基础头部
            assert headers["Content-Type"] == "application/json"
            assert headers["Authorization"] == "Bearer test_token"
            assert headers["AI-Gateway-Uid"] == "test_uid"
            assert headers["AI-Gateway-Product-Name"] == "test_product"
            assert headers["AI-Gateway-Intention-Code"] == "test_intention"
            assert headers["Client-Request-Id"] == "test_request_id"
            assert jdata == {"test": "data"}

    def test_get_httpcall_datas_with_optional_headers(self):
        """测试获取 HTTP 调用数据带可选头部"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            company_id="test_company",
            user_id="test_user",
            ai_token="test_ai_token",
            ai_product_name="test_ai_product",
            ai_intention_code="test_ai_intention",
            model_type="test_model_type"
        )

        with patch('commons.llm_gateway.models.public_model_gateway.request_id_context') as mock_context, \
             patch.object(self.gateway, '_get_dict_params', return_value={}):
            mock_context.get.return_value = "test_request_id"

            headers, jdata = self.gateway._get_httpcall_datas(params)

            # 验证可选头部被设置
            assert headers["AI-Gateway-Company-Id"] == "test_company"
            assert headers["AI-Gateway-Uid"] == "test_user"  # 覆盖默认值
            assert headers["AI-Gateway-Billing-Token"] == "test_ai_token"
            assert headers["AI-Gateway-Product-Name"] == "test_ai_product"  # 覆盖默认值
            assert headers["AI-Gateway-Intention-Code"] == "test_ai_intention"  # 覆盖默认值
            assert headers["AI-Gateway-Model-Choice"] == "test_model_type"

            # 验证参数被清空
            assert params.company_id is None
            assert params.user_id is None
            assert params.ai_token is None
            assert params.ai_product_name is None
            assert params.ai_intention_code is None
            assert params.model_type is None

    def test_parse_params_chat(self):
        """测试解析聊天参数"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            chat_type=ChatType.chat
        )

        with patch('commons.llm_gateway.models.public_model_gateway.get_private_context', return_value={"Private-Header": "value"}), \
             patch.object(self.gateway, '_get_httpcall_datas', return_value=({"test": "header"}, {"test": "data"})):

            url, headers, jdata = self.gateway.parse_params(params)

            assert url == "http://test-public.com/api/v2/llm/chat"
            assert headers["test"] == "header"
            assert headers["Private-Header"] == "value"
            assert jdata == {"test": "data"}

    def test_parse_params_multimodal(self):
        """测试解析多模态参数"""
        messages = [Message(role="user", content="Describe this image")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            chat_type=ChatType.multimodal
        )

        with patch('commons.llm_gateway.models.public_model_gateway.get_private_context', return_value=None), \
             patch.object(self.gateway, '_get_httpcall_datas', return_value=({"test": "header"}, {"test": "data"})):

            url, headers, jdata = self.gateway.parse_params(params)

            assert url == "http://test-public.com/api/v2/llm/multimodal"
            assert headers == {"test": "header"}
            assert jdata == {"test": "data"}


class TestPublicModelGatewayChatMethods:
    """测试公共模型网关聊天方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="test_provider",
            model="test_model",
            prom_token=False,
            record_token=False
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            self.gateway = PublicModelGateway(self.conf)

    def test_record_res_with_usage(self):
        """测试记录响应带使用量"""
        usage = Usage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"
        sec_text.answer_flag = 1
        sec_text.extra_text = ["extra1", "extra2"]

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging:
            self.gateway.record_res(usage, params)

            # 验证日志被调用
            mock_logging.info.assert_called_once()
            log_call = mock_logging.info.call_args[0][0]
            assert "test_provider" in log_call
            assert "test_model" in log_call
            assert "total_tokens: 30" in log_call
            assert "prompt_tokens: 10" in log_call
            assert "completion_tokens: 20" in log_call

    def test_record_res_with_prom_token(self):
        """测试记录响应带 Prometheus 令牌"""
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="test_provider",
            model="test_model",
            prom_token=True,
            record_token=False
        )

        mock_mcount = Mock()
        with patch('commons.llm_gateway.models.public_model_gateway.get_mcount', return_value=mock_mcount), \
             patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            usage = Usage(prompt_tokens=5, completion_tokens=15, total_tokens=20)
            messages = [Message(role="user", content="Hello")]
            sec_text = SecText(scene="test_scene")

            params = ChatParams(
                stream=False,
                provider="test_provider",
                model="test_model",
                version="v1.0",
                messages=messages,
                sec_text=sec_text
            )

            gateway.record_res(usage, params)

            # 验证 Prometheus 令牌记录被调用
            mock_mcount.record_token.assert_called_once_with(
                "public_model_gateway", 15, 5, 20, "test_model", "test_provider", "v1.0"
            )

    def test_record_res_with_record_token(self):
        """测试记录响应带令牌记录"""
        mock_callback = Mock()
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="test_provider",
            model="test_model",
            prom_token=False,
            record_token=True,
            record_token_key="test_key",
            record_token_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            usage = Usage(prompt_tokens=8, completion_tokens=12, total_tokens=20)
            messages = [Message(role="user", content="Hello")]
            sec_text = SecText(scene="test_scene")

            params = ChatParams(
                stream=False,
                provider="test_provider",
                model="test_model",
                messages=messages,
                sec_text=sec_text
            )

            gateway.record_res(usage, params)

            # 验证令牌记录回调被调用
            mock_callback.assert_called_once_with("test_key_test_provider_test_model", 20)

    def test_record_res_without_usage(self):
        """测试记录响应无使用量"""
        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging:
            self.gateway.record_res(None, params)

            # 验证日志被调用，但不包含使用量信息
            mock_logging.info.assert_called_once()
            log_call = mock_logging.info.call_args[0][0]
            assert "test_provider" in log_call
            assert "test_model" in log_call
            assert "total_tokens" not in log_call

    def test_record_res_with_zero_tokens(self):
        """测试记录响应零令牌使用量"""
        usage = Usage(prompt_tokens=0, completion_tokens=0, total_tokens=0)
        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging:
            self.gateway.record_res(usage, params)

            # 验证日志被调用，但不包含使用量详细信息（因为 total_tokens <= 0）
            mock_logging.info.assert_called_once()
            log_call = mock_logging.info.call_args[0][0]
            assert "test_provider" in log_call
            assert "test_model" in log_call
            assert "total_tokens" not in log_call

    def test_chat_success_with_session(self):
        """测试使用会话的成功聊天"""
        # 创建带会话的网关
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="test_provider",
            model="test_model"
        )

        mock_session = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"code": "Success", "message": "success", "created": **********, "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}, "choices": [{"index": 0, "finish_reason": "stop", "logprobs": 0.0, "text": "Hello response"}]}'
        mock_session.post.return_value = mock_response

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'), \
             patch('commons.llm_gateway.models.public_model_gateway.aiohttp.TCPConnector'), \
             patch('commons.llm_gateway.models.public_model_gateway.requests.Session', return_value=mock_session):
            gateway = PublicModelGateway(conf, pool_max=5)

            messages = [Message(role="user", content="Hello")]
            params = ChatParams(
                stream=False,
                provider="test_provider",
                model="test_model",
                messages=messages
            )

            with patch.object(gateway, 'record_res') as mock_record_res:
                result = gateway.chat(params)

                # 验证返回结果
                if isinstance(result, tuple):
                    response, trace_info = result
                    assert response.code == PublicCode.OK
                    assert trace_info.usage.total_tokens == 30
                else:
                    assert result.code == PublicCode.OK

                # 验证会话被使用
                mock_session.post.assert_called_once()
                # 验证记录方法被调用
                mock_record_res.assert_called_once()

    def test_chat_success_without_session(self):
        """测试不使用会话的成功聊天"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"code": "Success", "message": "success", "created": **********, "usage": {"prompt_tokens": 5, "completion_tokens": 15, "total_tokens": 20}, "choices": [{"index": 0, "finish_reason": "stop", "logprobs": 0.0, "text": "No session response"}]}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证返回结果
            if isinstance(result, tuple):
                response, trace_info = result
                assert response.code == PublicCode.OK
                assert trace_info.usage.total_tokens == 20
            else:
                assert result.code == PublicCode.OK

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_http_error(self):
        """测试聊天 HTTP 错误"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = '{"code": "Failed", "message": "Internal server error"}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证错误被记录
            mock_logging.error.assert_called()

            # 验证返回失败响应
            assert result.code == PublicCode.FAILED
            assert "ai-gateway fail" in result.message

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_audit_error(self):
        """测试聊天审核错误"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = '{"code": "AuditError.InternalInputNoPass", "message": "Audit failed"}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证返回审核错误响应
            assert result.code == "AuditError.InternalInputNoPass"
            assert result.message == "Audit failed"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_privilege_error(self):
        """测试聊天权限错误"""
        mock_response = Mock()
        mock_response.status_code = 403
        mock_response.text = '{"code": "Privilege.CheckFailed", "message": "Permission denied"}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证返回权限错误响应
            assert result.code == "Privilege.CheckFailed"
            assert result.message == "Permission denied"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_limit_error(self):
        """测试聊天限制错误"""
        mock_response = Mock()
        mock_response.status_code = 429
        mock_response.text = '{"code": "RateLimited.User", "message": "Rate limit exceeded"}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证返回限制错误响应
            assert result.code == "RateLimited.User"
            assert result.message == "Rate limit exceeded"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_success_with_audit_error_in_response(self):
        """测试成功响应但包含审核错误"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"code": "AuditError.LlmOutputNoPass", "message": "Output audit failed", "created": **********, "usage": {"prompt_tokens": 5, "completion_tokens": 0, "total_tokens": 5}, "choices": []}'

        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证错误和警告被记录
            mock_logging.error.assert_called()
            mock_logging.warning.assert_called_with("sec_text: test_from")

            # 验证返回审核错误响应
            assert result.code == "AuditError.LlmOutputNoPass"
            assert result.message == "Output audit failed"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_exception_handling(self):
        """测试聊天异常处理"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', side_effect=Exception("Network error")), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证异常被记录
            mock_logging.error.assert_called_once()

            # 验证返回失败响应
            assert result.code == PublicCode.FAILED
            assert "Network error" in result.message

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_stream_assertion(self):
        """测试聊天流式断言"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,  # 流式模式应该触发断言错误
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with pytest.raises(AssertionError):
            self.gateway.chat(params)

    def test_chat_multimodal_url(self):
        """测试多模态聊天 URL"""
        messages = [Message(role="user", content="Describe this image")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            chat_type=ChatType.multimodal
        )

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"code": "Success", "message": "success", "created": **********, "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}, "choices": [{"index": 0, "finish_reason": "stop", "logprobs": 0.0, "text": "Multimodal response"}]}'

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response) as mock_post, \
             patch.object(self.gateway, 'record_res') as mock_record_res:
            result = self.gateway.chat(params)

            # 验证使用了多模态 URL
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            # 检查第一个位置参数（URL）
            assert call_args[0][0] == "http://test-public.com/api/v2/llm/multimodal"

            # 验证返回结果
            if isinstance(result, tuple):
                response, trace_info = result
                assert response.code == PublicCode.OK
            else:
                assert result.code == PublicCode.OK

            # 验证记录方法被调用
            mock_record_res.assert_called_once()


class TestPublicModelGatewayAsyncMethods:
    """测试公共模型网关异步方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="test_provider",
            model="test_model",
            prom_token=False,
            record_token=False
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            self.gateway = PublicModelGateway(self.conf)

    @pytest.mark.asyncio
    async def test_async_get_tpm_with_callback(self):
        """测试异步获取 TPM 带回调"""
        mock_callback = AsyncMock(return_value=500)
        conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            record_token=True,
            record_token_key="async_tpm_key",
            async_get_tpm_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            gateway = PublicModelGateway(conf)

            result = await gateway.async_get_tpm("test_key")

            assert result == 500
            mock_callback.assert_called_once_with("async_tpm_key_test_key")

    @pytest.mark.asyncio
    async def test_async_get_tpm_without_callback(self):
        """测试异步获取 TPM 无回调"""
        result = await self.gateway.async_get_tpm("test_key")
        assert result == 0

    @pytest.mark.asyncio
    async def test_async_chat_success(self):
        """测试异步聊天成功"""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = '{"code": "Success", "message": "success", "created": **********, "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}, "choices": [{"index": 0, "finish_reason": "stop", "logprobs": 0.0, "text": "Async response"}]}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            result = await self.gateway.async_chat(params)

            # 验证返回结果
            if isinstance(result, tuple):
                response, trace_info = result
                assert response.code == PublicCode.OK
                assert trace_info.usage.total_tokens == 30
            else:
                assert result.code == PublicCode.OK

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_http_error(self):
        """测试异步聊天 HTTP 错误"""
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = '{"code": "Failed", "message": "Internal server error"}'

        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            result = await self.gateway.async_chat(params)

            # 验证错误被记录
            mock_logging.error.assert_called()

            # 验证返回失败响应
            assert result.code == PublicCode.FAILED
            assert "ai-gateway fail" in result.message

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_audit_error(self):
        """测试异步聊天审核错误"""
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = '{"code": "AuditError.InternalInputNoPass", "message": "Audit failed"}'

        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            result = await self.gateway.async_chat(params)

            # 验证警告被记录
            mock_logging.warning.assert_called_with("sec_text: test_from")

            # 验证返回审核错误响应
            assert result.code == "AuditError.InternalInputNoPass"
            assert result.message == "Audit failed"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_success_with_audit_error_in_response(self):
        """测试异步聊天成功响应但包含审核错误"""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = '{"code": "AuditError.LlmOutputNoPass", "message": "Output audit failed", "created": **********, "usage": {"prompt_tokens": 5, "completion_tokens": 0, "total_tokens": 5}, "choices": []}'

        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"

        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            result = await self.gateway.async_chat(params)

            # 验证错误和警告被记录
            mock_logging.error.assert_called()
            mock_logging.warning.assert_called_with("sec_text: test_from")

            # 验证返回审核错误响应
            assert result.code == "AuditError.LlmOutputNoPass"
            assert result.message == "Output audit failed"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_exception_handling(self):
        """测试异步聊天异常处理"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch.object(self.gateway._async_sess, 'post', side_effect=Exception("Network error")), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            result = await self.gateway.async_chat(params)

            # 验证异常被记录
            mock_logging.error.assert_called()

            # 验证返回失败响应
            assert result.code == PublicCode.FAILED
            assert "Network error" in result.message

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_stream_assertion(self):
        """测试异步聊天流式断言"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,  # 流式模式应该触发断言错误
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with pytest.raises(AssertionError):
            await self.gateway.async_chat(params)


class TestPublicModelGatewayStreamMethods:
    """测试公共模型网关流式方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="test_provider",
            model="test_model",
            prom_token=False,
            record_token=False
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            self.gateway = PublicModelGateway(self.conf)

    def test_chat_stream_success(self):
        """测试流式聊天成功"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"code": "Success", "message": "success", "choices": [{"index": 0, "finish_reason": "continue", "logprobs": 0.0, "text": "Stream 1"}], "usage": {"prompt_tokens": 5, "completion_tokens": 1, "total_tokens": 6}}',
            b'data: {"code": "Success", "message": "success", "choices": [{"index": 0, "finish_reason": "stop", "logprobs": 0.0, "text": "Stream 2"}], "usage": {"prompt_tokens": 5, "completion_tokens": 2, "total_tokens": 7}}'
        ]

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            results = list(self.gateway.chat_stream(params))

            # 验证有结果返回
            assert len(results) == 2

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_stream_http_error(self):
        """测试流式聊天 HTTP 错误"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = '{"code": "Failed", "message": "Internal server error"}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            results = list(self.gateway.chat_stream(params))

            # 验证错误被记录
            mock_logging.error.assert_called()

            # 验证有错误响应返回
            assert len(results) >= 1
            assert results[0].code == PublicCode.FAILED

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_stream_audit_error(self):
        """测试流式聊天审核错误"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = '{"code": "AuditError.InternalInputNoPass", "message": "Audit failed"}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            results = list(self.gateway.chat_stream(params))

            # 验证有审核错误响应返回
            assert len(results) >= 1
            assert results[0].code == "AuditError.InternalInputNoPass"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_stream_invalid_line_format(self):
        """测试流式聊天无效行格式"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'{"code": "AuditError.LlmOutputNoPass", "message": "Invalid format"}'  # 不以 "data:" 开头
        ]

        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"

        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            results = list(self.gateway.chat_stream(params))

            # 验证错误和警告被记录
            mock_logging.error.assert_called()
            mock_logging.warning.assert_called_with("sec_text: test_from")

            # 验证有审核错误响应返回
            assert len(results) >= 1
            assert results[0].code == "AuditError.LlmOutputNoPass"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_stream_non_ok_response_in_stream(self):
        """测试流式聊天中的非 OK 响应"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"code": "Failed", "message": "Stream error"}'
        ]

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            results = list(self.gateway.chat_stream(params))

            # 验证错误被记录
            mock_logging.error.assert_called()

            # 验证有结果返回
            assert len(results) >= 1

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    def test_chat_stream_exception_handling(self):
        """测试流式聊天异常处理"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch('commons.llm_gateway.models.public_model_gateway.requests.post', side_effect=Exception("Network error")), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            results = list(self.gateway.chat_stream(params))

            # 验证异常被记录
            mock_logging.error.assert_called()

            # 验证有错误响应返回
            assert len(results) >= 1
            assert results[0].code == PublicCode.FAILED
            assert "Network error" in results[0].message

            # 验证记录方法被调用
            mock_record_res.assert_called_once()


class TestPublicModelGatewayAsyncStreamMethods:
    """测试公共模型网关异步流式方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = PublicModelGateway.Conf(
            host="http://test-public.com",
            token="test_token",
            uid="test_uid",
            product_name="test_product",
            intention_code="test_intention",
            provider="test_provider",
            model="test_model",
            prom_token=False,
            record_token=False
        )

        with patch('commons.llm_gateway.models.public_model_gateway.aiohttp.ClientSession'):
            self.gateway = PublicModelGateway(self.conf)

    @pytest.mark.asyncio
    async def test_async_chat_stream_success(self):
        """测试异步流式聊天成功"""
        mock_response = AsyncMock()
        mock_response.status = 200

        # 模拟异步内容迭代器
        async def mock_content():
            yield b'data: {"code": "Success", "message": "success", "choices": [{"index": 0, "finish_reason": "continue", "logprobs": 0.0, "text": "Async stream 1"}], "usage": {"prompt_tokens": 5, "completion_tokens": 1, "total_tokens": 6}}'
            yield b'data: {"code": "Success", "message": "success", "choices": [{"index": 0, "finish_reason": "stop", "logprobs": 0.0, "text": "Async stream 2"}], "usage": {"prompt_tokens": 5, "completion_tokens": 2, "total_tokens": 7}}'

        mock_response.content = mock_content()

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            results = []
            async for result in self.gateway.async_chat_stream(params):
                results.append(result)

            # 验证有结果返回
            assert len(results) >= 0

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_stream_http_error(self):
        """测试异步流式聊天 HTTP 错误"""
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = '{"code": "Failed", "message": "Internal server error"}'

        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            results = []
            async for result in self.gateway.async_chat_stream(params):
                results.append(result)

            # 验证错误被记录
            mock_logging.error.assert_called()

            # 验证有错误响应返回
            assert len(results) >= 1
            assert results[0].code == PublicCode.FAILED

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_stream_audit_error(self):
        """测试异步流式聊天审核错误"""
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = '{"code": "AuditError.InternalInputNoPass", "message": "Audit failed"}'

        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"

        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            results = []
            async for result in self.gateway.async_chat_stream(params):
                results.append(result)

            # 验证警告被记录
            mock_logging.warning.assert_called_with("sec_text: test_from")

            # 验证有审核错误响应返回
            assert len(results) >= 1
            assert results[0].code == "AuditError.InternalInputNoPass"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_stream_invalid_line_format(self):
        """测试异步流式聊天无效行格式"""
        mock_response = AsyncMock()
        mock_response.status = 200

        # 模拟异步内容迭代器
        async def mock_content():
            yield b'{"code": "AuditError.LlmOutputNoPass", "message": "Invalid format"}'  # 不以 "data:" 开头

        mock_response.content = mock_content()

        messages = [Message(role="user", content="Hello")]
        sec_text = SecText(scene="test_scene")
        sec_text.from_ = "test_from"

        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages,
            sec_text=sec_text
        )

        with patch.object(self.gateway._async_sess, 'post') as mock_post, \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            # 设置异步上下文管理器
            mock_post.return_value.__aenter__.return_value = mock_response
            mock_post.return_value.__aexit__.return_value = None

            results = []
            async for result in self.gateway.async_chat_stream(params):
                results.append(result)

            # 验证错误和警告被记录
            mock_logging.error.assert_called()
            mock_logging.warning.assert_called_with("sec_text: test_from")

            # 验证有审核错误响应返回
            assert len(results) >= 1
            assert results[0].code == "AuditError.LlmOutputNoPass"

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_stream_exception_handling(self):
        """测试异步流式聊天异常处理"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=True,
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with patch.object(self.gateway._async_sess, 'post', side_effect=Exception("Network error")), \
             patch('commons.llm_gateway.models.public_model_gateway.logging') as mock_logging, \
             patch.object(self.gateway, 'record_res') as mock_record_res:

            results = []
            async for result in self.gateway.async_chat_stream(params):
                results.append(result)

            # 验证异常被记录
            mock_logging.error.assert_called()

            # 验证有错误响应返回
            assert len(results) >= 1
            assert results[0].code == PublicCode.FAILED
            assert "Network error" in results[0].message

            # 验证记录方法被调用
            mock_record_res.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_stream_assertion(self):
        """测试异步流式聊天断言"""
        messages = [Message(role="user", content="Hello")]
        params = ChatParams(
            stream=False,  # 非流式模式应该触发断言错误
            provider="test_provider",
            model="test_model",
            messages=messages
        )

        with pytest.raises(AssertionError):
            async for result in self.gateway.async_chat_stream(params):
                pass
