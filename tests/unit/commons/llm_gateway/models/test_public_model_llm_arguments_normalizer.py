"""
commons.llm_gateway.models.public_model_llm_arguments_normalizer 模块的测试
"""
import pytest
from unittest.mock import Mock, patch
from typing import List, Dict, Any

from commons.llm_gateway.models.public_model_llm_arguments_normalizer import (
    NormalizeParams,
    PublicModelParamsNormalizer,
    MinimaxParamsNormalizer,
    ParameterProcessor,
    parameter_process_categories
)
from commons.llm_gateway.models.chat_data import Message


class TestNormalizeParams:
    """测试标准化参数类"""

    def test_normalize_params_creation(self):
        """测试标准化参数创建"""
        messages = [Message(role="user", content="Hello")]
        params = NormalizeParams(
            messages=messages,
            context="Test context",
            extended_llm_arguments={"key": "value"}
        )
        
        assert params.messages == messages
        assert params.context == "Test context"
        assert params.extended_llm_arguments == {"key": "value"}

    def test_normalize_params_defaults(self):
        """测试标准化参数默认值"""
        params = NormalizeParams()
        
        assert params.messages is None
        assert params.context is None
        assert params.extended_llm_arguments is None

    def test_normalize_params_dict_exclude_none(self):
        """测试字典转换排除 None 值"""
        params = NormalizeParams(
            messages=[Message(role="user", content="Hello")],
            context=None,
            extended_llm_arguments={"key": "value"}
        )
        
        result = params.dict()
        
        # 验证 None 值被排除
        assert "context" not in result
        assert "messages" in result
        assert "extended_llm_arguments" in result


class TestPublicModelParamsNormalizer:
    """测试公共模型参数标准化器抽象类"""

    def test_abstract_class_cannot_be_instantiated(self):
        """测试抽象类不能被实例化"""
        with pytest.raises(TypeError):
            PublicModelParamsNormalizer()


class TestMinimaxParamsNormalizer:
    """测试 Minimax 参数标准化器"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.normalizer = MinimaxParamsNormalizer()

    def test_normalize_llm_arguments_basic(self):
        """测试基本的 LLM 参数标准化"""
        messages = [
            Message(role="user", content="Hello", name="user"),
        ]
        
        result = self.normalizer.normalize_llm_arguments("test_key", messages)
        
        assert isinstance(result, NormalizeParams)
        assert result.messages == messages
        assert result.context == "WPSAI人工智能"
        assert "test_key" in result.extended_llm_arguments
        
        # 验证扩展参数结构
        extended_args = result.extended_llm_arguments["test_key"]
        assert extended_args["mask_sensitive_info"] is False
        assert extended_args["bot_setting"][0]["bot_name"] == "WPSAI"
        assert extended_args["bot_setting"][0]["content"] == "WPSAI人工智能"
        assert extended_args["reply_constraints"]["sender_type"] == "BOT"
        assert extended_args["reply_constraints"]["sender_name"] == "WPSAI"

    def test_normalize_llm_arguments_with_system_message(self):
        """测试带系统消息的 LLM 参数标准化"""
        messages = [
            Message(role="system", content="Custom system prompt"),
            Message(role="user", content="Hello", name="user"),
        ]
        
        result = self.normalizer.normalize_llm_arguments("test_key", messages)
        
        # 验证系统消息被提取为上下文
        assert result.context == "Custom system prompt"
        # 验证消息列表不包含系统消息
        assert len(result.messages) == 1
        assert result.messages[0].role == "user"
        assert result.messages[0].content == "Hello"

    def test_normalize_llm_arguments_fix_missing_role(self):
        """测试修复缺失角色的消息"""
        messages = [
            Message(role="", content="Hello without role"),
        ]
        
        result = self.normalizer.normalize_llm_arguments("test_key", messages)
        
        # 验证角色被设置为默认值
        assert result.messages[0].role == "user"

    def test_normalize_llm_arguments_fix_missing_name(self):
        """测试修复缺失名称的消息"""
        messages = [
            Message(role="user", content="Hello", name=""),
        ]
        
        result = self.normalizer.normalize_llm_arguments("test_key", messages)
        
        # 验证名称被设置为角色值
        assert result.messages[0].name == "user"

    def test_normalize_llm_arguments_empty_messages(self):
        """测试空消息列表"""
        messages = []
        
        result = self.normalizer.normalize_llm_arguments("test_key", messages)
        
        assert result.messages == []
        assert result.context == "WPSAI人工智能"  # 使用默认上下文

    def test_normalize_llm_arguments_multiple_messages(self):
        """测试多条消息的标准化"""
        messages = [
            Message(role="user", content="First message", name=""),
            Message(role="", content="Second message", name="assistant"),
            Message(role="user", content="Third message", name="user"),
        ]
        
        result = self.normalizer.normalize_llm_arguments("test_key", messages)
        
        # 验证所有消息都被正确处理
        assert len(result.messages) == 3
        assert result.messages[0].name == "user"  # 名称被设置为角色
        assert result.messages[1].role == "user"  # 角色被设置为默认值
        assert result.messages[1].name == "assistant"  # 名称保持原值（不为空）
        assert result.messages[2].role == "user"
        assert result.messages[2].name == "user"

    def test_class_attributes(self):
        """测试类属性"""
        assert MinimaxParamsNormalizer._context == "WPSAI人工智能"
        assert MinimaxParamsNormalizer._bot_name == "WPSAI"


class TestParameterProcessor:
    """测试参数处理器"""

    def test_parameter_processor_initialization(self):
        """测试参数处理器初始化"""
        processor = ParameterProcessor("minimax", "test_model", {"key": "value"})
        
        assert processor.provider == "minimax"
        assert processor._model == "test_model"
        assert processor._extend_llm_arguments == {"key": "value"}

    def test_parameter_processor_initialization_with_none_arguments(self):
        """测试参数处理器初始化（扩展参数为 None）"""
        processor = ParameterProcessor("minimax", "test_model", None)
        
        assert processor.provider == "minimax"
        assert processor._model == "test_model"
        assert processor._extend_llm_arguments == {}

    def test_process_params_with_minimax_provider(self):
        """测试处理 minimax 提供商的参数"""
        processor = ParameterProcessor("minimax", "test_model", {})
        messages = [Message(role="user", content="Hello")]
        
        result = processor.process_params(messages)
        
        assert isinstance(result, NormalizeParams)
        assert result.messages == messages
        assert result.context == "WPSAI人工智能"
        assert "minimax_test_model" in result.extended_llm_arguments

    def test_process_params_with_minimax_zone_provider(self):
        """测试处理 minimax-zone 提供商的参数"""
        processor = ParameterProcessor("minimax-zone", "test_model", {})
        messages = [Message(role="user", content="Hello")]
        
        result = processor.process_params(messages)
        
        assert isinstance(result, NormalizeParams)
        assert result.messages == messages
        assert result.context == "WPSAI人工智能"
        assert "minimax-zone_test_model" in result.extended_llm_arguments

    def test_process_params_with_existing_key_in_arguments(self):
        """测试处理已存在键的扩展参数"""
        existing_args = {"minimax_test_model": {"existing": "value"}}
        processor = ParameterProcessor("minimax", "test_model", existing_args)
        messages = [Message(role="user", content="Hello")]
        
        result = processor.process_params(messages)
        
        # 验证返回空的标准化参数（因为键已存在）
        assert isinstance(result, NormalizeParams)
        assert result.messages is None
        assert result.context is None
        assert result.extended_llm_arguments is None

    def test_process_params_with_unsupported_provider(self):
        """测试处理不支持的提供商"""
        processor = ParameterProcessor("unsupported", "test_model", {})
        messages = [Message(role="user", content="Hello")]
        
        result = processor.process_params(messages)
        
        # 验证返回空的标准化参数
        assert isinstance(result, NormalizeParams)
        assert result.messages is None
        assert result.context is None
        assert result.extended_llm_arguments is None

    def test_parameter_process_categories_mapping(self):
        """测试参数处理类别映射"""
        assert "minimax" in parameter_process_categories
        assert "minimax-zone" in parameter_process_categories
        assert parameter_process_categories["minimax"] == MinimaxParamsNormalizer
        assert parameter_process_categories["minimax-zone"] == MinimaxParamsNormalizer


class TestIntegration:
    """集成测试"""

    def test_full_workflow_minimax(self):
        """测试 minimax 的完整工作流程"""
        # 创建处理器
        processor = ParameterProcessor("minimax", "abab6.5s-chat", {})
        
        # 创建测试消息
        messages = [
            Message(role="system", content="You are a helpful assistant"),
            Message(role="user", content="Hello, how are you?", name=""),
            Message(role="", content="I'm fine, thank you!", name="assistant"),
        ]
        
        # 处理参数
        result = processor.process_params(messages)
        
        # 验证结果
        assert isinstance(result, NormalizeParams)
        assert len(result.messages) == 2  # 系统消息被移除
        assert result.context == "You are a helpful assistant"  # 系统消息成为上下文
        
        # 验证消息修正
        assert result.messages[0].role == "user"
        assert result.messages[0].name == "user"  # 空名称被修正
        assert result.messages[1].role == "user"  # 空角色被修正
        assert result.messages[1].name == "assistant"  # 名称保持原值（不为空）
        
        # 验证扩展参数
        key = "minimax_abab6.5s-chat"
        assert key in result.extended_llm_arguments
        extended_args = result.extended_llm_arguments[key]
        assert extended_args["mask_sensitive_info"] is False
        assert extended_args["bot_setting"][0]["bot_name"] == "WPSAI"
        assert extended_args["bot_setting"][0]["content"] == "You are a helpful assistant"
        assert extended_args["reply_constraints"]["sender_type"] == "BOT"
        assert extended_args["reply_constraints"]["sender_name"] == "WPSAI"
