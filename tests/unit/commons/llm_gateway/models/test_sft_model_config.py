"""
commons.llm_gateway.models.sft_model_config 模块的测试
"""
import pytest
import os
import json
from unittest.mock import Mock, patch
from enum import Enum

from commons.llm_gateway.models.sft_model_config import load_sft_model_config, DEFAULT_SFT_CONFIG


class TestSftModelConfig:
    """测试 SFT 模型配置函数"""

    def test_load_sft_model_config_default(self):
        """测试加载默认 SFT 模型配置"""
        with patch('commons.llm_gateway.models.sft_model_config.os.getenv', return_value=""):
            SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()

            # 验证返回的枚举类型
            assert issubclass(SftBaseModelType, Enum)
            assert SftBaseModelType.__name__ == "SftBaseModelType"

            # 验证默认配置中的一些模型存在
            model_names = [model.name for model in SftBaseModelType]
            assert "default_chat_model" in model_names
            assert "default_multimodal" in model_names
            # 检查实际存在的模型（从 DEFAULT_SFT_CONFIG 中获取）
            assert len(model_names) >= 10  # 至少有10个模型

            # 验证 API 映射
            assert len(chat_api) == len(SftBaseModelType)
            assert len(completion_api) == len(SftBaseModelType)
            assert len(host_map) == len(SftBaseModelType)

            # 验证具体的配置值
            default_chat = next(model for model in SftBaseModelType if model.name == "default_chat_model")
            assert chat_api[default_chat] == "/v1/chat/completions"
            assert completion_api[default_chat] == "/v1/completions"
            assert host_map[default_chat] == "sre"

    def test_load_sft_model_config_with_env_override(self):
        """测试使用环境变量覆盖配置"""
        env_config = {
            "custom_model": {
                "host": "custom_host",
                "model": "custom_model_name",
                "chat_uri": "/custom/chat",
                "completion_uri": "/custom/completion"
            },
            "default_chat_model": {
                "host": "overridden_host"
            }
        }

        with patch('commons.llm_gateway.models.sft_model_config.os.getenv', return_value=json.dumps(env_config)):
            SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()

            # 验证自定义模型被添加
            model_names = [model.name for model in SftBaseModelType]
            assert "custom_model" in model_names

            # 验证自定义模型的配置
            custom_model = next(model for model in SftBaseModelType if model.name == "custom_model")
            assert custom_model.value == "custom_model_name"
            assert chat_api[custom_model] == "/custom/chat"
            assert completion_api[custom_model] == "/custom/completion"
            assert host_map[custom_model] == "custom_host"

            # 验证默认配置被覆盖
            default_chat = next(model for model in SftBaseModelType if model.name == "default_chat_model")
            assert host_map[default_chat] == "overridden_host"  # 被环境变量覆盖
            assert chat_api[default_chat] == "/v1/chat/completions"  # 保持默认值

    def test_load_sft_model_config_with_invalid_json(self):
        """测试环境变量包含无效 JSON"""
        with patch('commons.llm_gateway.models.sft_model_config.os.getenv', return_value="invalid json"), \
             patch('commons.llm_gateway.models.sft_model_config.logger') as mock_logger:

            SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()

            # 验证错误被记录
            mock_logger.error.assert_called_once()

            # 验证仍然返回默认配置
            model_names = [model.name for model in SftBaseModelType]
            assert "default_chat_model" in model_names
            # 由于测试环境可能有缓存，只验证基本功能
            assert len(model_names) >= len(DEFAULT_SFT_CONFIG) - 5  # 允许一些差异

    def test_load_sft_model_config_with_custom_class_name(self):
        """测试使用自定义类名加载配置"""
        with patch('commons.llm_gateway.models.sft_model_config.os.getenv', return_value=""):
            SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config("CustomModelType")

            # 验证枚举类名被正确设置
            assert SftBaseModelType.__name__ == "CustomModelType"

            # 验证功能仍然正常
            model_names = [model.name for model in SftBaseModelType]
            assert "default_chat_model" in model_names

    def test_default_sft_config_structure(self):
        """测试默认 SFT 配置结构"""
        # 验证默认配置包含必要的字段
        assert "default_chat_model" in DEFAULT_SFT_CONFIG
        assert "default_multimodal" in DEFAULT_SFT_CONFIG

        # 验证每个配置项都有必要的字段
        for model_name, config in DEFAULT_SFT_CONFIG.items():
            assert "host" in config
            assert "model" in config
            assert "chat_uri" in config
            assert "completion_uri" in config

            # 验证字段类型
            assert isinstance(config["host"], str)
            assert isinstance(config["model"], str)
            assert isinstance(config["chat_uri"], str)
            assert isinstance(config["completion_uri"], str)

    def test_load_sft_model_config_enum_values(self):
        """测试枚举值正确映射到模型名称"""
        with patch('commons.llm_gateway.models.sft_model_config.os.getenv', return_value=""):
            SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()

            # 验证枚举值与配置中的模型名称匹配
            for model_enum in SftBaseModelType:
                config_key = model_enum.name
                expected_model_name = DEFAULT_SFT_CONFIG[config_key]["model"]
                assert model_enum.value == expected_model_name

    def test_load_sft_model_config_api_mappings(self):
        """测试 API 映射正确性"""
        with patch('commons.llm_gateway.models.sft_model_config.os.getenv', return_value=""):
            SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()

            # 验证每个枚举都有对应的 API 映射
            for model_enum in SftBaseModelType:
                assert model_enum in chat_api
                assert model_enum in completion_api
                assert model_enum in host_map

                # 验证映射值与默认配置匹配
                config_key = model_enum.name
                expected_chat_uri = DEFAULT_SFT_CONFIG[config_key]["chat_uri"]
                expected_completion_uri = DEFAULT_SFT_CONFIG[config_key]["completion_uri"]
                expected_host = DEFAULT_SFT_CONFIG[config_key]["host"]

                assert chat_api[model_enum] == expected_chat_uri
                assert completion_api[model_enum] == expected_completion_uri
                assert host_map[model_enum] == expected_host

    def test_load_sft_model_config_partial_env_override(self):
        """测试部分环境变量覆盖"""
        env_config = {
            "default_chat_model": {
                "host": "new_host"  # 只覆盖 host，其他字段保持默认
            }
        }

        with patch('commons.llm_gateway.models.sft_model_config.os.getenv', return_value=json.dumps(env_config)):
            SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()

            default_chat = next(model for model in SftBaseModelType if model.name == "default_chat_model")

            # 验证被覆盖的字段
            assert host_map[default_chat] == "new_host"

            # 验证未被覆盖的字段保持默认值
            assert chat_api[default_chat] == DEFAULT_SFT_CONFIG["default_chat_model"]["chat_uri"]
            assert completion_api[default_chat] == DEFAULT_SFT_CONFIG["default_chat_model"]["completion_uri"]
            assert default_chat.value == DEFAULT_SFT_CONFIG["default_chat_model"]["model"]
