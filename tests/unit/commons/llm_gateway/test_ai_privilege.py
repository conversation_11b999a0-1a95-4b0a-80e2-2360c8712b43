"""
commons.llm_gateway.ai_privilege 模块的测试
"""
import pytest
import json
import time
from unittest.mock import Mock, patch, MagicMock
import requests
from fastapi import status

from commons.llm_gateway.ai_privilege import (
    ResultCode, RespModel, AuthCheckData, UseAIPrivilege, AIPrivilegeRpc
)


class TestResultCode:
    """测试结果代码枚举"""

    def test_result_code_values(self):
        """测试结果代码枚举值"""
        assert ResultCode.OK == "ok"
        assert ResultCode.FAIL == "error"


class TestRespModel:
    """测试响应模型"""

    def test_init_with_defaults(self):
        """测试使用默认值初始化"""
        resp = RespModel()
        assert resp.result == ResultCode.OK
        assert resp.msg == ""
        assert resp.data is None

    def test_init_with_values(self):
        """测试使用指定值初始化"""
        test_data = {"key": "value"}
        resp = RespModel(
            result=ResultCode.FAIL,
            msg="测试错误",
            data=test_data
        )
        assert resp.result == ResultCode.FAIL
        assert resp.msg == "测试错误"
        assert resp.data == test_data

    def test_parse_from_json(self):
        """测试从JSON解析"""
        json_str = '{"result": "ok", "msg": "成功", "data": {"test": "value"}}'
        resp = RespModel.parse_raw(json_str)
        assert resp.result == "ok"
        assert resp.msg == "成功"
        assert resp.data == {"test": "value"}


class TestAuthCheckData:
    """测试认证检查数据模型"""

    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        auth_data = AuthCheckData(
            ai_available=True,
            reason_code=200,
            can_replace=False,
            token="test_token_123",
            ai_point=100,
            ai_point_total=1000,
            limit_code=0,
            limit_msg="正常",
            limit_expire=0
        )
        assert auth_data.ai_available is True
        assert auth_data.reason_code == 200
        assert auth_data.can_replace is False
        assert auth_data.token == "test_token_123"
        assert auth_data.ai_point == 100
        assert auth_data.ai_point_total == 1000
        assert auth_data.limit_code == 0
        assert auth_data.limit_msg == "正常"
        assert auth_data.limit_expire == 0

    def test_parse_from_dict(self):
        """测试从字典解析"""
        data_dict = {
            "ai_available": False,
            "reason_code": 403,
            "can_replace": True,
            "token": "expired_token",
            "ai_point": 0,
            "ai_point_total": 500,
            "limit_code": 1001,
            "limit_msg": "账户被限制",
            "limit_expire": 1640995200
        }
        auth_data = AuthCheckData.parse_obj(data_dict)
        assert auth_data.ai_available is False
        assert auth_data.reason_code == 403
        assert auth_data.can_replace is True
        assert auth_data.token == "expired_token"
        assert auth_data.ai_point == 0
        assert auth_data.ai_point_total == 500
        assert auth_data.limit_code == 1001
        assert auth_data.limit_msg == "账户被限制"
        assert auth_data.limit_expire == 1640995200


class TestUseAIPrivilege:
    """测试使用AI权限数据模型"""

    def test_init_with_required_fields(self):
        """测试使用必需字段初始化"""
        privilege = UseAIPrivilege(
            flow_id="flow_123456",
            ai_available=True,
            reason_code=200,
            limit_code=0,
            remain_value=99
        )
        assert privilege.flow_id == "flow_123456"
        assert privilege.ai_available is True
        assert privilege.reason_code == 200
        assert privilege.limit_code == 0
        assert privilege.remain_value == 99
        assert privilege.company_id == 0  # 默认值

    def test_init_with_company_id(self):
        """测试使用企业ID初始化"""
        privilege = UseAIPrivilege(
            flow_id="flow_company",
            ai_available=True,
            reason_code=200,
            limit_code=0,
            remain_value=500,
            company_id=12345
        )
        assert privilege.company_id == 12345

    def test_parse_from_dict(self):
        """测试从字典解析"""
        data_dict = {
            "flow_id": "flow_789",
            "ai_available": False,
            "reason_code": 403,
            "limit_code": 1002,
            "remain_value": -1,
            "company_id": 67890
        }
        privilege = UseAIPrivilege.parse_obj(data_dict)
        assert privilege.flow_id == "flow_789"
        assert privilege.ai_available is False
        assert privilege.reason_code == 403
        assert privilege.limit_code == 1002
        assert privilege.remain_value == -1
        assert privilege.company_id == 67890


class TestAIPrivilegeRpc:
    """测试AI权限RPC类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        AIPrivilegeRpc._instances = {}
        self.rpc = AIPrivilegeRpc()

    def test_singleton_pattern(self):
        """测试单例模式"""
        rpc1 = AIPrivilegeRpc()
        rpc2 = AIPrivilegeRpc()
        assert rpc1 is rpc2

    def test_init(self):
        """测试初始化"""
        self.rpc.init(
            ak="test_ak",
            sk="test_sk",
            host="http://test-host",
            product_name="test_product",
            intention_code="test_intention"
        )
        assert self.rpc._ak == "test_ak"
        assert self.rpc._sk == "test_sk"
        assert self.rpc._host == "http://test-host"
        assert self.rpc._product_name == "test_product"
        assert self.rpc._intention_code == "test_intention"

    def test_sig_without_body(self):
        """测试无请求体的签名生成"""
        self.rpc.init("ak", "sk", "host", "product", "intention")
        
        with patch('commons.llm_gateway.ai_privilege.sig_wps2') as mock_sig:
            with patch('time.strftime', return_value="Mon, 01 Jan 2024 00:00:00 GMT"):
                mock_sig.return_value = {"Authorization": "test_auth"}
                
                result = self.rpc._sig("/test/uri")
                
                mock_sig.assert_called_once_with(
                    "/test/uri", None, "ak", "sk", "Mon, 01 Jan 2024 00:00:00 GMT"
                )
                assert result == {"Authorization": "test_auth"}

    def test_sig_with_body(self):
        """测试有请求体的签名生成"""
        self.rpc.init("ak", "sk", "host", "product", "intention")
        test_body = {"key": "value"}
        
        with patch('commons.llm_gateway.ai_privilege.sig_wps2') as mock_sig:
            with patch('time.strftime', return_value="Mon, 01 Jan 2024 00:00:00 GMT"):
                mock_sig.return_value = {"Authorization": "test_auth"}
                
                result = self.rpc._sig("/test/uri", test_body)
                
                expected_body = json.dumps(test_body).encode("utf-8")
                mock_sig.assert_called_once_with(
                    "/test/uri", expected_body, "ak", "sk", "Mon, 01 Jan 2024 00:00:00 GMT"
                )
                assert result == {"Authorization": "test_auth"}

    def test_call_success(self):
        """测试成功的RPC调用"""
        self.rpc.init("ak", "sk", "http://test-host", "product", "intention")

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = status.HTTP_200_OK
        mock_response.text = '{"result": "ok", "msg": "成功", "data": {"test": "value"}}'

        with patch('commons.llm_gateway.ai_privilege.requests.request', return_value=mock_response):
            with patch.object(self.rpc, '_sig', return_value={"Authorization": "test_auth"}):
                with patch('commons.llm_gateway.ai_privilege.request_id_context') as mock_context:
                    mock_context.get.return_value = "req_123"

                    result = self.rpc._call("POST", "/test/uri", {"key": "value"})

                    assert isinstance(result, RespModel)
                    assert result.result == "ok"
                    assert result.msg == "成功"
                    assert result.data == {"test": "value"}

    def test_call_http_error(self):
        """测试HTTP错误响应"""
        self.rpc.init("ak", "sk", "http://test-host", "product", "intention")

        # 模拟HTTP错误响应
        mock_response = Mock()
        mock_response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        mock_response.text = "Internal Server Error"

        with patch('commons.llm_gateway.ai_privilege.requests.request', return_value=mock_response):
            with patch.object(self.rpc, '_sig', return_value={"Authorization": "test_auth"}):
                with patch('commons.llm_gateway.ai_privilege.request_id_context') as mock_context:
                    mock_context.get.return_value = "req_123"

                    with pytest.raises(Exception) as exc_info:
                        self.rpc._call("POST", "/test/uri", {"key": "value"})

                    assert "ai_privilege rpc request fail" in str(exc_info.value)
                    assert "500" in str(exc_info.value)

    def test_call_business_error(self):
        """测试业务错误响应"""
        self.rpc.init("ak", "sk", "http://test-host", "product", "intention")

        # 模拟业务错误响应
        mock_response = Mock()
        mock_response.status_code = status.HTTP_200_OK
        mock_response.text = '{"result": "error", "msg": "业务错误", "data": null}'

        with patch('commons.llm_gateway.ai_privilege.requests.request', return_value=mock_response):
            with patch.object(self.rpc, '_sig', return_value={"Authorization": "test_auth"}):
                with patch('commons.llm_gateway.ai_privilege.request_id_context') as mock_context:
                    mock_context.get.return_value = "req_123"

                    with pytest.raises(Exception) as exc_info:
                        self.rpc._call("POST", "/test/uri", {"key": "value"})

                    assert "ai_privilege rpc request fail" in str(exc_info.value)

    def test_call_with_custom_headers_and_cookies(self):
        """测试带自定义头部和Cookie的调用"""
        self.rpc.init("ak", "sk", "http://test-host", "product", "intention")

        mock_response = Mock()
        mock_response.status_code = status.HTTP_200_OK
        mock_response.text = '{"result": "ok", "msg": "成功", "data": {}}'

        custom_headers = {"Custom-Header": "custom_value"}
        custom_cookies = {"session": "session_value"}

        with patch('commons.llm_gateway.ai_privilege.requests.request', return_value=mock_response) as mock_request:
            with patch.object(self.rpc, '_sig', return_value={"Authorization": "test_auth"}):
                with patch('commons.llm_gateway.ai_privilege.request_id_context') as mock_context:
                    mock_context.get.return_value = "req_123"

                    self.rpc._call("POST", "/test/uri", {"key": "value"},
                                 cookies=custom_cookies, headers=custom_headers, timeout_second=60)

                    # 验证请求参数
                    mock_request.assert_called_once()
                    call_args = mock_request.call_args

                    # 验证头部合并
                    expected_headers = {
                        "Authorization": "test_auth",
                        "Custom-Header": "custom_value",
                        "X-Request-ID": "req_123"
                    }
                    assert call_args[1]["headers"] == expected_headers
                    assert call_args[1]["cookies"] == custom_cookies
                    assert call_args[1]["timeout"] == 60

    def test_auth_check_success(self):
        """测试成功的认证检查"""
        self.rpc.init("ak", "sk", "http://test-host", "test_product", "test_intention")

        # 模拟成功的认证检查响应
        auth_data = {
            "ai_available": True,
            "reason_code": 200,
            "can_replace": False,
            "token": "auth_token_123",
            "ai_point": 100,
            "ai_point_total": 1000,
            "limit_code": 0,
            "limit_msg": "正常",
            "limit_expire": 0
        }

        mock_resp = RespModel(result=ResultCode.OK, data=auth_data)

        with patch.object(self.rpc, '_call', return_value=mock_resp) as mock_call:
            result = self.rpc.auth_check(12345)

            # 验证调用参数
            expected_body = {
                "user_id": 12345,
                "product_name": "test_product",
                "intention_code": "test_intention"
            }
            mock_call.assert_called_once_with("POST", "/privilege_auth/v1/auth_check", data=expected_body)

            # 验证返回结果
            assert isinstance(result, AuthCheckData)
            assert result.ai_available is True
            assert result.reason_code == 200
            assert result.token == "auth_token_123"
            assert result.ai_point == 100

    def test_use_ai_privilege_without_count(self):
        """测试不指定数量的AI权限使用"""
        self.rpc.init("ak", "sk", "http://test-host", "test_product", "test_intention")

        # 模拟成功的权限使用响应
        privilege_data = {
            "flow_id": "flow_123456",
            "ai_available": True,
            "reason_code": 200,
            "limit_code": 0,
            "remain_value": 99,
            "company_id": 0
        }

        mock_resp = RespModel(result=ResultCode.OK, data=privilege_data)

        with patch.object(self.rpc, '_call', return_value=mock_resp) as mock_call:
            result = self.rpc.use_ai_privilege(12345)

            # 验证调用参数
            expected_body = {
                "user_id": 12345,
                "product_name": "test_product",
                "intention_code": "test_intention"
            }
            mock_call.assert_called_once_with("POST", "/privilege_auth/v1/use_ai_privilege", data=expected_body)

            # 验证返回结果
            assert isinstance(result, UseAIPrivilege)
            assert result.flow_id == "flow_123456"
            assert result.ai_available is True
            assert result.remain_value == 99

    def test_use_ai_privilege_with_count(self):
        """测试指定数量的AI权限使用"""
        self.rpc.init("ak", "sk", "http://test-host", "test_product", "test_intention")

        privilege_data = {
            "flow_id": "flow_789",
            "ai_available": True,
            "reason_code": 200,
            "limit_code": 0,
            "remain_value": 95,
            "company_id": 12345
        }

        mock_resp = RespModel(result=ResultCode.OK, data=privilege_data)

        with patch.object(self.rpc, '_call', return_value=mock_resp) as mock_call:
            result = self.rpc.use_ai_privilege(12345, count=5)

            # 验证调用参数包含count
            expected_body = {
                "user_id": 12345,
                "product_name": "test_product",
                "intention_code": "test_intention",
                "count": 5
            }
            mock_call.assert_called_once_with("POST", "/privilege_auth/v1/use_ai_privilege", data=expected_body)

            # 验证返回结果
            assert isinstance(result, UseAIPrivilege)
            assert result.flow_id == "flow_789"
            assert result.remain_value == 95
            assert result.company_id == 12345

    def test_integration_workflow(self):
        """测试完整的工作流程"""
        self.rpc.init("test_ak", "test_sk", "http://api.test.com", "insight_ai", "doc_parse")

        # 模拟认证检查
        auth_response = Mock()
        auth_response.status_code = 200
        auth_response.text = json.dumps({
            "result": "ok",
            "data": {
                "ai_available": True,
                "reason_code": 200,
                "can_replace": False,
                "token": "valid_token",
                "ai_point": 50,
                "ai_point_total": 100,
                "limit_code": 0,
                "limit_msg": "",
                "limit_expire": 0
            }
        })

        # 模拟权限使用
        privilege_response = Mock()
        privilege_response.status_code = 200
        privilege_response.text = json.dumps({
            "result": "ok",
            "data": {
                "flow_id": "flow_integration_test",
                "ai_available": True,
                "reason_code": 200,
                "limit_code": 0,
                "remain_value": 49,
                "company_id": 0
            }
        })

        with patch('commons.llm_gateway.ai_privilege.requests.request') as mock_request:
            with patch('commons.llm_gateway.ai_privilege.request_id_context') as mock_context:
                mock_context.get.return_value = "integration_req"
                # 设置不同的响应
                mock_request.side_effect = [auth_response, privilege_response]

                # 执行认证检查
                auth_result = self.rpc.auth_check(999)
                assert auth_result.ai_available is True
                assert auth_result.ai_point == 50

                # 执行权限使用
                privilege_result = self.rpc.use_ai_privilege(999, count=1)
                assert privilege_result.ai_available is True
                assert privilege_result.remain_value == 49

                # 验证两次调用
                assert mock_request.call_count == 2

    def test_privilege_check_scenarios_simulation(self):
        """测试权限检查场景模拟"""
        # 模拟不同的权限检查场景
        scenarios = [
            {"user": "admin", "model": "gpt-4", "tokens": 100, "expected": True},
            {"user": "user", "model": "gpt-3.5", "tokens": 50, "expected": True},
            {"user": "guest", "model": "gpt-4", "tokens": 1000, "expected": False},
        ]

        for scenario in scenarios:
            # 模拟权限检查逻辑
            def mock_check(user, model, tokens):
                if user == "admin":
                    return True
                elif user == "user" and tokens <= 100:
                    return True
                else:
                    return False

            result = mock_check(scenario["user"], scenario["model"], scenario["tokens"])
            assert result == scenario["expected"]

    def test_quota_management_simulation(self):
        """测试配额管理模拟"""
        # 模拟配额管理系统
        class QuotaManager:
            def __init__(self):
                self.quotas = {
                    "user1": {"total": 1000, "used": 200},
                    "user2": {"total": 500, "used": 450},
                    "user3": {"total": 2000, "used": 0}
                }

            def check_quota(self, user_id, requested_tokens):
                if user_id not in self.quotas:
                    return False, 0

                quota_info = self.quotas[user_id]
                remaining = quota_info["total"] - quota_info["used"]

                if remaining >= requested_tokens:
                    return True, remaining
                else:
                    return False, remaining

        quota_manager = QuotaManager()

        # 测试不同用户的配额检查
        can_use, remaining = quota_manager.check_quota("user1", 100)
        assert can_use is True
        assert remaining == 800

        can_use, remaining = quota_manager.check_quota("user2", 100)
        assert can_use is False
        assert remaining == 50

        can_use, remaining = quota_manager.check_quota("user3", 500)
        assert can_use is True
        assert remaining == 2000

    def test_auth_check_data_basic(self):
        """测试认证检查数据的基本功能"""
        # 测试基本的数据结构（使用实际的字段）
        auth_data = AuthCheckData(
            ai_available=True,
            reason_code=0,
            can_replace=True,
            token="test_token",
            ai_point=100,
            ai_point_total=1000,
            limit_code=0,
            limit_msg="",
            limit_expire=0
        )

        # 验证基本属性
        assert auth_data.ai_available is True
        assert auth_data.reason_code == 0
        assert auth_data.can_replace is True
        assert auth_data.token == "test_token"
        assert auth_data.ai_point == 100

        # 测试序列化
        json_str = auth_data.json()
        assert "ai_available" in json_str
        assert "reason_code" in json_str
        assert "token" in json_str

    def test_resp_model_basic_scenarios(self):
        """测试响应模型基本场景"""
        # 测试成功响应
        success_resp = RespModel(
            result=ResultCode.OK,
            msg="权限检查成功",
            data=None
        )

        assert success_resp.result == ResultCode.OK
        assert success_resp.msg == "权限检查成功"
        assert success_resp.data is None

        # 测试错误响应
        error_resp = RespModel(
            result=ResultCode.FAIL,
            msg="权限检查失败",
            data=None
        )

        assert error_resp.result == ResultCode.FAIL
        assert error_resp.msg == "权限检查失败"
        assert error_resp.data is None
