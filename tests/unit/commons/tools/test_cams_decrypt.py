import pytest
import base64
import hashlib
from unittest.mock import patch, Mock
from commons.tools.cams_decrypt import (
    get_content_hash, cbc_encrypt, cbc_decrypt, cams_encrypt, cams_decrypt,
    ecis_decrypt, cams_sk_part
)


class TestCamsDecrypt:
    """Test cases for CAMS encryption/decryption functionality"""
    
    def test_get_content_hash(self):
        """Test content hash generation"""
        test_content = "hello world"
        
        hash_result = get_content_hash(test_content)
        
        # Verify it's a hex string
        assert isinstance(hash_result, str)
        assert len(hash_result) == 64  # SHA256 produces 64 character hex string
        
        # Verify it's deterministic
        hash_result2 = get_content_hash(test_content)
        assert hash_result == hash_result2
        
        # Verify different content produces different hash
        different_hash = get_content_hash("different content")
        assert hash_result != different_hash
    
    def test_get_content_hash_empty_string(self):
        """Test hash generation for empty string"""
        empty_hash = get_content_hash("")
        
        assert isinstance(empty_hash, str)
        assert len(empty_hash) == 64
        
        # Should be the SHA256 of empty string
        expected_hash = hashlib.sha256("".encode()).hexdigest()
        assert empty_hash == expected_hash
    
    def test_get_content_hash_unicode(self):
        """Test hash generation for unicode content"""
        unicode_content = "测试内容"
        
        hash_result = get_content_hash(unicode_content)
        
        assert isinstance(hash_result, str)
        assert len(hash_result) == 64
    
    def test_cbc_encrypt_decrypt_round_trip(self):
        """Test CBC encryption and decryption round trip"""
        data = b"test data for encryption"
        key = "16bytekey1234567"  # 16 bytes
        iv = "16byteiv12345678"   # 16 bytes
        
        # Encrypt
        encrypted = cbc_encrypt(data, key, iv)
        
        # Verify encrypted is base64 string
        assert isinstance(encrypted, str)
        
        # Verify it can be base64 decoded
        encrypted_bytes = base64.b64decode(encrypted)
        assert isinstance(encrypted_bytes, bytes)
        
        # Decrypt
        decrypted = cbc_decrypt(encrypted_bytes, key, iv)
        
        # Verify round trip
        assert decrypted == data.decode('utf-8')
    
    def test_cbc_encrypt_different_data_sizes(self):
        """Test CBC encryption with different data sizes"""
        key = "16bytekey1234567"
        iv = "16byteiv12345678"
        
        test_data = [
            b"a",  # 1 byte
            b"test",  # 4 bytes
            b"fifteen_bytes_.",  # 15 bytes
            b"exactly16bytes!",  # 16 bytes (one block)
            b"this is longer than 16 bytes and needs padding",  # > 16 bytes
        ]
        
        for data in test_data:
            encrypted = cbc_encrypt(data, key, iv)
            encrypted_bytes = base64.b64decode(encrypted)
            decrypted = cbc_decrypt(encrypted_bytes, key, iv)
            
            assert decrypted == data.decode('utf-8')
    
    def test_cbc_encrypt_empty_data(self):
        """Test CBC encryption with empty data"""
        data = b""
        key = "16bytekey1234567"
        iv = "16byteiv12345678"
        
        encrypted = cbc_encrypt(data, key, iv)
        encrypted_bytes = base64.b64decode(encrypted)
        decrypted = cbc_decrypt(encrypted_bytes, key, iv)
        
        assert decrypted == ""
    
    def test_cams_encrypt_decrypt_round_trip(self):
        """Test CAMS encryption and decryption round trip"""
        ak = "test_access_key"
        source_str = "test source string"
        
        # Encrypt
        encrypted = cams_encrypt(ak, source_str)
        
        # Verify encrypted is base64 string
        assert isinstance(encrypted, str)
        
        # Decrypt
        decrypted = cams_decrypt(ak, encrypted)
        
        # Verify round trip
        assert decrypted == source_str
    
    def test_cams_encrypt_decrypt_with_custom_sk_part(self):
        """Test CAMS encryption/decryption with custom sk_part"""
        ak = "test_access_key"
        source_str = "test source string"
        custom_sk_part = "CUSTOM_SK_PART"
        
        # Encrypt with custom sk_part
        encrypted = cams_encrypt(ak, source_str, custom_sk_part)
        
        # Decrypt with same custom sk_part
        decrypted = cams_decrypt(ak, encrypted, custom_sk_part)
        
        assert decrypted == source_str
        
        # Verify different sk_part produces different encryption
        different_encrypted = cams_encrypt(ak, source_str, "DIFFERENT_SK")
        assert encrypted != different_encrypted
    
    def test_cams_encrypt_decrypt_default_sk_part(self):
        """Test CAMS encryption/decryption uses default sk_part"""
        ak = "test_access_key"
        source_str = "test source string"
        
        # Test with empty sk_part (should use default)
        encrypted1 = cams_encrypt(ak, source_str, "")
        encrypted2 = cams_encrypt(ak, source_str)  # No sk_part parameter
        
        # Should use default cams_sk_part in both cases
        assert encrypted1 == encrypted2
        
        # Decrypt both
        decrypted1 = cams_decrypt(ak, encrypted1, "")
        decrypted2 = cams_decrypt(ak, encrypted2)
        
        assert decrypted1 == source_str
        assert decrypted2 == source_str
    
    def test_cams_encrypt_decrypt_different_ak(self):
        """Test that different AK produces different results"""
        source_str = "test source string"
        ak1 = "access_key_1"
        ak2 = "access_key_2"
        
        encrypted1 = cams_encrypt(ak1, source_str)
        encrypted2 = cams_encrypt(ak2, source_str)
        
        # Different AK should produce different encryption
        assert encrypted1 != encrypted2
        
        # Each should decrypt correctly with its own AK
        assert cams_decrypt(ak1, encrypted1) == source_str
        assert cams_decrypt(ak2, encrypted2) == source_str
    
    def test_cams_decrypt_invalid_base64(self):
        """Test CAMS decrypt with invalid base64"""
        ak = "test_access_key"
        invalid_b64 = "not_valid_base64!"
        
        with pytest.raises(Exception):  # base64.standard_b64decode will raise
            cams_decrypt(ak, invalid_b64)
    
    def test_ecis_decrypt(self):
        """Test ECIS decryption functionality"""
        cams_sk = "test_cams_secret_key"
        
        # Create a test encrypted string using the same logic as ecis_decrypt
        # This is a bit complex since we need to reverse-engineer the encryption
        
        # Test data
        original_data = "test_secret_data"
        
        # Calculate AES key (same as in ecis_decrypt)
        h = hashlib.md5()
        h.update(cams_sk.encode())
        akey = h.hexdigest()
        
        # Create test encrypted data using same logic
        from Crypto.Cipher import AES
        cipher = AES.new(akey.encode(), AES.MODE_CBC, akey.encode()[:AES.block_size])
        
        # Add padding manually (same as original implementation expects)
        data_to_encrypt = original_data.encode()
        padding_length = 16 - (len(data_to_encrypt) % 16)
        if padding_length == 0:
            padding_length = 16
        padded_data = data_to_encrypt + bytes([padding_length] * padding_length)
        
        encrypted_data = cipher.encrypt(padded_data)
        ecis_esk = base64.b64encode(encrypted_data).decode()
        
        # Test decryption
        decrypted = ecis_decrypt(cams_sk, ecis_esk)
        
        assert decrypted == original_data
    
    def test_ecis_decrypt_empty_cams_sk(self):
        """Test ECIS decrypt with empty CAMS SK"""
        cams_sk = ""
        
        # Create encrypted data with empty key
        h = hashlib.md5()
        h.update(cams_sk.encode())
        akey = h.hexdigest()
        
        original_data = "test"
        
        from Crypto.Cipher import AES
        cipher = AES.new(akey.encode(), AES.MODE_CBC, akey.encode()[:AES.block_size])
        
        data_to_encrypt = original_data.encode()
        padding_length = 16 - (len(data_to_encrypt) % 16)
        if padding_length == 0:
            padding_length = 16
        padded_data = data_to_encrypt + bytes([padding_length] * padding_length)
        
        encrypted_data = cipher.encrypt(padded_data)
        ecis_esk = base64.b64encode(encrypted_data).decode()
        
        decrypted = ecis_decrypt(cams_sk, ecis_esk)
        assert decrypted == original_data
    
    def test_ecis_decrypt_invalid_base64(self):
        """Test ECIS decrypt with invalid base64"""
        cams_sk = "test_secret"
        invalid_b64 = "not_valid_base64!"
        
        with pytest.raises(Exception):  # base64.b64decode will raise
            ecis_decrypt(cams_sk, invalid_b64)
    
    def test_cams_sk_part_constant(self):
        """Test that cams_sk_part constant is defined"""
        assert cams_sk_part == "CAMS_SK_PART"
        assert isinstance(cams_sk_part, str)
    
    def test_get_content_hash_consistency(self):
        """Test hash consistency across multiple calls"""
        content = "consistency test"
        
        hashes = [get_content_hash(content) for _ in range(10)]
        
        # All hashes should be identical
        assert len(set(hashes)) == 1
        assert all(h == hashes[0] for h in hashes)
    
    def test_cbc_encrypt_different_keys(self):
        """Test CBC encryption with different keys produces different results"""
        data = b"test data"
        iv = "16byteiv12345678"
        key1 = "key1234567890123"
        key2 = "different_key123"
        
        encrypted1 = cbc_encrypt(data, key1, iv)
        encrypted2 = cbc_encrypt(data, key2, iv)
        
        assert encrypted1 != encrypted2
    
    def test_cbc_encrypt_different_iv(self):
        """Test CBC encryption with different IV produces different results"""
        data = b"test data"
        key = "16bytekey1234567"
        iv1 = "iv1234567890abcd"
        iv2 = "different_iv_123"
        
        encrypted1 = cbc_encrypt(data, key, iv1)
        encrypted2 = cbc_encrypt(data, key, iv2)
        
        assert encrypted1 != encrypted2
    
    def test_cams_key_derivation(self):
        """Test that CAMS uses correct key derivation"""
        ak = "test_ak"
        source_str = "test"
        
        # Manually derive key and IV like cams_encrypt does
        expected_iv = get_content_hash(ak)[:16]
        expected_key = get_content_hash(cams_sk_part)[:16]
        
        # Encrypt using cams_encrypt
        encrypted = cams_encrypt(ak, source_str)
        
        # Verify we can decrypt using manually derived key/IV
        encrypted_bytes = base64.b64decode(encrypted)
        decrypted = cbc_decrypt(encrypted_bytes, expected_key, expected_iv)
        
        assert decrypted == source_str
    
    def test_unicode_strings_in_encryption(self):
        """Test encryption/decryption with unicode strings"""
        ak = "测试访问密钥"
        source_str = "测试源字符串"
        
        encrypted = cams_encrypt(ak, source_str)
        decrypted = cams_decrypt(ak, encrypted)
        
        assert decrypted == source_str
    
    def test_large_string_encryption(self):
        """Test encryption/decryption with large strings"""
        ak = "test_ak"
        # Create a large string (multiple AES blocks)
        large_string = "test data " * 1000  # ~10KB string
        
        encrypted = cams_encrypt(ak, large_string)
        decrypted = cams_decrypt(ak, encrypted)
        
        assert decrypted == large_string
    
    def test_ecis_decrypt_edge_cases(self):
        """Test ECIS decrypt with edge cases"""
        # Test with very short CAMS SK
        short_sk = "x"
        
        # Create encrypted data
        h = hashlib.md5()
        h.update(short_sk.encode())
        akey = h.hexdigest()
        
        original_data = "test"
        
        from Crypto.Cipher import AES
        cipher = AES.new(akey.encode(), AES.MODE_CBC, akey.encode()[:AES.block_size])
        
        data_to_encrypt = original_data.encode()
        padding_length = 16 - (len(data_to_encrypt) % 16)
        if padding_length == 0:
            padding_length = 16
        padded_data = data_to_encrypt + bytes([padding_length] * padding_length)
        
        encrypted_data = cipher.encrypt(padded_data)
        ecis_esk = base64.b64encode(encrypted_data).decode()
        
        decrypted = ecis_decrypt(short_sk, ecis_esk)
        assert decrypted == original_data


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
