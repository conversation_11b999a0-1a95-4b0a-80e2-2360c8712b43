import pytest
import os
import tempfile
import time
import logging
import json
import async<PERSON>
from unittest.mock import patch, <PERSON><PERSON>, <PERSON>Mock
from commons.tools.utils import (
    Singleton, SingletonABCMeta, WalkDir, norm_http_params, error_trace,
    calc_time, async_calc_time, BaseModelT
)
from abc import ABC, abstractmethod


class TestSingleton:
    """Test cases for Singleton metaclass"""
    
    def teardown_method(self):
        """Clean up singleton instances after each test"""
        Singleton._instances.clear()
    
    def test_singleton_basic(self):
        """Test basic singleton functionality"""
        class TestClass(metaclass=Singleton):
            def __init__(self, value=None):
                self.value = value
        
        # Create two instances
        instance1 = TestClass("first")
        instance2 = TestClass("second")
        
        # Should be the same instance
        assert instance1 is instance2
        assert id(instance1) == id(instance2)
        
        # First instance value should be preserved
        assert instance1.value == "first"
        assert instance2.value == "first"
    
    def test_singleton_different_classes(self):
        """Test that different classes have different singleton instances"""
        class ClassA(metaclass=Singleton):
            pass
        
        class ClassB(metaclass=Singleton):
            pass
        
        instance_a1 = ClassA()
        instance_a2 = ClassA()
        instance_b1 = ClassB()
        instance_b2 = ClassB()
        
        # Same class instances should be identical
        assert instance_a1 is instance_a2
        assert instance_b1 is instance_b2
        
        # Different class instances should be different
        assert instance_a1 is not instance_b1
        assert instance_a2 is not instance_b2


class TestSingletonABCMeta:
    """Test cases for SingletonABCMeta metaclass"""
    
    def teardown_method(self):
        """Clean up singleton instances after each test"""
        SingletonABCMeta._instances.clear()
    
    def test_singleton_abc_meta_basic(self):
        """Test SingletonABCMeta basic functionality"""
        class TestAbstractClass(ABC, metaclass=SingletonABCMeta):
            @abstractmethod
            def abstract_method(self):
                pass
        
        class ConcreteClass(TestAbstractClass):
            def abstract_method(self):
                return "implemented"
        
        # Create instances
        instance1 = ConcreteClass()
        instance2 = ConcreteClass()
        
        # Should be singleton
        assert instance1 is instance2
        assert instance1.abstract_method() == "implemented"
    
    def test_singleton_abc_meta_cannot_instantiate_abstract(self):
        """Test that abstract classes cannot be instantiated"""
        class AbstractClass(ABC, metaclass=SingletonABCMeta):
            @abstractmethod
            def abstract_method(self):
                pass
        
        # Should raise TypeError when trying to instantiate abstract class
        with pytest.raises(TypeError):
            AbstractClass()


class TestWalkDir:
    """Test cases for WalkDir class"""
    
    def test_walk_dir_basic(self):
        """Test WalkDir basic functionality"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files
            test_files = ['file1.txt', 'file2.py', 'file3.json']
            for filename in test_files:
                with open(os.path.join(temp_dir, filename), 'w') as f:
                    f.write('test content')
            
            # Create subdirectory with files
            subdir = os.path.join(temp_dir, 'subdir')
            os.makedirs(subdir)
            with open(os.path.join(subdir, 'subfile.txt'), 'w') as f:
                f.write('sub content')
            
            # Test WalkDir
            walker = WalkDir(temp_dir)
            results = list(walker())
            
            # Should find all files
            filenames = [fname for fname, fdir in results]
            assert 'file1.txt' in filenames
            assert 'file2.py' in filenames
            assert 'file3.json' in filenames
            assert 'subfile.txt' in filenames
            
            # Should have correct directory paths
            file_paths = {fname: fdir for fname, fdir in results}
            assert file_paths['file1.txt'] == temp_dir
            assert file_paths['subfile.txt'] == subdir
    
    def test_walk_dir_empty_directory(self):
        """Test WalkDir with empty directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            walker = WalkDir(temp_dir)
            results = list(walker())
            
            # Should return empty list
            assert results == []
    
    def test_walk_dir_nonexistent_directory(self):
        """Test WalkDir with non-existent directory"""
        # os.walk() doesn't always raise exception for non-existent directories
        # It may just return empty results. Let's test this behavior instead.
        walker = WalkDir("/nonexistent/directory/path/that/does/not/exist")
        
        # The iterator should work but return no results for non-existent directories
        results = list(walker())
        
        # Should return empty list (os.walk handles non-existent directories gracefully)
        assert results == []


class TestNormHttpParams:
    """Test cases for norm_http_params function"""
    
    def test_norm_http_params_http_get_boolean(self):
        """Test boolean normalization for HTTP GET"""
        params = {
            'enabled': True,
            'disabled': False,
            'other': 'value'
        }
        
        result = norm_http_params(params, is_http_get=True)
        
        assert result['enabled'] == 'true'
        assert result['disabled'] == 'false'
        assert result['other'] == 'value'
    
    def test_norm_http_params_non_get_boolean(self):
        """Test boolean handling for non-GET requests"""
        params = {
            'enabled': True,
            'disabled': False,
            'other': 'value'
        }
        
        result = norm_http_params(params, is_http_get=False)
        
        assert result['enabled'] is True
        assert result['disabled'] is False
        assert result['other'] == 'value'
    
    def test_norm_http_params_none_values(self):
        """Test None value filtering"""
        params = {
            'keep': 'value',
            'remove': None,
            'also_keep': 42
        }
        
        result = norm_http_params(params, is_http_get=True)
        
        assert 'keep' in result
        assert 'remove' not in result
        assert 'also_keep' in result
        assert result['keep'] == 'value'
        assert result['also_keep'] == 42
    
    def test_norm_http_params_nested_dict(self):
        """Test nested dictionary normalization"""
        params = {
            'top_level': 'value',
            'nested': {
                'enabled': True,
                'count': 5,
                'removed': None
            }
        }
        
        result = norm_http_params(params, is_http_get=True)
        
        assert result['top_level'] == 'value'
        assert isinstance(result['nested'], dict)
        assert result['nested']['enabled'] == 'true'
        assert result['nested']['count'] == 5
        assert 'removed' not in result['nested']
    
    def test_norm_http_params_list_handling(self):
        """Test list normalization"""
        params = {
            'simple_list': [1, 2, 3],
            'dict_list': [
                {'enabled': True, 'name': 'item1'},
                {'enabled': False, 'name': 'item2', 'removed': None}
            ]
        }
        
        result = norm_http_params(params, is_http_get=True)
        
        assert result['simple_list'] == [1, 2, 3]
        assert len(result['dict_list']) == 2
        assert result['dict_list'][0]['enabled'] == 'true'
        assert result['dict_list'][0]['name'] == 'item1'
        assert result['dict_list'][1]['enabled'] == 'false'
        assert result['dict_list'][1]['name'] == 'item2'
        assert 'removed' not in result['dict_list'][1]
    
    def test_norm_http_params_complex_nested(self):
        """Test complex nested structure"""
        params = {
            'level1': {
                'level2': {
                    'enabled': True,
                    'items': [
                        {'active': False, 'skip': None},
                        'string_item'
                    ]
                },
                'simple': 'value'
            }
        }
        
        result = norm_http_params(params, is_http_get=True)
        
        assert result['level1']['level2']['enabled'] == 'true'
        assert result['level1']['simple'] == 'value'
        assert result['level1']['level2']['items'][0]['active'] == 'false'
        assert 'skip' not in result['level1']['level2']['items'][0]
        assert result['level1']['level2']['items'][1] == 'string_item'


class TestErrorTrace:
    """Test cases for error_trace function"""
    
    @patch('logging.error')
    def test_error_trace_basic(self, mock_log_error):
        """Test basic error trace functionality"""
        try:
            raise ValueError("Test error message")
        except ValueError:
            error_trace()
        
        # Should have called logging.error once
        mock_log_error.assert_called_once()
        
        # Get the logged message
        logged_message = mock_log_error.call_args[0][0]
        error_dict = json.loads(logged_message)
        
        # Verify structure
        assert 'Error Type' in error_dict
        assert 'Message' in error_dict
        assert 'TraceBack' in error_dict
        
        # Verify content
        assert 'ValueError' in error_dict['Error Type']
        assert 'Test error message' in error_dict['Message']
        assert isinstance(error_dict['TraceBack'], list)
    
    @patch('logging.error')
    def test_error_trace_nested_calls(self, mock_log_error):
        """Test error trace with nested function calls"""
        def inner_function():
            raise RuntimeError("Inner error")
        
        def outer_function():
            inner_function()
        
        try:
            outer_function()
        except RuntimeError:
            error_trace()
        
        mock_log_error.assert_called_once()
        logged_message = mock_log_error.call_args[0][0]
        error_dict = json.loads(logged_message)
        
        # Should have multiple stack frames
        assert len(error_dict['TraceBack']) >= 2
        
        # Each frame should have required fields
        for frame in error_dict['TraceBack']:
            assert 'file' in frame
            assert 'line' in frame
            assert 'func' in frame
            assert 'text' in frame


class TestCalcTime:
    """Test cases for calc_time decorator"""
    
    @patch('logging.info')
    def test_calc_time_function(self, mock_log_info):
        """Test calc_time decorator on function"""
        @calc_time
        def test_function(x, y):
            time.sleep(0.01)  # Small delay
            return x + y
        
        result = test_function(3, 4)
        
        assert result == 7
        mock_log_info.assert_called_once()
        
        # Check log message format
        log_message = mock_log_info.call_args[0][0]
        # The class name will be <locals> when defined inside a test function
        assert 'func:test_function' in log_message
        assert 'cost time:' in log_message
    
    @patch('logging.info')
    def test_calc_time_method(self, mock_log_info):
        """Test calc_time decorator on class method"""
        class TestClass:
            @calc_time
            def test_method(self, value):
                return value * 2
        
        instance = TestClass()
        result = instance.test_method(5)
        
        assert result == 10
        mock_log_info.assert_called_once()
        
        log_message = mock_log_info.call_args[0][0]
        assert 'class:TestClass' in log_message
        assert 'func:test_method' in log_message
    
    def test_calc_time_preserves_function_metadata(self):
        """Test that calc_time preserves function metadata"""
        @calc_time
        def documented_function():
            """This is a test function"""
            return "result"
        
        assert documented_function.__name__ == 'documented_function'
        assert documented_function.__doc__ == "This is a test function"


class TestAsyncCalcTime:
    """Test cases for async_calc_time decorator"""
    
    @pytest.mark.asyncio
    @patch('logging.info')
    async def test_async_calc_time_function(self, mock_log_info):
        """Test async_calc_time decorator"""
        @async_calc_time
        async def async_test_function(x, y):
            await asyncio.sleep(0.001)  # Small delay
            return x * y
        
        result = await async_test_function(3, 4)
        
        assert result == 12
        mock_log_info.assert_called_once()
        
        log_message = mock_log_info.call_args[0][0]
        # The class name will be <locals> when defined inside a test function
        assert 'func:async_test_function' in log_message
        assert 'cost time:' in log_message
    
    @pytest.mark.asyncio
    @patch('logging.info')
    async def test_async_calc_time_method(self, mock_log_info):
        """Test async_calc_time decorator on class method"""
        class AsyncTestClass:
            @async_calc_time
            async def async_method(self, value):
                await asyncio.sleep(0.001)
                return value + 10
        
        instance = AsyncTestClass()
        result = await instance.async_method(5)
        
        assert result == 15
        mock_log_info.assert_called_once()
        
        log_message = mock_log_info.call_args[0][0]
        assert 'class:AsyncTestClass' in log_message
        assert 'func:async_method' in log_message
    
    def test_async_calc_time_preserves_function_metadata(self):
        """Test that async_calc_time preserves function metadata"""
        @async_calc_time
        async def documented_async_function():
            """This is an async test function"""
            return "async result"
        
        assert documented_async_function.__name__ == 'documented_async_function'
        assert documented_async_function.__doc__ == "This is an async test function"


class TestBaseModelT:
    """Test cases for BaseModelT type variable"""
    
    def test_base_model_t_is_type_var(self):
        """Test that BaseModelT is a TypeVar"""
        from typing import TypeVar
        assert isinstance(BaseModelT, TypeVar)
    
    def test_base_model_t_bound_to_base_model(self):
        """Test that BaseModelT is bound to BaseModel"""
        from pydantic import BaseModel
        assert BaseModelT.__bound__ is BaseModel


class TestIntegration:
    """Integration tests for multiple utilities"""
    
    def teardown_method(self):
        """Clean up singleton instances"""
        Singleton._instances.clear()
        SingletonABCMeta._instances.clear()
    
    @patch('logging.info')
    def test_singleton_with_calc_time(self, mock_log_info):
        """Test singleton pattern combined with calc_time decorator"""
        class TimedSingleton(metaclass=Singleton):
            def __init__(self):
                self.call_count = 0
            
            @calc_time
            def timed_method(self):
                self.call_count += 1
                return self.call_count
        
        instance1 = TimedSingleton()
        instance2 = TimedSingleton()
        
        assert instance1 is instance2
        
        result1 = instance1.timed_method()
        result2 = instance2.timed_method()
        
        assert result1 == 1
        assert result2 == 2
        assert instance1.call_count == 2
        assert mock_log_info.call_count == 2
    
    def test_norm_http_params_with_walkdir_results(self):
        """Test norm_http_params with WalkDir-like structure"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test file
            test_file = os.path.join(temp_dir, 'test.txt')
            with open(test_file, 'w') as f:
                f.write('content')
            
            walker = WalkDir(temp_dir)
            files = list(walker())
            
            # Create HTTP params structure
            params = {
                'scan_results': [
                    {'filename': fname, 'directory': fdir, 'processed': True}
                    for fname, fdir in files
                ],
                'total_files': len(files),
                'scan_complete': True
            }
            
            normalized = norm_http_params(params, is_http_get=True)
            
            assert normalized['scan_complete'] == 'true'
            assert normalized['total_files'] == len(files)
            assert len(normalized['scan_results']) == len(files)
            assert normalized['scan_results'][0]['processed'] == 'true'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
