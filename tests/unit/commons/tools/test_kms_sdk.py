import pytest
import os
import base64
import tempfile
from unittest.mock import patch, Mock, mock_open
from commons.tools.kms_sdk import KmsLocalClient, rootHead, keyHead, NONCE_BYTE_SIZE


class TestKmsLocalClient:
    """Test cases for KmsLocalClient class"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        if hasattr(KmsLocalClient, '_instances'):
            KmsLocalClient._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(KmsLocalClient, '_instances'):
            KmsLocalClient._instances.clear()
    
    def test_singleton_behavior(self):
        """Test that KmsLocalClient follows singleton pattern"""
        client1 = KmsLocalClient()
        client2 = KmsLocalClient()
        
        assert client1 is client2
        assert id(client1) == id(client2)
    
    def test_init_state(self):
        """Test initial state of KmsLocalClient"""
        client = KmsLocalClient()
        
        assert client.rootKey is None
        assert client.keyId is None
    
    def test_make_key(self):
        """Test makeKey method"""
        client = KmsLocalClient()
        
        head = "test-header"
        mid = "test-mid"
        tail = "test-tail"
        
        key = client.makeKey(head, mid, tail)
        
        # Should return MD5 hex string
        assert isinstance(key, str)
        assert len(key) == 32  # MD5 hex is 32 characters
        
        # Should be deterministic
        key2 = client.makeKey(head, mid, tail)
        assert key == key2
        
        # Different inputs should produce different keys
        different_key = client.makeKey("different", mid, tail)
        assert key != different_key
    
    def test_make_key_with_unicode(self):
        """Test makeKey with unicode characters"""
        client = KmsLocalClient()
        
        key = client.makeKey("测试头", "测试中", "测试尾")
        
        assert isinstance(key, str)
        assert len(key) == 32
    
    def test_init_with_files(self):
        """Test init method with config and secret files"""
        client = KmsLocalClient()
        
        # Create temporary files
        config_content = "config content"
        secret_content = "secret content"
        key_id = "test-key-id"
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as config_file:
            config_file.write(config_content)
            config_path = config_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as secret_file:
            secret_file.write(secret_content)
            secret_path = secret_file.name
        
        try:
            client.init(key_id, config_path, secret_path)
            
            # Should have set keyId
            assert client.keyId == key_id
            
            # Should have generated rootKey
            assert client.rootKey is not None
            assert isinstance(client.rootKey, str)
            assert len(client.rootKey) == 32
            
            # Verify key generation
            expected_key = client.makeKey(rootHead, config_content, secret_content)
            assert client.rootKey == expected_key
        
        finally:
            # Clean up temporary files
            os.unlink(config_path)
            os.unlink(secret_path)
    
    def test_init_with_whitespace_in_files(self):
        """Test init method strips whitespace from file contents"""
        client = KmsLocalClient()
        
        config_content = "\n\t  config content  \r\n\t  "
        secret_content = "\r\n  secret content  \n\r"
        key_id = "test-key-id"
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as config_file:
            config_file.write(config_content)
            config_path = config_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as secret_file:
            secret_file.write(secret_content)
            secret_path = secret_file.name
        
        try:
            client.init(key_id, config_path, secret_path)
            
            # Should have stripped whitespace and generated correct key
            expected_key = client.makeKey(rootHead, "config content", "secret content")
            assert client.rootKey == expected_key
        
        finally:
            os.unlink(config_path)
            os.unlink(secret_path)
    
    def test_init_file_not_found(self):
        """Test init with non-existent files"""
        client = KmsLocalClient()
        
        with pytest.raises(FileNotFoundError):
            client.init("key-id", "/nonexistent/config", "/nonexistent/secret")
    
    def test_encrypt_decrypt_gcm_round_trip(self):
        """Test GCM encryption/decryption round trip"""
        client = KmsLocalClient()
        
        message = b"test message for encryption"
        key = b"32-byte-key-for-aes-encryption!!"  # 32 bytes
        
        # Encrypt
        ciphertext, nonce, auth_tag = client.encrypt_GCM(message, key)
        
        # Verify components
        assert isinstance(ciphertext, bytes)
        assert isinstance(nonce, bytes)
        assert isinstance(auth_tag, bytes)
        assert len(nonce) == 12  # GCM nonce size
        assert len(auth_tag) == 16  # GCM auth tag size
        
        # Create combined format like decrypt expects
        combined = nonce + ciphertext + auth_tag
        
        # Decrypt
        decrypted = client.decrypt_GCM(combined, key)
        
        assert decrypted == message
    
    def test_encrypt_gcm_different_messages(self):
        """Test that different messages produce different ciphertexts"""
        client = KmsLocalClient()
        
        key = b"32-byte-key-for-aes-encryption!!"
        message1 = b"first message"
        message2 = b"second message"
        
        ciphertext1, nonce1, tag1 = client.encrypt_GCM(message1, key)
        ciphertext2, nonce2, tag2 = client.encrypt_GCM(message2, key)
        
        # Should produce different results
        assert ciphertext1 != ciphertext2
        assert nonce1 != nonce2  # Random nonces should be different
        assert tag1 != tag2
    
    def test_encrypt_gcm_same_message_different_nonces(self):
        """Test that same message produces different ciphertexts due to random nonce"""
        client = KmsLocalClient()
        
        key = b"32-byte-key-for-aes-encryption!!"
        message = b"same message"
        
        ciphertext1, nonce1, tag1 = client.encrypt_GCM(message, key)
        ciphertext2, nonce2, tag2 = client.encrypt_GCM(message, key)
        
        # Should have different nonces and therefore different ciphertexts
        assert nonce1 != nonce2
        assert ciphertext1 != ciphertext2
        assert tag1 != tag2
    
    @patch('base64.standard_b64decode')
    def test_get_business_key(self, mock_b64decode):
        """Test getBusinessKey method"""
        client = KmsLocalClient()
        client.rootKey = "test-root-key-32-characters-!!"
        client.keyId = "base64-encoded-key-id"
        
        # Mock the base64 decode to return a known value
        mock_encrypted_key = b"mock-encrypted-business-key-data"
        mock_b64decode.return_value = mock_encrypted_key
        
        # Mock the decrypt_GCM method
        with patch.object(client, 'decrypt_GCM') as mock_decrypt:
            mock_decrypt.return_value = b"decrypted-business-key-longer-than-32-bytes"
            
            business_key = client.getBusinessKey()
            
            # Should call base64 decode with keyId
            mock_b64decode.assert_called_once_with(client.keyId)
            
            # Should call decrypt_GCM with correct parameters
            mock_decrypt.assert_called_once_with(
                mock_encrypted_key,
                client.rootKey.encode('utf-8')
            )
            
            # Should return first 32 bytes
            expected_key = b"decrypted-business-key-longer-than-32-bytes"[:32]
            assert business_key == expected_key
            assert len(business_key) == 32
    
    def test_do_encrypt(self):
        """Test DoEncrypt method"""
        client = KmsLocalClient()
        
        plain_text = b"test plain text"
        
        with patch.object(client, 'getBusinessKey') as mock_get_key:
            mock_get_key.return_value = b"32-byte-business-key-for-test!!"
            
            with patch.object(client, 'encrypt_GCM') as mock_encrypt:
                mock_encrypt.return_value = (b"ciphertext", b"nonce123", b"authtag123")
                
                result = client.DoEncrypt(plain_text)
                
                # Should call getBusinessKey
                mock_get_key.assert_called_once()
                
                # Should call encrypt_GCM with correct parameters
                mock_encrypt.assert_called_once_with(plain_text, b"32-byte-business-key-for-test!!")
                
                # Should return base64 encoded result
                assert isinstance(result, bytes)
                
                # Decode to verify format
                decoded = base64.standard_b64decode(result)
                assert decoded == b"nonce123" + b"ciphertext" + b"authtag123"
    
    def test_do_decrypt(self):
        """Test DoDecrypt method"""
        client = KmsLocalClient()
        
        cipher_data = b"test cipher data"
        
        with patch.object(client, 'getBusinessKey') as mock_get_key:
            mock_get_key.return_value = b"32-byte-business-key-for-test!!"
            
            with patch.object(client, 'decrypt_GCM') as mock_decrypt:
                mock_decrypt.return_value = b"decrypted plain text"
                
                result = client.DoDecrypt(cipher_data)
                
                # Should call getBusinessKey
                mock_get_key.assert_called_once()
                
                # Should call decrypt_GCM with correct parameters
                mock_decrypt.assert_called_once_with(cipher_data, b"32-byte-business-key-for-test!!")
                
                # Should return decrypted bytes
                assert result == b"decrypted plain text"
    
    def test_decrypt_base64(self):
        """Test DecryptBase64 method"""
        client = KmsLocalClient()
        
        # Create base64 encoded data
        test_data = b"test encrypted data"
        base64_data = base64.b64encode(test_data).decode('utf-8')
        
        with patch.object(client, 'DoDecrypt') as mock_do_decrypt:
            mock_do_decrypt.return_value = b"decrypted result"
            
            result = client.DecryptBase64(base64_data)
            
            # Should call DoDecrypt with decoded data
            mock_do_decrypt.assert_called_once_with(test_data)
            
            # Should return string result
            assert result == "decrypted result"
            assert isinstance(result, str)
    
    def test_decrypt_base64_with_unicode(self):
        """Test DecryptBase64 with unicode result"""
        client = KmsLocalClient()
        
        base64_data = base64.b64encode(b"test").decode('utf-8')
        
        with patch.object(client, 'DoDecrypt') as mock_do_decrypt:
            mock_do_decrypt.return_value = "测试结果".encode('utf-8')
            
            result = client.DecryptBase64(base64_data)
            
            assert result == "测试结果"
            assert isinstance(result, str)
    
    def test_constants(self):
        """Test module constants"""
        assert rootHead == "kingsoft-header"
        assert keyHead == "kingsoft-key"
        assert NONCE_BYTE_SIZE == 12
    
    def test_full_integration_workflow(self):
        """Test full workflow integration"""
        # Create temporary files
        config_content = "test-config"
        secret_content = "test-secret"
        key_id_data = b"encrypted-key-data"
        key_id = base64.b64encode(key_id_data).decode('utf-8')
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as config_file:
            config_file.write(config_content)
            config_path = config_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as secret_file:
            secret_file.write(secret_content)
            secret_path = secret_file.name
        
        try:
            client = KmsLocalClient()
            
            # Initialize client
            client.init(key_id, config_path, secret_path)
            
            # Verify initialization
            assert client.keyId == key_id
            assert client.rootKey is not None
            
            # Test key generation is working
            expected_root_key = client.makeKey(rootHead, config_content, secret_content)
            assert client.rootKey == expected_root_key
        
        finally:
            os.unlink(config_path)
            os.unlink(secret_path)
    
    def test_decrypt_gcm_invalid_data(self):
        """Test decrypt_GCM with invalid data format"""
        client = KmsLocalClient()
        key = b"32-byte-key-for-aes-encryption!!"
        
        # Data too short (less than nonce + auth tag)
        with pytest.raises(Exception):  # Will raise various crypto exceptions
            client.decrypt_GCM(b"short", key)
    
    def test_decrypt_gcm_wrong_key(self):
        """Test decrypt_GCM with wrong key"""
        client = KmsLocalClient()
        
        # Encrypt with one key
        message = b"test message"
        correct_key = b"32-byte-key-for-aes-encryption!!"
        wrong_key = b"wrong-32-byte-key-for-aes-encryp!!"
        
        ciphertext, nonce, auth_tag = client.encrypt_GCM(message, correct_key)
        combined = nonce + ciphertext + auth_tag
        
        # Try to decrypt with wrong key
        with pytest.raises(Exception):  # Should raise authentication error
            client.decrypt_GCM(combined, wrong_key)
    
    def test_singleton_persistence_across_operations(self):
        """Test that singleton state persists across operations"""
        client1 = KmsLocalClient()
        
        # Set some state
        client1.rootKey = "test-key"
        client1.keyId = "test-id"
        
        # Get another instance
        client2 = KmsLocalClient()
        
        # Should have same state
        assert client2.rootKey == "test-key"
        assert client2.keyId == "test-id"
        assert client1 is client2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
