import pytest
import logging
from unittest.mock import patch
from commons.tools.url_validator import validate_http_url, URL_PATTERN


class TestUrlValidator:
    """Test cases for URL validation functionality"""
    
    def test_valid_http_urls(self):
        """Test valid HTTP URLs"""
        valid_urls = [
            "http://example.com",
            "https://example.com",
            "http://www.example.com",
            "https://www.example.com",
            "http://subdomain.example.com",
            "https://subdomain.example.com",
            "http://example.com:8080",
            "https://example.com:443",
            "http://localhost",
            "https://localhost",
            "http://localhost:3000",
            "http://127.0.0.1",
            "https://***********",
            "http://***********:8080",
        ]
        
        for url in valid_urls:
            assert validate_http_url(url) is True, f"URL should be valid: {url}"
    
    def test_valid_urls_with_paths(self):
        """Test valid URLs with paths and query parameters"""
        valid_urls = [
            "http://example.com/",
            "https://example.com/path",
            "http://example.com/path/to/resource",
            "https://example.com/path?query=value",
            "http://example.com/path?query=value&other=param",
            "https://example.com/path/file.html",
            "http://example.com/api/v1/resource",
            "https://example.com:8080/secure/path",
            "http://kna-model.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/edd25bc355d343cc8413c0f73d658d66.png?Signature=DY4Wqw%2F5d7aM12aSduuTydSWzK4%3D&Expires=3235722162&KSSAccessKeyId=AKLT3OWV80e4QWe6KQ5x8Viz",
        ]
        
        for url in valid_urls:
            assert validate_http_url(url) is True, f"URL should be valid: {url}"
    
    def test_invalid_urls(self):
        """Test invalid URLs"""
        invalid_urls = [
            "",
            "not_a_url",
            "ftp://example.com",
            "mailto:<EMAIL>",
            "file:///path/to/file",
            "javascript:alert('hello')",
            "data:text/plain;base64,SGVsbG8gV29ybGQ=",
            "//example.com",
            "http://",
            "https://",
            "http:///path",
            "https:///path",
            "http://.",
            "http://..",
            "http://../",
            "http://./",
        ]
        
        for url in invalid_urls:
            assert validate_http_url(url) is False, f"URL should be invalid: {url}"
    
    def test_invalid_domains(self):
        """Test URLs with invalid domain formats"""
        invalid_urls = [
            "http://.com",
            "http://-.example.com",
            "http://example-.com",
            "http://ex ample.com",  # Space in domain
            "http://example..com",  # Double dot
            "http://example.com.",  # Trailing dot (this might be valid in some contexts)
            "http://-example.com",
            "http://example.c",  # TLD too short
        ]
        
        for url in invalid_urls:
            result = validate_http_url(url)
            # Note: Some of these might actually be valid according to the regex
            # The test documents the current behavior
            if result:
                print(f"Note: URL '{url}' is considered valid by current regex")
    
    def test_edge_case_domains(self):
        """Test edge case domain formats"""
        edge_case_urls = [
            "http://example.museum",  # Long TLD
            "http://sub1.sub2.example.com",  # Multiple subdomains
            "http://123.456.789.012",  # Invalid IP (but matches pattern)
            "http://999.999.999.999",  # Invalid IP (but matches pattern)
        ]
        
        for url in edge_case_urls:
            result = validate_http_url(url)
            print(f"Edge case '{url}': {result}")
    
    @patch('logging.error')
    def test_logging_on_invalid_url(self, mock_log_error):
        """Test that logging.error is called for invalid URLs"""
        invalid_url = "not_a_url"
        
        result = validate_http_url(invalid_url)
        
        assert result is False
        mock_log_error.assert_called_once_with(f"Invalid URL format: {invalid_url}")
    
    @patch('logging.error')
    def test_no_logging_on_valid_url(self, mock_log_error):
        """Test that logging.error is not called for valid URLs"""
        valid_url = "https://example.com"
        
        result = validate_http_url(valid_url)
        
        assert result is True
        mock_log_error.assert_not_called()
    
    def test_url_pattern_regex_directly(self):
        """Test the URL_PATTERN regex directly"""
        # Test valid patterns
        valid_patterns = [
            "http://example.com",
            "https://test.org",
            "http://localhost:8080",
            "https://***********:443/path"
        ]
        
        for pattern in valid_patterns:
            match = URL_PATTERN.match(pattern)
            assert match is not None, f"Pattern should match: {pattern}"
    
    def test_url_pattern_case_insensitive(self):
        """Test that URL pattern is case insensitive"""
        urls = [
            "HTTP://EXAMPLE.COM",
            "HTTPS://EXAMPLE.COM",
            "http://EXAMPLE.com",
            "https://example.COM",
        ]
        
        for url in urls:
            assert validate_http_url(url) is True, f"URL should be valid (case insensitive): {url}"
    
    def test_url_with_unicode_domain(self):
        """Test URLs with unicode characters in domain (should fail)"""
        unicode_urls = [
            "http://例え.テスト",
            "https://müller.de",
            "http://тест.рф",
        ]
        
        for url in unicode_urls:
            # These should fail with current regex as it doesn't handle IDN
            result = validate_http_url(url)
            # Just documenting current behavior
            print(f"Unicode URL '{url}': {result}")
    
    def test_url_with_special_characters_in_path(self):
        """Test URLs with special characters in path"""
        special_urls = [
            "https://example.com/path with spaces",  # Should fail
            "https://example.com/path%20with%20spaces",  # Should pass
            "https://example.com/path?q=hello world",  # Should fail  
            "https://example.com/path?q=hello%20world",  # Should pass
            "https://example.com/файл.html",  # Unicode in path
        ]
        
        for url in special_urls:
            result = validate_http_url(url)
            print(f"Special chars URL '{url}': {result}")
    
    def test_empty_string_input(self):
        """Test empty string input"""
        result = validate_http_url("")
        assert result is False
    
    def test_none_input_handling(self):
        """Test None input handling (should raise exception)"""
        with pytest.raises(TypeError):
            validate_http_url(None)
    
    def test_non_string_input(self):
        """Test non-string input handling"""
        with pytest.raises(TypeError):
            validate_http_url(123)
        
        with pytest.raises(TypeError):
            validate_http_url(['http://example.com'])


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
