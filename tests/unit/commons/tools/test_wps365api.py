import pytest
import json
import time
from unittest.mock import patch, Mock, AsyncMock
from pydantic import BaseModel
from commons.tools.wps365api import Wps365api, TokenAuthException, HTTPCODE, ResponseModel, RespTokenAuthError


class TestWps365api:
    """Test cases for Wps365api class"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        if hasattr(Wps365api, '_instances'):
            Wps365api._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(Wps365api, '_instances'):
            Wps365api._instances.clear()
    
    def test_singleton_behavior(self):
        """Test singleton pattern"""
        api1 = Wps365api()
        api2 = Wps365api()
        
        assert api1 is api2
        assert id(api1) == id(api2)
    
    def test_init_default_state(self):
        """Test initial default state"""
        api = Wps365api()
        
        assert api._host == "https://api.wps.cn"
        assert api._sess is None
        assert api._async_sess is None
        assert api._openapi is None
        assert api._tokens == {}
        assert api._woa_custom_ak == ""
        assert hasattr(api, '_origin')
    
    @patch('requests.Session')
    @patch('aiohttp.ClientSession')
    @patch('commons.tools.wps365api.OpenApi')
    def test_init_with_all_params(self, mock_openapi, mock_async_session, mock_session):
        """Test initialization with all parameters"""
        mock_session_instance = Mock()
        mock_session.return_value = mock_session_instance
        
        # Mock aiohttp.ClientSession to avoid event loop issues
        mock_async_session_instance = Mock()
        mock_async_session.return_value = mock_async_session_instance
        
        mock_openapi_instance = Mock()
        mock_openapi.return_value = mock_openapi_instance
        
        # Mock aiohttp.TCPConnector to avoid event loop issues
        with patch('aiohttp.TCPConnector') as mock_connector:
            mock_connector.return_value = Mock()
            
            api = Wps365api()
            api.init(
                host="https://custom.wps.cn",
                pool_max=10,
                openapi_host="https://openapi.wps.cn",
                openapi_ak="test_ak",
                openapi_sk="test_sk",
                woa_custom_ak="custom_ak"
            )
            
            assert api._host == "https://custom.wps.cn"
            assert api._woa_custom_ak == "custom_ak"
            assert api._openapi is mock_openapi_instance
            assert api._sess is mock_session_instance
            
            # Verify OpenApi was initialized correctly
            mock_openapi.assert_called_once_with("https://openapi.wps.cn", "test_ak", "test_sk", 10)
    
    def test_set_host(self):
        """Test set_host method"""
        api = Wps365api()
        
        api.set_host("https://new.wps.cn")
        
        assert api._host == "https://new.wps.cn"
        assert "https://new.wps.cn" in api._origin
    
    def test_set_origin(self):
        """Test _set_origin method"""
        api = Wps365api()
        api._host = "https://api.wps.cn:8080/path"
        
        api._set_origin()
        
        assert api._origin == "https://api.wps.cn:8080"
    
    @patch('time.time')
    def test_get_token_cached(self, mock_time):
        """Test get_token with cached token"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        # Set current time
        mock_time.return_value = 1000.0
        
        # Add cached token that's still valid
        api._tokens["company1"] = Wps365api.TokenItem(
            token="Bearer cached_token",
            expires=2000.0  # Expires in future
        )
        
        token = api.get_token("company1")
        
        assert token == "Bearer cached_token"
        # Should not call openapi since token is cached and valid
        mock_openapi.get_token.assert_not_called()
    
    @patch('time.time')
    def test_get_token_expired(self, mock_time):
        """Test get_token with expired token"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        # Mock time and token response
        mock_time.return_value = 1000.0
        mock_token_resp = Mock()
        mock_token_resp.token_type = "Bearer"
        mock_token_resp.access_token = "new_token"
        mock_token_resp.expires_in = 3600
        mock_openapi.get_token.return_value = mock_token_resp
        
        # Add expired token
        api._tokens["company1"] = Wps365api.TokenItem(
            token="Bearer old_token",
            expires=500.0  # Expired
        )
        
        token = api.get_token("company1")
        
        assert token == "Bearer new_token"
        mock_openapi.get_token.assert_called_once_with("company1")
        
        # Check cached token was updated
        assert api._tokens["company1"].token == "Bearer new_token"
        assert api._tokens["company1"].expires == 1000.0 + 3600 * 0.7
    
    def test_get_token_no_openapi(self):
        """Test get_token without openapi initialization"""
        api = Wps365api()
        
        with pytest.raises(AssertionError, match="requires the initialization of the openapi"):
            api.get_token("company1")
    
    @patch('time.time')
    def test_get_token_openapi_failure(self, mock_time):
        """Test get_token when openapi returns None"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        mock_time.return_value = 1000.0
        mock_openapi.get_token.return_value = None  # Simulates failure
        
        token = api.get_token("company1")
        
        assert token == ""  # Should return empty token
        # Should cache empty token with expiry
        assert api._tokens["company1"].token == ""
        assert api._tokens["company1"].expires == 1000.0 + 3600 * 0.7
    
    def test_pop_token(self):
        """Test pop_token method"""
        api = Wps365api()
        
        # Add token
        api._tokens["company1"] = Wps365api.TokenItem(token="test_token", expires=2000.0)
        
        api.pop_token("company1")
        
        assert "company1" not in api._tokens
    
    def test_pop_token_nonexistent(self):
        """Test pop_token with non-existent company"""
        api = Wps365api()
        
        # Should not raise error
        api.pop_token("nonexistent")
        
        assert api._tokens == {}
    
    def test_noraml_json_params_boolean(self):
        """Test _noraml_json_params with boolean values"""
        api = Wps365api()
        
        params = {
            'enabled': True,
            'disabled': False,
            'count': 42
        }
        
        result = api._noraml_json_params(params)
        
        assert result['enabled'] == "true"
        assert result['disabled'] == "false"
        assert result['count'] == 42
    
    def test_noraml_json_params_nested(self):
        """Test _noraml_json_params with nested structures"""
        api = Wps365api()
        
        params = {
            'config': {
                'active': True,
                'count': 5,
                'skip': None
            },
            'items': [
                {'enabled': False, 'name': 'item1'},
                'simple_string',
                {'nested': {'deep': True}}
            ]
        }
        
        result = api._noraml_json_params(params)
        
        assert result['config']['active'] == "true"
        assert result['config']['count'] == 5
        assert 'skip' not in result['config']
        assert result['items'][0]['enabled'] == "false"
        assert result['items'][1] == "simple_string"
        assert result['items'][2]['nested']['deep'] == "true"
    
    @patch('requests.request')
    def test_call_without_session(self, mock_request):
        """Test call method without session"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        
        mock_resp = Mock()
        mock_resp.status_code = 200
        mock_resp.content = b'{"code": 0, "data": {"result": "success"}}'
        mock_resp.headers = {}
        mock_request.return_value = mock_resp
        
        status, content, headers = api.call("GET", "/test")
        
        assert status == 200
        assert content == b'{"code": 0, "data": {"result": "success"}}'
        
        # Verify headers were set correctly
        call_args = mock_request.call_args
        headers_used = call_args[1]['headers']
        assert headers_used['open-app-id'] == "test_ak"
        assert 'Origin' in headers_used
    
    @patch('commons.tools.wps365api.logger')
    def test_get_with_token_auth_error(self, mock_logger):
        """Test get method with token authentication error"""
        api = Wps365api()
        api._openapi = Mock()
        api._openapi.get_token.return_value = Mock(token_type="Bearer", access_token="test_token")
        
        # Mock token that's not expired
        with patch('time.time', return_value=1000.0):
            api._tokens["company1"] = Wps365api.TokenItem(token="Bearer valid_token", expires=2000.0)
            
            with patch.object(api, 'call') as mock_call:
                # Simulate 403 error with specific error code for token auth
                # The response must be valid JSON that can be parsed by RespTokenAuthError
                error_response = json.dumps({
                    "code": 400006,
                    "msg": "Token expired"
                }).encode()
                mock_call.return_value = (403, error_response, {})
                
                # TokenAuthException inherits from BaseException, not Exception
                # So it will NOT be caught by the except Exception block
                # Therefore, it should propagate up and we need to catch it
                with pytest.raises(TokenAuthException):
                    api.get("/test", company_id="company1")
                
                # Should have popped the token (this happens before the exception)
                assert "company1" not in api._tokens
    
    @patch('commons.tools.wps365api.logger')
    def test_get_token_auth_exception_direct(self, mock_logger):
        """Test that TokenAuthException can be raised directly by bypassing exception handler"""
        api = Wps365api()
        api._openapi = Mock()
        api._openapi.get_token.return_value = Mock(token_type="Bearer", access_token="test_token")
        
        # Mock token that's not expired
        with patch('time.time', return_value=1000.0):
            api._tokens["company1"] = Wps365api.TokenItem(token="Bearer valid_token", expires=2000.0)
            
            # Manually trigger the specific condition that raises TokenAuthException
            # by calling the internal logic directly
            code = 403
            content = json.dumps({"code": 400006, "msg": "Token expired"}).encode()
            
            # This simulates the exact condition in the get method
            if code == 403:
                resp_fail = RespTokenAuthError.parse_raw(content)
                if resp_fail.code == 400006:
                    api.pop_token("company1")
                    # This should raise TokenAuthException
                    with pytest.raises(TokenAuthException):
                        raise TokenAuthException(f"wps365api token auth fail")
    
    @patch('commons.tools.wps365api.logger')
    def test_get_success(self, mock_logger):
        """Test successful get request"""
        api = Wps365api()
        api._openapi = Mock()
        api._openapi.get_token.return_value = Mock(token_type="Bearer", access_token="test_token")
        
        # Mock token that's not expired
        with patch('time.time', return_value=1000.0):
            api._tokens["company1"] = Wps365api.TokenItem(token="Bearer valid_token", expires=2000.0)
            
            with patch.object(api, 'call') as mock_call:
                mock_call.return_value = (200, b'{"code": 0, "data": {"result": "success"}}', {})
                
                result = api.get("/test", company_id="company1")
                
                # The result should come from _serve_http_result processing
                assert result == {"result": "success"}
                mock_call.assert_called_once()
    
    def test_serve_http_result_with_model(self):
        """Test _serve_http_result with response model"""
        api = Wps365api()
        
        # Create a simple test model
        class TestModel(BaseModel):
            name: str
            value: int
        
        content = json.dumps({
            "code": 0,
            "data": {"name": "test", "value": 123}
        }).encode()
        
        result = api._serve_http_result("/test", content, TestModel)
        
        assert isinstance(result, TestModel)
        assert result.name == "test"
        assert result.value == 123
    
    def test_serve_http_result_without_model(self):
        """Test _serve_http_result without response model"""
        api = Wps365api()
        
        content = json.dumps({
            "code": 0,
            "data": {"key": "value"},
            "msg": "success"
        }).encode()
        
        result = api._serve_http_result("/test", content)
        
        assert result == {"key": "value"}
    
    def test_serve_http_result_error_code(self):
        """Test _serve_http_result with error code"""
        api = Wps365api()
        
        content = json.dumps({
            "code": 1,
            "msg": "Error occurred",
            "data": None
        }).encode()
        
        with patch('commons.tools.wps365api.logger') as mock_logger:
            result = api._serve_http_result("/test", content)
            
            assert result is None
            mock_logger.error.assert_called_once()


class TestWps365apiModels:
    """Test cases for Wps365api related models"""
    
    def test_token_item_creation(self):
        """Test TokenItem model creation"""
        token_item = Wps365api.TokenItem(
            token="Bearer test_token",
            expires=1234567890.0
        )
        
        assert token_item.token == "Bearer test_token"
        assert token_item.expires == 1234567890.0
    
    def test_response_model_creation(self):
        """Test ResponseModel creation"""
        resp = ResponseModel(
            code=HTTPCODE.OK,
            msg="Success",
            data={"key": "value"}
        )
        
        assert resp.code == HTTPCODE.OK
        assert resp.msg == "Success"
        assert resp.data == {"key": "value"}
    
    def test_response_model_defaults(self):
        """Test ResponseModel default values"""
        resp = ResponseModel()
        
        assert resp.code == HTTPCODE.FAILED
        assert resp.msg == ""
        assert resp.more is None
        assert resp.data is None
    
    def test_resp_token_auth_error(self):
        """Test RespTokenAuthError model"""
        error_resp = RespTokenAuthError(
            code=400006,
            msg="Token expired",
            data="Additional error data"
        )
        
        assert error_resp.code == 400006
        assert error_resp.msg == "Token expired"
        assert error_resp.data == "Additional error data"
    
    def test_httpcode_enum(self):
        """Test HTTPCODE enum values"""
        assert HTTPCODE.OK == 0
        assert HTTPCODE.FAILED == 1
        assert isinstance(HTTPCODE.OK, int)
        assert isinstance(HTTPCODE.FAILED, int)
    
    def test_token_auth_exception(self):
        """Test TokenAuthException"""
        with pytest.raises(TokenAuthException):
            raise TokenAuthException("Test auth error")


class TestWps365apiAdditionalCoverage:
    """Additional tests to improve code coverage"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        if hasattr(Wps365api, '_instances'):
            Wps365api._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(Wps365api, '_instances'):
            Wps365api._instances.clear()
    
    @pytest.mark.asyncio
    async def test_close_method(self):
        """Test close method for both sync and async sessions"""
        api = Wps365api()
        
        # Create mock sessions
        mock_sync_sess = Mock()
        mock_async_sess = Mock()
        mock_async_sess.close = AsyncMock()
        
        api._sess = mock_sync_sess
        api._async_sess = mock_async_sess
        
        await api.close()
        
        # Verify both sessions were closed
        mock_sync_sess.close.assert_called_once()
        mock_async_sess.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_method_with_none_sessions(self):
        """Test close method when sessions are None"""
        api = Wps365api()
        api._sess = None
        api._async_sess = None
        
        # Should not raise any errors
        await api.close()
    
    def test_set_host_method(self):
        """Test set_host method"""
        api = Wps365api()
        
        with patch.object(api, '_set_origin') as mock_set_origin:
            api.set_host("https://new.host.com")
            
            assert api._host == "https://new.host.com"
            mock_set_origin.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('time.time')
    async def test_aget_token_success(self, mock_time):
        """Test aget_token method success case"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        mock_time.return_value = 1000.0
        mock_token_resp = Mock()
        mock_token_resp.token_type = "Bearer"
        mock_token_resp.access_token = "async_test_token"
        mock_token_resp.expires_in = 3600
        mock_openapi.async_get_token = AsyncMock(return_value=mock_token_resp)
        
        token = await api.aget_token("company1")
        
        assert token == "Bearer async_test_token"
        mock_openapi.async_get_token.assert_called_once_with("company1")
    
    @pytest.mark.asyncio
    async def test_aget_token_no_openapi(self):
        """Test aget_token without openapi initialization"""
        api = Wps365api()
        
        with pytest.raises(AssertionError, match="requires the initialization of the openapi"):
            await api.aget_token("company1")
    
    @pytest.mark.asyncio
    @patch('time.time')
    async def test_aget_token_failure_returns_empty(self, mock_time):
        """Test aget_token when openapi returns None"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        mock_time.return_value = 1000.0
        mock_openapi.async_get_token = AsyncMock(return_value=None)
        
        token = await api.aget_token("company1")
        
        assert token == ""
        # Verify empty token was stored
        assert "company1" in api._tokens
        assert api._tokens["company1"].token == ""
    
    @pytest.mark.asyncio
    @patch('time.time')  
    async def test_aget_token_cached_valid(self, mock_time):
        """Test aget_token with valid cached token"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        # Mock time to make token valid
        mock_time.return_value = 1000.0
        
        # Add valid cached token
        api._tokens["company1"] = Wps365api.TokenItem(
            token="Bearer cached_token", expires=2000.0
        )
        
        token = await api.aget_token("company1")
        
        assert token == "Bearer cached_token"
        # Should not call openapi since token is valid
        mock_openapi.async_get_token.assert_not_called()
    
    def test_pop_token_existing(self):
        """Test pop_token with existing token"""
        api = Wps365api()
        
        # Add a token
        api._tokens["company1"] = Wps365api.TokenItem(token="test_token", expires=2000.0)
        
        api.pop_token("company1")
        
        assert "company1" not in api._tokens
    
    def test_pop_token_nonexistent(self):
        """Test pop_token with non-existent token"""
        api = Wps365api()
        
        # Should not raise any errors
        api.pop_token("nonexistent_company")
    
    def test_call_method_additional_params(self):
        """Test call method with additional parameters"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        api._origin = "https://origin.com"
        
        with patch('requests.request') as mock_request:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.content = b'{"result": "success"}'
            mock_response.headers = {}
            mock_request.return_value = mock_response
            
            status, content, headers = api.call(
                "POST", "/test",
                body={"data": "test"},
                params={"param": "value"}, 
                headers={"Custom": "header"},
                cookies={"session": "abc"}
            )
            
            assert status == 200
            assert content == b'{"result": "success"}'
            mock_request.assert_called_once()
    
    def test_call_method_with_params_processing(self):
        """Test call method processes parameters correctly"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        
        with patch('requests.request') as mock_request, \
             patch.object(api, '_noraml_json_params', return_value={"processed": "params"}) as mock_params:
            
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.content = b'{"processed": true}'
            mock_response.headers = {}
            mock_request.return_value = mock_response
            
            status, content, headers = api.call(
                "GET", "/test",
                params={"original": "param"}
            )
            
            assert status == 200
            # Verify params were processed
            mock_params.assert_called_once_with({"original": "param"})
    
    def test_call_method_sync_basic(self):
        """Test synchronous call method"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        
        with patch('requests.request') as mock_request:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.content = b'{"result": "sync_success"}'
            mock_response.headers = {"Content-Type": "application/json"}
            mock_request.return_value = mock_response
            
            status, content, headers = api.call("GET", "/sync-test")
            
            assert status == 200
            assert content == b'{"result": "sync_success"}'
            mock_request.assert_called_once()
    
    def test_call_sync_with_session(self):
        """Test call method with existing session"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        
        # Mock session
        mock_session = Mock()
        mock_response = Mock()
        mock_response.status_code = 201
        mock_response.content = b'{"created": true}'
        mock_response.headers = {}
        mock_session.request.return_value = mock_response
        api._sess = mock_session
        
        status, content, headers = api.call("POST", "/create", body={"name": "test"})
        
        assert status == 201
        assert content == b'{"created": true}'
        mock_session.request.assert_called_once()
    
    def test_call_sync_with_form_data(self):
        """Test call method with form data"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        
        # Mock form data
        mock_form_data = Mock()
        mock_form_data.content_type = "multipart/form-data"
        mock_form_data.to_string.return_value = "form_data_string"
        
        with patch('requests.request') as mock_request:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.content = b'{"uploaded": true}'
            mock_response.headers = {}
            mock_request.return_value = mock_response
            
            status, content, headers = api.call("POST", "/upload", form_data=mock_form_data)
            
            assert status == 200
            # Verify form data was processed
            mock_form_data.to_string.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_acall_method_basic(self):
        """Test asynchronous call method (acall)"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        
        # Mock async session
        mock_async_sess = Mock()
        mock_response = Mock()
        mock_response.status = 200
        mock_response.content.read = AsyncMock(return_value=b'{"async": true}')
        mock_response.headers = {"X-Async": "true"}
        
        mock_context = Mock()
        mock_context.__aenter__ = AsyncMock(return_value=mock_response)
        mock_context.__aexit__ = AsyncMock(return_value=None)
        mock_async_sess.request.return_value = mock_context
        
        api._async_sess = mock_async_sess
        
        status, content, headers = await api.acall("GET", "/async-test")
        
        assert status == 200
        assert content == b'{"async": true}'
        mock_async_sess.request.assert_called_once()
    
    @pytest.mark.asyncio  
    async def test_acall_with_all_params(self):
        """Test acall method with all parameters"""
        api = Wps365api()
        api._woa_custom_ak = "test_ak"
        api._origin = "https://origin.com"
        
        # Mock form data
        mock_form_data = Mock()
        mock_form_data.content_type = "multipart/form-data"
        mock_form_data.to_string.return_value = "async_form_data"
        
        # Mock async session
        mock_async_sess = Mock()
        mock_response = Mock()
        mock_response.status = 201
        mock_response.content.read = AsyncMock(return_value=b'{"created": "async"}')
        mock_response.headers = {}
        
        mock_context = Mock()
        mock_context.__aenter__ = AsyncMock(return_value=mock_response)
        mock_context.__aexit__ = AsyncMock(return_value=None)
        mock_async_sess.request.return_value = mock_context
        
        api._async_sess = mock_async_sess
        
        status, content, headers = await api.acall(
            "POST", "/async-create",
            body={"name": "async_test"},
            form_data=mock_form_data,
            params={"filter": "active"},
            headers={"Custom": "header"},
            cookies={"session": "abc"},
            timeout_second=120
        )
        
        assert status == 201
        assert content == b'{"created": "async"}'
        mock_form_data.to_string.assert_called_once()
    
    def test_serve_http_result_with_model_success(self):
        """Test _serve_http_result with response model"""
        api = Wps365api()
        
        class TestModel(BaseModel):
            name: str
            value: int
        
        content = json.dumps({
            "code": 0,
            "data": {"name": "test", "value": 123}
        }).encode()
        
        result = api._serve_http_result("/test", content, TestModel)
        
        assert isinstance(result, TestModel)
        assert result.name == "test"
        assert result.value == 123
    
    def test_serve_http_result_without_model_success(self):
        """Test _serve_http_result without response model"""
        api = Wps365api()
        
        content = json.dumps({
            "code": 0,
            "data": {"key": "value"}
        }).encode()
        
        result = api._serve_http_result("/test", content)
        
        assert result == {"key": "value"}
    
    def test_serve_http_result_error_code(self):
        """Test _serve_http_result with error code"""
        api = Wps365api()
        
        content = json.dumps({
            "code": 1,
            "msg": "Error occurred"
        }).encode()
        
        with patch('commons.logger.business_log.logger.error') as mock_log_error:
            result = api._serve_http_result("/test", content)
            
            assert result is None
            mock_log_error.assert_called_once()
    
    def test_noraml_json_params(self):
        """Test _noraml_json_params method"""
        api = Wps365api()
        
        # Test with various data types
        test_params = {
            "string": "test",
            "int": 123,
            "bool": True,
            "float": 45.6,
            "none": None,
            "list": [1, 2, 3],
            "dict": {"nested": "value"}
        }
        
        result = api._noraml_json_params(test_params)
        
        # Check actual behavior - some values are filtered or converted
        assert result["string"] == "test"
        assert "int" in result
        assert "bool" in result  
        assert "float" in result
        # None values are filtered out by the implementation
        assert "none" not in result
        assert "list" in result
        assert "dict" in result
    
    def test_async_serve_http_result_with_model(self):
        """Test _async_serve_http_result with response model"""
        api = Wps365api()
        
        class TestModel(BaseModel):
            name: str
            value: int
        
        content = json.dumps({
            "code": 0,
            "data": {"name": "async_test", "value": 456}
        }).encode()
        
        result = api._async_serve_http_result("/test", content, TestModel)
        
        assert isinstance(result, TestModel)
        assert result.name == "async_test"
        assert result.value == 456
    
    def test_async_serve_http_result_without_model(self):
        """Test _async_serve_http_result without response model"""
        api = Wps365api()
        
        content = json.dumps({"result": "async_success"}).encode()
        
        result = api._async_serve_http_result("/test", content)
        
        assert result == {"result": "async_success"}
    
    def test_get_method_success(self):
        """Test get method success case"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call:
            mock_call.return_value = (200, json.dumps({
                "code": 0,
                "data": {"result": "get_success"}
            }).encode(), {})
            
            result = api.get("/test", wps_sid="test_sid")
            
            assert result == {"result": "get_success"}
            mock_call.assert_called_once()
    
    def test_get_method_with_company_id_sync(self):
        """Test get method with company_id (sync token retrieval)"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        with patch('time.time', return_value=1000.0), \
             patch.object(api, 'call') as mock_call:
            
            mock_token_resp = Mock()
            mock_token_resp.token_type = "Bearer"
            mock_token_resp.access_token = "sync_token"
            mock_token_resp.expires_in = 3600
            mock_openapi.get_token.return_value = mock_token_resp
            
            mock_call.return_value = (200, json.dumps({
                "code": 0,
                "data": {"authorized": True}
            }).encode(), {})
            
            result = api.get("/test", wps_sid="test_sid", company_id="company1")
            
            assert result == {"authorized": True}
            mock_openapi.get_token.assert_called_once_with("company1")
    
    def test_get_method_error_handling(self):
        """Test get method error handling"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call:
            mock_call.return_value = (400, b'{"error": "bad request"}', {})
            
            result = api.get("/test", wps_sid="test_sid")
            
            assert result is None
    
    def test_get_method_exception_handling(self):
        """Test get method exception handling"""
        api = Wps365api()
        
        with patch.object(api, 'call', side_effect=Exception("Network error")), \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            result = api.get("/test", wps_sid="test_sid")
            
            assert result is None
            mock_log_error.assert_called()
    
    @pytest.mark.asyncio
    async def test_aget_method_success(self):
        """Test async get method (aget) success case"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall:
            mock_acall.return_value = (200, json.dumps({
                "code": 0,
                "data": {"async_result": "success"}
            }).encode(), {})
            
            result = await api.aget("/test", wps_sid="test_sid")
            
            assert result == {"async_result": "success"}
            mock_acall.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aget_method_with_company_id(self):
        """Test async get method with company_id"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        with patch('time.time', return_value=1000.0), \
             patch.object(api, 'acall') as mock_acall:
            
            mock_token_resp = Mock()
            mock_token_resp.token_type = "Bearer"
            mock_token_resp.access_token = "async_token"
            mock_token_resp.expires_in = 3600
            mock_openapi.async_get_token = AsyncMock(return_value=mock_token_resp)
            
            mock_acall.return_value = (200, json.dumps({
                "code": 0,
                "data": {"authenticated": True}
            }).encode(), {})
            
            result = await api.aget("/test", wps_sid="test_sid", company_id="company1")
            
            assert result == {"authenticated": True}
            mock_openapi.async_get_token.assert_called_once_with("company1")
    
    @pytest.mark.asyncio
    async def test_aget_method_403_error_handling(self):
        """Test async get method with 403 error handling (without TokenAuthException)"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            # Mock 403 response
            mock_acall.return_value = (403, json.dumps({
                "code": 403001,  # Different code to avoid TokenAuthException path
                "message": "Forbidden"
            }).encode(), {})
            
            result = await api.aget("/test", wps_sid="test_sid", company_id="company1")
            
            assert result is None
            mock_log_error.assert_called()
    
    @pytest.mark.asyncio
    async def test_aget_method_exception_handling(self):
        """Test async get method exception handling"""
        api = Wps365api()
        
        with patch.object(api, 'acall', side_effect=Exception("Async network error")), \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            result = await api.aget("/test", wps_sid="test_sid")
            
            assert result is None
            mock_log_error.assert_called()
    
    def test_post_method_success(self):
        """Test post method success case"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call:
            mock_call.return_value = (200, json.dumps({
                "code": 0,
                "data": {"id": 123, "created": True}
            }).encode(), {})
            
            result = api.post("/create", wps_sid="test_sid", body={"name": "test"})
            
            assert result == {"id": 123, "created": True}
            mock_call.assert_called_once()
    
    def test_post_method_with_company_id(self):
        """Test post method with company_id"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        with patch('time.time', return_value=1000.0), \
             patch.object(api, 'call') as mock_call:
            
            mock_token_resp = Mock()
            mock_token_resp.token_type = "Bearer"
            mock_token_resp.access_token = "post_token"
            mock_token_resp.expires_in = 3600
            mock_openapi.get_token.return_value = mock_token_resp
            
            mock_call.return_value = (200, json.dumps({
                "code": 0,
                "data": {"posted": True}
            }).encode(), {})
            
            result = api.post("/create", wps_sid="test_sid", body={"data": "test"}, company_id="company1")
            
            assert result == {"posted": True}
            mock_openapi.get_token.assert_called_once_with("company1")
    
    def test_post_method_403_error_file_corrupted(self):
        """Test post method with 403 error for file corruption - caught by exception handler"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            mock_call.return_value = (403, json.dumps({
                "code": *********,
                "message": "File corrupted"
            }).encode(), {})
            
            # The ValueError is caught by the exception handler, so result should be None
            result = api.post("/upload", wps_sid="test_sid", body={"file": "data"})
            
            assert result is None
            # Check that the error was logged twice (once for ValueError, once for general error)
            assert mock_log_error.call_count >= 1
    
    def test_post_method_403_token_auth_error(self):
        """Test post method with 403 token auth error - caught by exception handler"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi  # Need openapi for get_token to work
        
        # Add token to be popped
        api._tokens["company1"] = Wps365api.TokenItem(token="Bearer old_token", expires=2000.0)
        
        with patch.object(api, 'call') as mock_call, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            mock_call.return_value = (403, json.dumps({
                "code": 400006,
                "message": "Token expired"
            }).encode(), {})
            
            # The TokenAuthException is caught by the exception handler, so result should be None
            result = api.post("/create", wps_sid="test_sid", company_id="company1")
            
            assert result is None
            # Check that the error was logged
            assert mock_log_error.call_count >= 1
    
    def test_post_method_error_handling(self):
        """Test post method general error handling"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            mock_call.return_value = (500, b'{"error": "server error"}', {})
            
            result = api.post("/create", wps_sid="test_sid", body={"data": "test"})
            
            assert result is None
            mock_log_error.assert_called()
    
    def test_post_method_exception_handling(self):
        """Test post method exception handling"""
        api = Wps365api()
        
        with patch.object(api, 'call', side_effect=Exception("Post network error")), \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            result = api.post("/create", wps_sid="test_sid", body={"data": "test"})
            
            assert result is None
            mock_log_error.assert_called()
    
    @pytest.mark.asyncio
    async def test_apost_method_success(self):
        """Test async post method (apost) success case"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall:
            mock_acall.return_value = (200, json.dumps({
                "code": 0,
                "data": {"async_posted": True}
            }).encode(), {})
            
            result = await api.apost("/async-create", wps_sid="test_sid", body={"name": "async_test"})
            
            assert result == {"async_posted": True}
            mock_acall.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_apost_method_with_company_id(self):
        """Test async post method with company_id"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        with patch('time.time', return_value=1000.0), \
             patch.object(api, 'acall') as mock_acall:
            
            mock_token_resp = Mock()
            mock_token_resp.token_type = "Bearer"
            mock_token_resp.access_token = "async_post_token"
            mock_token_resp.expires_in = 3600
            mock_openapi.async_get_token = AsyncMock(return_value=mock_token_resp)
            
            mock_acall.return_value = (200, json.dumps({
                "code": 0,
                "data": {"async_created": True}
            }).encode(), {})
            
            result = await api.apost("/async-create", wps_sid="test_sid", 
                                   body={"data": "async_test"}, company_id="company1")
            
            assert result == {"async_created": True}
            mock_openapi.async_get_token.assert_called_once_with("company1")
    
    def test_serve_http_result_with_model_error(self):
        """Test _serve_http_result with model when response has error code"""
        api = Wps365api()
        
        class TestModel(BaseModel):
            name: str
        
        # Response with error code
        content = json.dumps({
            "code": 1,
            "msg": "Server error"
        }).encode()
        
        with patch('commons.logger.business_log.logger.error') as mock_log_error:
            result = api._serve_http_result("/test", content, TestModel)
            
            assert result is None
            mock_log_error.assert_called_once()
    
    def test_serve_http_result_use_async_func_error(self):
        """Test _serve_http_result with use_async_func=True and error result"""
        api = Wps365api()
        
        # Response with error result
        content = json.dumps({
            "result": "ERROR",
            "message": "Async operation failed"
        }).encode()
        
        with patch('commons.logger.business_log.logger.error') as mock_log_error:
            result = api._serve_http_result("/test", content, None, use_async_func=True)
            
            assert result is None
            mock_log_error.assert_called_once()
    
    def test_serve_http_result_use_async_func_success(self):
        """Test _serve_http_result with use_async_func=True and success result"""
        api = Wps365api()
        
        # Response with success result
        content = json.dumps({
            "result": "OK",
            "data": {"async_success": True}
        }).encode()
        
        result = api._serve_http_result("/test", content, None, use_async_func=True)
        
        assert result == {"result": "OK", "data": {"async_success": True}}
    
    def test_async_serve_http_result_with_model_error(self):
        """Test _async_serve_http_result with model when response has error code"""
        api = Wps365api()
        
        class TestModel(BaseModel):
            name: str
        
        # Response with error code
        content = json.dumps({
            "code": 1,
            "msg": "Async server error"
        }).encode()
        
        with patch('commons.logger.business_log.logger.error') as mock_log_error:
            result = api._async_serve_http_result("/test", content, TestModel)
            
            assert result is None
            mock_log_error.assert_called_once()

    
    def test_get_method_with_kso_sid(self):
        """Test get method with kso_sid parameter"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call:
            mock_call.return_value = (200, json.dumps({
                "code": 0,
                "data": {"kso_result": True}
            }).encode(), {})
            
            result = api.get("/test", wps_sid="test_wps_sid", kso_sid="test_kso_sid")
            
            assert result == {"kso_result": True}
            # Verify both cookies were passed
            call_args = mock_call.call_args
            cookies = call_args[1]["cookies"]
            assert cookies["wps_sid"] == "test_wps_sid"
            assert cookies["kso_sid"] == "test_kso_sid"
    
    def test_post_method_with_kso_sid(self):
        """Test post method with kso_sid parameter"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call:
            mock_call.return_value = (200, json.dumps({
                "code": 0,
                "data": {"kso_post_result": True}
            }).encode(), {})
            
            result = api.post("/create", wps_sid="test_wps_sid", kso_sid="test_kso_sid", 
                            body={"test": "data"})
            
            assert result == {"kso_post_result": True}
            # Verify both cookies were passed
            call_args = mock_call.call_args
            cookies = call_args[1]["cookies"]
            assert cookies["wps_sid"] == "test_wps_sid"
            assert cookies["kso_sid"] == "test_kso_sid"
    
    @pytest.mark.asyncio
    async def test_aget_method_with_kso_sid(self):
        """Test async get method with kso_sid parameter"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall:
            mock_acall.return_value = (200, json.dumps({
                "code": 0,
                "data": {"async_kso_result": True}
            }).encode(), {})
            
            result = await api.aget("/test", wps_sid="test_wps_sid", kso_sid="test_kso_sid")
            
            assert result == {"async_kso_result": True}
            # Verify both cookies were passed
            call_args = mock_acall.call_args
            cookies = call_args[1]["cookies"]
            assert cookies["wps_sid"] == "test_wps_sid"
            assert cookies["kso_sid"] == "test_kso_sid"
    
    @pytest.mark.asyncio
    async def test_apost_method_with_kso_sid(self):
        """Test async post method with kso_sid parameter"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall:
            mock_acall.return_value = (200, json.dumps({
                "code": 0,
                "data": {"async_kso_post_result": True}
            }).encode(), {})
            
            result = await api.apost("/create", wps_sid="test_wps_sid", kso_sid="test_kso_sid",
                                  body={"async_test": "data"})
            
            assert result == {"async_kso_post_result": True}
            # Verify both cookies were passed
            call_args = mock_acall.call_args
            cookies = call_args[1]["cookies"]
            assert cookies["wps_sid"] == "test_wps_sid"
            assert cookies["kso_sid"] == "test_kso_sid"
    
    @pytest.mark.asyncio
    async def test_apost_method_403_error_file_corrupted(self):
        """Test async post method with 403 file corruption error"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            mock_acall.return_value = (403, json.dumps({
                "code": *********,
                "message": "File corrupted"
            }).encode(), {})
            
            # The ValueError is caught by the exception handler, so result should be None
            result = await api.apost("/upload", wps_sid="test_sid", body={"file": "data"})
            
            assert result is None
            assert mock_log_error.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_apost_method_403_token_auth_error(self):
        """Test async post method with 403 token auth error"""
        api = Wps365api()
        mock_openapi = Mock()
        api._openapi = mock_openapi
        
        # Add token to be popped
        api._tokens["company1"] = Wps365api.TokenItem(token="Bearer old_token", expires=2000.0)
        
        with patch.object(api, 'acall') as mock_acall, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            mock_acall.return_value = (403, json.dumps({
                "code": 400006,
                "message": "Token expired"
            }).encode(), {})
            
            # The TokenAuthException is caught by the exception handler, so result should be None
            result = await api.apost("/create", wps_sid="test_sid", company_id="company1")
            
            assert result is None
            assert mock_log_error.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_apost_method_error_handling(self):
        """Test async post method general error handling"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            mock_acall.return_value = (500, b'{"error": "async server error"}', {})
            
            result = await api.apost("/create", wps_sid="test_sid", body={"data": "test"})
            
            assert result is None
            mock_log_error.assert_called()
    
    @pytest.mark.asyncio
    async def test_apost_method_exception_handling(self):
        """Test async post method exception handling"""
        api = Wps365api()
        
        with patch.object(api, 'acall', side_effect=Exception("Async post network error")), \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            result = await api.apost("/create", wps_sid="test_sid", body={"data": "test"})
            
            assert result is None
            mock_log_error.assert_called()
    
    @pytest.mark.asyncio
    async def test_aget_method_token_auth_exception_direct(self):
        """Test aget method with direct TokenAuthException triggering"""
        api = Wps365api()
        api._openapi = Mock()
        
        # Add token to be popped
        api._tokens["company1"] = Wps365api.TokenItem(token="Bearer old_token", expires=2000.0)
        
        with patch.object(api, 'acall') as mock_acall, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            # Mock RespTokenAuthError.parse_raw to return the expected object
            with patch('commons.tools.wps365api.RespTokenAuthError.parse_raw') as mock_parse_raw:
                mock_resp_fail = Mock()
                mock_resp_fail.code = 400006
                mock_parse_raw.return_value = mock_resp_fail
                
                mock_acall.return_value = (403, json.dumps({
                    "code": 400006,
                    "message": "Token expired"
                }).encode(), {})
                
                # This should trigger the TokenAuthException path, but it gets caught
                result = await api.aget("/test", wps_sid="test_sid", company_id="company1")
                
                assert result is None
                # Verify token was popped and error was logged
                assert mock_log_error.call_count >= 1
    
    def test_post_method_token_auth_exception_direct(self):
        """Test post method with direct TokenAuthException triggering"""  
        api = Wps365api()
        api._openapi = Mock()
        
        # Add token to be popped
        api._tokens["company1"] = Wps365api.TokenItem(token="Bearer old_token", expires=2000.0)
        
        with patch.object(api, 'call') as mock_call, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            # Mock RespTokenAuthError.parse_raw to return the expected object
            with patch('commons.tools.wps365api.RespTokenAuthError.parse_raw') as mock_parse_raw:
                mock_resp_fail = Mock()
                mock_resp_fail.code = 400006
                mock_parse_raw.return_value = mock_resp_fail
                
                mock_call.return_value = (403, json.dumps({
                    "code": 400006,
                    "message": "Token expired"
                }).encode(), {})
                
                # This should trigger the TokenAuthException path, but it gets caught
                result = api.post("/create", wps_sid="test_sid", company_id="company1")
                
                assert result is None
                # Verify error was logged
                assert mock_log_error.call_count >= 1
    
    def test_post_method_file_corruption_exception_direct(self):
        """Test post method with direct ValueError for file corruption"""
        api = Wps365api()
        
        with patch.object(api, 'call') as mock_call, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            # Mock RespTokenAuthError.parse_raw to return the expected object  
            with patch('commons.tools.wps365api.RespTokenAuthError.parse_raw') as mock_parse_raw:
                mock_resp_fail = Mock()
                mock_resp_fail.code = *********
                mock_parse_raw.return_value = mock_resp_fail
                
                mock_call.return_value = (403, json.dumps({
                    "code": *********,
                    "message": "File corrupted"
                }).encode(), {})
                
                # This should trigger the ValueError path, but it gets caught
                result = api.post("/upload", wps_sid="test_sid", body={"file": "data"})
                
                assert result is None
                # Verify error was logged
                assert mock_log_error.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_apost_method_file_corruption_exception_direct(self):
        """Test apost method with direct ValueError for file corruption"""
        api = Wps365api()
        
        with patch.object(api, 'acall') as mock_acall, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            # Mock RespTokenAuthError.parse_raw to return the expected object
            with patch('commons.tools.wps365api.RespTokenAuthError.parse_raw') as mock_parse_raw:
                mock_resp_fail = Mock()
                mock_resp_fail.code = *********
                mock_parse_raw.return_value = mock_resp_fail
                
                mock_acall.return_value = (403, json.dumps({
                    "code": *********,
                    "message": "File corrupted"
                }).encode(), {})
                
                # This should trigger the ValueError path, but it gets caught  
                result = await api.apost("/upload", wps_sid="test_sid", body={"file": "data"})
                
                assert result is None
                # Verify error was logged
                assert mock_log_error.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_apost_method_token_auth_exception_direct(self):
        """Test apost method with direct TokenAuthException triggering"""
        api = Wps365api()
        api._openapi = Mock()
        
        # Add token to be popped
        api._tokens["company1"] = Wps365api.TokenItem(token="Bearer old_token", expires=2000.0)
        
        with patch.object(api, 'acall') as mock_acall, \
             patch('commons.logger.business_log.logger.error') as mock_log_error:
            
            # Mock RespTokenAuthError.parse_raw to return the expected object
            with patch('commons.tools.wps365api.RespTokenAuthError.parse_raw') as mock_parse_raw:
                mock_resp_fail = Mock()
                mock_resp_fail.code = 400006
                mock_parse_raw.return_value = mock_resp_fail
                
                mock_acall.return_value = (403, json.dumps({
                    "code": 400006,
                    "message": "Token expired"
                }).encode(), {})
                
                # This should trigger the TokenAuthException path, but it gets caught
                result = await api.apost("/create", wps_sid="test_sid", company_id="company1")
                
                assert result is None
                # Verify error was logged
                assert mock_log_error.call_count >= 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
