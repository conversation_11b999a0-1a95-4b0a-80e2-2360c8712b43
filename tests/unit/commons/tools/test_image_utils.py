import pytest
import tempfile
import os
from io import BytesIO
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from PIL import Image
from commons.tools.image_utils import image_compress


class TestImageUtils:
    """Test cases for image utility functions"""
    
    def create_test_image(self, mode='RGB', size=(100, 100), color=(255, 0, 0)):
        """Helper method to create test images"""
        image = Image.new(mode, size, color)
        bio = BytesIO()
        if mode == 'RGBA':
            image.save(bio, format='PNG')
        else:
            image.save(bio, format='JPEG')
        bio.seek(0)
        return bio.getvalue()
    
    def test_image_compress_from_bytes(self):
        """Test image compression from bytes input"""
        # Create a test image
        test_image_bytes = self.create_test_image(size=(200, 200))
        
        # Compress the image
        compressed = image_compress(test_image_bytes, res_size=50, start_quality=80)
        
        # Check that result is bytes
        assert isinstance(compressed, bytes)
        # Check that compressed size is smaller than or similar to original (compression may not always reduce size for small images)
        assert len(compressed) > 0
        
        # Verify the compressed image can be opened
        compressed_image = Image.open(BytesIO(compressed))
        assert compressed_image.format == 'JPEG'
        assert compressed_image.mode == 'RGB'
    
    def test_image_compress_from_file_path(self):
        """Test image compression from file path"""
        # Create a temporary test image file
        test_image_bytes = self.create_test_image(size=(150, 150))
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
            temp_file.write(test_image_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Compress from file path
            compressed = image_compress(temp_file_path, res_size=30)
            
            assert isinstance(compressed, bytes)
            assert len(compressed) > 0
            
            # Verify compressed image
            compressed_image = Image.open(BytesIO(compressed))
            assert compressed_image.format == 'JPEG'
        finally:
            # Clean up
            os.unlink(temp_file_path)
    
    def test_image_compress_rgba_to_rgb_conversion(self):
        """Test RGBA to RGB conversion during compression"""
        # Create RGBA test image
        test_image_bytes = self.create_test_image(mode='RGBA', size=(100, 100), color=(255, 0, 0, 128))
        
        compressed = image_compress(test_image_bytes, res_size=50)
        
        # Verify the result is RGB JPEG
        compressed_image = Image.open(BytesIO(compressed))
        assert compressed_image.mode == 'RGB'
        assert compressed_image.format == 'JPEG'
    
    def test_image_compress_with_alpha_channel_error(self):
        """Test RGBA conversion with alpha channel extraction error"""
        with patch('PIL.Image.open') as mock_open:
            # Create a mock image that raises exception when accessing alpha
            mock_image = Mock()
            mock_image.mode = 'RGBA'
            mock_image.size = (100, 100)
            mock_image.split.side_effect = Exception("Alpha channel error")
            mock_image.convert.return_value = mock_image
            
            # Mock the background image creation
            mock_bg = Mock()
            mock_bg.paste = Mock()
            
            with patch('PIL.Image.new', return_value=mock_bg):
                mock_open.return_value = mock_image
                
                # Mock the save operation
                with patch.object(mock_image, 'save'):
                    test_bytes = b'fake_image_data'
                    # This should handle the alpha channel error gracefully
                    result = image_compress(test_bytes)
                    
                    assert isinstance(result, bytes)
    
    def test_image_compress_rgba_conversion_exception(self):
        """Test exception handling during RGBA to RGB conversion"""
        with patch('PIL.Image.open') as mock_open:
            mock_image = Mock()
            mock_image.mode = 'RGBA'
            mock_image.size = (100, 100)
            mock_image.split.side_effect = Exception("Split error")
            mock_open.return_value = mock_image
            
            with patch('PIL.Image.new') as mock_new:
                mock_new.side_effect = Exception("Background creation error")
                
                test_bytes = b'fake_image_data'
                
                with pytest.raises(Exception, match="Background creation error"):
                    image_compress(test_bytes)
    
    def test_image_compress_quality_iteration(self):
        """Test quality iteration logic"""
        test_image_bytes = self.create_test_image(size=(300, 300))
        
        # Test with specific quality parameters
        compressed = image_compress(
            test_image_bytes,
            res_size=20,  # Very small target
            start_quality=90,
            end_quality=10,
            quality_step=20
        )
        
        assert isinstance(compressed, bytes)
        # Should have compressed to target size or minimum quality
        compressed_size_kb = len(compressed) / 1024
        assert compressed_size_kb <= 20 or True  # Either meets size or reached min quality
    
    def test_image_compress_reaches_end_quality(self):
        """Test compression stops at end quality"""
        test_image_bytes = self.create_test_image(size=(500, 500))
        
        # Use parameters that will force reaching end quality
        compressed = image_compress(
            test_image_bytes,
            res_size=1,  # Impossibly small target
            start_quality=50,
            end_quality=10,
            quality_step=10
        )
        
        assert isinstance(compressed, bytes)
        assert len(compressed) > 0
    
    def test_image_compress_reaches_target_size(self):
        """Test compression stops when target size is reached"""
        test_image_bytes = self.create_test_image(size=(200, 200))
        
        # Use reasonable parameters
        compressed = image_compress(
            test_image_bytes,
            res_size=100,  # Achievable target
            start_quality=80,
            end_quality=10,
            quality_step=10
        )
        
        compressed_size_kb = len(compressed) / 1024
        # Should be close to or under target size
        assert compressed_size_kb <= 100 or compressed_size_kb <= 120  # Allow some tolerance
    
    def test_image_compress_custom_parameters(self):
        """Test compression with custom parameters"""
        test_image_bytes = self.create_test_image(size=(150, 150))
        
        compressed = image_compress(
            test_image_bytes,
            res_size=80,
            start_quality=70,
            end_quality=20,
            quality_step=15
        )
        
        assert isinstance(compressed, bytes)
        assert len(compressed) > 0
    
    def test_image_compress_single_quality_step(self):
        """Test compression with single quality step"""
        test_image_bytes = self.create_test_image(size=(100, 100))
        
        compressed = image_compress(
            test_image_bytes,
            res_size=50,
            start_quality=30,
            end_quality=20,
            quality_step=50  # Large step, will go directly to end
        )
        
        assert isinstance(compressed, bytes)
        assert len(compressed) > 0
    
    @patch('logging.debug')
    def test_image_compress_logging(self, mock_debug):
        """Test that compression logs debug information"""
        test_image_bytes = self.create_test_image(size=(100, 100))
        
        image_compress(test_image_bytes, res_size=50)
        
        # Should have logged base size and compressed size
        assert mock_debug.call_count >= 2
        
        # Check log messages contain size information
        log_calls = [call.args[0] for call in mock_debug.call_args_list]
        assert any("base size:" in msg for msg in log_calls)
        assert any("compressed size:" in msg for msg in log_calls)
    
    def test_image_compress_file_not_found(self):
        """Test behavior with non-existent file path"""
        non_existent_path = "/non/existent/file.jpg"
        
        with pytest.raises(FileNotFoundError):
            image_compress(non_existent_path)
    
    def test_image_compress_invalid_image_bytes(self):
        """Test behavior with invalid image bytes"""
        invalid_bytes = b"not_an_image"
        
        with pytest.raises(Exception):  # PIL will raise various exceptions
            image_compress(invalid_bytes)
    
    def test_image_compress_empty_bytes(self):
        """Test behavior with empty bytes"""
        empty_bytes = b""
        
        with pytest.raises(Exception):  # PIL will raise exception
            image_compress(empty_bytes)
    
    @patch('PIL.Image.open')
    def test_image_compress_pil_exception_handling(self, mock_open):
        """Test PIL exception handling"""
        mock_open.side_effect = Exception("PIL Error")
        
        test_bytes = b"fake_image_data"
        
        with pytest.raises(Exception, match="PIL Error"):
            image_compress(test_bytes)
    
    def test_image_compress_rgba_with_valid_alpha(self):
        """Test RGBA image with valid alpha channel"""
        # Create RGBA image with alpha channel
        rgba_image = Image.new('RGBA', (100, 100), (255, 0, 0, 128))
        bio = BytesIO()
        rgba_image.save(bio, format='PNG')
        test_bytes = bio.getvalue()
        
        compressed = image_compress(test_bytes, res_size=50)
        
        # Should successfully convert to RGB
        result_image = Image.open(BytesIO(compressed))
        assert result_image.mode == 'RGB'
    
    def test_image_compress_default_parameters(self):
        """Test compression with default parameters"""
        test_image_bytes = self.create_test_image(size=(200, 200))
        
        # Use all default parameters
        compressed = image_compress(test_image_bytes)
        
        assert isinstance(compressed, bytes)
        assert len(compressed) > 0
        
        # With defaults: res_size=200, should compress reasonably
        compressed_size_kb = len(compressed) / 1024
        assert compressed_size_kb > 0
    
    def test_image_compress_very_small_image(self):
        """Test compression of very small image"""
        test_image_bytes = self.create_test_image(size=(10, 10))
        
        compressed = image_compress(test_image_bytes, res_size=100)
        
        # Small image should compress easily to under target
        assert isinstance(compressed, bytes)
        compressed_size_kb = len(compressed) / 1024
        assert compressed_size_kb <= 100
    
    def test_image_compress_already_small_image(self):
        """Test compression of image already smaller than target"""
        small_image_bytes = self.create_test_image(size=(50, 50))
        
        compressed = image_compress(small_image_bytes, res_size=1000)  # Large target
        
        # Should compress but likely already under target
        assert isinstance(compressed, bytes)
        assert len(compressed) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
