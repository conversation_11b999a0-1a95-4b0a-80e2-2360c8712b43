import pytest
import asyncio
import warnings
from unittest.mock import patch, Mock, AsyncMock
from commons.tools.kafka_metrics import KafkaConsumerMetrics


class TestKafkaConsumerMetrics:
    """Test cases for KafkaConsumerMetrics class"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        if hasattr(KafkaConsumerMetrics, '_instances'):
            KafkaConsumerMetrics._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(KafkaConsumerMetrics, '_instances'):
            KafkaConsumerMetrics._instances.clear()
    
    def test_singleton_behavior(self):
        """Test singleton pattern"""
        metrics1 = KafkaConsumerMetrics()
        metrics2 = KafkaConsumerMetrics()
        
        assert metrics1 is metrics2
        assert id(metrics1) == id(metrics2)
    
    def test_init_state(self):
        """Test initial state"""
        metrics = KafkaConsumerMetrics()
        
        assert metrics.bootstrap_servers is None
    
    def test_init_with_servers(self):
        """Test initialization with bootstrap servers"""
        metrics = KafkaConsumerMetrics()
        servers = ["localhost:9092", "kafka2:9092"]
        
        metrics.init(servers)
        
        assert metrics.bootstrap_servers == servers
    
    @pytest.mark.asyncio
    @patch('commons.tools.kafka_metrics.KafkaAdminClient')
    @patch('commons.tools.kafka_metrics.KafkaConsumerDao')
    async def test_get_lag_success(self, mock_consumer_dao_class, mock_admin_client_class):
        """Test successful lag calculation"""
        metrics = KafkaConsumerMetrics()
        metrics.init(["localhost:9092"])
        
        # Mock admin client
        mock_admin_client = Mock()
        mock_admin_client_class.return_value.get_client.return_value = mock_admin_client
        
        # Mock consumer
        mock_consumer = Mock()
        mock_consumer_dao = Mock()
        mock_consumer_dao.get_consumer = AsyncMock(return_value=mock_consumer)
        mock_consumer_dao_class.return_value = mock_consumer_dao
        
        # Mock partition methods
        with patch.object(metrics, '_get_partition_ids') as mock_get_partitions:
            with patch.object(metrics, '_create_topic_partitions') as mock_create_partitions:
                with patch.object(metrics, '_get_committed_offsets') as mock_get_committed:
                    with patch.object(metrics, '_get_end_offsets') as mock_get_end:
                        with patch.object(metrics, '_close_consumer') as mock_close:
                            
                            # Setup mocks
                            mock_get_partitions.return_value = [0, 1, 2]
                            mock_topic_partitions = [Mock(), Mock(), Mock()]
                            mock_create_partitions.return_value = mock_topic_partitions
                            mock_get_committed.return_value = {0: 100, 1: 200, 2: 300}  # committed offsets
                            mock_get_end.return_value = {0: 150, 1: 250, 2: 350}  # end offsets
                            
                            lag = await metrics.get_lag("test_topic", "test_group")
                            
                            # Lag should be sum of (end - committed) for each partition
                            # (150-100) + (250-200) + (350-300) = 50 + 50 + 50 = 150
                            assert lag == 150
                            
                            # Verify calls
                            mock_get_partitions.assert_called_once_with(mock_admin_client, "test_topic")
                            mock_create_partitions.assert_called_once_with([0, 1, 2], "test_topic")
                            mock_consumer.assign.assert_called_once_with(mock_topic_partitions)
                            mock_close.assert_called_once_with(mock_consumer)
    
    @pytest.mark.asyncio
    @patch('commons.tools.kafka_metrics.KafkaAdminClient')
    @patch('commons.tools.kafka_metrics.KafkaConsumerDao')
    async def test_get_lag_exception(self, mock_consumer_dao_class, mock_admin_client_class):
        """Test get_lag exception handling"""
        metrics = KafkaConsumerMetrics()
        metrics.init(["localhost:9092"])
        
        # Mock admin client
        mock_admin_client = Mock()
        mock_admin_client_class.return_value.get_client.return_value = mock_admin_client
        
        # Mock consumer dao to raise exception
        mock_consumer_dao_class.side_effect = Exception("Connection failed")
        
        with patch('logging.error') as mock_log_error:
            with pytest.raises(Exception, match="Connection failed"):
                await metrics.get_lag("test_topic", "test_group")
            
            mock_log_error.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_throughput_invalid_interval(self):
        """Test get_throughput with invalid interval"""
        metrics = KafkaConsumerMetrics()
        
        with pytest.raises(ValueError, match="采样间隔时间必须大于0"):
            await metrics.get_throughput([], interval_seconds=0.0)
        
        with pytest.raises(ValueError, match="采样间隔时间必须大于0"):
            await metrics.get_throughput([], interval_seconds=-1.0)
    
    @pytest.mark.asyncio
    @patch('commons.tools.kafka_metrics.KafkaAdminClient')
    @patch('commons.tools.kafka_metrics.KafkaConsumerDao')
    @patch('asyncio.sleep')
    async def test_get_throughput_success(self, mock_sleep, mock_consumer_dao_class, mock_admin_client_class):
        """Test successful throughput calculation"""
        metrics = KafkaConsumerMetrics()
        metrics.init(["localhost:9092"])
        
        # Mock admin client
        mock_admin_client = Mock()
        mock_admin_client_class.return_value.get_client.return_value = mock_admin_client
        
        # Mock consumer
        mock_consumer = Mock()
        mock_consumer.assign = Mock()
        mock_consumer.stop = AsyncMock()
        mock_consumer_dao = Mock()
        mock_consumer_dao.get_consumer = AsyncMock(return_value=mock_consumer)
        mock_consumer_dao_class.return_value = mock_consumer_dao
        
        parse_config = [
            {"topic": "topic1", "consumer_group_id": "group1"},
            {"topic": "topic2", "consumer_group_id": "group2"}
        ]
        
        with patch.object(metrics, '_get_partition_ids') as mock_get_partitions:
            with patch.object(metrics, '_create_topic_partitions') as mock_create_partitions:
                with patch.object(metrics, '_get_committed_offsets') as mock_get_committed:
                    with patch.object(metrics, '_calculate_throughput') as mock_calc_throughput:
                        
                        # Setup mocks
                        mock_get_partitions.return_value = [0, 1]
                        mock_topic_partitions = [Mock(), Mock()]
                        mock_create_partitions.return_value = mock_topic_partitions
                        
                        # Mock offset calls
                        mock_get_committed.side_effect = [
                            {0: 100, 1: 200},  # start offsets for topic1
                            {0: 300, 1: 400},  # start offsets for topic2
                            {0: 150, 1: 250},  # end offsets for topic1
                            {0: 350, 1: 450},  # end offsets for topic2
                        ]
                        
                        mock_calc_throughput.side_effect = [10.5, 20.3]  # throughput results
                        
                        result = await metrics.get_throughput(parse_config, interval_seconds=60.0)
                        
                        assert len(result) == 2
                        assert result[0]["topic"] == "topic1"
                        assert result[0]["group_id"] == "group1"
                        assert result[0]["throughput"] == 10.5
                        assert result[1]["topic"] == "topic2"
                        assert result[1]["group_id"] == "group2"
                        assert result[1]["throughput"] == 20.3
                        
                        # Verify sleep was called
                        mock_sleep.assert_called_once_with(60.0)
    
    @pytest.mark.asyncio
    async def test_get_partition_ids(self):
        """Test _get_partition_ids method"""
        metrics = KafkaConsumerMetrics()
        
        mock_admin_client = Mock()
        mock_admin_client.describe_topics = AsyncMock(return_value=[
            {
                "partitions": [
                    {"partition": 0},
                    {"partition": 1},
                    {"partition": 2}
                ]
            }
        ])
        
        partition_ids = await metrics._get_partition_ids(mock_admin_client, "test_topic")
        
        assert partition_ids == [0, 1, 2]
        mock_admin_client.describe_topics.assert_called_once_with(["test_topic"])
    
    def test_create_topic_partitions(self):
        """Test _create_topic_partitions method"""
        metrics = KafkaConsumerMetrics()
        
        partition_ids = [0, 1, 2]
        topic_name = "test_topic"
        
        partitions = metrics._create_topic_partitions(partition_ids, topic_name)
        
        assert len(partitions) == 3
        for i, partition in enumerate(partitions):
            assert partition.topic == topic_name
            assert partition.partition == i
    
    @pytest.mark.asyncio
    async def test_get_committed_offsets_normal(self):
        """Test _get_committed_offsets with normal offsets"""
        metrics = KafkaConsumerMetrics()
        
        # Mock consumer
        mock_consumer = Mock()
        mock_consumer.committed = AsyncMock(side_effect=[100, 200, 300])
        
        # Mock partitions
        mock_partitions = [
            Mock(partition=0),
            Mock(partition=1),
            Mock(partition=2)
        ]
        
        offsets = await metrics._get_committed_offsets(mock_consumer, mock_partitions)
        
        assert offsets == {0: 100, 1: 200, 2: 300}
        assert mock_consumer.committed.call_count == 3
    
    @pytest.mark.asyncio
    async def test_get_committed_offsets_with_none_values(self):
        """Test _get_committed_offsets when some offsets are None"""
        metrics = KafkaConsumerMetrics()
        
        # Mock partitions first
        mock_partitions = [
            Mock(partition=0),
            Mock(partition=1),
            Mock(partition=2)
        ]
        
        # Mock consumer
        mock_consumer = Mock()
        mock_consumer.committed = AsyncMock(side_effect=[100, None, 300])
        mock_consumer.beginning_offsets = AsyncMock(return_value={mock_partitions[1]: 50})
        
        offsets = await metrics._get_committed_offsets(mock_consumer, mock_partitions)
        
        assert offsets == {0: 100, 1: 50, 2: 300}  # None replaced with beginning offset
        
        # Verify beginning_offsets was called for partition 1 
        mock_consumer.beginning_offsets.assert_called_once_with([mock_partitions[1]])
    
    @pytest.mark.asyncio
    async def test_get_committed_offsets_with_exception(self):
        """Test _get_committed_offsets with exception handling"""
        metrics = KafkaConsumerMetrics()
        
        # Mock consumer
        mock_consumer = Mock()
        mock_consumer.committed = AsyncMock(side_effect=[100, Exception("Partition error"), 300])
        
        # Mock partitions
        mock_partitions = [
            Mock(partition=0),
            Mock(partition=1),
            Mock(partition=2)
        ]
        
        with patch('logging.warning') as mock_log_warning:
            offsets = await metrics._get_committed_offsets(mock_consumer, mock_partitions)
            
            assert offsets == {0: 100, 1: 0, 2: 300}  # Exception results in 0
            mock_log_warning.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_end_offsets(self):
        """Test _get_end_offsets method"""
        metrics = KafkaConsumerMetrics()
        
        # Mock consumer
        mock_consumer = Mock()
        mock_consumer.seek_to_end = AsyncMock()
        mock_consumer.position = AsyncMock(side_effect=[150, 250, 350])
        
        # Mock partitions
        mock_partitions = [
            Mock(partition=0),
            Mock(partition=1),
            Mock(partition=2)
        ]
        
        end_offsets = await metrics._get_end_offsets(mock_consumer, mock_partitions)
        
        assert end_offsets == {0: 150, 1: 250, 2: 350}
        mock_consumer.seek_to_end.assert_called_once()
        assert mock_consumer.position.call_count == 3
    
    def test_calculate_throughput_normal(self):
        """Test _calculate_throughput with normal data"""
        metrics = KafkaConsumerMetrics()
        
        start_offsets = {0: 100, 1: 200, 2: 300}
        end_offsets = {0: 150, 1: 250, 2: 350}
        interval = 60.0
        
        throughput = metrics._calculate_throughput(start_offsets, end_offsets, interval)
        
        # Total delta: (150-100) + (250-200) + (350-300) = 50 + 50 + 50 = 150
        # Throughput: 150 / 60 = 2.5
        assert throughput == 2.5
    
    def test_calculate_throughput_negative_delta(self):
        """Test _calculate_throughput with negative delta (offset reset)"""
        metrics = KafkaConsumerMetrics()
        
        start_offsets = {0: 100, 1: 200}
        end_offsets = {0: 50, 1: 250}  # Partition 0 reset
        interval = 60.0
        
        with patch('logging.warning') as mock_log_warning:
            throughput = metrics._calculate_throughput(start_offsets, end_offsets, interval)
            
            # Delta for partition 0: 50-100 = -50, should be treated as 0
            # Delta for partition 1: 250-200 = 50
            # Total: 0 + 50 = 50, throughput = 50/60 = 0.833
            assert throughput == 0.833
            mock_log_warning.assert_called_once()
    
    def test_calculate_throughput_missing_partition(self):
        """Test _calculate_throughput with missing partition in end offsets"""
        metrics = KafkaConsumerMetrics()
        
        start_offsets = {0: 100, 1: 200, 2: 300}
        end_offsets = {0: 150, 1: 250}  # Missing partition 2
        interval = 60.0
        
        with pytest.raises(RuntimeError, match="分区 2 在采样期间消失"):
            metrics._calculate_throughput(start_offsets, end_offsets, interval)
    
    def test_calculate_throughput_empty_data(self):
        """Test _calculate_throughput with empty data"""
        metrics = KafkaConsumerMetrics()
        
        with pytest.raises(RuntimeError, match="无法获取有效偏移量数据"):
            metrics._calculate_throughput({}, {}, 60.0)
        
        with pytest.raises(RuntimeError, match="无法获取有效偏移量数据"):
            metrics._calculate_throughput(None, {0: 100}, 60.0)
        
        with pytest.raises(RuntimeError, match="无法获取有效偏移量数据"):
            metrics._calculate_throughput({0: 100}, None, 60.0)
    
    def test_calculate_throughput_zero_interval(self):
        """Test _calculate_throughput with zero interval"""
        metrics = KafkaConsumerMetrics()
        
        start_offsets = {0: 100}
        end_offsets = {0: 150}
        
        # This would cause division by zero
        with pytest.raises(ZeroDivisionError):
            metrics._calculate_throughput(start_offsets, end_offsets, 0.0)
    
    @pytest.mark.asyncio
    async def test_close_consumer_with_consumer(self):
        """Test _close_consumer with valid consumer"""
        metrics = KafkaConsumerMetrics()
        
        mock_consumer = Mock()
        mock_consumer.stop = AsyncMock()
        
        await metrics._close_consumer(mock_consumer)
        
        mock_consumer.stop.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_consumer_with_none(self):
        """Test _close_consumer with None consumer"""
        metrics = KafkaConsumerMetrics()
        
        # Should not raise any exception
        await metrics._close_consumer(None)
    
    @pytest.mark.asyncio
    @patch('commons.tools.kafka_metrics.KafkaAdminClient')
    @patch('commons.tools.kafka_metrics.KafkaConsumerDao')
    @patch('asyncio.sleep')
    async def test_get_throughput_partial_failure(self, mock_sleep, mock_consumer_dao_class, mock_admin_client_class):
        """Test get_throughput with partial failures"""
        metrics = KafkaConsumerMetrics()
        metrics.init(["localhost:9092"])
        
        # Mock admin client
        mock_admin_client = Mock()
        mock_admin_client_class.return_value.get_client.return_value = mock_admin_client
        
        # Mock consumers
        mock_consumer1 = Mock()
        mock_consumer1.assign = Mock()
        mock_consumer1.stop = AsyncMock()
        
        mock_consumer2 = Mock()
        mock_consumer2.assign = Mock()
        mock_consumer2.stop = AsyncMock()
        
        mock_consumer_dao1 = Mock()
        mock_consumer_dao1.get_consumer = AsyncMock(return_value=mock_consumer1)
        mock_consumer_dao2 = Mock()
        mock_consumer_dao2.get_consumer = AsyncMock(return_value=mock_consumer2)
        
        mock_consumer_dao_class.side_effect = [mock_consumer_dao1, mock_consumer_dao2]
        
        parse_config = [
            {"topic": "topic1", "consumer_group_id": "group1"},
            {"topic": "topic2", "consumer_group_id": "group2"}
        ]
        
        with patch.object(metrics, '_get_partition_ids') as mock_get_partitions:
            with patch.object(metrics, '_create_topic_partitions') as mock_create_partitions:
                with patch.object(metrics, '_get_committed_offsets') as mock_get_committed:
                    with patch.object(metrics, '_calculate_throughput') as mock_calc_throughput:
                        
                        # Setup mocks
                        mock_get_partitions.return_value = [0]
                        mock_create_partitions.return_value = [Mock()]
                        
                        # First call succeeds, second calls succeed for start but fail for end
                        mock_get_committed.side_effect = [
                            {0: 100},  # start for topic1
                            {0: 200},  # start for topic2
                            {0: 150},  # end for topic1 (success)
                            Exception("Failed to get end offset")  # end for topic2 (failure)
                        ]
                        
                        mock_calc_throughput.return_value = 5.0
                        
                        with patch('logging.error') as mock_log_error:
                            result = await metrics.get_throughput(parse_config, interval_seconds=30.0)
                            
                            # Should have one successful result
                            assert len(result) == 1
                            assert result[0]["topic"] == "topic1"
                            assert result[0]["group_id"] == "group1"
                            assert result[0]["throughput"] == 5.0
                            
                            # Should have logged error for second topic
                            mock_log_error.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_throughput_empty_config(self):
        """Test get_throughput with empty configuration"""
        metrics = KafkaConsumerMetrics()
        metrics.init(["localhost:9092"])
        
        with patch('asyncio.sleep'):
            result = await metrics.get_throughput([], interval_seconds=60.0)
            
            assert result == []
    
    def test_calculate_throughput_rounding(self):
        """Test _calculate_throughput rounding behavior"""
        metrics = KafkaConsumerMetrics()
        
        start_offsets = {0: 100}
        end_offsets = {0: 167}  # Delta = 67
        interval = 60.0
        
        throughput = metrics._calculate_throughput(start_offsets, end_offsets, interval)
        
        # 67 / 60 = 1.11666..., should round to 3 decimal places
        assert throughput == 1.117


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
