import pytest
import json
import warnings
from unittest.mock import patch, <PERSON><PERSON>, AsyncMock
from pydantic import BaseModel
from commons.tools.async_wps365api import AsyncWps365Api, TokenAuthException, HTTPCODE, ResponseModel, RespTokenAuthError


class TestAsyncWps365Api:
    """Test cases for AsyncWps365Api class"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        if hasattr(AsyncWps365Api, '_instances'):
            AsyncWps365Api._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(AsyncWps365Api, '_instances'):
            AsyncWps365Api._instances.clear()
    
    def test_singleton_behavior(self):
        """Test singleton pattern"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api1 = AsyncWps365Api()
            api2 = AsyncWps365Api()
            
            assert api1 is api2
            assert id(api1) == id(api2)
    
    def test_init_deprecation_warning(self):
        """Test that initialization shows deprecation warning"""
        with pytest.warns(DeprecationWarning, match="AsyncWps365Api is deprecated"):
            AsyncWps365Api()
    
    def test_init_default_state(self):
        """Test initial default state"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            assert api._host == "https://api.wps.cn"
            assert api._pool_max == -1
            assert api._openapi is None
            assert api._tokens == {}
            assert api._woa_custom_ak is None
            assert hasattr(api, '_origin')
    
    @patch('commons.tools.async_wps365api.OpenApi')
    def test_init_with_params(self, mock_openapi):
        """Test initialization with parameters"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            mock_openapi_instance = Mock()
            mock_openapi.return_value = mock_openapi_instance
            
            api = AsyncWps365Api()
            api.init(
                host="https://custom.wps.cn",
                pool_max=5,
                openapi_host="https://openapi.wps.cn",
                openapi_ak="test_ak",
                openapi_sk="test_sk",
                woa_custom_ak="custom_ak"
            )
            
            assert api._host == "https://custom.wps.cn"
            assert api._woa_custom_ak == "custom_ak"
            assert api._openapi is mock_openapi_instance
            assert api._pool_max == 5
            
            mock_openapi.assert_called_once_with("https://openapi.wps.cn", "test_ak", "test_sk", 5)
    
    def test_update_host(self):
        """Test update_host method"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api.update_host("https://new.wps.cn")
            
            assert api._host == "https://new.wps.cn"
            assert "https://new.wps.cn" in api._origin
    
    def test_set_origin(self):
        """Test _set_origin method"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._host = "https://api.wps.cn:8080/path"
            
            api._set_origin()
            
            assert api._origin == "https://api.wps.cn:8080"
    
    @pytest.mark.asyncio
    async def test_get_token_no_openapi(self):
        """Test get_token without openapi initialization"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            with pytest.raises(AssertionError, match="requires the initialization of the openapi"):
                await api.get_token("company1")
    
    @pytest.mark.asyncio
    @patch('time.time')
    async def test_get_token_success(self, mock_time):
        """Test successful token retrieval"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            mock_openapi = Mock()
            api._openapi = mock_openapi
            
            mock_time.return_value = 1000.0
            mock_token_resp = Mock()
            mock_token_resp.token_type = "Bearer"
            mock_token_resp.access_token = "async_token"
            mock_token_resp.expires_in = 3600
            mock_openapi.async_get_token = AsyncMock(return_value=mock_token_resp)
            
            token = await api.get_token("company1")
            
            assert token == "Bearer async_token"
            mock_openapi.async_get_token.assert_called_once_with("company1")
    
    @pytest.mark.asyncio
    @patch('time.time')
    async def test_get_token_failure_returns_empty(self, mock_time):
        """Test get_token when openapi returns None"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            mock_openapi = Mock()
            api._openapi = mock_openapi
            
            mock_time.return_value = 1000.0
            mock_openapi.async_get_token = AsyncMock(return_value=None)
            
            token = await api.get_token("company1")
            
            assert token == ""
    
    def test_pop_token_async(self):
        """Test pop_token method in async context"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            # Add token
            api._tokens["company1"] = AsyncWps365Api.TokenItem(token="test_token", expires=2000.0)
            
            api.pop_token("company1")
            
            assert "company1" not in api._tokens
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession')
    async def test_call_method_basic(self, mock_session_class):
        """Test call method basic functionality"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._woa_custom_ak = "test_ak"
            
            # Mock aiohttp session and response
            mock_resp = Mock()
            mock_resp.status = 200
            mock_resp.read = AsyncMock(return_value=b'{"result": "success"}')
            mock_resp.headers = {}
            
            mock_session = Mock()
            mock_session.request = Mock()
            mock_session.request.return_value.__aenter__ = AsyncMock(return_value=mock_resp)
            mock_session.request.return_value.__aexit__ = AsyncMock(return_value=None)
            
            mock_session_class.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_class.return_value.__aexit__ = AsyncMock(return_value=None)
            
            status, content, headers = await api.call("GET", "/test")
            
            assert status == 200
            assert content == b'{"result": "success"}'
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession')
    @patch('commons.tools.async_wps365api.request_id_context')
    async def test_call_with_all_params(self, mock_request_context, mock_session_class):
        """Test call method with all parameters to cover missing lines"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._woa_custom_ak = "test_ak"
            api._pool_max = 10  # Enable connection pool
            api._openapi = Mock()
            api._openapi.async_get_token = AsyncMock(return_value=Mock(
                token_type="Bearer", access_token="test_token", expires_in=3600
            ))
            
            # Mock request context to set X-Request-Id
            mock_request_context.get.return_value = "request-123"
            
            # Mock aiohttp components
            mock_connector = Mock()
            mock_timeout = Mock()
            
            with patch('aiohttp.TCPConnector', return_value=mock_connector):
                with patch('aiohttp.ClientTimeout', return_value=mock_timeout):
                    mock_resp = Mock()
                    mock_resp.status = 200
                    mock_resp.read = AsyncMock(return_value=b'{"success": true}')
                    mock_resp.headers = {"Content-Type": "application/json"}
                    
                    mock_session = Mock()
                    mock_session.request = Mock()
                    mock_session.request.return_value.__aenter__ = AsyncMock(return_value=mock_resp)
                    mock_session.request.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    mock_session_class.return_value.__aenter__ = AsyncMock(return_value=mock_session)
                    mock_session_class.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    # Call with all parameters
                    status, content, headers = await api.call(
                        "POST", "/test",
                        body={"key": "value", "nested": {"bool": True}},
                        params={"param1": "value1", "bool_param": False},
                        cookies={"session": "abc123"},
                        headers={"Custom-Header": "custom_value"},
                        company_id="company1",
                        timeout_second=120
                    )
                    
                    assert status == 200
                    assert content == b'{"success": true}'
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession')
    async def test_call_with_403_error_and_token_auth_exception(self, mock_session_class):
        """Test call method with 403 error leading to TokenAuthException"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._woa_custom_ak = "test_ak"
            api._openapi = Mock()
            api._openapi.async_get_token = AsyncMock(return_value=Mock(
                token_type="Bearer", access_token="test_token", expires_in=3600
            ))
            
            # Mock 403 response with token auth error
            mock_resp = Mock()
            mock_resp.status = 403
            mock_resp.read = AsyncMock(return_value=json.dumps({
                "code": 400006,
                "message": "Token expired"
            }).encode())
            mock_resp.headers = {}
            
            mock_session = Mock()
            mock_session.request = Mock()
            mock_session.request.return_value.__aenter__ = AsyncMock(return_value=mock_resp)
            mock_session.request.return_value.__aexit__ = AsyncMock(return_value=None)
            
            mock_session_class.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_class.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # Add token to be popped
            api._tokens["company1"] = AsyncWps365Api.TokenItem(token="Bearer old_token", expires=2000.0)
            
            with patch('logging.error'):
                with pytest.raises(TokenAuthException):
                    await api.call("GET", "/test", company_id="company1")
                
                # Token should have been popped
                assert "company1" not in api._tokens
    
    @pytest.mark.asyncio
    async def test_get_method_success(self):
        """Test get method success case"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._openapi = Mock()
            api._openapi.async_get_token = AsyncMock(return_value=Mock(
                token_type="Bearer", access_token="test_token", expires_in=3600
            ))
            
            # Mock call method
            with patch.object(api, 'call') as mock_call:
                mock_call.return_value = (200, json.dumps({
                    "code": 0,
                    "data": {"result": "success"}
                }).encode(), {})
                
                result = await api.get("/test", "wps_sid_123", company_id="company1")
                
                assert result == {"result": "success"}
                mock_call.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_method_error_handling(self):
        """Test get method error handling - simplified approach"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            # Mock the call method directly to return error response
            with patch.object(api, 'call') as mock_call:
                mock_call.return_value = (400, json.dumps({
                    "code": 1001,
                    "message": "Bad request"
                }).encode(), {})
                
                # Test error extraction - need non-empty dict since if error: checks truthiness
                error_dict = {"existing": "value"}  # Non-empty dict so if error: returns True
                result = await api.get("/test", "wps_sid_123", error=error_dict)
                
                assert result is None
                # Verify error code was extracted (this is the core functionality we're testing)
                assert error_dict.get("code") == 1001
                assert error_dict.get("existing") == "value"  # Original content preserved
                mock_call.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_method_exception_handling(self):
        """Test get method exception handling"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            # Mock call to raise exception
            with patch.object(api, 'call', side_effect=Exception("Network error")):
                with patch('logging.error') as mock_log_error:
                    result = await api.get("/test", "wps_sid_123")
                    
                    assert result is None
                    mock_log_error.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_post_method_success(self):
        """Test post method success case"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._openapi = Mock()
            api._openapi.async_get_token = AsyncMock(return_value=Mock(
                token_type="Bearer", access_token="test_token", expires_in=3600
            ))
            
            # Mock call method
            with patch.object(api, 'call') as mock_call:
                mock_call.return_value = (200, json.dumps({
                    "code": 0,
                    "data": {"id": 123, "created": True}
                }).encode(), {})
                
                result = await api.post("/create", "wps_sid_123", 
                                      body={"name": "test"}, company_id="company1")
                
                assert result == {"id": 123, "created": True}
                mock_call.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_post_method_error_handling(self):
        """Test post method error handling - simplified approach"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            # Mock the call method directly to return error response
            with patch.object(api, 'call') as mock_call:
                mock_call.return_value = (500, json.dumps({
                    "code": 5001,
                    "message": "Internal server error"
                }).encode(), {})
                
                # Test error extraction - need non-empty dict since if error: checks truthiness
                error_dict = {"existing": "value"}  # Non-empty dict so if error: returns True
                result = await api.post("/create", "wps_sid_123", error=error_dict)
                
                assert result is None
                # Verify error code was extracted
                assert error_dict.get("code") == 5001
                assert error_dict.get("existing") == "value"  # Original content preserved
                mock_call.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_post_method_exception_handling(self):
        """Test post method exception handling"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            # Mock call to raise exception
            with patch.object(api, 'call', side_effect=Exception("Connection error")):
                with patch('logging.error') as mock_log_error:
                    result = await api.post("/create", "wps_sid_123", body={"test": "data"})
                    
                    assert result is None
                    mock_log_error.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_method_with_headers_and_params(self):
        """Test get method with headers and params"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            with patch.object(api, 'call') as mock_call:
                mock_call.return_value = (200, json.dumps({
                    "code": 0,
                    "data": {"result": "success"}
                }).encode(), {})
                
                # Test with headers parameter
                result = await api.get(
                    "/test", "wps_sid_123",
                    params={"q": "search"},
                    headers={"Custom": "header"}
                )
                
                assert result == {"result": "success"}
                
                # Verify headers were processed
                call_args = mock_call.call_args
                assert "headers" in call_args[1]
                headers_used = call_args[1]["headers"]
                assert headers_used["Custom"] == "header"
    
    @pytest.mark.asyncio
    async def test_get_method_error_json_parsing_failure(self):
        """Test get method when error JSON parsing fails"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            error_dict = {}
            with patch.object(api, 'call') as mock_call:
                # Return invalid JSON that will cause parsing exception
                mock_call.return_value = (400, b"invalid json", {})
                
                result = await api.get("/test", "wps_sid_123", error=error_dict)
                
                assert result is None
                # error dict should remain empty due to JSON parsing failure
                assert error_dict == {}
    
    @pytest.mark.asyncio 
    async def test_post_method_error_json_parsing_failure(self):
        """Test post method when error JSON parsing fails"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            error_dict = {}
            with patch.object(api, 'call') as mock_call:
                # Return invalid JSON that will cause parsing exception
                mock_call.return_value = (500, b"server error - not json", {})
                
                result = await api.post("/create", "wps_sid_123", error=error_dict)
                
                assert result is None
                # error dict should remain empty due to JSON parsing failure
                assert error_dict == {}
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession')
    async def test_call_with_pool_max_and_request_id(self, mock_session_class):
        """Test call method with pool_max and request_id to cover lines 122, 136, 140"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._woa_custom_ak = "test_ak"
            api._pool_max = 5  # Enable pool
            api._openapi = Mock()
            api._openapi.async_get_token = AsyncMock(return_value=Mock(
                token_type="Bearer", access_token="auth_token", expires_in=3600
            ))
            
            # Mock request_id_context
            with patch('commons.tools.async_wps365api.request_id_context') as mock_context:
                mock_context.get.return_value = "req-123"
                
                # Mock aiohttp components
                with patch('aiohttp.TCPConnector') as mock_connector:
                    with patch('aiohttp.ClientTimeout') as mock_timeout:
                        mock_resp = Mock()
                        mock_resp.status = 200
                        mock_resp.read = AsyncMock(return_value=b'{"data": "success"}')
                        mock_resp.headers = {}
                        
                        mock_session = Mock()
                        mock_session.request = Mock()
                        mock_session.request.return_value.__aenter__ = AsyncMock(return_value=mock_resp)
                        mock_session.request.return_value.__aexit__ = AsyncMock(return_value=None)
                        
                        mock_session_class.return_value.__aenter__ = AsyncMock(return_value=mock_session)
                        mock_session_class.return_value.__aexit__ = AsyncMock(return_value=None)
                        
                        # Call with params, body, headers, and company_id
                        status, content, headers = await api.call(
                            "POST", "/test",
                            body={"key": "value", "active": True},
                            params={"filter": "active", "enabled": False},
                            headers={"Custom": "value"},
                            company_id="company1"
                        )
                        
                        assert status == 200
                        assert content == b'{"data": "success"}'
                        
                        # Verify connector was created with limit
                        mock_connector.assert_called_once_with(limit=5)
                        # Verify timeout was set
                        mock_timeout.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession')
    async def test_call_403_error_non_auth_code(self, mock_session_class):
        """Test call method with 403 error but non-auth error code"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            # Mock 403 response with different error code
            mock_resp = Mock()
            mock_resp.status = 403
            mock_resp.read = AsyncMock(return_value=json.dumps({
                "code": 403001,  # Different from 400006
                "message": "Forbidden access"
            }).encode())
            mock_resp.headers = {}
            
            mock_session = Mock()
            mock_session.request = Mock()
            mock_session.request.return_value.__aenter__ = AsyncMock(return_value=mock_resp)
            mock_session.request.return_value.__aexit__ = AsyncMock(return_value=None)
            
            mock_session_class.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_class.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with patch('logging.error') as mock_log_error:
                status, content, headers = await api.call("GET", "/test")
                
                assert status == 403
                # Should have logged the error
                mock_log_error.assert_called()
                # Should not raise TokenAuthException since code != 400006
                assert content == json.dumps({"code": 403001, "message": "Forbidden access"}).encode()
    
    def test_serve_http_result_with_model(self):
        """Test _serve_http_result with response model"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            class TestModel(BaseModel):
                name: str
                value: int
            
            content = json.dumps({
                "code": 0,
                "data": {"name": "test", "value": 123}
            }).encode()
            
            result = api._serve_http_result("/test", content, TestModel)
            
            assert isinstance(result, TestModel)
            assert result.name == "test"
            assert result.value == 123
    
    def test_serve_http_result_without_model(self):
        """Test _serve_http_result without response model"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            content = json.dumps({
                "code": 0,
                "data": {"key": "value"}
            }).encode()
            
            result = api._serve_http_result("/test", content)
            
            assert result == {"key": "value"}
    
    def test_serve_http_result_error_code(self):
        """Test _serve_http_result with error code"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            content = json.dumps({
                "code": 1,
                "msg": "Error occurred"
            }).encode()
            
            with patch('logging.error') as mock_log_error:
                result = api._serve_http_result("/test", content)
                
                assert result is None
                mock_log_error.assert_called_once()


class TestAsyncWps365ApiModels:
    """Test cases for AsyncWps365Api related models"""
    
    def test_token_item_creation(self):
        """Test TokenItem model creation"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            token_item = AsyncWps365Api.TokenItem(
                token="Bearer async_token",
                expires=1234567890.0
            )
            
            assert token_item.token == "Bearer async_token"
            assert token_item.expires == 1234567890.0
    
    def test_models_same_as_sync_version(self):
        """Test that models are consistent with sync version"""
        # ResponseModel
        resp = ResponseModel(code=HTTPCODE.OK, msg="test")
        assert resp.code == 0
        
        # RespTokenAuthError
        error_resp = RespTokenAuthError(code=400006, message="expired")
        assert error_resp.code == 400006
        assert error_resp.message == "expired"


class TestAsyncWps365ApiCoverage:
    """Additional tests to improve code coverage"""
    
    def test_serve_http_result_error_with_model(self):
        """Test _serve_http_result error handling with model"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            class TestModel(BaseModel):
                name: str
            
            # Error response with model
            content = json.dumps({
                "code": 1,
                "msg": "Error occurred"
            }).encode()
            
            with patch('logging.error') as mock_log_error:
                result = api._serve_http_result("/test", content, TestModel)
                
                assert result is None
                mock_log_error.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_cached_token(self):
        """Test get method using cached token"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._openapi = Mock()  # Need openapi for get_token call
            
            # Add cached token that's still valid
            with patch('time.time', return_value=1000.0):
                api._tokens["company1"] = AsyncWps365Api.TokenItem(
                    token="Bearer cached_token", expires=2000.0
                )
                
                with patch.object(api, 'call') as mock_call:
                    mock_call.return_value = (200, json.dumps({
                        "code": 0,
                        "data": {"cached": True}
                    }).encode(), {})
                    
                    result = await api.get("/test", "wps_sid_123", company_id="company1")
                    
                    assert result == {"cached": True}
                    
                    # Verify call was made with cached token
                    call_args = mock_call.call_args
                    headers = call_args[1]["headers"]
                    assert headers["Authorization"] == "Bearer cached_token"
    
    @pytest.mark.asyncio
    async def test_post_with_response_model(self):
        """Test post method with response model"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            
            class CreateResponse(BaseModel):
                id: int
                name: str
            
            with patch.object(api, 'call') as mock_call:
                mock_call.return_value = (200, json.dumps({
                    "code": 0,
                    "data": {"id": 456, "name": "created_item"}
                }).encode(), {})
                
                result = await api.post("/create", "wps_sid_123", 
                                      body={"name": "new_item"}, 
                                      resp_model=CreateResponse)
                
                assert isinstance(result, CreateResponse)
                assert result.id == 456
                assert result.name == "created_item"
    
    @pytest.mark.asyncio
    async def test_get_token_cached_valid(self):
        """Test get_token with valid cached token"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            api._openapi = Mock()
            
            # Mock time to make token valid
            with patch('time.time', return_value=1000.0):
                # Add valid cached token
                api._tokens["company1"] = AsyncWps365Api.TokenItem(
                    token="Bearer cached_valid", expires=2000.0
                )
                
                token = await api.get_token("company1")
                
                assert token == "Bearer cached_valid"
                # Should not call openapi since token is valid
                api._openapi.async_get_token.assert_not_called()
    
    def test_init_without_host_parameter(self):
        """Test init method without host parameter"""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            
            api = AsyncWps365Api()
            original_host = api._host
            
            # Initialize without host parameter
            api.init(pool_max=5, woa_custom_ak="test_ak")
            
            # Host should remain unchanged
            assert api._host == original_host
            assert api._woa_custom_ak == "test_ak"
            assert api._pool_max == 5
    


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
