import pytest
import asyncio
import time
import warnings
from unittest.mock import <PERSON><PERSON>, patch, <PERSON><PERSON>ock, AsyncMock, create_autospec
from concurrent.futures import Process<PERSON><PERSON><PERSON>xecutor, Future
from commons.thread.multiprocess import MultiProcess, task_wrapper
from commons.logger.logger import PassableLogContext

# Configure warnings filtering at module level to prevent cross-contamination
warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
# Also filter more general coroutine warnings
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*coroutine.*")

# Apply pytest warning filters
pytestmark = [
    pytest.mark.filterwarnings("ignore::RuntimeWarning"),
    pytest.mark.filterwarnings("ignore:coroutine.*was never awaited"),
]


# Helper functions for testing
def simple_function(x):
    """Simple function for testing"""
    return x * 2


def add_numbers(a, b):
    """Function that adds two numbers"""
    return a + b


def slow_function(delay):
    """Function that takes time"""
    time.sleep(delay)
    return f"completed after {delay}s"


def error_function():
    """Function that raises an error"""
    raise ValueError("Test error in multiprocess")


def function_with_kwargs(x, multiplier=1, offset=0):
    """Function that uses keyword arguments"""
    return (x * multiplier) + offset


class TestTaskWrapper:
    """Test cases for task_wrapper function"""
    
    def setup_method(self):
        """Setup method to ensure clean state for each test"""
        warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
    
    def test_task_wrapper_basic(self):
        """Test task_wrapper with basic function"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mock_context = Mock(spec_set=['export'])
            mock_context.export = Mock()
            
            result = task_wrapper(simple_function, mock_context, 5)
            
            assert result == 10
            mock_context.export.assert_called_once()
    
    def test_task_wrapper_with_args(self):
        """Test task_wrapper with multiple arguments"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mock_context = Mock(spec_set=['export'])
            mock_context.export = Mock()
            
            result = task_wrapper(add_numbers, mock_context, 3, 7)
            
            assert result == 10
            mock_context.export.assert_called_once()
    
    def test_task_wrapper_with_kwargs(self):
        """Test task_wrapper with keyword arguments"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mock_context = Mock(spec_set=['export'])
            mock_context.export = Mock()
            
            result = task_wrapper(function_with_kwargs, mock_context, 5, multiplier=3, offset=1)
            
            assert result == 16  # (5 * 3) + 1
            mock_context.export.assert_called_once()
    
    def test_task_wrapper_with_exception(self):
        """Test task_wrapper when function raises exception"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mock_context = Mock(spec_set=['export'])
            mock_context.export = Mock()
            
            with pytest.raises(ValueError, match="Test error in multiprocess"):
                task_wrapper(error_function, mock_context)
            
            mock_context.export.assert_called_once()
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_task_wrapper_log_context_export(self, mock_passable_log_context):
        """Test that task_wrapper calls export on log context"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mock_context = mock_passable_log_context.return_value
            mock_context.export = Mock()
            
            result = task_wrapper(simple_function, mock_context, 3)
            
            assert result == 6
            mock_context.export.assert_called_once()


class TestMultiProcess:
    """Test cases for MultiProcess class"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        # Filter warnings at test method level
        warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
        warnings.filterwarnings("ignore", category=RuntimeWarning)
        
        if hasattr(MultiProcess, '_instances'):
            MultiProcess._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(MultiProcess, '_instances'):
            MultiProcess._instances.clear()
    
    def test_singleton_behavior(self):
        """Test that MultiProcess follows singleton pattern"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            instance1 = MultiProcess()
            instance2 = MultiProcess()
            
            assert instance1 is instance2
            assert id(instance1) == id(instance2)
    
    def test_init_creates_instance(self):
        """Test initial instance creation"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mp = MultiProcess()
            
            assert mp.process_pool is None
            assert isinstance(mp, MultiProcess)
    
    @patch('commons.thread.multiprocess.ProcessPoolExecutor')
    def test_init_with_default_parameters(self, mock_executor):
        """Test init method with default parameters"""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mp = MultiProcess()
            mock_instance = Mock()
            mock_executor.return_value = mock_instance
            
            mp.init()
            
            mock_executor.assert_called_once_with(None, None, None, ())
            assert mp.process_pool is mock_instance
    
    @patch('commons.thread.multiprocess.ProcessPoolExecutor')
    def test_init_with_custom_parameters(self, mock_executor):
        """Test init method with custom parameters"""
        mp = MultiProcess()
        mock_instance = Mock()
        mock_executor.return_value = mock_instance
        
        custom_initializer = Mock()
        custom_initargs = (1, 2, 3)
        
        mp.init(max_workers=4, initializer=custom_initializer, initargs=custom_initargs)
        
        mock_executor.assert_called_once_with(4, None, custom_initializer, custom_initargs)
        assert mp.process_pool is mock_instance
    
    @patch('commons.thread.multiprocess.ProcessPoolExecutor')
    def test_init_with_mp_context(self, mock_executor):
        """Test init method with multiprocessing context"""
        mp = MultiProcess()
        mock_instance = Mock()
        mock_executor.return_value = mock_instance
        mock_context = Mock()
        
        mp.init(max_workers=2, mp_context=mock_context)
        
        mock_executor.assert_called_once_with(2, mock_context, None, ())
        assert mp.process_pool is mock_instance
    
    @patch('commons.thread.multiprocess.time.sleep')
    @patch('commons.thread.multiprocess.psutil.Process')
    @patch('logging.info')
    def test_close_normal_shutdown(self, mock_logging, mock_process_class, mock_sleep):
        """Test close method with normal shutdown"""
        mp = MultiProcess()
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool._processes = [123, 456]  # Mock PIDs
        mock_pool.shutdown = Mock()
        mp.process_pool = mock_pool
        
        # Mock psutil processes
        mock_proc1 = Mock()
        mock_proc1.is_running.return_value = False
        mock_proc2 = Mock()
        mock_proc2.is_running.return_value = False
        mock_process_class.side_effect = [mock_proc1, mock_proc2]
        
        mp.close()
        
        mock_logging.assert_called_once_with("进程池close------------------")
        mock_pool.shutdown.assert_called_once_with(wait=False)
        mock_sleep.assert_called_once_with(10)
        assert mock_process_class.call_count == 2
        mock_proc1.kill.assert_not_called()
        mock_proc2.kill.assert_not_called()
    
    @patch('commons.thread.multiprocess.time.sleep')
    @patch('commons.thread.multiprocess.psutil.Process')
    @patch('logging.info')
    def test_close_force_kill_running_processes(self, mock_logging, mock_process_class, mock_sleep):
        """Test close method with force killing running processes"""
        mp = MultiProcess()
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool._processes = [123]
        mock_pool.shutdown = Mock()
        mp.process_pool = mock_pool
        
        # Mock psutil process that is still running
        mock_proc = Mock()
        mock_proc.is_running.return_value = True
        mock_proc.kill = Mock()
        mock_process_class.return_value = mock_proc
        
        mp.close()
        
        mock_pool.shutdown.assert_called_once_with(wait=False)
        mock_sleep.assert_called_once_with(10)
        mock_proc.kill.assert_called_once()
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_run_basic_function(self, mock_log_context_class):
        """Test run method with basic function"""
        mp = MultiProcess()
        
        # Mock log context
        mock_log_context = Mock()
        mock_log_context_class.dump.return_value = {"key": "value"}
        mock_log_context_class.return_value = mock_log_context
        
        # Mock process pool
        mock_future = Mock()
        mock_future.result.return_value = 10
        mock_pool = Mock()
        mock_pool.submit.return_value = mock_future
        mp.process_pool = mock_pool
        
        result = mp.run(simple_function, 5)
        
        assert result == 10
        mock_pool.submit.assert_called_once()
        args, kwargs = mock_pool.submit.call_args
        assert args[0] == task_wrapper
        assert args[1] == simple_function
        assert args[2] == mock_log_context
        assert args[3] == 5
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_run_with_multiple_args(self, mock_log_context_class):
        """Test run method with multiple arguments"""
        mp = MultiProcess()
        
        # Mock log context
        mock_log_context = Mock()
        mock_log_context_class.dump.return_value = {"key": "value"}
        mock_log_context_class.return_value = mock_log_context
        
        # Mock process pool
        mock_future = Mock()
        mock_future.result.return_value = 15
        mock_pool = Mock()
        mock_pool.submit.return_value = mock_future
        mp.process_pool = mock_pool
        
        result = mp.run(add_numbers, 7, 8)
        
        assert result == 15
        mock_pool.submit.assert_called_once()
        args, kwargs = mock_pool.submit.call_args
        assert args[0] == task_wrapper
        assert args[1] == add_numbers
        assert args[2] == mock_log_context
        assert args[3] == 7
        assert args[4] == 8
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_run_with_kwargs(self, mock_log_context_class):
        """Test run method with keyword arguments"""
        # Filter warnings to prevent cross-contamination from coroutine tests
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
            
            mp = MultiProcess()
            
            # Create strict mocks to prevent unwanted attribute access
            mock_log_context = create_autospec(PassableLogContext, instance=True)
            mock_log_context_class.dump.return_value = {"key": "value"}
            mock_log_context_class.return_value = mock_log_context
            
            # Mock process pool with spec to prevent unwanted attribute access
            mock_future = Mock(spec_set=['result'])
            mock_future.result.return_value = 20
            mock_pool = Mock(spec_set=['submit'])
            mock_pool.submit.return_value = mock_future
            mp.process_pool = mock_pool
            
            result = mp.run(function_with_kwargs, 5, multiplier=4)
            
            assert result == 20
            mock_pool.submit.assert_called_once()
            # kwargs are not passed directly to task_wrapper, they go through submit
            args, kwargs = mock_pool.submit.call_args
            assert 'multiplier' in kwargs
            assert kwargs['multiplier'] == 4
    
    @pytest.mark.asyncio
    @patch('commons.thread.multiprocess.PassableLogContext')
    async def test_arun_basic_function(self, mock_log_context_class):
        """Test arun method with basic function"""
        mp = MultiProcess()
        
        # Mock log context
        mock_log_context = Mock()
        mock_log_context_class.dump.return_value = {"key": "value"}
        mock_log_context_class.return_value = mock_log_context
        
        # Mock process pool and event loop
        mock_pool = Mock()
        mp.process_pool = mock_pool
        
        with patch('asyncio.get_running_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor = AsyncMock(return_value=12)
            mock_get_loop.return_value = mock_loop
            
            result = await mp.arun(simple_function, 6)
            
            assert result == 12
            mock_loop.run_in_executor.assert_called_once()
            args, kwargs = mock_loop.run_in_executor.call_args
            assert args[0] == mock_pool
            assert args[1] == task_wrapper
            assert args[2] == simple_function
            assert args[3] == mock_log_context
            assert args[4] == 6
    
    @pytest.mark.asyncio
    @patch('commons.thread.multiprocess.PassableLogContext')
    async def test_arun_with_multiple_args(self, mock_log_context_class):
        """Test arun method with multiple arguments"""
        mp = MultiProcess()
        
        # Mock log context
        mock_log_context = Mock()
        mock_log_context_class.dump.return_value = {}
        mock_log_context_class.return_value = mock_log_context
        
        # Mock process pool and event loop
        mock_pool = Mock()
        mp.process_pool = mock_pool
        
        with patch('asyncio.get_running_loop') as mock_get_loop:
            mock_loop = Mock()
            mock_loop.run_in_executor = AsyncMock(return_value=100)
            mock_get_loop.return_value = mock_loop
            
            result = await mp.arun(add_numbers, 30, 70)
            
            assert result == 100
            mock_loop.run_in_executor.assert_called_once()
            args, kwargs = mock_loop.run_in_executor.call_args
            assert args[4] == 30
            assert args[5] == 70
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_submit_basic_function(self, mock_log_context_class):
        """Test submit method (fire and forget)"""
        mp = MultiProcess()
        
        # Mock log context
        mock_log_context = Mock()
        mock_log_context_class.dump.return_value = {"key": "value"}
        mock_log_context_class.return_value = mock_log_context
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool.submit = Mock()
        mp.process_pool = mock_pool
        
        # submit should not return anything (fire and forget)
        result = mp.submit(simple_function, 8)
        
        assert result is None
        mock_pool.submit.assert_called_once()
        args, kwargs = mock_pool.submit.call_args
        assert args[0] == task_wrapper
        assert args[1] == simple_function
        assert args[2] == mock_log_context
        assert args[3] == 8
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_submit_with_kwargs(self, mock_log_context_class):
        """Test submit method with keyword arguments"""
        mp = MultiProcess()
        
        # Mock log context
        mock_log_context = Mock()
        mock_log_context_class.dump.return_value = {}
        mock_log_context_class.return_value = mock_log_context
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool.submit = Mock()
        mp.process_pool = mock_pool
        
        mp.submit(function_with_kwargs, 3, multiplier=5, offset=2)
        
        mock_pool.submit.assert_called_once()
        args, kwargs = mock_pool.submit.call_args
        assert 'multiplier' in kwargs
        assert 'offset' in kwargs
        assert kwargs['multiplier'] == 5
        assert kwargs['offset'] == 2
    
    def test_map_basic_functionality(self):
        """Test map method with basic functionality"""
        mp = MultiProcess()
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool.map.return_value = [2, 4, 6, 8]
        mp.process_pool = mock_pool
        
        iterable = [1, 2, 3, 4]
        result = mp.map(simple_function, iterable)
        
        assert result == [2, 4, 6, 8]
        mock_pool.map.assert_called_once_with(simple_function, iterable, timeout=None, chunksize=1)
    
    def test_map_with_timeout_and_chunksize(self):
        """Test map method with custom timeout and chunksize"""
        mp = MultiProcess()
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool.map.return_value = [10, 20, 30]
        mp.process_pool = mock_pool
        
        iterable = [5, 10, 15]
        result = mp.map(simple_function, iterable, timeout=30, chunksize=2)
        
        assert result == [10, 20, 30]
        mock_pool.map.assert_called_once_with(simple_function, iterable, timeout=30, chunksize=2)
    
    def test_map_with_multiple_iterables(self):
        """Test map method with multiple iterables"""
        mp = MultiProcess()
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool.map.return_value = [4, 9, 16]
        mp.process_pool = mock_pool
        
        iterable1 = [1, 2, 3]
        iterable2 = [3, 7, 13]
        result = mp.map(add_numbers, iterable1, iterable2)
        
        assert result == [4, 9, 16]
        mock_pool.map.assert_called_once()
        args, kwargs = mock_pool.map.call_args
        assert args[0] == add_numbers
        assert args[1] == iterable1
        assert args[2] == iterable2
    
    def test_map_empty_iterable(self):
        """Test map method with empty iterable"""
        mp = MultiProcess()
        
        # Mock process pool
        mock_pool = Mock()
        mock_pool.map.return_value = []
        mp.process_pool = mock_pool
        
        result = mp.map(simple_function, [])
        
        assert result == []
        mock_pool.map.assert_called_once_with(simple_function, [], timeout=None, chunksize=1)
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_integration_init_run_close(self, mock_log_context_class):
        """Test integration of init, run, and close methods"""
        mp = MultiProcess()
        
        # Mock log context
        mock_log_context = Mock()
        mock_log_context_class.dump.return_value = {}
        mock_log_context_class.return_value = mock_log_context
        
        with patch('commons.thread.multiprocess.ProcessPoolExecutor') as mock_executor_class:
            mock_pool = Mock()
            mock_pool._processes = []
            mock_pool.shutdown = Mock()
            
            mock_future = Mock()
            mock_future.result.return_value = 14
            mock_pool.submit.return_value = mock_future
            
            mock_executor_class.return_value = mock_pool
            
            # Initialize
            mp.init(max_workers=2)
            assert mp.process_pool is mock_pool
            
            # Run a task
            result = mp.run(simple_function, 7)
            assert result == 14
            
            # Close
            with patch('commons.thread.multiprocess.time.sleep'):
                mp.close()
            mock_pool.shutdown.assert_called_once()
    
    def test_multiple_instances_same_singleton(self):
        """Test that multiple MultiProcess instances are the same singleton"""
        mp1 = MultiProcess()
        mp2 = MultiProcess()
        mp3 = MultiProcess()
        
        assert mp1 is mp2 is mp3
        
        # Initialize one should affect all
        with patch('commons.thread.multiprocess.ProcessPoolExecutor') as mock_executor:
            mock_pool = Mock()
            mock_executor.return_value = mock_pool
            
            mp1.init(max_workers=4)
            
            assert mp1.process_pool is mock_pool
            assert mp2.process_pool is mock_pool
            assert mp3.process_pool is mock_pool
    
    @patch('commons.thread.multiprocess.PassableLogContext')
    def test_log_context_dump_called(self, mock_log_context_class):
        """Test that PassableLogContext.dump() is called correctly"""
        mp = MultiProcess()
        
        mock_dump_data = {"session_id": "test123", "user": "testuser"}
        mock_log_context_class.dump.return_value = mock_dump_data
        mock_log_instance = Mock()
        mock_log_context_class.return_value = mock_log_instance
        
        # Mock process pool
        mock_future = Mock()
        mock_future.result.return_value = "test_result"
        mock_pool = Mock()
        mock_pool.submit.return_value = mock_future
        mp.process_pool = mock_pool
        
        mp.run(simple_function, 5)
        
        # Verify PassableLogContext.dump was called
        mock_log_context_class.dump.assert_called_once()
        # Verify PassableLogContext was instantiated with dumped data
        mock_log_context_class.assert_called_once_with(**mock_dump_data)
    
    def test_error_handling_in_run(self):
        """Test error handling in run method"""
        mp = MultiProcess()
        
        with patch('commons.thread.multiprocess.PassableLogContext') as mock_log_context_class:
            mock_log_context = Mock()
            mock_log_context_class.dump.return_value = {}
            mock_log_context_class.return_value = mock_log_context
            
            # Mock process pool to raise exception in future.result()
            mock_future = Mock()
            mock_future.result.side_effect = ValueError("Process execution error")
            mock_pool = Mock()
            mock_pool.submit.return_value = mock_future
            mp.process_pool = mock_pool
            
            with pytest.raises(ValueError, match="Process execution error"):
                mp.run(simple_function, 5)
    
    @pytest.mark.asyncio
    async def test_error_handling_in_arun(self):
        """Test error handling in arun method"""
        mp = MultiProcess()
        
        with patch('commons.thread.multiprocess.PassableLogContext') as mock_log_context_class:
            mock_log_context = Mock()
            mock_log_context_class.dump.return_value = {}
            mock_log_context_class.return_value = mock_log_context
            
            mock_pool = Mock()
            mp.process_pool = mock_pool
            
            with patch('asyncio.get_running_loop') as mock_get_loop:
                mock_loop = Mock()
                mock_loop.run_in_executor = AsyncMock(side_effect=RuntimeError("Async execution error"))
                mock_get_loop.return_value = mock_loop
                
                with pytest.raises(RuntimeError, match="Async execution error"):
                    await mp.arun(simple_function, 5)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])