import pytest
import os
import time
from unittest.mock import Mo<PERSON>, patch, MagicMock
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, Future
from commons.thread.multithread import MultiThread


class TestMultiThread:
    """Test cases for MultiThread class"""
    
    def setup_method(self):
        """Reset cpu_count before each test"""
        MultiThread.cpu_count = os.cpu_count()
    
    def test_initial_cpu_count(self):
        """Test that initial cpu_count is set correctly"""
        assert MultiThread.cpu_count == os.cpu_count()
        assert isinstance(MultiThread.cpu_count, int)
        assert MultiThread.cpu_count > 0
    
    def test_set_cpu_count_positive_value(self):
        """Test setting cpu_count to positive value"""
        original_count = MultiThread.cpu_count
        new_count = 4
        
        MultiThread.set_cpu_count(new_count)
        
        assert MultiThread.cpu_count == new_count
        
        # Reset to original value
        MultiThread.cpu_count = original_count
    
    def test_set_cpu_count_different_values(self):
        """Test setting cpu_count to different values"""
        test_values = [1, 2, 8, 16]
        original_count = MultiThread.cpu_count
        
        for value in test_values:
            MultiThread.set_cpu_count(value)
            assert MultiThread.cpu_count == value
        
        # Reset to original value
        MultiThread.cpu_count = original_count
    
    def test_set_cpu_count_zero_or_negative(self):
        """Test setting cpu_count to zero or negative value"""
        original_count = MultiThread.cpu_count
        
        # Test zero
        MultiThread.set_cpu_count(0)
        assert MultiThread.cpu_count == 0
        
        # Test negative
        MultiThread.set_cpu_count(-1)
        assert MultiThread.cpu_count == -1
        
        # Reset to original value
        MultiThread.cpu_count = original_count
    
    def test_sync_run_with_simple_function(self):
        """Test sync_run with simple function and list"""
        def add_one(x):
            return x + 1
        
        items = [1, 2, 3, 4, 5]
        results = MultiThread.sync_run(add_one, items)
        
        # Results should contain all values + 1
        assert len(results) == len(items)
        assert sorted(results) == [2, 3, 4, 5, 6]
    
    def test_sync_run_with_additional_args(self):
        """Test sync_run with additional positional arguments"""
        def add_values(x, y, z):
            return x + y + z
        
        items = [1, 2, 3]
        results = MultiThread.sync_run(add_values, items, 10, 20)
        
        assert len(results) == len(items)
        assert sorted(results) == [31, 32, 33]  # 1+10+20, 2+10+20, 3+10+20
    
    def test_sync_run_with_kwargs(self):
        """Test sync_run with keyword arguments"""
        def multiply_with_factor(x, factor=2):
            return x * factor
        
        items = [2, 4, 6]
        results = MultiThread.sync_run(multiply_with_factor, items, factor=3)
        
        assert len(results) == len(items)
        assert sorted(results) == [6, 12, 18]  # 2*3, 4*3, 6*3
    
    def test_sync_run_with_mixed_args_kwargs(self):
        """Test sync_run with both positional and keyword arguments"""
        def complex_operation(x, y, multiplier=1):
            return (x + y) * multiplier
        
        items = [1, 2, 3]
        results = MultiThread.sync_run(complex_operation, items, 5, multiplier=2)
        
        assert len(results) == len(items)
        assert sorted(results) == [12, 14, 16]  # (1+5)*2, (2+5)*2, (3+5)*2
    
    def test_sync_run_with_none_results(self):
        """Test sync_run with function that returns None values"""
        def return_none_for_even(x):
            if x % 2 == 0:
                return None
            return x
        
        items = [1, 2, 3, 4, 5]
        results = MultiThread.sync_run(return_none_for_even, items)
        
        # Only odd numbers should be in results (None values filtered out)
        assert len(results) == 3
        assert sorted(results) == [1, 3, 5]
    
    def test_sync_run_with_false_results(self):
        """Test sync_run with function that returns False values"""
        def return_false_for_even(x):
            if x % 2 == 0:
                return False
            return x
        
        items = [1, 2, 3, 4, 5]
        results = MultiThread.sync_run(return_false_for_even, items)
        
        # False values should be filtered out
        assert len(results) == 3
        assert sorted(results) == [1, 3, 5]
    
    def test_sync_run_with_zero_results(self):
        """Test sync_run with function that returns 0"""
        def return_zero_for_one(x):
            if x == 1:
                return 0
            return x
        
        items = [1, 2, 3]
        results = MultiThread.sync_run(return_zero_for_one, items)
        
        # 0 is falsy, so should be filtered out
        assert len(results) == 2
        assert sorted(results) == [2, 3]
    
    def test_sync_run_with_truthy_results(self):
        """Test sync_run with truthy/falsy values"""
        def return_various_values(x):
            if x == 1:
                return ""  # falsy
            elif x == 2:
                return []  # falsy
            elif x == 3:
                return "hello"  # truthy
            elif x == 4:
                return [1, 2]  # truthy
            else:
                return x
        
        items = [1, 2, 3, 4, 5]
        results = MultiThread.sync_run(return_various_values, items)
        
        # Only truthy values should remain
        assert len(results) == 3
        assert "hello" in results
        assert [1, 2] in results
        assert 5 in results
    
    def test_sync_run_with_empty_iterator(self):
        """Test sync_run with empty iterator"""
        def dummy_func(x):
            return x
        
        items = []
        results = MultiThread.sync_run(dummy_func, items)
        
        assert results == []
    
    def test_sync_run_with_single_item(self):
        """Test sync_run with single item iterator"""
        def square(x):
            return x * x
        
        items = [5]
        results = MultiThread.sync_run(square, items)
        
        assert results == [25]
    
    def test_sync_run_with_generator(self):
        """Test sync_run with generator iterator"""
        def double(x):
            return x * 2
        
        def number_generator():
            for i in range(3):
                yield i + 1
        
        results = MultiThread.sync_run(double, number_generator())
        
        assert len(results) == 3
        assert sorted(results) == [2, 4, 6]
    
    def test_sync_run_with_custom_iterator(self):
        """Test sync_run with custom iterator class"""
        def add_ten(x):
            return x + 10
        
        class CustomIterator:
            def __init__(self):
                self.items = [1, 2, 3]
                self.index = 0
            
            def __iter__(self):
                return self
            
            def __next__(self):
                if self.index < len(self.items):
                    result = self.items[self.index]
                    self.index += 1
                    return result
                raise StopIteration
        
        custom_iter = CustomIterator()
        results = MultiThread.sync_run(add_ten, custom_iter)
        
        assert len(results) == 3
        assert sorted(results) == [11, 12, 13]
    
    def test_sync_run_with_exception_in_function(self):
        """Test sync_run when function raises exception"""
        def raise_error_for_three(x):
            if x == 3:
                raise ValueError(f"Error for {x}")
            return x * 2
        
        items = [1, 2, 3, 4]
        
        # The function should handle exceptions and continue with other items
        with pytest.raises(ValueError):
            MultiThread.sync_run(raise_error_for_three, items)
    
    def test_sync_run_with_slow_function(self):
        """Test sync_run with function that takes time"""
        def slow_function(x):
            time.sleep(0.01)  # Small delay
            return x * 2
        
        items = [1, 2, 3]
        start_time = time.time()
        results = MultiThread.sync_run(slow_function, items)
        end_time = time.time()
        
        # Should complete and parallelism should make it faster than sequential
        assert len(results) == 3
        assert sorted(results) == [2, 4, 6]
        # With parallelism, should be faster than 3 * 0.01 seconds
        assert (end_time - start_time) < 0.05
    
    def test_sync_run_with_different_cpu_counts(self):
        """Test sync_run with different cpu_count settings"""
        def simple_func(x):
            return x * 3
        
        items = [1, 2, 3, 4]
        original_count = MultiThread.cpu_count
        
        # Test with different cpu counts
        for cpu_count in [1, 2, 4]:
            MultiThread.set_cpu_count(cpu_count)
            results = MultiThread.sync_run(simple_func, items)
            assert len(results) == 4
            assert sorted(results) == [3, 6, 9, 12]
        
        # Reset to original
        MultiThread.cpu_count = original_count
    
    @patch('commons.thread.multithread.ThreadPoolExecutor')
    def test_sync_run_uses_correct_max_workers(self, mock_executor_class):
        """Test that sync_run uses correct max_workers"""
        mock_executor = MagicMock()
        mock_executor.__enter__ = MagicMock(return_value=mock_executor)
        mock_executor.__exit__ = MagicMock(return_value=None)
        mock_executor_class.return_value = mock_executor
        
        # Mock submit and as_completed
        mock_future = MagicMock()
        mock_future.result.return_value = "test_result"
        mock_executor.submit.return_value = mock_future
        
        with patch('commons.thread.multithread.as_completed') as mock_as_completed:
            mock_as_completed.return_value = [mock_future]
            
            def dummy_func(x):
                return x
            
            items = [1, 2, 3]
            MultiThread.set_cpu_count(8)
            
            MultiThread.sync_run(dummy_func, items)
            
            # Verify ThreadPoolExecutor was called with correct max_workers
            mock_executor_class.assert_called_once_with(max_workers=8)
    
    @patch('commons.thread.multithread.as_completed')
    @patch('commons.thread.multithread.ThreadPoolExecutor')
    def test_sync_run_future_handling(self, mock_executor_class, mock_as_completed):
        """Test proper future handling in sync_run"""
        mock_executor = MagicMock()
        mock_executor.__enter__ = MagicMock(return_value=mock_executor)
        mock_executor.__exit__ = MagicMock(return_value=None)
        mock_executor_class.return_value = mock_executor
        
        # Create mock futures with different results
        mock_future1 = MagicMock()
        mock_future1.result.return_value = "result1"
        mock_future2 = MagicMock()
        mock_future2.result.return_value = None  # This should be filtered out
        mock_future3 = MagicMock()
        mock_future3.result.return_value = "result3"
        
        mock_executor.submit.side_effect = [mock_future1, mock_future2, mock_future3]
        mock_as_completed.return_value = [mock_future1, mock_future2, mock_future3]
        
        def dummy_func(x):
            return x
        
        items = [1, 2, 3]
        results = MultiThread.sync_run(dummy_func, items)
        
        # Should only include non-None results
        assert len(results) == 2
        assert "result1" in results
        assert "result3" in results
        assert None not in results
    
    def test_sync_run_preserves_function_signature(self):
        """Test that sync_run preserves the function signature and arguments"""
        call_log = []
        
        def logging_func(x, y, z=None, **kwargs):
            call_log.append((x, y, z, kwargs))
            return f"{x}-{y}-{z}"
        
        items = [1, 2]
        results = MultiThread.sync_run(logging_func, items, "fixed_y", z="fixed_z", extra="extra_value")
        
        # Check that function was called with correct arguments
        assert len(call_log) == 2
        assert (1, "fixed_y", "fixed_z", {"extra": "extra_value"}) in call_log
        assert (2, "fixed_y", "fixed_z", {"extra": "extra_value"}) in call_log
        
        # Check results
        assert len(results) == 2
        assert "1-fixed_y-fixed_z" in results
        assert "2-fixed_y-fixed_z" in results

    def test_sync_run_with_print_function_like_main_example(self, capfd):
        """Test sync_run with print function similar to __main__ example"""
        def func(a, b):
            """Function similar to the one in __main__ block"""
            print(a + b)
            return a + b  # Return value so it's not filtered out
        
        items = [1, 2, 3, 4]
        results = MultiThread.sync_run(func, items, 10)
        
        # Check results (should be [11, 12, 13, 14])
        assert len(results) == 4
        assert sorted(results) == [11, 12, 13, 14]
        
        # Check that print was called (output should contain the sums)
        captured = capfd.readouterr()
        assert "11" in captured.out or "12" in captured.out or "13" in captured.out or "14" in captured.out

    def test_sync_run_with_custom_iterator_like_main_example(self):
        """Test sync_run with custom iterator class similar to __main__ example"""
        class IteratorClass(object):
            """Iterator class similar to the one in __main__ block"""
            def __init__(self):
                self.items = [2, 4, 6, 8]

            def __iter__(self):
                for item in self.items:
                    yield item
        
        def func(a, b):
            """Function similar to the one in __main__ block"""
            return a + b
        
        # Test with custom iterator and additional argument
        results = MultiThread.sync_run(func, IteratorClass(), 20)
        
        # Check results (should be [22, 24, 26, 28])
        assert len(results) == 4
        assert sorted(results) == [22, 24, 26, 28]

    def test_sync_run_main_example_scenarios_combined(self, capfd):
        """Test scenarios that mirror the exact __main__ block examples"""
        # First scenario: func with print, list items, and argument 10
        def func(a, b):
            print(a + b)
            return a + b  # Return so it's not filtered as falsy
        
        items = [1, 2, 3, 4]
        results1 = MultiThread.sync_run(func, items, 10)
        
        assert len(results1) == 4
        assert sorted(results1) == [11, 12, 13, 14]
        
        # Second scenario: same func with IteratorClass and argument 20
        class IteratorClass(object):
            def __init__(self):
                self.items = [2, 4, 6, 8]

            def __iter__(self):
                for item in self.items:
                    yield item
        
        results2 = MultiThread.sync_run(func, IteratorClass(), 20)
        
        assert len(results2) == 4
        assert sorted(results2) == [22, 24, 26, 28]
        
        # Verify print output exists
        captured = capfd.readouterr()
        output_lines = captured.out.strip().split('\n') if captured.out.strip() else []
        # Should have output from both runs (8 total prints)
        assert len([line for line in output_lines if line.strip()]) >= 4

    def test_sync_run_with_void_function_like_main(self, capfd):
        """Test sync_run with void function that only prints (like main example)"""
        def func(a, b):
            """Function that only prints, returns None (like original main example)"""
            print(a + b)
            # Explicitly return None like the original function
        
        items = [1, 2, 3, 4]
        results = MultiThread.sync_run(func, items, 10)
        
        # Results should be empty because func returns None (falsy)
        assert results == []
        
        # But print should have occurred
        captured = capfd.readouterr()
        assert captured.out.strip()  # Should have some output

    def test_iterator_class_instantiation_and_iteration(self):
        """Test the IteratorClass from main example for complete coverage"""
        class IteratorClass(object):
            def __init__(self):
                self.items = [2, 4, 6, 8]

            def __iter__(self):
                for item in self.items:
                    yield item
        
        # Test instantiation
        iterator_instance = IteratorClass()
        assert iterator_instance.items == [2, 4, 6, 8]
        
        # Test iteration
        collected_items = list(iterator_instance)
        assert collected_items == [2, 4, 6, 8]
        
        # Test multiple iterations work
        collected_again = list(iterator_instance)
        assert collected_again == [2, 4, 6, 8]

    @patch('builtins.__name__', '__main__')
    def test_main_block_simulation(self, capfd):
        """Simulate the main block execution by mocking __name__"""
        # This approach tries to simulate what happens in the __main__ block
        # by redefining the same functions and classes locally
        
        def func(a, b):
            print(a + b)
        
        items = [1, 2, 3, 4]
        MultiThread.sync_run(func, items, 10)
        
        class IteratorClass(object):
            def __init__(self):
                self.items = [2, 4, 6, 8]
            
            def __iter__(self):
                for item in self.items:
                    yield item
        
        MultiThread.sync_run(func, IteratorClass(), 20)
        
        # Verify output was produced
        captured = capfd.readouterr()
        output_lines = [line.strip() for line in captured.out.split('\n') if line.strip()]
        
        # Should have output from both MultiThread.sync_run calls
        assert len(output_lines) >= 4  # At least some output from the print statements

    def test_exact_main_reproduction_for_coverage(self, capfd):
        """Reproduce the exact scenarios from __main__ for maximum coverage"""
        # Define func exactly as in __main__ 
        def func(a, b):
            print(a + b)
        
        # First call: MultiThread.sync_run(func, items, 10)
        items = [1, 2, 3, 4]
        MultiThread.sync_run(func, items, 10)
        
        # Define IteratorClass exactly as in __main__
        class IteratorClass(object):
            def __init__(self):
                self.items = [2, 4, 6, 8]
            
            def __iter__(self):
                for item in self.items:
                    yield item
        
        # Second call: MultiThread.sync_run(func, IteratorClass(), 20)
        MultiThread.sync_run(func, IteratorClass(), 20)
        
        # Verify the prints occurred
        captured = capfd.readouterr()
        output = captured.out
        
        # Should have printed values from both runs
        # First run: 1+10=11, 2+10=12, 3+10=13, 4+10=14
        # Second run: 2+20=22, 4+20=24, 6+20=26, 8+20=28
        assert output  # Should have some output
        
        # Convert output to numbers to verify correct computation
        output_numbers = []
        for line in output.strip().split('\n'):
            if line.strip().isdigit():
                output_numbers.append(int(line.strip()))
        
        # Should have some numbers from the computations
        assert len(output_numbers) >= 4


if __name__ == "__main__":
    pytest.main([__file__, "-v"])