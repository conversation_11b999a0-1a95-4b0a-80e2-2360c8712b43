"""
commons.context.ai_context 模块的测试
"""
import pytest
import os
import json
from unittest.mock import patch, Mock
from pydantic import ValidationError

from commons.context.ai_context import (
    _get_class_or_method_name,
    LLMConfig,
    AiContext,
    AiContextManager,
    ai_context
)
from commons.llm_gateway.llm import LLModelRpc


class TestGetClassOrMethodName:
    """测试 _get_class_or_method_name 函数"""

    def test_string_input(self):
        """测试字符串输入"""
        result = _get_class_or_method_name("test_function")
        assert result == "llm_config_test_function"

    def test_class_input(self):
        """测试类输入"""
        class TestClass:
            pass
        
        result = _get_class_or_method_name(TestClass)
        assert result == "llm_config_TestClass"

    def test_callable_input(self):
        """测试可调用对象输入"""
        def test_function():
            pass
        
        result = _get_class_or_method_name(test_function)
        assert result == "llm_config_test_function"

    def test_invalid_input(self):
        """测试无效输入"""
        with pytest.raises(TypeError, match="参数类型错误"):
            _get_class_or_method_name(123)


class TestLLMConfig:
    """测试 LLMConfig 类"""

    def test_init_with_defaults(self):
        """测试使用默认值初始化"""
        config = LLMConfig()
        assert config.gateway == LLModelRpc.Gateway.Public
        assert config.provider == ""
        assert config.model == ""
        assert config.version == ""
        assert config.sft_base_model == ""

    def test_init_with_values(self):
        """测试使用指定值初始化"""
        config = LLMConfig(
            gateway=LLModelRpc.Gateway.Private,
            provider="zhipu",
            model="glm4",
            version="v1.0",
            sft_base_model="base_model"
        )
        assert config.gateway == LLModelRpc.Gateway.Private
        assert config.provider == "zhipu"
        assert config.model == "glm4"
        assert config.version == "v1.0"
        assert config.sft_base_model == "base_model"

    def test_get_selector(self):
        """测试获取模型选择器"""
        config = LLMConfig(
            provider="zhipu",
            model="glm4",
            version="v1.0"
        )
        selector = config.get_selector()
        assert selector.provider == "zhipu"
        assert selector.model == "glm4"
        assert selector.version == "v1.0"


class TestAiContext:
    """测试 AiContext 类"""

    def test_init_with_defaults(self):
        """测试使用默认值初始化"""
        context = AiContext()
        assert context.ip is None
        assert context.request_id is None
        assert context.local_id is None
        assert context.uri is None
        assert context.log_index == 0
        assert context.wps_sid is None
        assert context.uid is None
        assert context.cid is None
        assert context.llm_configs == {}
        assert context.model_type is None
        assert context.product_name is None
        assert context.intention_code is None
        assert context.billing_token is None
        assert context.is_multi_modal is False

    def test_init_with_values(self):
        """测试使用指定值初始化"""
        llm_configs = {"test": LLMConfig()}
        context = AiContext(
            ip="***********",
            request_id="req-123",
            local_id="local-456",
            uri="/api/test",
            log_index=5,
            wps_sid="wps-789",
            uid="user-123",
            cid="company-456",
            llm_configs=llm_configs,
            model_type="chat",
            product_name="test_product",
            intention_code="intent_001",
            billing_token="bill-token",
            is_multi_modal=True
        )
        assert context.ip == "***********"
        assert context.request_id == "req-123"
        assert context.local_id == "local-456"
        assert context.uri == "/api/test"
        assert context.log_index == 5
        assert context.wps_sid == "wps-789"
        assert context.uid == "user-123"
        assert context.cid == "company-456"
        assert context.llm_configs == llm_configs
        assert context.model_type == "chat"
        assert context.product_name == "test_product"
        assert context.intention_code == "intent_001"
        assert context.billing_token == "bill-token"
        assert context.is_multi_modal is True


class TestAiContextManager:
    """测试 AiContextManager 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置上下文变量
        ai_context.set(AiContext())

    def test_set_and_get_ai_context(self):
        """测试设置和获取 AI 上下文"""
        test_context = AiContext(ip="***********", request_id="test-123")
        
        AiContextManager.set_ai_context(test_context)
        retrieved_context = AiContextManager.get_ai_context()
        
        assert retrieved_context.ip == "***********"
        assert retrieved_context.request_id == "test-123"

    def test_set_llm_config_new(self):
        """测试设置新的 LLM 配置"""
        config = LLMConfig(provider="zhipu", model="glm4")
        
        AiContextManager.set_llm_config("test_function", config)
        
        context = AiContextManager.get_ai_context()
        assert "llm_config_test_function" in context.llm_configs
        assert context.llm_configs["llm_config_test_function"].provider == "zhipu"

    def test_set_llm_config_duplicate(self):
        """测试设置重复的 LLM 配置"""
        config1 = LLMConfig(provider="zhipu", model="glm4")
        config2 = LLMConfig(provider="minimax", model="abab")
        
        AiContextManager.set_llm_config("test_function", config1)
        
        with pytest.raises(ValueError, match="已存在名为llm_config_test_function的LLM配置"):
            AiContextManager.set_llm_config("test_function", config2)

    def test_set_llm_config_from_env(self):
        """测试从环境变量设置 LLM 配置"""
        env_config = {
            "gateway": "Private",
            "provider": "zhipu",
            "model": "glm4",
            "version": "v1.0",
            "sft_base_model": "base"
        }
        
        with patch.dict(os.environ, {"llm_config_test_function": json.dumps(env_config)}):
            default_config = LLMConfig(provider="default", model="default")
            AiContextManager.set_llm_config("test_function", default_config)
            
            context = AiContextManager.get_ai_context()
            config = context.llm_configs["llm_config_test_function"]
            assert config.provider == "zhipu"
            assert config.model == "glm4"

    def test_get_llm_config_exists(self):
        """测试获取存在的 LLM 配置"""
        config = LLMConfig(provider="zhipu", model="glm4")
        AiContextManager.set_llm_config("test_function", config)
        
        retrieved_config = AiContextManager.get_llm_config("test_function")
        assert retrieved_config is not None
        assert retrieved_config.provider == "zhipu"
        assert retrieved_config.model == "glm4"

    def test_get_llm_config_not_exists(self):
        """测试获取不存在的 LLM 配置"""
        retrieved_config = AiContextManager.get_llm_config("nonexistent_function")
        assert retrieved_config is None

    def test_set_llm_config_with_class(self):
        """测试使用类设置 LLM 配置"""
        class TestClass:
            pass
        
        config = LLMConfig(provider="zhipu", model="glm4")
        AiContextManager.set_llm_config(TestClass, config)
        
        retrieved_config = AiContextManager.get_llm_config(TestClass)
        assert retrieved_config is not None
        assert retrieved_config.provider == "zhipu"

    def test_set_llm_config_with_callable(self):
        """测试使用可调用对象设置 LLM 配置"""
        def test_function():
            pass
        
        config = LLMConfig(provider="zhipu", model="glm4")
        AiContextManager.set_llm_config(test_function, config)
        
        retrieved_config = AiContextManager.get_llm_config(test_function)
        assert retrieved_config is not None
        assert retrieved_config.provider == "zhipu"


class TestAiContextVar:
    """测试 ai_context 上下文变量"""

    def test_default_value(self):
        """测试默认值"""
        # 创建新的上下文变量实例来测试默认值
        import contextvars
        test_context_var = contextvars.ContextVar('test_ai_context', default=AiContext())
        
        default_context = test_context_var.get()
        assert isinstance(default_context, AiContext)
        assert default_context.ip is None
        assert default_context.request_id is None

    def test_context_isolation(self):
        """测试上下文隔离"""
        import asyncio
        
        async def task1():
            context1 = AiContext(ip="***********")
            ai_context.set(context1)
            await asyncio.sleep(0.01)  # 让出控制权
            assert ai_context.get().ip == "***********"
        
        async def task2():
            context2 = AiContext(ip="***********")
            ai_context.set(context2)
            await asyncio.sleep(0.01)  # 让出控制权
            assert ai_context.get().ip == "***********"
        
        async def run_test():
            await asyncio.gather(task1(), task2())
        
        asyncio.run(run_test())
