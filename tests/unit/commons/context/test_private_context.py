"""
commons.context.private_context 模块的测试
"""
import pytest
import json
from unittest.mock import patch

from commons.context.private_context import (
    PrivateHeadersContext,
    get_private_context,
    set_private_context,
    private_context
)


class TestPrivateHeadersContext:
    """测试 PrivateHeadersContext 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.context = PrivateHeadersContext()

    def test_init(self):
        """测试初始化"""
        assert self.context._headers_context == {}

    def test_set_user_agent(self):
        """测试设置 User-Agent"""
        headers = {"User-Agent": "Mozilla/5.0 Test Browser"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["User-Agent"] == "Mozilla/5.0 Test Browser"

    def test_set_x_forwarded_for(self):
        """测试设置 X-Forwarded-For"""
        headers = {"X-Forwarded-For": "***********, ********"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["X-Forwarded-For"] == "***********, ********"

    def test_set_x_real_ip(self):
        """测试设置 X-Real-IP"""
        headers = {"X-Real-IP": "*************"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["X-Real-IP"] == "*************"

    def test_set_device_id(self):
        """测试设置 Device-ID"""
        headers = {"Device-ID": "device-12345"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["Device-ID"] == "device-12345"

    def test_set_device_name(self):
        """测试设置 Device-Name"""
        headers = {"Device-Name": "iPhone 13"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["Device-Name"] == "iPhone 13"

    def test_set_x_app_name(self):
        """测试设置 X-App-Name"""
        headers = {"X-App-Name": "WPS Office"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["X-App-Name"] == "WPS Office"

    def test_set_x_app_version(self):
        """测试设置 X-App-Version"""
        headers = {"X-App-Version": "1.2.3"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["X-App-Version"] == "1.2.3"

    def test_set_device_platform(self):
        """测试设置 Device-Platform"""
        headers = {"Device-Platform": "iOS"}
        self.context.set(headers)
        
        result = self.context.get()
        assert result["Device-Platform"] == "iOS"

    def test_set_multiple_headers(self):
        """测试设置多个头部"""
        headers = {
            "User-Agent": "Mozilla/5.0 Test Browser",
            "X-Forwarded-For": "***********, ********",
            "X-Real-IP": "*************",
            "Device-ID": "device-12345",
            "Device-Name": "iPhone 13",
            "X-App-Name": "WPS Office",
            "X-App-Version": "1.2.3",
            "Device-Platform": "iOS"
        }
        self.context.set(headers)
        
        result = self.context.get()
        assert result["User-Agent"] == "Mozilla/5.0 Test Browser"
        assert result["X-Forwarded-For"] == "***********, ********"
        assert result["X-Real-IP"] == "*************"
        assert result["Device-ID"] == "device-12345"
        assert result["Device-Name"] == "iPhone 13"
        assert result["X-App-Name"] == "WPS Office"
        assert result["X-App-Version"] == "1.2.3"
        assert result["Device-Platform"] == "iOS"

    def test_set_irrelevant_headers(self):
        """测试设置不相关的头部"""
        headers = {
            "Authorization": "Bearer token",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 Test Browser"
        }
        self.context.set(headers)
        
        result = self.context.get()
        # 只有 User-Agent 应该被保存
        assert result == {"User-Agent": "Mozilla/5.0 Test Browser"}

    def test_set_empty_headers(self):
        """测试设置空头部"""
        headers = {}
        self.context.set(headers)
        
        result = self.context.get()
        assert result == {}

    def test_set_overwrite_headers(self):
        """测试覆盖头部"""
        # 第一次设置
        headers1 = {"User-Agent": "Browser 1.0"}
        self.context.set(headers1)
        
        # 第二次设置
        headers2 = {"User-Agent": "Browser 2.0", "Device-ID": "device-123"}
        self.context.set(headers2)
        
        result = self.context.get()
        assert result["User-Agent"] == "Browser 2.0"
        assert result["Device-ID"] == "device-123"

    def test_get_empty_context(self):
        """测试获取空上下文"""
        result = self.context.get()
        assert result == {}


class TestPrivateContextFunctions:
    """测试私有上下文函数"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置上下文变量
        private_context.set("")

    def test_set_and_get_private_context(self):
        """测试设置和获取私有上下文"""
        test_context = {
            "user_id": "12345",
            "session_id": "session-abc",
            "permissions": ["read", "write"]
        }
        
        set_private_context(test_context)
        result = get_private_context()
        
        assert result == test_context

    def test_get_private_context_empty(self):
        """测试获取空的私有上下文"""
        result = get_private_context()
        assert result is None

    def test_get_private_context_empty_string(self):
        """测试获取空字符串的私有上下文"""
        private_context.set("")
        result = get_private_context()
        assert result is None

    def test_set_private_context_complex_data(self):
        """测试设置复杂数据的私有上下文"""
        test_context = {
            "user": {
                "id": "12345",
                "name": "Test User",
                "roles": ["admin", "user"]
            },
            "session": {
                "id": "session-abc",
                "created_at": "2024-01-01T00:00:00Z",
                "expires_at": "2024-01-02T00:00:00Z"
            },
            "metadata": {
                "source": "web",
                "version": "1.0.0"
            }
        }
        
        set_private_context(test_context)
        result = get_private_context()
        
        assert result == test_context
        assert result["user"]["id"] == "12345"
        assert result["session"]["id"] == "session-abc"
        assert result["metadata"]["source"] == "web"

    def test_set_private_context_with_special_characters(self):
        """测试设置包含特殊字符的私有上下文"""
        test_context = {
            "message": "Hello, 世界! 🌍",
            "symbols": "!@#$%^&*()",
            "unicode": "测试数据"
        }
        
        set_private_context(test_context)
        result = get_private_context()
        
        assert result == test_context
        assert result["message"] == "Hello, 世界! 🌍"
        assert result["symbols"] == "!@#$%^&*()"
        assert result["unicode"] == "测试数据"

    def test_set_private_context_overwrite(self):
        """测试覆盖私有上下文"""
        # 第一次设置
        context1 = {"key1": "value1"}
        set_private_context(context1)
        
        # 第二次设置
        context2 = {"key2": "value2"}
        set_private_context(context2)
        
        result = get_private_context()
        assert result == context2
        assert "key1" not in result
        assert result["key2"] == "value2"

    def test_private_context_json_serialization(self):
        """测试私有上下文的 JSON 序列化"""
        test_context = {
            "numbers": [1, 2, 3],
            "boolean": True,
            "null_value": None,
            "nested": {"inner": "value"}
        }
        
        set_private_context(test_context)
        
        # 直接检查存储的 JSON 字符串
        stored_json = private_context.get()
        parsed_context = json.loads(stored_json)
        
        assert parsed_context == test_context

    def test_get_private_context_invalid_json(self):
        """测试获取无效 JSON 的私有上下文"""
        # 直接设置无效的 JSON 字符串
        private_context.set("invalid json string")
        
        # 应该抛出 JSON 解析异常
        with pytest.raises(json.JSONDecodeError):
            get_private_context()

    def test_context_isolation(self):
        """测试上下文隔离"""
        import asyncio
        
        async def task1():
            context1 = {"task": "task1", "data": "data1"}
            set_private_context(context1)
            await asyncio.sleep(0.01)  # 让出控制权
            result = get_private_context()
            assert result["task"] == "task1"
        
        async def task2():
            context2 = {"task": "task2", "data": "data2"}
            set_private_context(context2)
            await asyncio.sleep(0.01)  # 让出控制权
            result = get_private_context()
            assert result["task"] == "task2"
        
        async def run_test():
            await asyncio.gather(task1(), task2())
        
        asyncio.run(run_test())
