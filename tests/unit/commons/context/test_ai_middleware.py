"""
commons.context.ai_middleware 模块的测试
"""
import pytest
import uuid
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request, Response
from fastapi.datastructures import Headers

from commons.context.ai_middleware import AiMiddleware
from commons.context.ai_context import <PERSON><PERSON><PERSON>xtMana<PERSON>, AiContext


class TestAiMiddleware:
    """测试 AiMiddleware 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建模拟的 FastAPI 应用
        self.mock_app = Mock()
        self.middleware = AiMiddleware(self.mock_app)

    def test_init(self):
        """测试中间件初始化"""
        assert self.middleware.app == self.mock_app

    @pytest.mark.asyncio
    async def test_dispatch_with_client_request_id(self):
        """测试使用 Client-Request-Id 的请求处理"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "Client-Request-Id": "test-client-request-id",
            "wps_sid": "test-wps-sid"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {"route": Mock(path="/api/test")}
        mock_request.url.path = "/api/test"

        # 创建模拟响应
        mock_response = Mock(spec=Response)

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response

        # 执行中间件
        with patch.object(AiContextManager, 'set_ai_context') as mock_set_context:
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # 验证结果
            assert result == mock_response
            mock_set_context.assert_called_once()
            
            # 验证传递给 set_ai_context 的参数
            call_args = mock_set_context.call_args[0][0]
            assert isinstance(call_args, AiContext)
            assert call_args.ip == "*************"
            assert call_args.request_id == "test-client-request-id"
            assert call_args.uri == "/api/test"
            assert call_args.wps_sid == "test-wps-sid"
            assert call_args.local_id is not None  # 应该生成了 local_id

    @pytest.mark.asyncio
    async def test_dispatch_with_x_request_id(self):
        """测试使用 X-Request-Id 的请求处理"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "X-Request-Id": "test-x-request-id",
            "wps_sid": "test-wps-sid"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {"route": Mock(path="/api/test")}
        mock_request.url.path = "/api/test"

        # 创建模拟响应
        mock_response = Mock(spec=Response)

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response

        # 执行中间件
        with patch.object(AiContextManager, 'set_ai_context') as mock_set_context:
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # 验证结果
            assert result == mock_response
            mock_set_context.assert_called_once()
            
            # 验证传递给 set_ai_context 的参数
            call_args = mock_set_context.call_args[0][0]
            assert call_args.request_id == "test-x-request-id"

    @pytest.mark.asyncio
    async def test_dispatch_without_request_id(self):
        """测试没有请求ID的请求处理"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "wps_sid": "test-wps-sid"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {"route": Mock(path="/api/test")}
        mock_request.url.path = "/api/test"

        # 创建模拟响应
        mock_response = Mock(spec=Response)

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response

        # 执行中间件
        with patch('uuid.uuid4', return_value="generated-uuid"):
            with patch.object(AiContextManager, 'set_ai_context') as mock_set_context:
                result = await self.middleware.dispatch(mock_request, mock_call_next)

                # 验证结果
                assert result == mock_response
                mock_set_context.assert_called_once()
                
                # 验证传递给 set_ai_context 的参数
                call_args = mock_set_context.call_args[0][0]
                assert call_args.request_id == "generated-uuid"

    @pytest.mark.asyncio
    async def test_dispatch_without_route_in_scope(self):
        """测试 scope 中没有 route 的请求处理"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "Client-Request-Id": "test-request-id",
            "wps_sid": "test-wps-sid"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {}  # 没有 route
        mock_request.url.path = "/api/test"

        # 创建模拟响应
        mock_response = Mock(spec=Response)

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response

        # 执行中间件
        with patch.object(AiContextManager, 'set_ai_context') as mock_set_context:
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # 验证结果
            assert result == mock_response
            mock_set_context.assert_called_once()
            
            # 验证传递给 set_ai_context 的参数
            call_args = mock_set_context.call_args[0][0]
            assert call_args.uri == "/api/test"  # 应该使用 request.url.path

    @pytest.mark.asyncio
    async def test_dispatch_without_wps_sid(self):
        """测试没有 wps_sid 的请求处理"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "Client-Request-Id": "test-request-id"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {"route": Mock(path="/api/test")}
        mock_request.url.path = "/api/test"

        # 创建模拟响应
        mock_response = Mock(spec=Response)

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response

        # 执行中间件
        with patch.object(AiContextManager, 'set_ai_context') as mock_set_context:
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # 验证结果
            assert result == mock_response
            mock_set_context.assert_called_once()
            
            # 验证传递给 set_ai_context 的参数
            call_args = mock_set_context.call_args[0][0]
            assert call_args.wps_sid == ""  # 应该是空字符串

    @pytest.mark.asyncio
    async def test_dispatch_generates_local_id(self):
        """测试中间件生成 local_id"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "Client-Request-Id": "test-request-id"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {"route": Mock(path="/api/test")}
        mock_request.url.path = "/api/test"

        # 创建模拟响应
        mock_response = Mock(spec=Response)

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            return mock_response

        # 执行中间件
        with patch('uuid.uuid4', return_value="generated-local-uuid"):
            with patch.object(AiContextManager, 'set_ai_context') as mock_set_context:
                result = await self.middleware.dispatch(mock_request, mock_call_next)

                # 验证结果
                assert result == mock_response
                mock_set_context.assert_called_once()
                
                # 验证传递给 set_ai_context 的参数
                call_args = mock_set_context.call_args[0][0]
                assert call_args.local_id == "generated-local-uuid"

    @pytest.mark.asyncio
    async def test_dispatch_exception_propagation(self):
        """测试异常传播"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "Client-Request-Id": "test-request-id"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {"route": Mock(path="/api/test")}
        mock_request.url.path = "/api/test"

        # 创建抛出异常的下一个处理器
        async def mock_call_next(request):
            raise ValueError("Test exception")

        # 执行中间件，应该传播异常
        with patch.object(AiContextManager, 'set_ai_context'):
            with pytest.raises(ValueError, match="Test exception"):
                await self.middleware.dispatch(mock_request, mock_call_next)

    @pytest.mark.asyncio
    async def test_dispatch_context_set_before_call_next(self):
        """测试上下文在调用下一个处理器之前设置"""
        # 创建模拟请求
        mock_request = Mock(spec=Request)
        mock_request.headers = Headers({
            "Client-Request-Id": "test-request-id"
        })
        mock_request.client.host = "*************"
        mock_request.scope = {"route": Mock(path="/api/test")}
        mock_request.url.path = "/api/test"

        # 创建模拟响应
        mock_response = Mock(spec=Response)

        # 用于验证调用顺序的列表
        call_order = []

        # 创建模拟的下一个处理器
        async def mock_call_next(request):
            call_order.append("call_next")
            return mock_response

        # 模拟 set_ai_context
        def mock_set_context(context):
            call_order.append("set_context")

        # 执行中间件
        with patch.object(AiContextManager, 'set_ai_context', side_effect=mock_set_context):
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # 验证调用顺序
            assert call_order == ["set_context", "call_next"]
            assert result == mock_response
