import pytest
from unittest.mock import patch, <PERSON><PERSON>, AsyncMock
from starlette.requests import Request
from starlette.responses import Response
from starlette.applications import Starlette
from opentelemetry import trace
from opentelemetry.trace import SpanKind, StatusCode

from commons.trace.middleware import TraceMiddleware


class TestTraceMiddleware:
    """Test cases for TraceMiddleware class"""
    
    def setup_method(self):
        """Setup test environment"""
        self.app = Starlette()
        self.mock_tracer = Mock()
        self.middleware = TraceMiddleware(self.app, self.mock_tracer)
    
    def test_init_default_tracer(self):
        """Test middleware initialization with default tracer"""
        with patch('commons.trace.middleware.trace.get_tracer') as mock_get_tracer:
            mock_tracer = Mock()
            mock_get_tracer.return_value = mock_tracer
            
            middleware = TraceMiddleware(self.app)
            
            assert middleware._tracer is mock_tracer
            mock_get_tracer.assert_called_once_with("commons.trace.middleware")
    
    def test_init_custom_tracer(self):
        """Test middleware initialization with custom tracer"""
        custom_tracer = Mock()
        middleware = TraceMiddleware(self.app, custom_tracer)
        
        assert middleware._tracer is custom_tracer
    
    @pytest.mark.asyncio
    async def test_dispatch_successful_execution(self):
        """Test successful dispatch execution"""
        # Mock request and response
        mock_request = Mock()
        mock_request.url.path = "/test"
        mock_request.client.host = "127.0.0.1"
        mock_request.headers = {"X-Request-Id": "test-123"}
        
        mock_response = Mock()
        mock_call_next = AsyncMock(return_value=mock_response)
        
        # Mock the entire dispatch method to avoid complex span mocking
        with patch.object(self.middleware, 'dispatch', wraps=self.middleware.dispatch) as spy_dispatch:
            # Patch the internal components that we can't easily mock
            with patch('commons.trace.middleware.extract') as mock_extract, \
                 patch('commons.trace.middleware.format_trace_id') as mock_format_trace_id, \
                 patch('commons.trace.middleware.format_span_id') as mock_format_span_id:
                
                # Create a mock span context manager
                mock_span = Mock()
                mock_span_context = Mock()
                mock_span_context.trace_id = 123456789
                mock_span_context.span_id = 987654321
                mock_span.get_span_context.return_value = mock_span_context
                
                # Create proper context manager mock
                mock_context_manager = Mock()
                mock_context_manager.__enter__ = Mock(return_value=mock_span)
                mock_context_manager.__exit__ = Mock(return_value=None)
                
                self.mock_tracer.start_as_current_span.return_value = mock_context_manager
                mock_extract.return_value = Mock()
                mock_format_trace_id.return_value = "formatted_trace_id"
                mock_format_span_id.return_value = "formatted_span_id"
                
                result = await self.middleware.dispatch(mock_request, mock_call_next)
                
                assert result is mock_response
                mock_call_next.assert_called_once_with(mock_request)
                mock_extract.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_dispatch_with_exception_handling(self):
        """Test dispatch with exception handling"""
        mock_request = Mock()
        mock_request.url.path = "/error"
        mock_request.client.host = "127.0.0.1"
        mock_request.headers = {}
        
        test_exception = RuntimeError("Test exception")
        mock_call_next = AsyncMock(side_effect=test_exception)
        
        # Mock span and context manager
        mock_span = Mock()
        mock_span_context = Mock()
        mock_span_context.trace_id = 111111111
        mock_span_context.span_id = 222222222
        mock_span.get_span_context.return_value = mock_span_context
        
        # Create context manager that handles exceptions properly
        class MockSpanContextManager:
            def __enter__(self):
                return mock_span
            def __exit__(self, exc_type, exc_val, exc_tb):
                if exc_val:
                    mock_span.set_status(StatusCode.ERROR)
                    mock_span.record_exception(exc_val)
                return None
        
        self.mock_tracer.start_as_current_span.return_value = MockSpanContextManager()
        
        with patch('commons.trace.middleware.extract') as mock_extract, \
             patch('commons.trace.middleware.format_trace_id'), \
             patch('commons.trace.middleware.format_span_id'), \
             patch('commons.trace.middleware.uuid.uuid4') as mock_uuid4:
            
            mock_extract.return_value = Mock()
            mock_uuid4.return_value = "error-uuid"
            
            # Execute and expect exception
            with pytest.raises(RuntimeError, match="Test exception"):
                await self.middleware.dispatch(mock_request, mock_call_next)
            
            # Verify span error handling (may be called multiple times)
            mock_span.set_status.assert_called_with(StatusCode.ERROR)
            mock_span.record_exception.assert_called_with(test_exception)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])