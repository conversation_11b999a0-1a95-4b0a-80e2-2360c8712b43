"""
commons.crypt.model_decrypt 模块的测试
"""
import pytest
import os
import tempfile
import shutil
from unittest.mock import patch, Mock

from commons.crypt.model_decrypt import (
    decrypt_model, encrypt_model, DECRYPT_FILE_FLAG
)


class TestModelDecrypt:
    """测试模型解密功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建临时目录用于测试
        self.test_dir = tempfile.mkdtemp()
        self.model_dir = os.path.join(self.test_dir, "test_model")
        os.makedirs(self.model_dir)

    def teardown_method(self):
        """每个测试方法后的清理"""
        # 清理临时目录
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_decrypt_model_with_encrypted_files(self):
        """测试解密包含加密文件的模型"""
        # 创建测试文件
        normal_file = os.path.join(self.model_dir, "config.json")
        encrypted_file = os.path.join(self.model_dir, f"model.bin{DECRYPT_FILE_FLAG}")
        
        with open(normal_file, 'w') as f:
            f.write('{"model": "test"}')
        
        # 创建一个模拟的加密文件
        with open(encrypted_file, 'wb') as f:
            f.write(b"encrypted_content")
        
        # 模拟解密函数
        with patch('commons.crypt.model_decrypt.decrypt_file') as mock_decrypt:
            mock_decrypt.return_value = None  # decrypt_file 没有返回值
            
            # 执行解密
            result_path = decrypt_model(self.model_dir)
            
            # 验证结果
            assert os.path.exists(result_path)
            assert os.path.isdir(result_path)
            
            # 验证普通文件被复制
            copied_normal_file = os.path.join(result_path, "config.json")
            assert os.path.exists(copied_normal_file)
            with open(copied_normal_file, 'r') as f:
                assert f.read() == '{"model": "test"}'
            
            # 验证加密文件被解密
            # 由于路径可能有双斜杠，我们只检查调用是否发生
            mock_decrypt.assert_called_once()

    def test_decrypt_model_with_custom_path(self):
        """测试使用自定义路径解密模型"""
        custom_path = os.path.join(self.test_dir, "custom_output")
        
        # 创建测试文件
        test_file = os.path.join(self.model_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        # 执行解密
        result_path = decrypt_model(self.model_dir, custom_path)
        
        # 验证结果
        assert result_path == custom_path
        assert os.path.exists(custom_path)
        
        # 验证文件被复制
        copied_file = os.path.join(custom_path, "test.txt")
        assert os.path.exists(copied_file)

    def test_decrypt_model_with_model_name(self):
        """测试使用模型名称解密模型"""
        model_name = "my_model"
        
        # 创建测试文件
        test_file = os.path.join(self.model_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        # 执行解密
        result_path = decrypt_model(self.model_dir, model_name=model_name)
        
        # 验证结果路径包含模型名称
        assert model_name in result_path
        assert os.path.exists(result_path)

    def test_decrypt_model_with_subdirectories(self):
        """测试解密包含子目录的模型"""
        # 创建子目录和文件
        subdir = os.path.join(self.model_dir, "subdir")
        os.makedirs(subdir)
        
        main_file = os.path.join(self.model_dir, "main.txt")
        sub_file = os.path.join(subdir, "sub.txt")
        encrypted_sub_file = os.path.join(subdir, f"encrypted.bin{DECRYPT_FILE_FLAG}")
        
        with open(main_file, 'w') as f:
            f.write("main content")
        with open(sub_file, 'w') as f:
            f.write("sub content")
        with open(encrypted_sub_file, 'wb') as f:
            f.write(b"encrypted sub content")
        
        # 模拟 WalkDir 和 decrypt_file
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            with patch('commons.crypt.model_decrypt.decrypt_file') as mock_decrypt:
                # 模拟 WalkDir 返回所有文件
                mock_walk_instance = Mock()
                mock_walk_instance.return_value = [
                    ("main.txt", self.model_dir),
                    ("sub.txt", subdir),
                    (f"encrypted.bin{DECRYPT_FILE_FLAG}", subdir)
                ]
                mock_walk_dir.return_value = mock_walk_instance
                
                # 执行解密
                result_path = decrypt_model(self.model_dir)
                
                # 验证解密函数被调用
                mock_decrypt.assert_called_once()

    def test_decrypt_model_creates_directories(self):
        """测试解密时创建必要的目录"""
        # 创建嵌套的子目录结构
        deep_subdir = os.path.join(self.model_dir, "level1", "level2", "level3")
        os.makedirs(deep_subdir)
        
        test_file = os.path.join(deep_subdir, "deep_file.txt")
        with open(test_file, 'w') as f:
            f.write("deep content")
        
        # 模拟 WalkDir
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            mock_walk_instance = Mock()
            mock_walk_instance.return_value = [
                ("deep_file.txt", deep_subdir)
            ]
            mock_walk_dir.return_value = mock_walk_instance
            
            # 执行解密
            result_path = decrypt_model(self.model_dir)
            
            # 验证深层目录被创建
            expected_deep_dir = os.path.join(result_path, "level1", "level2", "level3")
            assert os.path.exists(expected_deep_dir)

    def test_decrypt_model_empty_directory(self):
        """测试解密空目录"""
        # 模拟 WalkDir 返回空列表
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            mock_walk_instance = Mock()
            mock_walk_instance.return_value = []
            mock_walk_dir.return_value = mock_walk_instance
            
            # 执行解密
            result_path = decrypt_model(self.model_dir)
            
            # 验证输出目录被创建
            assert os.path.exists(result_path)
            assert os.path.isdir(result_path)

    def test_decrypt_model_default_temp_path(self):
        """测试使用默认临时路径"""
        # 创建测试文件
        test_file = os.path.join(self.model_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        
        # 模拟时间戳
        with patch('time.time_ns', return_value=123456789):
            result_path = decrypt_model(self.model_dir)
            
            # 验证路径包含时间戳
            assert "123456789" in result_path
            assert tempfile.gettempdir() in result_path


class TestModelEncrypt:
    """测试模型加密功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建临时目录用于测试
        self.test_dir = tempfile.mkdtemp()
        self.source_dir = os.path.join(self.test_dir, "source")
        self.target_dir = os.path.join(self.test_dir, "target")
        os.makedirs(self.source_dir)
        os.makedirs(self.target_dir)

    def teardown_method(self):
        """每个测试方法后的清理"""
        # 清理临时目录
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_encrypt_model_single_file(self):
        """测试加密单个文件的模型"""
        # 创建源文件
        source_file = os.path.join(self.source_dir, "model.bin")
        with open(source_file, 'wb') as f:
            f.write(b"model binary content")
        
        # 模拟 WalkDir 和 encrypt_file
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            with patch('commons.crypt.model_decrypt.encrypt_file') as mock_encrypt:
                # 模拟 WalkDir 返回文件
                mock_walk_instance = Mock()
                mock_walk_instance.return_value = [
                    ("model.bin", self.source_dir)
                ]
                mock_walk_dir.return_value = mock_walk_instance
                
                # 执行加密
                encrypt_model(self.source_dir, self.target_dir)
                
                # 验证加密函数被调用
                expected_target_file = os.path.join(self.target_dir, f"model.bin{DECRYPT_FILE_FLAG}")
                mock_encrypt.assert_called_once_with(source_file, expected_target_file)

    def test_encrypt_model_multiple_files(self):
        """测试加密多个文件的模型"""
        # 创建多个源文件
        files = ["config.json", "model.bin", "vocab.txt"]
        for filename in files:
            filepath = os.path.join(self.source_dir, filename)
            with open(filepath, 'w') as f:
                f.write(f"content of {filename}")
        
        # 模拟 WalkDir 和 encrypt_file
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            with patch('commons.crypt.model_decrypt.encrypt_file') as mock_encrypt:
                # 模拟 WalkDir 返回所有文件
                mock_walk_instance = Mock()
                mock_walk_instance.return_value = [
                    (filename, self.source_dir) for filename in files
                ]
                mock_walk_dir.return_value = mock_walk_instance
                
                # 执行加密
                encrypt_model(self.source_dir, self.target_dir)
                
                # 验证所有文件都被加密
                assert mock_encrypt.call_count == len(files)

    def test_encrypt_model_with_subdirectories(self):
        """测试加密包含子目录的模型"""
        # 创建子目录和文件
        subdir = os.path.join(self.source_dir, "subdir")
        os.makedirs(subdir)
        
        main_file = os.path.join(self.source_dir, "main.txt")
        sub_file = os.path.join(subdir, "sub.txt")
        
        with open(main_file, 'w') as f:
            f.write("main content")
        with open(sub_file, 'w') as f:
            f.write("sub content")
        
        # 模拟 WalkDir 和 encrypt_file
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            with patch('commons.crypt.model_decrypt.encrypt_file') as mock_encrypt:
                with patch('os.makedirs') as mock_makedirs:
                    # 模拟 WalkDir 返回所有文件
                    mock_walk_instance = Mock()
                    mock_walk_instance.return_value = [
                        ("main.txt", self.source_dir),
                        ("sub.txt", subdir)
                    ]
                    mock_walk_dir.return_value = mock_walk_instance
                    
                    # 执行加密
                    encrypt_model(self.source_dir, self.target_dir)
                    
                    # 验证目录被创建
                    assert mock_makedirs.call_count >= 1
                    
                    # 验证文件被加密
                    assert mock_encrypt.call_count == 2

    def test_encrypt_model_creates_target_directories(self):
        """测试加密时创建目标目录"""
        # 创建源文件
        source_file = os.path.join(self.source_dir, "test.txt")
        with open(source_file, 'w') as f:
            f.write("test content")
        
        # 模拟 WalkDir
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            with patch('commons.crypt.model_decrypt.encrypt_file') as mock_encrypt:
                with patch('os.makedirs') as mock_makedirs:
                    # 模拟 WalkDir 返回文件
                    mock_walk_instance = Mock()
                    mock_walk_instance.return_value = [
                        ("test.txt", self.source_dir)
                    ]
                    mock_walk_dir.return_value = mock_walk_instance
                    
                    # 执行加密
                    encrypt_model(self.source_dir, self.target_dir)
                    
                    # 验证 makedirs 被调用
                    mock_makedirs.assert_called()

    def test_encrypt_model_print_output(self):
        """测试加密时的打印输出"""
        # 创建源文件
        source_file = os.path.join(self.source_dir, "test.txt")
        with open(source_file, 'w') as f:
            f.write("test content")
        
        # 模拟 WalkDir 和其他函数
        with patch('commons.crypt.model_decrypt.WalkDir') as mock_walk_dir:
            with patch('commons.crypt.model_decrypt.encrypt_file') as mock_encrypt:
                with patch('builtins.print') as mock_print:
                    # 模拟 WalkDir 返回文件
                    mock_walk_instance = Mock()
                    mock_walk_instance.return_value = [
                        ("test.txt", self.source_dir)
                    ]
                    mock_walk_dir.return_value = mock_walk_instance
                    
                    # 执行加密
                    encrypt_model(self.source_dir, self.target_dir)
                    
                    # 验证打印输出
                    mock_print.assert_called()
                    print_args = mock_print.call_args[0][0]
                    assert "已加密并保存为" in print_args


class TestConstants:
    """测试常量"""

    def test_decrypt_file_flag(self):
        """测试解密文件标志"""
        assert DECRYPT_FILE_FLAG == ".enc"
