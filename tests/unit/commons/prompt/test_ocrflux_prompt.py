import pytest
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from commons.prompt.ocrflux_prompt import prompt, prompt_pic
from commons.llm_gateway.models.chat_data import Message, SftMultiModalImage, SftMultiModalText, SftMultiModalImageUrl


class TestPromptConstants:
    """Test cases for prompt constants"""
    
    def test_prompt_constant_content(self):
        """Test that prompt constant contains expected content"""
        assert "文档的一页图像" in prompt
        assert "纯文本表示形式" in prompt
        assert "HTML 格式" in prompt
        assert "H1 标题" in prompt
        assert "不要产生幻觉" in prompt
    
    def test_prompt_pic_constant_content(self):
        """Test that prompt_pic constant contains expected content"""
        assert "一张图片" in prompt_pic
        assert "文字信息" in prompt_pic
        assert "纯文本表示形式" in prompt_pic
        assert "HTML 格式" in prompt_pic
        assert "H1 标题" in prompt_pic
        assert "不要产生幻觉" in prompt_pic
    
    def test_prompt_constants_are_strings(self):
        """Test that prompt constants are strings"""
        assert isinstance(prompt, str)
        assert isinstance(prompt_pic, str)
        assert len(prompt) > 0
        assert len(prompt_pic) > 0
    
    def test_prompt_constants_differences(self):
        """Test differences between prompt constants"""
        assert "文档的一页图像" in prompt
        assert "一张图片" in prompt_pic
        assert prompt != prompt_pic


if __name__ == "__main__":
    pytest.main([__file__, "-v"])