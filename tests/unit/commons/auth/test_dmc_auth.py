"""
commons.auth.dmc_checkauth 模块的测试
"""
import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request
from fastapi.datastructures import Headers

from commons.auth.dmc_checkauth import DmcCheckAuth


class TestDmcCheckAuth:
    """测试 DMC 认证功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        DmcCheckAuth._instances = {}
        self.dmc_auth = DmcCheckAuth()

    def test_init_success(self):
        """测试初始化成功"""
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance

            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

            assert self.dmc_auth._auth_rpc is not None
            mock_auth_request.assert_called_once()

    def test_init_failure(self):
        """测试初始化失败"""
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (400, '{"result": "error"}')
            mock_auth_request.return_value = mock_auth_instance

            with pytest.raises(Exception, match="init auth failed!"):
                self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

    def test_singleton_pattern(self):
        """测试单例模式"""
        auth1 = DmcCheckAuth()
        auth2 = DmcCheckAuth()
        assert auth1 is auth2

    @pytest.mark.asyncio
    async def test_authorization_success(self):
        """测试认证成功"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        # 模拟请求
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.url.query = "param=value"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2:test_ak:test_signature",
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        # 模拟 _check 方法返回成功
        with patch.object(self.dmc_auth, '_check', return_value=(True, False)):
            result = await self.dmc_auth.authorization(mock_request)
            assert result is True

    @pytest.mark.asyncio
    async def test_authorization_missing_date(self):
        """测试认证 - 缺少日期头"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.url.query = ""
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Authorization": "WPS-2:test_ak:test_signature",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=None)

        result = await self.dmc_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_missing_auth_header(self):
        """测试认证 - 缺少认证头"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.url.query = ""
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=None)

        result = await self.dmc_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_malformed_auth_header(self):
        """测试认证 - 格式错误的认证头"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.url.query = ""
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Authorization": "WPS-2:invalid_format",
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=None)

        result = await self.dmc_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_with_retry(self):
        """测试认证 - 带重试机制"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.url.query = ""
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2:test_ak:test_signature",
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        # 第一次检查失败但需要重试，第二次成功
        with patch.object(self.dmc_auth, '_check', side_effect=[(False, True), (True, False)]):
            with patch.object(self.dmc_auth, '_get_check_sk_list') as mock_get_sk_list:
                mock_get_sk_list.cache_clear = Mock()
                result = await self.dmc_auth.authorization(mock_request)
                assert result is True
                mock_get_sk_list.cache_clear.assert_called_once()

    def test_check_success(self):
        """测试 _check 方法成功"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        with patch.object(self.dmc_auth, '_get_check_sk_list', return_value=["test_sk"]):
            with patch('commons.auth.dmc_checkauth.sig_wps2') as mock_sig:
                mock_sig.return_value = {"Authorization": "test_auth_string"}

                result, retry = self.dmc_auth._check("test_auth_string", "/test", None, "test_ak", "test_date", "application/json")
                assert result is True
                assert retry is False

    def test_check_no_sk_list(self):
        """测试 _check 方法 - 无 SK 列表"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        with patch.object(self.dmc_auth, '_get_check_sk_list', return_value=None):
            result, retry = self.dmc_auth._check("test_auth_string", "/test", None, "test_ak", "test_date", "application/json")
            assert result is False
            assert retry is False

    def test_check_signature_mismatch(self):
        """测试 _check 方法 - 签名不匹配"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        with patch.object(self.dmc_auth, '_get_check_sk_list', return_value=["test_sk"]):
            with patch('commons.auth.dmc_checkauth.sig_wps2') as mock_sig:
                mock_sig.return_value = {"Authorization": "different_auth_string"}

                result, retry = self.dmc_auth._check("test_auth_string", "/test", None, "test_ak", "test_date", "application/json")
                assert result is False
                assert retry is True

    def test_get_check_sk_list_success(self):
        """测试 _get_check_sk_list 方法成功"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        # 模拟 RPC 调用返回
        mock_response = {
            "object": {
                "app_keys": [
                    {"app_key": "sk1"},
                    {"app_key": "sk2"}
                ]
            }
        }
        self.dmc_auth._auth_rpc.call.return_value = (200, json.dumps(mock_response))

        result = self.dmc_auth._get_check_sk_list("test_ak")
        assert result == ["sk1", "sk2"]

    def test_get_check_sk_list_exception(self):
        """测试 _get_check_sk_list 方法异常"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        # 模拟 RPC 调用抛出异常
        self.dmc_auth._auth_rpc.call.side_effect = Exception("Network error")

        with patch('logging.error') as mock_log_error:
            result = self.dmc_auth._get_check_sk_list("test_ak")
            assert result is None
            mock_log_error.assert_called_once()

    @pytest.mark.asyncio
    async def test_authorization_multipart_form_data(self):
        """测试处理 multipart/form-data 内容类型"""
        # 初始化
        with patch('commons.auth.dmc_checkauth.AuthRequest') as mock_auth_request:
            mock_auth_instance = Mock()
            mock_auth_instance.call.return_value = (200, '{"result": "ok"}')
            mock_auth_request.return_value = mock_auth_instance
            self.dmc_auth.init("http://test-host", "test_ak", "test_sk")

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.url.query = ""
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2:test_ak:test_signature",
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary"
        })
        mock_request.body = AsyncMock(return_value=None)

        with patch.object(self.dmc_auth, '_check', return_value=(True, False)) as mock_check:
            result = await self.dmc_auth.authorization(mock_request)
            assert result is True
            # 验证 content_type 被正确处理为 "multipart/form-data"
            mock_check.assert_called_once()
            args = mock_check.call_args[0]
            assert args[5] == "multipart/form-data"  # content_type 参数