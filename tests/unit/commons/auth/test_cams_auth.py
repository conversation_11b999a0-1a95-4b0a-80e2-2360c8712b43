"""
commons.auth.cams_checkauth 模块的测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request
from fastapi.datastructures import Headers

from commons.auth.private_checkauth import PrivateCheckAuth


class TestCamsCheckAuth:
    """测试 CAMS 认证功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        PrivateCheckAuth._instances = {}
        self.cams_auth = PrivateCheckAuth()
        self.test_aksk_dict = {
            "test_ak": "test_sk",
            "another_ak": "another_sk"
        }

    def test_init(self):
        """测试初始化"""
        self.cams_auth.init(self.test_aksk_dict)
        assert self.cams_auth._aksk_dict == self.test_aksk_dict

    def test_singleton_pattern(self):
        """测试单例模式"""
        auth1 = PrivateCheckAuth()
        auth2 = PrivateCheckAuth()
        assert auth1 is auth2

    @pytest.mark.asyncio
    async def test_authorization_wps4_success(self):
        """测试 WPS4 签名认证成功"""
        self.cams_auth.init(self.test_aksk_dict)

        # 模拟请求
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Wps-Docs-Authorization": "WPS-4 test_ak:test_signature",
            "Wps-Docs-Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        # 模拟签名验证
        with patch('commons.auth.cams_checkauth.sig_wps4') as mock_sig:
            mock_sig.return_value = {
                "Wps-Docs-Authorization": "WPS-4 test_ak:test_signature"
            }

            result = await self.cams_auth.authorization(mock_request)
            assert result is True

    @pytest.mark.asyncio
    async def test_authorization_wps4_invalid_ak(self):
        """测试 WPS4 签名认证 - 无效的 AK"""
        self.cams_auth.init(self.test_aksk_dict)

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Wps-Docs-Authorization": "WPS-4 invalid_ak:test_signature",
            "Wps-Docs-Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_wps4_missing_date(self):
        """测试 WPS4 签名认证 - 缺少日期头"""
        self.cams_auth.init(self.test_aksk_dict)

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Wps-Docs-Authorization": "WPS-4 test_ak:test_signature",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_wps4_malformed_auth_header(self):
        """测试 WPS4 签名认证 - 格式错误的认证头"""
        self.cams_auth.init(self.test_aksk_dict)

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Wps-Docs-Authorization": "WPS-4 invalid_format",
            "Wps-Docs-Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_wps2_success(self):
        """测试 WPS2 签名认证成功"""
        self.cams_auth.init(self.test_aksk_dict)

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2:test_ak:test_signature",
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        with patch('commons.auth.cams_checkauth.sig_wps2') as mock_sig:
            mock_sig.return_value = {
                "Authorization": "WPS-2:test_ak:test_signature"
            }

            result = await self.cams_auth.authorization(mock_request)
            assert result is True

    @pytest.mark.asyncio
    async def test_authorization_wps2_invalid_format(self):
        """测试 WPS2 签名认证 - 格式错误"""
        self.cams_auth.init(self.test_aksk_dict)

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2:invalid:format:extra",
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_no_auth_header(self):
        """测试认证 - 缺少认证头"""
        self.cams_auth.init(self.test_aksk_dict)

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "GET"
        mock_request.headers = Headers({
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_multipart_form_data(self):
        """测试处理 multipart/form-data 内容类型"""
        self.cams_auth.init(self.test_aksk_dict)

        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Wps-Docs-Authorization": "WPS-4 test_ak:test_signature",
            "Wps-Docs-Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary"
        })
        mock_request.body = AsyncMock(return_value=None)

        with patch('commons.auth.cams_checkauth.sig_wps4') as mock_sig:
            mock_sig.return_value = {
                "Wps-Docs-Authorization": "WPS-4 test_ak:test_signature"
            }

            result = await self.cams_auth.authorization(mock_request)
            assert result is True
            # 验证 content_type 被正确处理为 "multipart/form-data"
            mock_sig.assert_called_once()
            args = mock_sig.call_args[0]
            assert args[6] == "multipart/form-data"  # content_type 参数

    @pytest.mark.asyncio
    async def test_authorization_wps2_malformed_auth_header(self):
        """测试 WPS2 签名格式错误的认证头部"""
        self.cams_auth.init(self.test_aksk_dict)

        # 模拟请求 - 认证头部格式错误（缺少冒号）
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2 test_ak_no_colon",  # 缺少冒号，覆盖第32行
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_wps2_invalid_ak_format(self):
        """测试 WPS2 签名中 AK 格式错误"""
        self.cams_auth.init(self.test_aksk_dict)

        # 模拟请求 - AK:SK 格式错误（缺少冒号）
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2 invalid_ak_sk_format",  # AK:SK 格式错误，覆盖第35行
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_wps2_empty_sk(self):
        """测试 WPS2 签名中 SK 为空"""
        # 初始化时包含空 SK
        aksk_dict_with_empty_sk = {
            "test_ak": "",  # 空 SK，覆盖第55行
            "valid_ak": "valid_sk"
        }
        self.cams_auth.init(aksk_dict_with_empty_sk)

        # 模拟请求
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2 test_ak:test_signature",
            "Date": "Mon, 01 Jan 2024 00:00:00 GMT",
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False

    @pytest.mark.asyncio
    async def test_authorization_wps2_missing_date_header(self):
        """测试 WPS2 签名缺少 Date 头部"""
        self.cams_auth.init(self.test_aksk_dict)

        # 模拟请求 - 缺少 Date 头部
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/test/path"
        mock_request.method = "POST"
        mock_request.headers = Headers({
            "Authorization": "WPS-2 test_ak:test_signature",
            # 缺少 Date 头部，覆盖第60行
            "Content-Type": "application/json"
        })
        mock_request.body = AsyncMock(return_value=b'{"test": "data"}')

        result = await self.cams_auth.authorization(mock_request)
        assert result is False