"""
commons.monitor.prom 模块的测试
"""
import pytest
import platform
import threading
import time
from unittest.mock import Mock, patch, MagicMock
from urllib.error import HTTPError

from commons.monitor.prom import (
    getlocalip, MCounter, init_prometheus, get_mcount, mcount
)


class TestGetLocalIP:
    """测试获取本地IP功能"""

    @patch('commons.monitor.prom.platform.system')
    def test_getlocalip_linux(self, mock_platform):
        """测试在Linux系统下获取本地IP"""
        mock_platform.return_value.lower.return_value = "linux"
        
        with patch('socket.socket') as mock_socket:
            with patch('fcntl.ioctl') as mock_ioctl:
                with patch('socket.inet_ntoa') as mock_inet_ntoa:
                    # 模拟成功获取IP
                    mock_inet_ntoa.return_value = "*************"
                    mock_ioctl.return_value = b'\x00' * 20 + b'\xc0\xa8\x01\x64'  # *************
                    
                    result = getlocalip("eth0")
                    assert result == "*************"
                    
                    # 验证调用
                    mock_socket.assert_called_once()
                    mock_ioctl.assert_called_once()

    @patch('commons.monitor.prom.platform.system')
    def test_getlocalip_linux_exception(self, mock_platform):
        """测试在Linux系统下获取IP时发生异常"""
        mock_platform.return_value.lower.return_value = "linux"
        
        with patch('socket.socket') as mock_socket:
            with patch('fcntl.ioctl', side_effect=Exception("网络接口错误")):
                with patch('logging.error') as mock_log_error:
                    result = getlocalip("eth0")
                    assert result == "127.0.0.1"
                    mock_log_error.assert_called_once()

    @patch('commons.monitor.prom.platform.system')
    def test_getlocalip_non_linux(self, mock_platform):
        """测试在非Linux系统下获取本地IP"""
        mock_platform.return_value.lower.return_value = "windows"
        
        result = getlocalip("eth0")
        assert result == "127.0.0.1"


class TestMCounter:
    """测试监控计数器"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置全局变量
        global mcount
        mcount = None

    @patch('commons.monitor.prom.getlocalip')
    def test_init_basic(self, mock_getlocalip):
        """测试基本初始化"""
        mock_getlocalip.return_value = "*************"
        
        counter = MCounter(
            gateway="http://prometheus-gateway:9091",
            job_name_prefix="test-app"
        )
        
        assert counter.gateway == "http://prometheus-gateway:9091"
        assert counter.job_name_prefix == "test-app"
        assert counter.username is None
        assert counter.password is None
        assert counter.white_endpoint == []
        assert counter.app == "test-app"
        assert counter.ip == "*************"
        assert counter.job_name == "test-app:*************"

    @patch('commons.monitor.prom.getlocalip')
    def test_init_with_auth(self, mock_getlocalip):
        """测试带认证的初始化"""
        mock_getlocalip.return_value = "********"
        
        counter = MCounter(
            gateway="http://secure-gateway:9091",
            job_name_prefix="secure-app",
            username="admin",
            password="secret",
            white_endpoint=["/api/health", "/api/metrics"]
        )
        
        assert counter.username == "admin"
        assert counter.password == "secret"
        assert counter.white_endpoint == ["/api/health", "/api/metrics"]

    @patch('commons.monitor.prom.getlocalip')
    def test_init_creates_metrics(self, mock_getlocalip):
        """测试初始化创建指标"""
        mock_getlocalip.return_value = "*************"
        
        counter = MCounter("http://gateway:9091", "test_app")
        
        # 验证指标被创建
        assert hasattr(counter, 'http_request_count')
        assert hasattr(counter, 'http_request_duration')
        assert hasattr(counter, 'token_count')
        assert hasattr(counter, 'parse_duration')
        assert hasattr(counter, 'parse_count')
        assert hasattr(counter, 'parse_step_duration')
        assert hasattr(counter, 'parse_step_count')
        
        # 验证指标名称（下划线替换连字符）
        from prometheus_client import Counter, Histogram
        assert isinstance(counter.http_request_count, Counter)
        assert isinstance(counter.http_request_duration, Histogram)

    def test_auth_handler_with_credentials(self):
        """测试带凭据的认证处理器"""
        counter = MCounter("http://gateway:9091", "test-app", "user", "pass")
        
        with patch('commons.monitor.prom.basic_auth_handler') as mock_basic_auth:
            mock_basic_auth.return_value = "auth_result"
            
            result = counter._auth_handler("url", "POST", 30, {}, "data")
            
            assert result == "auth_result"
            mock_basic_auth.assert_called_once_with("url", "POST", 30, {}, "data", "user", "pass")

    def test_auth_handler_without_credentials(self):
        """测试不带凭据的认证处理器"""
        counter = MCounter("http://gateway:9091", "test-app")
        
        with patch('commons.monitor.prom.default_handler') as mock_default:
            mock_default.return_value = "default_result"
            
            result = counter._auth_handler("url", "GET", 30, {}, "data")
            
            assert result == "default_result"
            mock_default.assert_called_once_with("url", "GET", 30, {}, "data")

    @patch('commons.monitor.prom.getlocalip')
    def test_record_success(self, mock_getlocalip):
        """测试成功记录指标"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app", white_endpoint=["/api/test"])
        
        # 模拟指标对象
        mock_counter_metric = Mock()
        mock_histogram_metric = Mock()
        counter.http_request_count = Mock()
        counter.http_request_count.labels.return_value = mock_counter_metric
        counter.http_request_duration = Mock()
        counter.http_request_duration.labels.return_value = mock_histogram_metric
        
        # 记录指标
        counter.record("/api/test", "kafka_type", 1.5)
        
        # 验证调用
        counter.http_request_count.labels.assert_called_once_with("*************", "test-app", "/api/test", "kafka_type")
        mock_counter_metric.inc.assert_called_once()
        
        counter.http_request_duration.labels.assert_called_once_with("*************", "test-app", "/api/test", "kafka_type")
        mock_histogram_metric.observe.assert_called_once_with(1.5)

    @patch('commons.monitor.prom.getlocalip')
    def test_record_not_in_whitelist(self, mock_getlocalip):
        """测试记录不在白名单的端点"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app", white_endpoint=["/api/allowed"])
        
        # 模拟指标对象
        counter.http_request_count = Mock()
        counter.http_request_duration = Mock()
        
        # 记录不在白名单的端点
        counter.record("/api/blocked", "kafka_type", 1.0)
        
        # 验证没有调用指标记录
        counter.http_request_count.labels.assert_not_called()
        counter.http_request_duration.labels.assert_not_called()

    @patch('commons.monitor.prom.getlocalip')
    def test_record_without_whitelist(self, mock_getlocalip):
        """测试没有白名单时的记录"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")  # 没有白名单
        
        # 模拟指标对象
        mock_counter_metric = Mock()
        counter.http_request_count = Mock()
        counter.http_request_count.labels.return_value = mock_counter_metric
        counter.http_request_duration = Mock()
        
        # 记录指标
        counter.record("/any/endpoint", "kafka_type")
        
        # 验证调用（没有白名单限制）
        counter.http_request_count.labels.assert_called_once()
        mock_counter_metric.inc.assert_called_once()

    @patch('commons.monitor.prom.getlocalip')
    def test_record_exception_handling(self, mock_getlocalip):
        """测试记录时的异常处理"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")
        
        # 模拟指标抛出异常
        counter.http_request_count = Mock()
        counter.http_request_count.labels.side_effect = Exception("指标错误")
        
        with patch('logging.error') as mock_log_error:
            # 记录指标，不应该抛出异常
            counter.record("/api/test", "kafka_type", 1.0)
            
            # 验证异常被记录
            mock_log_error.assert_called_once()

    @patch('commons.monitor.prom.getlocalip')
    @patch('commons.monitor.prom.uri_context')
    def test_record_token_success(self, mock_uri_context, mock_getlocalip):
        """测试成功记录token指标"""
        mock_getlocalip.return_value = "*************"
        mock_uri_context.get.return_value = "/api/chat"
        
        counter = MCounter("http://gateway:9091", "test-app", white_endpoint=["/api/test"])
        
        # 模拟token计数器
        mock_completion_metric = Mock()
        mock_prompt_metric = Mock()
        mock_total_metric = Mock()
        counter.token_count = Mock()
        counter.token_count.labels.side_effect = [mock_completion_metric, mock_prompt_metric, mock_total_metric]
        
        # 记录token
        counter.record_token("/api/test", 100, 50, 150, "gpt-4", "openai", "v1")
        
        # 验证三次调用
        assert counter.token_count.labels.call_count == 3
        
        # 验证调用参数
        calls = counter.token_count.labels.call_args_list
        assert calls[0][0] == ("*************", "test-app", "/api/chat", "/api/test_completion_tokens", "no", "gpt-4", "openai", "v1")
        assert calls[1][0] == ("*************", "test-app", "/api/chat", "/api/test_prompt_tokens", "no", "gpt-4", "openai", "v1")
        assert calls[2][0] == ("*************", "test-app", "/api/chat", "/api/test_total_tokens", "no", "gpt-4", "openai", "v1")
        
        # 验证计数
        mock_completion_metric.inc.assert_called_once_with(100)
        mock_prompt_metric.inc.assert_called_once_with(50)
        mock_total_metric.inc.assert_called_once_with(150)

    @patch('commons.monitor.prom.getlocalip')
    def test_record_parse_success(self, mock_getlocalip):
        """测试成功记录解析指标"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")

        # 模拟解析指标
        mock_count_metric = Mock()
        mock_duration_metric = Mock()
        counter.parse_count = Mock()
        counter.parse_count.labels.return_value = mock_count_metric
        counter.parse_duration = Mock()
        counter.parse_duration.labels.return_value = mock_duration_metric

        # 记录解析指标
        counter.record_parse("pdf", "ocr", 2.5)

        # 验证调用
        counter.parse_count.labels.assert_called_once_with("*************", "test-app", "pdf", "ocr")
        mock_count_metric.inc.assert_called_once()

        counter.parse_duration.labels.assert_called_once_with("*************", "test-app", "pdf", "ocr")
        mock_duration_metric.observe.assert_called_once_with(2.5)

    @patch('commons.monitor.prom.getlocalip')
    def test_step_record_parse_success(self, mock_getlocalip):
        """测试成功记录步骤解析指标"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")

        # 模拟步骤解析指标
        mock_step_count_metric = Mock()
        mock_step_duration_metric = Mock()
        counter.parse_step_count = Mock()
        counter.parse_step_count.labels.return_value = mock_step_count_metric
        counter.parse_step_duration = Mock()
        counter.parse_step_duration.labels.return_value = mock_step_duration_metric

        # 记录步骤解析指标
        counter.step_record_parse("preprocessing", "extract_text", 1.2)

        # 验证调用
        counter.parse_step_count.labels.assert_called_once_with("*************", "test-app", "preprocessing", "extract_text")
        mock_step_count_metric.inc.assert_called_once()

        counter.parse_step_duration.labels.assert_called_once_with("*************", "test-app", "preprocessing", "extract_text")
        mock_step_duration_metric.observe.assert_called_once_with(1.2)

    @patch('commons.monitor.prom.getlocalip')
    def test_record_methods_exception_handling(self, mock_getlocalip):
        """测试记录方法的异常处理"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")

        # 模拟指标抛出异常
        counter.parse_count = Mock()
        counter.parse_count.labels.side_effect = Exception("解析指标错误")

        with patch('logging.error') as mock_log_error:
            # 记录解析指标，不应该抛出异常
            counter.record_parse("pdf", "layout", 3.0)

            # 验证异常被记录
            mock_log_error.assert_called_once()

    @patch('commons.monitor.prom.getlocalip')
    @patch('commons.monitor.prom.pushadd_to_gateway')
    @patch('time.sleep')
    def test_run_success_cycle(self, mock_sleep, mock_pushadd, mock_getlocalip):
        """测试成功的运行周期"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")

        # 模拟成功的推送
        mock_pushadd.return_value = None

        # 模拟运行一次循环后停止
        call_count = 0
        def side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count >= 2:  # 运行两次后停止
                raise KeyboardInterrupt("停止测试")

        mock_sleep.side_effect = side_effect

        # 运行线程
        with pytest.raises(KeyboardInterrupt):
            counter.run()

        # 验证推送被调用
        assert mock_pushadd.call_count >= 1
        mock_pushadd.assert_called_with(
            "http://gateway:9091",
            job="test-app:*************",
            registry=counter.registry,
            timeout=MCounter.push_timeout,
            handler=counter._auth_handler
        )

    @patch('commons.monitor.prom.getlocalip')
    @patch('commons.monitor.prom.pushadd_to_gateway')
    @patch('time.sleep')
    def test_run_http_401_error(self, mock_sleep, mock_pushadd, mock_getlocalip):
        """测试运行时遇到401认证错误"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")

        # 模拟401认证错误
        http_error = HTTPError("url", 401, "Unauthorized", {}, None)
        mock_pushadd.side_effect = http_error

        with patch('logging.error') as mock_log_error:
            # 运行应该在401错误时退出
            counter.run()

            # 验证错误被记录
            mock_log_error.assert_called()
            error_call = mock_log_error.call_args_list[0]
            assert "prometheus pushgateway auth fail" in str(error_call[0][0])

    @patch('commons.monitor.prom.getlocalip')
    @patch('commons.monitor.prom.pushadd_to_gateway')
    @patch('time.sleep')
    def test_run_other_http_error(self, mock_sleep, mock_pushadd, mock_getlocalip):
        """测试运行时遇到其他HTTP错误"""
        mock_getlocalip.return_value = "*************"
        counter = MCounter("http://gateway:9091", "test-app")

        # 模拟其他HTTP错误
        http_error = HTTPError("url", 500, "Internal Server Error", {}, None)

        call_count = 0
        def pushadd_side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:  # 前两次抛出异常
                raise http_error
            else:  # 第三次停止测试
                raise KeyboardInterrupt("停止测试")

        mock_pushadd.side_effect = pushadd_side_effect

        with patch('logging.error') as mock_log_error:
            with pytest.raises(KeyboardInterrupt):
                counter.run()

            # 验证错误被记录
            assert mock_log_error.call_count >= 2
            # 验证sleep被调用（错误重试机制）
            assert mock_sleep.call_count >= 2


class TestGlobalFunctions:
    """测试全局函数"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置全局变量
        global mcount
        mcount = None

    @patch('commons.monitor.prom.MCounter')
    def test_init_prometheus_with_gateway(self, mock_mcounter_class):
        """测试使用网关初始化Prometheus"""
        mock_counter_instance = Mock()
        mock_mcounter_class.return_value = mock_counter_instance

        init_prometheus(
            gateway="http://prometheus:9091",
            job_name_prefix="test-service",
            username="admin",
            password="secret",
            white_endpoint=["/health"]
        )

        # 验证MCounter被创建
        mock_mcounter_class.assert_called_once_with(
            "http://prometheus:9091",
            "test-service",
            "admin",
            "secret",
            ["/health"]
        )

        # 验证线程被启动
        mock_counter_instance.start.assert_called_once()

        # 验证全局变量被设置
        assert get_mcount() == mock_counter_instance

    def test_init_prometheus_without_gateway(self):
        """测试没有网关时的初始化"""
        # 重置全局变量
        import commons.monitor.prom as prom_module
        prom_module.mcount = None

        # 测试空字符串
        init_prometheus("", "test-service")
        assert get_mcount() is None

        # 测试None
        init_prometheus(None, "test-service")
        assert get_mcount() is None

    def test_get_mcount_returns_global_instance(self):
        """测试获取全局计数器实例"""
        # 设置全局实例
        import commons.monitor.prom as prom_module
        test_instance = Mock()
        prom_module.mcount = test_instance

        result = get_mcount()
        assert result == test_instance

    def test_get_mcount_returns_none_when_not_initialized(self):
        """测试未初始化时获取计数器返回None"""
        import commons.monitor.prom as prom_module
        prom_module.mcount = None

        result = get_mcount()
        assert result is None
