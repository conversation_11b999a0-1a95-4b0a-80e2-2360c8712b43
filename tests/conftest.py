"""
全局测试配置和夹具
"""
import os
import pytest
from unittest.mock import patch, MagicMock


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境变量和模拟对象"""
    # 设置测试所需的环境变量
    test_env_vars = {
        "SERVICE_ID": "test_service_id",
        "SERVICE_KEY": "test_service_key",
        "AK": "test_ak",
        "SK": "test_sk",
        "SK_SALT": "test_sk_salt",
        "conf_env": "test",  # 这将阻止加载 setting_dev.py
        "DMC_HOST": "https://test-dmc.wps.cn",
        "REDIS_HOST": "localhost",
        "REDIS_PORT": "6379",
        "REDIS_PASSWORD": "",
        "REDIS_DB": "0",
        "MYSQL_HOST": "localhost",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "test",
        "MYSQL_PASSWORD": "test",
        "MYSQL_DATABASE": "test",
        "KAFKA_BOOTSTRAP_SERVERS": "localhost:9092",
        "PARSE_VERSION": "v1.0.0-test",
        "TRACE_VERBOSE": "0",
        "LOG_LEVEL": "INFO",
        "MONITOR_NAME": "test-monitor",
        "PROMETHEUS_GATEWAY": "localhost:9091",
        "PROMETHEUS_JOB_NAME_PREFIX": "test",
        "PROMETHEUS_USERNAME": "",
        "PROMETHEUS_PASSWORD": "",
        "WPS365_API_HOST": "https://test-api.wps.cn",
        "WPS365_POOL_MAX": "10",
        "WPS365_OPENAPI_HOST": "https://test-openapi.wps.cn",
        "WPS365_OPENAPI_AK": "test_openapi_ak",
        "WPS365_OPENAPI_SK": "test_openapi_sk",
        "WPS365_WOA_CUSTOM_AK": "test_woa_ak",
        "STORE_HOST": "https://test-store.wps.cn",
        "STORE_AK": "test_store_ak",
        "STORE_SK": "test_store_sk",
        "STORE_BUCKET": "test-bucket",
        "LLM_GATEWAY_HOST": "https://test-llm.wps.cn",
        "LLM_GATEWAY_AK": "test_llm_ak",
        "LLM_GATEWAY_SK": "test_llm_sk",
        "OCR_MODEL_HOST": "https://test-ocr.wps.cn",
        "RECALL_CHUNK_HOST": "https://test-recall.wps.cn",
        "INSIGHT_HOST": "https://test-insight.wps.cn",
        "QSEARCH_HOST": "https://test-qsearch.wps.cn",
        "WPS365_KDC_HOST": "https://test-kdc.wps.cn",
        "DRIVE_V5_RPC_HOST": "https://test-drive.wps.cn",
        # aidocs 的额外环境变量
        "aidocs_dst_ak": "test_ak",
        "aidocs_dst_sk": "test_sk",
        "aidocs_dst_host": "http://localhost:8080",
        "aidocs_dst_bucket": "test_bucket",
        "aidocs_dst_region": "us-east-1",
        "aidocs_dst_endpoint": "http://localhost:9000",
        "aidocs_dst_public_endpoint": "http://localhost:9000",
        "aidocs_dst_internal_endpoint": "http://localhost:9000",
        "aidocs_dst_public_bucket": "test_public_bucket",
        "aidocs_dst_internal_bucket": "test_internal_bucket",
        "aidocs_dst_public_region": "us-east-1",
        "aidocs_dst_internal_region": "us-east-1",
        "aidocs_dst_public_ak": "test_public_ak",
        "aidocs_dst_public_sk": "test_public_sk",
        "aidocs_dst_internal_ak": "test_internal_ak",
        "aidocs_dst_internal_sk": "test_internal_sk",
        # 追踪和监控环境变量
        "TRACE_REDIS_HOST": "localhost",
        "TRACE_REDIS_PORT": "6379",
        "TRACE_REDIS_PASSWORD": "",
        "TRACE_REDIS_DB": "1",
        "TRACE_ENABLED": "false",
        "TRACE_SAMPLE_RATE": "0.1",
        "TRACE_SERVICE_NAME": "aidocs_dst_server",
        "TRACE_SERVICE_VERSION": "1.0.0",
        "TRACE_ENVIRONMENT": "test",
        # 添加缺少的环境变量
        "DST_CATALOG_FILTER_KEY": "[]",
        "DST_CATALOG_FILTER_FLAG": "[]",
        "DST_CHECKBOX_FILTER_KEY": "[]",
        "SERVICE_ID": "test_service_id",
        "SERVICE_KEY": "test_service_key",
        "LOG_LEVEL": "INFO",
        "KS3_AK": "test_ks3_ak",
        "KS3_SK": "test_ks3_sk",
        "KS3_BUCKET": "test_bucket",
        "REDIS_HOSTS": '["localhost:6379"]',
        "REDIS_PASSWORD": "",
        "KAE_REDIS_NODES": '["localhost:6379"]',
        "KAE_REDIS_PWD": "",
        "wps365_host": "https://test-wps365.wps.cn",
        "openapi_sk": "test_openapi_sk",
        "ai_privilege_sk": "test_privilege_sk",
        "aidocs_dst_ak": "test_ak",
        "aidocs_dst_sk": "test_sk",
        "db_host": "localhost",
        "db_user": "test",
        "db_pwd": "test",
        "drive_v5_ak": "test_drive_ak",
        "drive_v5_sk": "test_drive_sk",
        "ECIS_MINIO_ADDR": "localhost:9000",
        "ECIS_MINIO_AK": "test_minio_ak",
        "ECIS_MINIO_SK": "test_minio_sk",
        "ECIS_MINIO_BUCKET": "test_bucket",
        "ECIS_MINIO_PREFIX_0": "test_prefix",
        "PROM_PASSWORD": "test_password",
    }
    
    # 设置环境变量
    for key, value in test_env_vars.items():
        os.environ.setdefault(key, value)

    yield

    # 由于使用了 setdefault，不需要清理


@pytest.fixture(autouse=True)
def mock_external_dependencies():
    """模拟测试期间不应调用的外部依赖"""
    # 简单的模拟，不导入有问题的模块
    yield {}


@pytest.fixture
def mock_business_logger():
    """用于测试的业务日志记录器模拟"""
    mock_logger = MagicMock()
    mock_logger.debug = MagicMock()
    mock_logger.info = MagicMock()
    mock_logger.warning = MagicMock()
    mock_logger.error = MagicMock()
    return mock_logger


@pytest.fixture
def sample_file_data():
    """用于测试的示例文件数据"""
    return {
        'content': b'sample file content',
        'filename': 'test.pdf',
        'content_type': 'application/pdf',
        'size': 1024
    }


@pytest.fixture
def sample_dst_data():
    """用于测试的示例 DST（文档结构树）数据"""
    return {
        'type': 'paragraph',
        'content': 'Sample paragraph content',
        'level': 1,
        'page': 1,
        'position': {'x': 0, 'y': 0, 'width': 100, 'height': 20}
    }


@pytest.fixture
def sample_chunk_data():
    """用于测试的示例块数据"""
    return {
        'content': 'Sample chunk content',
        'page': 1,
        'chunk_id': 'chunk_001',
        'embedding': [0.1, 0.2, 0.3, 0.4, 0.5]
    }


@pytest.fixture
def sample_parse_request():
    """用于测试的示例解析请求数据"""
    return {
        'file_name': 'test.pdf',
        'file_type': 'PDF',
        'parse_target': ['chunk', 'summary'],
        'return_ks3_url': True,
        'embed_enabled': True
    }
