"""
端到端集成测试
测试完整的业务流程
"""
import pytest
import asyncio
import os
from unittest.mock import patch, AsyncMock
from typing import Dict, Any

# 设置测试环境变量
os.environ.setdefault("conf_env", "test")

# 简化的测试，不依赖外部库
class MockResponse:
    def __init__(self, json_data: Dict[str, Any], status_code: int = 200):
        self._json_data = json_data
        self.status_code = status_code
    
    def json(self) -> Dict[str, Any]:
        return self._json_data


class MockAsyncClient:
    """模拟异步HTTP客户端"""
    
    def __init__(self):
        self.request_count = 0
    
    async def post(self, url: str, **kwargs) -> MockResponse:
        self.request_count += 1
        
        if "parse_pipeline" in url:
            # 第一次返回等待状态，后续返回完成状态
            if self.request_count == 1:
                return MockResponse({
                    "code": 0,
                    "data": {
                        "status": "wait",
                        "token": "workflow-token-123",
                        "fileinfo": {"name": "test.pdf", "size": 1024}
                    }
                })
            else:
                return MockResponse({
                    "code": 0,
                    "data": {
                        "status": "ok",
                        "token": "workflow-token-123",
                        "parse_res": {"chunks": [{"content": "workflow content", "page": 1}]}
                    }
                })
        elif "general_parse_res" in url:
            return MockResponse({
                "code": 0,
                "data": {
                    "status": "ok",
                    "token": "workflow-token-123",
                    "parse_res": {
                        "chunks": [{"content": "workflow content", "page": 1}],
                        "summary": "workflow summary"
                    }
                }
            })
        elif "merge_table" in url:
            return MockResponse({
                "code": 0,
                "data": {"content": ["merged table row 1", "merged table row 2"]}
            })
        else:
            return MockResponse({"code": 0, "data": {}})
    
    async def get(self, url: str, **kwargs) -> MockResponse:
        if "parse_version" in url:
            return MockResponse({"code": 0, "data": {"version": "v1.0.0-test"}})
        elif "kafka_lag" in url:
            return MockResponse({
                "code": 0,
                "data": [{"topic": "parse-topic-low", "group_id": "parse-group-low", "total_lag": 10}]
            })
        elif "kafka_throughput" in url:
            return MockResponse({
                "code": 0,
                "data": [{"topic": "parse-topic-low", "group_id": "parse-group-low", "throughput": 20.0}]
            })
        else:
            return MockResponse({"code": 0, "data": {}})


@pytest.fixture
def async_test_client():
    """创建模拟的异步测试客户端"""
    return MockAsyncClient()


class TestEndToEndWorkflow:
    """端到端工作流程测试"""

    @pytest.mark.asyncio
    async def test_complete_parse_workflow(self, async_test_client):
        """测试完整的解析工作流程：上传文件 -> 解析 -> 获取结果"""
        # 步骤1: 提交解析任务
        parse_data = {
            "file_name": "test_document.pdf",
            "file_type": "pdf",
            "parse_target": ["chunk", "summary", "keywords"],
            "return_ks3_url": True,
            "embed_enabled": True,
            "req_type": "background",
            "req_level": "normal",
            "file_url": "https://example.com/test.pdf"
        }
        
        parse_response = await async_test_client.post(
            "/api/v1/aidocs_dst/parse_pipeline",
            json=parse_data
        )
        
        assert parse_response.status_code == 200
        parse_result = parse_response.json()
        assert parse_result["code"] == 0
        assert parse_result["data"]["status"] == "wait"
        token = parse_result["data"]["token"]
        
        # 步骤2: 获取解析结果
        result_data = {
            "token": token,
            "return_ks3_url": True,
            "parse_target": ["chunk", "summary", "keywords"]
        }
        
        result_response = await async_test_client.post(
            "/api/v1/aidocs_dst/general_parse_res",
            json=result_data
        )
        
        assert result_response.status_code == 200
        result = result_response.json()
        assert result["code"] == 0
        assert result["data"]["status"] == "ok"
        assert result["data"]["token"] == token
        assert "parse_res" in result["data"]

    @pytest.mark.asyncio
    async def test_parse_with_table_merge_workflow(self, async_test_client):
        """测试解析包含表格合并的工作流程"""
        # 步骤1: 获取解析结果（包含表格内容）
        result_data = {
            "token": "test-token-table-123",
            "return_ks3_url": False,
            "parse_target": ["chunk"]
        }
        
        parse_response = await async_test_client.post(
            "/api/v1/aidocs_dst/general_parse_res",
            json=result_data
        )
        
        assert parse_response.status_code == 200
        parse_result = parse_response.json()
        assert parse_result["code"] == 0
        
        # 步骤2: 合并表格（模拟从解析结果中提取的表格内容）
        table_content = [
            "表格行1: 列1 | 列2 | 列3",
            "表格行2: 数据1 | 数据2 | 数据3"
        ]
        merge_data = {"content": table_content}
        
        merge_response = await async_test_client.post(
            "/api/v1/aidocs_dst/merge_table",
            json=merge_data
        )
        
        assert merge_response.status_code == 200
        merge_result = merge_response.json()
        assert merge_result["code"] == 0
        assert len(merge_result["data"]["content"]) == 2

    @pytest.mark.asyncio
    async def test_system_health_check_workflow(self, async_test_client):
        """测试系统健康检查工作流程"""
        # 检查版本信息
        version_resp = await async_test_client.get("/api/v1/aidocs_dst/parse_version")
        assert version_resp.status_code == 200
        version_data = version_resp.json()
        assert version_data["code"] == 0
        assert version_data["data"]["version"] == "v1.0.0-test"
        
        # 检查Kafka延迟
        lag_resp = await async_test_client.get("/api/v1/aidocs_dst/kafka_lag")
        assert lag_resp.status_code == 200
        lag_data = lag_resp.json()
        assert lag_data["code"] == 0
        assert len(lag_data["data"]) == 1
        
        # 检查Kafka吞吐量
        throughput_resp = await async_test_client.get("/api/v1/aidocs_dst/kafka_throughput")
        assert throughput_resp.status_code == 200
        throughput_data = throughput_resp.json()
        assert throughput_data["code"] == 0
        assert len(throughput_data["data"]) == 1
        
        # 验证系统整体健康状态
        total_lag = sum(item["total_lag"] for item in lag_data["data"])
        avg_throughput = sum(item["throughput"] for item in throughput_data["data"]) / len(throughput_data["data"])
        
        assert total_lag < 100  # 总延迟应该在合理范围内
        assert avg_throughput > 10  # 平均吞吐量应该大于阈值

    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self, async_test_client):
        """测试错误恢复工作流程"""
        # 创建一个会模拟错误然后恢复的客户端
        class ErrorRecoveryMockClient(MockAsyncClient):
            def __init__(self):
                super().__init__()
                self.error_count = 0
            
            async def post(self, url: str, **kwargs) -> MockResponse:
                if "parse_pipeline" in url:
                    self.error_count += 1
                    if self.error_count == 1:
                        # 第一次调用失败
                        return MockResponse({"code": -1, "message": "Service Error"})
                    else:
                        # 第二次调用成功
                        return MockResponse({
                            "code": 0,
                            "data": {"status": "ok", "token": "recovery-token"}
                        })
                return await super().post(url, **kwargs)
        
        error_client = ErrorRecoveryMockClient()
        
        parse_data = {
            "file_name": "test.pdf",
            "file_type": "pdf",
            "parse_target": ["chunk"],
            "file_url": "https://example.com/test.pdf"
        }
        
        # 第一次请求失败
        first_response = await error_client.post(
            "/api/v1/aidocs_dst/parse_pipeline",
            json=parse_data
        )
        
        assert first_response.status_code == 200
        first_result = first_response.json()
        assert first_result["code"] == -1
        assert first_result["message"] == "Service Error"
        
        # 第二次请求成功
        second_response = await error_client.post(
            "/api/v1/aidocs_dst/parse_pipeline",
            json=parse_data
        )
        
        assert second_response.status_code == 200
        second_result = second_response.json()
        assert second_result["code"] == 0
        assert second_result["data"]["status"] == "ok"

    @pytest.mark.asyncio
    async def test_concurrent_requests_workflow(self, async_test_client):
        """测试并发请求工作流程"""
        # 创建并发请求
        tasks = []
        for i in range(3):
            parse_data = {
                "file_name": f"test{i}.pdf",
                "file_type": "pdf",
                "parse_target": ["chunk"],
                "req_type": "background",
                "file_url": f"https://example.com/test{i}.pdf"
            }
            
            task = async_test_client.post(
                "/api/v1/aidocs_dst/parse_pipeline",
                json=parse_data
            )
            tasks.append(task)
        
        # 等待所有请求完成
        responses = await asyncio.gather(*tasks)
        
        # 验证所有请求都成功
        for i, response in enumerate(responses):
            assert response.status_code == 200
            result = response.json()
            assert result["code"] == 0
            # 第一个请求返回wait状态，其他返回ok状态
            assert result["data"]["status"] in ["wait", "ok"]

    @pytest.mark.asyncio
    async def test_api_integration_workflow(self, async_test_client):
        """测试API集成工作流程"""
        # 测试所有主要API端点
        endpoints = [
            ("GET", "/api/v1/aidocs_dst/parse_version"),
            ("GET", "/api/v1/aidocs_dst/kafka_lag"),
            ("GET", "/api/v1/aidocs_dst/kafka_throughput"),
        ]
        
        for method, endpoint in endpoints:
            if method == "GET":
                response = await async_test_client.get(endpoint)
            else:
                response = await async_test_client.post(endpoint, json={})
            
            assert response.status_code == 200
            result = response.json()
            assert result["code"] == 0
        
        # 测试POST端点
        post_endpoints = [
            ("/api/v1/aidocs_dst/parse_pipeline", {"file_name": "test.pdf", "file_type": "pdf"}),
            ("/api/v1/aidocs_dst/general_parse_res", {"token": "test-token"}),
            ("/api/v1/aidocs_dst/merge_table", {"content": ["row1", "row2"]})
        ]
        
        for endpoint, data in post_endpoints:
            response = await async_test_client.post(endpoint, json=data)
            assert response.status_code == 200
            result = response.json()
            assert result["code"] == 0

    @pytest.mark.asyncio
    async def test_performance_workflow(self, async_test_client):
        """测试性能工作流程"""
        import time
        
        # 测试单个请求的性能
        start_time = time.time()
        response = await async_test_client.get("/api/v1/aidocs_dst/parse_version")
        single_request_time = time.time() - start_time
        
        assert response.status_code == 200
        assert single_request_time < 1.0  # 单个请求应该很快
        
        # 测试批量请求的性能
        start_time = time.time()
        tasks = [
            async_test_client.get("/api/v1/aidocs_dst/parse_version")
            for _ in range(10)
        ]
        responses = await asyncio.gather(*tasks)
        batch_request_time = time.time() - start_time
        
        # 验证所有请求都成功
        for response in responses:
            assert response.status_code == 200
            assert response.json()["code"] == 0
        
        # 批量请求的平均时间应该合理
        avg_time = batch_request_time / len(tasks)
        assert avg_time < 0.5  # 平均每个请求应该很快
