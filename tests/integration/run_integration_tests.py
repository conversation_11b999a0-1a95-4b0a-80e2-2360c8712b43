#!/usr/bin/env python3
"""
集成测试运行脚本
"""
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent.parent.parent
        )
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            return True
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ {description} - 异常: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(description="运行集成测试")
    parser.add_argument(
        "--test-type",
        choices=["all", "minimal", "router", "kafka", "e2e", "performance"],
        default="minimal",
        help="选择要运行的测试类型"
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="详细输出"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="生成覆盖率报告"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="并行运行测试"
    )
    
    args = parser.parse_args()
    
    # 基础pytest命令
    base_cmd = "python -m pytest tests/integration"
    
    # 添加选项
    verbose_flag = ""
    if args.verbose:
        verbose_flag = " -v"
    
    if args.coverage:
        base_cmd += " --cov=routers --cov=services --cov-report=html --cov-report=term"
    
    if args.parallel:
        base_cmd += " -n auto"
    
    # 根据测试类型选择测试文件
    test_commands = []

    if args.test_type == "minimal":
        test_commands = [
            (f"{base_cmd}/test_minimal_integration.py{verbose_flag}", "最小化集成测试（无外部依赖）")
        ]
    elif args.test_type == "all":
        test_commands = [
            (f"{base_cmd}/test_minimal_integration.py{verbose_flag}", "最小化集成测试"),
            (f"{base_cmd}/test_router_integration.py{verbose_flag}", "路由接口集成测试"),
            (f"{base_cmd}/test_kafka_integration.py{verbose_flag}", "Kafka接口集成测试"),
            (f"{base_cmd}/test_end_to_end.py{verbose_flag}", "端到端集成测试"),
            (f"{base_cmd}/test_performance.py{verbose_flag}", "性能集成测试")
        ]
    elif args.test_type == "router":
        test_commands = [
            (f"{base_cmd}/test_router_integration.py{verbose_flag}", "路由接口集成测试")
        ]
    elif args.test_type == "kafka":
        test_commands = [
            (f"{base_cmd}/test_kafka_integration.py{verbose_flag}", "Kafka接口集成测试")
        ]
    elif args.test_type == "e2e":
        test_commands = [
            (f"{base_cmd}/test_end_to_end.py{verbose_flag}", "端到端集成测试")
        ]
    elif args.test_type == "performance":
        test_commands = [
            (f"{base_cmd}/test_performance.py{verbose_flag}", "性能集成测试")
        ]
    
    # 运行测试
    success_count = 0
    total_count = len(test_commands)
    
    print(f"开始运行 {total_count} 个集成测试套件...")
    
    for command, description in test_commands:
        if run_command(command, description):
            success_count += 1
    
    # 输出总结
    print(f"\n{'='*60}")
    print("测试运行总结")
    print(f"{'='*60}")
    print(f"总测试套件数: {total_count}")
    print(f"成功: {success_count}")
    print(f"失败: {total_count - success_count}")
    
    if success_count == total_count:
        print("🎉 所有集成测试都通过了!")
        return 0
    else:
        print("⚠️  部分集成测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
