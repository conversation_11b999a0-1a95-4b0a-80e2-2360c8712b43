#!/usr/bin/env python3
"""
快速验证集成测试脚本
用于快速验证所有集成测试是否正常工作
"""
import subprocess
import sys
import time


def run_command(command, description):
    """运行命令并返回结果"""
    print(f"🔄 {description}...")
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功 ({duration:.2f}s)")
            return True
        else:
            print(f"❌ {description} - 失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False
    except Exception as e:
        print(f"💥 {description} - 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🚀 开始快速验证集成测试...")
    print("=" * 50)
    
    # 测试命令列表
    tests = [
        ("pytest tests/integration/ -v --tb=short", "运行所有集成测试"),
        ("python tests/integration/run_integration_tests.py --test-type minimal", "运行最小化测试"),
        ("python tests/integration/run_integration_tests.py --test-type router", "运行路由测试"),
        ("python tests/integration/run_integration_tests.py --test-type kafka", "运行Kafka测试"),
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for command, description in tests:
        if run_command(command, description):
            success_count += 1
        print()
    
    # 输出总结
    print("=" * 50)
    print("📊 验证结果总结:")
    print(f"   总测试数: {total_count}")
    print(f"   成功: {success_count}")
    print(f"   失败: {total_count - success_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有验证都通过了！集成测试完全正常工作。")
        return 0
    else:
        print("⚠️  部分验证失败，请检查错误信息。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
