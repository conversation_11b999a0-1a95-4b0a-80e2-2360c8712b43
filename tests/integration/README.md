# 集成测试文档

本目录包含了 `routers/router.py` 中所有API接口的集成测试。

## 测试文件结构

```
tests/integration/
├── __init__.py                    # 模块初始化
├── conftest.py                    # 测试配置和夹具
├── test_router_integration.py    # 路由接口集成测试
├── test_kafka_integration.py     # Kafka接口集成测试
├── test_end_to_end.py            # 端到端集成测试
├── test_performance.py           # 性能集成测试
├── run_integration_tests.py      # 测试运行脚本
└── README.md                     # 本文档
```

## 测试覆盖的接口

### 1. 解析相关接口
- `POST /api/v1/aidocs_dst/parse_pipeline` - 文档解析管道
- `POST /api/v1/aidocs_dst/general_parse_res` - 获取解析结果

### 2. 表格合并接口
- `POST /api/v1/aidocs_dst/merge_table` - 表格内容合并

### 3. 版本信息接口
- `GET /api/v1/aidocs_dst/parse_version` - 获取解析版本信息

### 4. Kafka监控接口
- `GET /api/v1/aidocs_dst/kafka_lag` - 获取Kafka消息延迟
- `GET /api/v1/aidocs_dst/kafka_throughput` - 获取Kafka消息吞吐量

## 测试类型

### 1. 路由接口集成测试 (`test_router_integration.py`)
- 测试各个API接口的基本功能
- 测试成功和失败场景
- 测试参数验证
- 测试错误处理

### 2. Kafka接口集成测试 (`test_kafka_integration.py`)
- 测试Kafka延迟监控接口
- 测试Kafka吞吐量监控接口
- 测试并发访问
- 测试错误恢复

### 3. 端到端集成测试 (`test_end_to_end.py`)
- 测试完整的业务流程
- 测试多个接口的协作
- 测试系统健康检查
- 测试错误恢复工作流程

### 4. 性能集成测试 (`test_performance.py`)
- 测试接口响应时间
- 测试并发负载处理
- 测试大负载处理
- 测试内存使用稳定性

## 运行测试

### 快速验证（推荐）

```bash
# 快速验证所有测试是否正常工作
python tests/integration/quick_test.py
```

### 使用测试脚本

```bash
# 运行所有集成测试
python tests/integration/run_integration_tests.py --test-type all

# 运行特定类型的测试
python tests/integration/run_integration_tests.py --test-type minimal
python tests/integration/run_integration_tests.py --test-type router
python tests/integration/run_integration_tests.py --test-type kafka
python tests/integration/run_integration_tests.py --test-type e2e
python tests/integration/run_integration_tests.py --test-type performance

# 详细输出
python tests/integration/run_integration_tests.py --test-type all --verbose

# 生成覆盖率报告
python tests/integration/run_integration_tests.py --test-type all --coverage

# 并行运行测试
python tests/integration/run_integration_tests.py --test-type all --parallel
```

### 直接使用pytest

```bash
# 运行所有集成测试
pytest tests/integration/ -v

# 运行特定测试文件
pytest tests/integration/test_router_integration.py -v
pytest tests/integration/test_kafka_integration.py -v
pytest tests/integration/test_end_to_end.py -v
pytest tests/integration/test_performance.py -v

# 运行特定测试类
pytest tests/integration/test_router_integration.py::TestParseIntegration -v

# 运行特定测试方法
pytest tests/integration/test_router_integration.py::TestParseIntegration::test_parse_pipeline_success -v

# 生成覆盖率报告
pytest tests/integration/ --cov=routers --cov=services --cov-report=html --cov-report=term

# 并行运行测试
pytest tests/integration/ -n auto
```

## 测试环境要求

### 依赖包
- pytest
- pytest-asyncio
- httpx
- fastapi[all]
- pytest-cov (用于覆盖率报告)
- pytest-xdist (用于并行测试)

### 环境变量
测试会自动设置必要的环境变量，无需手动配置。主要的环境变量在 `tests/conftest.py` 中定义。

## 模拟和Mock

集成测试使用了以下模拟策略：

### 1. 外部服务模拟
- 认证服务 (DmcCheckAuth, PrivateCheckAuth)
- Redis数据库
- Kafka服务
- 对象存储服务
- LLM服务

### 2. 数据模拟
- 文件上传数据
- 解析请求和响应
- Kafka监控数据
- 版本信息

### 3. 错误场景模拟
- 服务异常
- 网络超时
- 数据格式错误
- 资源不足

## 测试数据

测试使用的示例数据在 `conftest.py` 中定义：

- `sample_parse_request_data` - 解析请求数据
- `sample_merge_table_request` - 表格合并请求数据
- `sample_general_parse_res_request` - 解析结果请求数据
- `mock_successful_*_response` - 各种成功响应数据

## 性能基准

性能测试的基准值：

- API响应时间: < 5秒
- Kafka监控接口响应时间: < 2秒
- 并发请求成功率: ≥ 90%
- 平均响应时间: < 1秒
- 错误处理响应时间: < 1秒

## 故障排除

### 常见问题

1. **测试失败: "Module not found"**
   - 确保在项目根目录运行测试
   - 检查PYTHONPATH设置

2. **测试失败: "Event loop is closed"**
   - 这是异步测试的常见问题
   - 确保使用了正确的pytest-asyncio配置

3. **测试超时**
   - 检查网络连接
   - 增加超时时间设置

4. **Mock不生效**
   - 检查patch路径是否正确
   - 确保在正确的作用域内使用mock

### 调试技巧

1. **使用详细输出**
   ```bash
   pytest tests/integration/ -v -s
   ```

2. **运行单个测试**
   ```bash
   pytest tests/integration/test_router_integration.py::TestParseIntegration::test_parse_pipeline_success -v -s
   ```

3. **查看覆盖率报告**
   ```bash
   pytest tests/integration/ --cov=routers --cov=services --cov-report=html
   # 然后打开 htmlcov/index.html
   ```

## 贡献指南

添加新的集成测试时，请遵循以下原则：

1. **测试命名**: 使用描述性的测试名称
2. **测试结构**: 遵循 Arrange-Act-Assert 模式
3. **Mock策略**: 只模拟外部依赖，不模拟被测试的代码
4. **断言**: 使用具体的断言，避免过于宽泛的检查
5. **文档**: 为复杂的测试场景添加注释

## 持续集成

这些集成测试应该在以下情况下运行：

1. 代码提交前
2. Pull Request创建时
3. 合并到主分支前
4. 定期的夜间构建

建议的CI配置：
```yaml
- name: Run Integration Tests
  run: |
    python tests/integration/run_integration_tests.py --coverage
```
