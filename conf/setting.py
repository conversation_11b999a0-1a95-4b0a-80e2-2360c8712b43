import os
from typing import List, Dict
import json

from commons.auth.auth_route import AuthPlatform
from commons.auth.auth_rpc import SigVerType
from commons.tools.kms_sdk import KmsLocalClient
from modules.entity.drive_entity import DriveParams, RpcName
from commons.llm_gateway.models.chat_data import LLMConfig

auth_platform = os.environ.get("AUTH_PLATFORM", AuthPlatform.dmc)


class ConfKms:
    KMS_SETTING_ENCRYPT = os.environ.get("KMS_SETTING_ENCRYPT", "false")
    KMS_CONFIG_PATH = os.environ.get("KMS_CONFIG_PATH", "")
    KMS_SECRET_PATH = os.environ.get("KMS_SECRET_PATH", "")
    KMS_SETTING_KEYID = os.environ.get("KMS_SETTING_KEYID", "")


kms_use = os.environ.get("KMS_USE", "0") == "1"
if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true":
    KmsLocalClient().init(ConfKms.KMS_SETTING_KEYID, ConfKms.KMS_CONFIG_PATH, ConfKms.KMS_SECRET_PATH)

#本服务接口注册到dmc上的服务
class ConfAuth:
    aksk_dict = {}
    if auth_platform == AuthPlatform.dmc:
        host = os.environ.get("DMC_HOST", "https://dmc.wps.cn")
        ak = os.environ["SERVICE_ID"]
        sk = os.environ["SERVICE_KEY"]
        aksk_dict[ak] = sk
    elif auth_platform == AuthPlatform.private:
        host = ""
        ak = os.environ["AK"]
        sk = KmsLocalClient().DecryptBase64(
            os.environ["SK"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ["SK"]
        aksk_dict[ak] = sk

class ConfPrometheus:
    gateway = f'{os.environ.get("SRE_PUSHGATEWAY_HOST", "127.0.0.1")}:{os.environ.get("SRE_PUSHGATEWAY_PORT", "9091")}'
    job_name_prefix = os.environ.get("PROM_JOB_NAME", "wps-aidocs-dst-server")
    username = os.environ.get("PROM_USERNAME", None)
    password = (
        KmsLocalClient().DecryptBase64(os.environ["PROM_PASSWORD"])
        if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true"
        else os.environ.get("PROM_PASSWORD", None)
    )


class ConfProcess:
    process_work_num = int(os.environ.get("process_work_num", "4"))
    # 至少启动3个进行给kafka 3个等级的topic消费
    if process_work_num < 3:
        process_work_num = 3


class ConfService:
    port = int(os.getenv('SERVICE_HTTP_PORT', "8080"))
    timeout_keep_alive = int(os.getenv('SERVICE_TIMEOUT_KEEP_ALIVE', "180"))


class ConfLogger:
    log_level = os.environ["LOG_LEVEL"]
    monitor_name = os.environ.get("LOG_MONITOR_NAME", "AiDocsParseServerMonitorError")


class ConfStore:
    store_dir: str = os.environ.get("store_dir", "insight-ai/doc-parse")
    back_store_dir: str = os.environ.get("back_store_dir", "insight-ai/doc-parse/back_store_dev")

    @classmethod
    def load(cls):
        if auth_platform == AuthPlatform.dmc:
            cls.host = os.environ.get("KS3_HOST", "ks3-cn-beijing-internal.ksyuncs.com")
            cls.ak = os.environ["KS3_AK"]
            cls.sk = os.environ["KS3_SK"]
            cls.bucket = os.environ["KS3_BUCKET"]
            cls.dir = os.environ.get("KS3_DIR", "dailyreport")
        else:
            cls.host = os.environ["ECIS_MINIO_ADDR"]
            cls.ak = os.environ["ECIS_MINIO_AK"]
            cls.sk = KmsLocalClient().DecryptBase64(
            os.environ["ECIS_MINIO_SK"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ["ECIS_MINIO_SK"]
            cls.bucket = os.environ["ECIS_MINIO_BUCKET"]
            cls.dir = os.environ["ECIS_MINIO_PREFIX_0"]

class ConfHttpTriton:
    host = os.environ.get("http_triton_host", "http://insight-model-test.kna.wps.cn")
    layout_uri = os.environ.get("http_triton_layout_uri", "/api/v1/ocr/doc_layout_3cls")
    orc_uri = os.environ.get("http_triton_ocr_uri", "/api/v1/ocr/ocr_pipeline")
    table_cls_uri = os.environ.get("http_triton_table_cls_uri", "/api/v1/ocr/table_cls")
    wired_table_ocr_uri = os.environ.get("http_triton_wired_table_ocr_uri", "/api/v1/ocr/wired_ocr")
    wireless_table_ocr_uri = os.environ.get("http_triton_wireless_table_ocr_uri", "/api/v1/ocr/wireless_table")
    text_embedding_uri = os.environ.get("http_triton_text_to_vector", "/api/v1/text/vector")
    monkey_ocr_uri = os.environ.get("http_triton_monkey_ocr_uri", "/ocr/table")
    kas_host = os.environ.get("http_triton_kas_host", "http://kmd-api.kas.wps.cn/api/11456-v1/Qa0N8c")
    @classmethod
    def load(cls):
        cls.host = os.environ.get("http_triton_host", "http://insight-model-test.kna.wps.cn")
        cls.layout_uri = os.environ.get("http_triton_layout_uri", "/api/v1/ocr/doc_layout_3cls")
        cls.orc_uri = os.environ.get("http_triton_ocr_uri", "/api/v1/ocr/ocr_pipeline")
        cls.table_cls_uri = os.environ.get("http_triton_table_cls_uri", "/api/v1/ocr/table_cls")
        cls.wired_table_ocr_uri = os.environ.get("http_triton_wired_table_ocr_uri", "/api/v1/ocr/wired_ocr")
        cls.wireless_table_ocr_uri = os.environ.get("http_triton_wireless_table_ocr_uri", "/api/v1/ocr/wireless_table")
        cls.text_embedding_uri = os.environ.get("http_triton_text_to_vector", "/api/v1/text/vector")
        cls.monkey_ocr_uri = os.environ.get("http_triton_monkey_ocr_uri", "/api/11456-v1/Qa0N8c/v1/ocr/table")
        cls.kas_host = os.environ.get("http_triton_kas_host", "http://kmd-api.kas.wps.cn/api/11456-v1/Qa0N8c")

class ConfTriton:
    host = os.environ.get("triton_host", "insight-model-dev.kna.wps.cn")

    @classmethod
    def load(cls):
        cls.host = os.environ.get("triton_host", "insight-model-dev.kna.wps.cn")

        cls.doc_segment_ckpt = os.environ.get("triton_doc_segment_ckpt", "/model/doc_segment_enc")
        cls.doc_segment_name = os.environ.get("triton_doc_segment_name", "doc_segment")
        cls.doc_segment_batch = int(os.environ.get("triton_doc_segment_batch", "4"))

        cls.bge_m3_ckpt = os.environ.get("triton_bge_m3_ckpt", "/model/bge-m3_enc/bge_20240509t_ckpt")
        cls.bge_m3_name = os.environ.get("triton_bge_m3_name", "bge-m3")
        cls.bge_m3_batch = int(os.environ.get("triton_bge_m3_batch", "4"))

        cls.ocr_pipeline_base_path = os.environ.get("triton_ocr_pipeline_base_path", "/model/ocr_pipeline_enc/20250113")

        cls.cycle_center_name = os.environ.get("triton_cycle_center_name", "cycle-center")

        cls.doclayout_name = os.environ.get("triton_doclayout_name", "doclayout")
        cls.doclayout_onnx = os.environ.get("triton_doclayout_onnx", "/doclayout_yolo_docstructbench_imgsz1024.onnx")

        cls.ppocr_v2_name = os.environ.get("triton_ppocr_v2_name", "ppocr-v2")
        cls.ppocr_v2_batch = int(os.environ.get("triton_ppocr_v2_batch", "4"))

        cls.ppocr_v4_det_name = os.environ.get("triton_ppocr_v4_det_name", "ppocr-v4-det")
        cls.ppocr_v4_det_batch = int(os.environ.get("triton_ppocr_v4_det_batch", "4"))

        cls.ppocr_v4_rec_name = os.environ.get("triton_ppocr_v4_rec_name", "ppocr-v4-rec")
        cls.ppocr_v4_rec_batch = int(os.environ.get("triton_ppocr_v4_rec_batch", "4"))
        cls.ppocr_v4_rec_onnx = os.environ.get("triton_ppocr_v4_rec_onnx", "/ch_PP-OCRv4_rec_infer.onnx")

        cls.slanet_name = os.environ.get("triton_slanet_name", "slanet")
        cls.slanet_onnx = os.environ.get("triton_slanet_onnx", "/slanet-plus.onnx")

        cls.yolox_name = os.environ.get("triton_yolox_name", "yolox")


class ConfRedis:
    @classmethod
    def load(cls):
        cls.hosts: List[str] = json.loads(os.environ["REDIS_HOSTS"])
        cls.password = KmsLocalClient().DecryptBase64(
            os.environ["REDIS_PASSWORD"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ[
            "REDIS_PASSWORD"]
        cls.prefix = os.environ.get("REDIS_PREFIX", "wps_kna_aidocs_dst_server_")
        cls.cluster = os.environ.get("REDIS_CLUSTER", "0") == "1"


class ConfTraceRedis:
    @classmethod
    def load(cls):
        cls.hosts: List[str] = json.loads(os.environ["KAE_REDIS_NODES"])
        cls.password = os.environ["KAE_REDIS_PWD"]
        cls.cluster = os.environ.get("KAE_REDIS_MODE", "cluster") == "cluster"
        cls.timeout = int(os.environ.get("KAE_REDIS_TIMEOUT", "1"))


class ConfWps365:
    @classmethod
    def load(cls):
        cls.api_host: str = os.environ["wps365_host"]
        # openapi配置用于获取应用身份access_token的鉴权
        cls.openapi_host: str = os.environ.get("openapi_host", None)
        cls.openapi_ak: str = os.environ.get("openapi_ak", None)
        cls.openapi_sk: str = KmsLocalClient().DecryptBase64(
            os.environ["openapi_sk"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ["openapi_sk"]
        cls.pool_max: int = int(os.environ.get("wps365_pool_max", "10"))
        # woa开放平台自建应用ak
        cls.woa_custom_ak: str = os.environ.get("woa_custom_ak", "")


class ConfLLM:
    activated = os.environ.get("ai_gateway_activated", "0") == "1"
    thread_num = int(os.environ.get("ai_gateway_threadnum", os.cpu_count()))

    @classmethod
    def load(cls):
        if cls.activated:
            # AI网关配置
            cls.host = os.environ.get("ai_gateway_host", "http://aigc-gateway-test.ksord.com")
            cls.token = os.environ.get("ai_gateway_token")
            cls.uid = os.environ.get("ai_gateway_uid")
            cls.product_name = os.environ.get("ai_gateway_product_name")
            cls.intention_code = os.environ.get("ai_gateway_intention_code")
            cls.sec_scene = os.environ.get("ai_gateway_sec_scene", "")
            # 兼容私有化不能配空字符串
            if cls.sec_scene == "empty":
                cls.sec_scene = ""
            cls.sec_from = os.environ.get("ai_gateway_sec_from", "AI_DRIVE_KNOWLEDGE")
            if cls.sec_from == "empty":
                cls.sec_from = ""
            # 模型
            cls.model = os.environ.get("ai_gateway_model", "qwen-plus")
            cls.provider = os.environ.get("ai_gateway_provider", "ali")
            cls.version = os.environ.get("ai_gateway_version", "")
            if cls.version == "empty":
                cls.version = ""
            cls.prom_token = os.environ.get("ai_gateway_prom_token", "1") == "1"
            # 权益配置
            cls.privilege_activated = os.environ.get("ai_privilege_activated", "0") == "1"
            if cls.privilege_activated:
                cls.privilege_host = os.environ.get("ai_privilege_host")
                cls.privilege_ak = os.environ.get("ai_privilege_ak")
                cls.privilege_sk = KmsLocalClient().DecryptBase64(
            os.environ["ai_privilege_sk"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ["ai_privilege_sk"]


class ConfSftLLM:
    activated = os.environ.get("ai_sft_activated", "0") == "1"

    @classmethod
    def load(cls):
        if cls.activated:
            cls.host = os.environ.get("ai_sft_host", "")
            cls.multimodal_host = os.environ.get("ai_sft_multimodal_host",
                                                 "http://privatization-model.kna.wps.cn/vl_7b")
            cls.token = os.environ.get("ai_sft_token")
            cls.thread_num = int(os.environ.get("ai_sft_threadnum", os.cpu_count()))
            cls.prom_token = os.environ.get("ai_sft_prom_token", True)


class ConfPrivateLLM:
    activated = os.environ.get("ai_private_activated", "0") == "1"

    @classmethod
    def load(cls):
        if cls.activated:
            cls.host = os.environ.get("ai_private_host", "http://weboffice-aigatewaypri")
            # minimax, zhipu, baidu, private
            cls.platform = os.environ.get("ai_private_platform", "private")
            cls.prom_token = os.environ.get("ai_private_prom_token", "0") == "1"
            cls.minimax_model = os.environ.get("ai_private_minimax_model", "abab5-chat")
            cls.thread_num = int(os.environ.get("ai_private_threadnum", os.cpu_count()))


class ConfLLMGateway:
    summary_gateway = int(os.environ.get("summary_gateway", "1"))
    if summary_gateway == 0:
        summary_provider = os.environ.get("summary_provider", "minimax-zone")
        summary_model = os.environ.get("summary_model", "abab6.5s-chat-wps")
        summary_version = os.environ.get("summary_version", "")
    image_preprocess_gateway = int(os.environ.get("image_preprocess_gateway", "1"))
    if image_preprocess_gateway == 0:
        image_preprocess_provider = os.environ.get("image_preprocess_provider", "stepfun")
        image_preprocess_model = os.environ.get("image_preprocess_model", "step-1v-32k")
        image_preprocess_version = os.environ.get("image_preprocess_version", "")
    sft_kas_ratio = float(os.environ.get("sft_kas_ratio", "0.35"))


    """
    各个模块LLM调用配置
    """
    llm_default_config = """
    {
        "CheckBox": {
            "gateway": 1,
            "sft": {
                "base_model": "qwen25_vl_3b_aidocs",
                "lora_model": "checkbox_refined"
            }
        },
        "OcrFlux": {
            "gateway": 1,
            "sft": {
                "base_model": "ocrflux_qwen25_vl_3b"
            }
        }
    }
    """

    """
    ！！参考！！
    私网模型配置，由客户根据AiHub平台配置的模型列表，将对应的功能配置上指定模型
    """
    private_config_example = """
    {
        "CheckBox": {
            "gateway": 0,    # 网关配置，0：公网AI网关，1：自部署模型
            "public": {    # 公网AI网关模型配置
                "provider": "deepseek",    # 模型信息
                "model": "deepseek-chat",    # 模型信息
                "version": "",    # 模型信息
                "product_name": "saas_aisearch_web",    # 可选，默认权益配置
                "intention_code": "saas_aidocs_aisearch",     # 可选，默认权益配置
                "sec_scene": "",     # 可选，默认审核配置
                "sec_from": "AI_DRIVE_KNOWLEDGE"    # 可选，默认审核配置
                "llm_types": ["llm-chat", "llm-multimodal"]    # 可选，模型支持的类型，聊天、多模态
            }
        },
        "OcrFlux": {
            "gateway": 0,    # 网关配置，0：公网AI网关，1：自部署模型
            "public": {    # 公网AI网关模型配置
                "provider": "deepseek",    # 模型信息
                "model": "deepseek-chat",    # 模型信息
                "version": "",    # 模型信息
                "product_name": "saas_aisearch_web",    # 可选，默认权益配置
                "intention_code": "saas_aidocs_aisearch",     # 可选，默认权益配置
                "sec_scene": "",     # 可选，默认审核配置
                "sec_from": "AI_DRIVE_KNOWLEDGE"    # 可选，默认审核配置
                "llm_types": ["llm-chat", "llm-multimodal"]    # 可选，模型支持的类型，聊天、多模态
            }
        }
    }
    """
    
    llm_config: dict | Dict[str, LLMConfig] = json.loads(os.environ.get("llm_config", llm_default_config))
    for k,v in llm_config.items():
        llm_config[k] = LLMConfig.model_validate(v)


class ConfAppKey:
    ak = os.environ["aidocs_dst_ak"]
    sk = KmsLocalClient().DecryptBase64(
            os.environ["aidocs_dst_sk"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ["aidocs_dst_sk"]





class ConfStepRpc:
    fake_title_host = os.environ.get("step_fake_title_host", "http://qsearch.wps.cn/notify/graph_dst_result")
    keyword_host = os.environ.get("step_keyword_host", "http://qsearch.wps.cn/notify/graph_dst_result")
    chunk_host = os.environ.get("step_chunk_host", "http://qsearch.wps.cn/notify/graph_dst_result")
    summary_host = os.environ.get("step_summary_host", "http://qsearch.wps.cn/notify/graph_dst_result")
    screen_shot_host = os.environ.get("step_screen_shot_host", "http://aidocs.wps.cn/notify/graph_dst_result")


class ConfInsightRpc:
    host = os.environ.get("insight_host", "http://insight.wps.cn")
    sig_type = os.environ.get("insight_sig_type", SigVerType.wps2)


class ConfRecallChunkRpc:
    host = os.environ.get("recall_chunk_host", "http://aidoc.wps.cn")
    sig_type = os.environ.get("recall_chunk_sig_type", SigVerType.wps2)


class ConfQSearchRPC:
    host = os.environ.get("qsearch_host", "http://qsearch.wps.cn")
    auth_platform = os.environ.get("qsearch_auth_platform", AuthPlatform.dmc)


class ConfKDC:
    host: str = os.environ.get("WPS365_KDC_HOST", "https://api.wps.cn")


class ConfFigureParse:
    checkpoints = os.environ.get("FP_CHECKPOINTS",
                                 "/app/aidocs-parse-server/modules/figureparser/checkpoints")
    det_model = os.environ.get("FP_DET_MODEL", "detector.onnx")
    rec_model = os.environ.get("FP_REC_MODEL", "recognize.onnx")


class ConfKafka:
    bootstrap_servers: List[str] = json.loads(os.environ.get("kafka_bootstrap_servers", "[\"127.0.0.1:9092\"]"))
    # level: 消费等级，low: 低级别，normal: 中级别，high: 高级别
    # topic: 消费的topic
    # partition: topic分区数
    # consumer_group_id: 消费者组id
    # consumer_process_work_num: 消费者进程数

    parse_default_config = """
    [
        {
            "level": "low",
            "topic": "aidocs_dst_server_topic_dev_low",
            "partition": 5,
            "consumer_group_id": "aidocs_dst_server_group_id_dev_low",
            "consumer_process_work_num": 1,
            "consumer_process_task_num": 1
        },
        {
            "level": "normal",
            "topic": "aidocs_dst_server_topic_dev_normal",
            "partition": 5,
            "consumer_group_id": "aidocs_dst_server_group_id_dev_normal",
            "consumer_process_work_num": 1,
            "consumer_process_task_num": 1
        },
        {
            "level": "high",
            "topic": "aidocs_dst_server_topic_dev_high",
            "partition": 5,
            "consumer_group_id": "aidocs_dst_server_group_id_dev_high",
            "consumer_process_work_num": 1,
            "consumer_process_task_num": 1
        }
    ]
    """
    parse_config: List[dict] = json.loads(os.environ.get("kafka_parse_config", parse_default_config))
    re_product_dist_lock_key = os.environ.get("re_product_dist_lock_key", "re_product_dist_lock_key")
    re_throughput_lock_key = os.environ.get("re_throughput_lock_key", "re_throughput_lock_key_dev")
    re_throughput_key = os.environ.get("re_throughput_key", "re_throughput_key_dev")


class ConfDB:
    @classmethod
    def load(cls):
        cls.host = os.environ["db_host"]
        cls.port = os.environ.get("db_port", "3306")
        cls.user = os.environ["db_user"]
        cls.pwd = KmsLocalClient().DecryptBase64(
            os.environ["db_pwd"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ["db_pwd"]
        cls.db_name = os.environ.get("db_name", "aidocs_dst")
        cls.pools = int(os.environ.get("db_pools", "5"))
        cls.env = os.environ.get("db_env", "dev")


class ConfCoroutine:
    default_coroutine_config = """
    {
        "chunk_limit": 5,
        "spliter_service_limit": 5,
        "split_text_limit": 10,
        "image_desc_embedding_limit": 10,
        "image_ocr_limit": 3,
        "kdc_limit": 10,
        "image_desc_limit": 3
    }
    """
    coroutine_config = json.loads(os.environ.get("coroutine_config", default_coroutine_config))
    chunk_limit = coroutine_config["chunk_limit"]
    spliter_service_limit = coroutine_config["spliter_service_limit"]
    split_text_limit = coroutine_config["split_text_limit"]
    image_desc_embedding_limit = coroutine_config["image_desc_embedding_limit"]
    image_ocr_limit = coroutine_config["image_ocr_limit"]
    kdc_limit = coroutine_config["kdc_limit"]
    image_desc_limit = coroutine_config["image_desc_limit"]


class ConfParseTarget:
    """
    通过配置禁用不同等级下的解析目标
    示例：
    {
        "low": ["img_desc"],
        "normal": ["chunk"],
        "high": []
    }
    """
    default_parse_target_disable_config = """
    {
        "low": [],
        "normal": [],
        "high": []
    }
    """
    parse_target_disable_config = json.loads(
        os.environ.get("parse_target_disable_config", default_parse_target_disable_config))


class ConfTrace:
    verbose = os.environ.get("TRACE_VERBOSE", "0") == "1"


class ConfImageFilter:
    width = int(os.environ.get("IMAGE_WIDTH", "30"))
    height = int(os.environ.get("IMAGE_HEIGHT", "30"))

class ConfPdfTableFilter:
    empty_limit = float(os.environ.get("PDF_EMPTY_LIMIT", "0.5"))

class ConfUseOcr:
    use_ocr = os.environ.get("use_os_ocr", "1")


class ConfParseVersion:
    version = os.environ.get("PARSE_VERSION", "v1")


class ConfHandlerName:
    default_handler_config = """
    {
        "uniparser_handler": "uni_parser",
        "dst_handler": "document_parser",
        "dst_enhance_handler": "dst_enhance",
        "dst_merge_handler": "dst_merge",
        "chunk_handler": "chunk",
        "screenshot_handler": "screenshot",
        "chunk_post_process_handler": "chunk_postprocess",
        "fake_title_handler": "fake_title",
        "keywords_handler": "keywords",
        "summary_handler": "summary",
        "image_to_pdf_handler": "image_to_pdf",
        "image_desc_handler": "image_desc",
        "chunk_recall_handler": "chunk_recall",
        "pre_check_handler": "pre_check"
    }
    """
    handler_config = json.loads(os.environ.get("handler_config", default_handler_config))
    uniparser_handler = handler_config["uniparser_handler"]
    dst_handler = handler_config["dst_handler"]
    dst_merge_handler = handler_config["dst_merge_handler"]
    dst_enhance_handler = handler_config["dst_enhance_handler"]
    chunk_handler = handler_config["chunk_handler"]
    screenshot_handler = handler_config["screenshot_handler"]
    chunk_post_process_handler = handler_config["chunk_post_process_handler"]
    fake_title_handler = handler_config["fake_title_handler"]
    keywords_handler = handler_config["keywords_handler"]
    summary_handler = handler_config["summary_handler"]
    image_to_pdf_handler = handler_config["image_to_pdf_handler"]
    image_desc_handler = handler_config["image_desc_handler"]
    chunk_recall_handler = handler_config["chunk_recall_handler"]
    pre_check_handler = handler_config["pre_check_handler"]


class DstFilter:
    catalog_filter_key: List[str] = json.loads(os.environ.get("DST_CATALOG_FILTER_KEY", "[]"))
    catalog_filter_flag: List[str] = json.loads(os.environ.get("DST_CATALOG_FILTER_FLAG", "[]"))
    catalog_max_page_num: int = int(os.environ.get("DST_CATALOG_MAX_PAGE_NUM", "5"))
    catalog_match_ratio: float = float(os.environ.get("DST_CATALOG_MATCH_RATIO", "0.5"))


class CheckboxConf:
    checkbox_filter_key: List[str] = json.loads(os.environ.get("DST_CHECKBOX_FILTER_KEY", "[]"))



class ConfDriveV5Rpc:
    ak = os.environ["drive_v5_ak"]
    sk = KmsLocalClient().DecryptBase64(
            os.environ["drive_v5_sk"]) if kms_use and ConfKms.KMS_SETTING_ENCRYPT == "true" else os.environ["drive_v5_sk"]

class ConfDriveRpcConfig:
    """Drive RPC配置类"""

    # 默认RPC配置（JSON格式）
    default_rpc_config = """
    {
        "implementations": {
            "wpsv5": {
                "class_path": "modules.rpc.drive_v5_rpc.DriveV5Rpc",
                "enabled": true,
                "host": "https://drive.wps.cn",
                "ak_env": "drive_v5_ak",
                "sk_env": "drive_v5_sk",
                "sig_type": "wps2",
                "download_uri": "/api/v5/developer/files/{file_id}/download?slave=true"
            }
        }
    }
    """

    # 从环境变量读取RPC配置，如果没有则使用默认配置
    rpc_config: dict = json.loads(os.environ.get("drive_rpc_config", default_rpc_config))
    
    @classmethod
    def get_validated_implementations(cls) -> dict[str, DriveParams]:
        """
        获取验证后的RPC实现配置
        
        Returns:
            dict[str, DriveParams]: 验证后的实现配置字典
            
        Raises:
            ValueError: 当配置无效时
        """
        implementations = cls.rpc_config.get("implementations", {})
        validated = {}
        
        for name, config in implementations.items():
            # 验证RPC名称
            if name not in RpcName.__members__.values():
                raise ValueError(f"Invalid RPC implementation name: {name}. Valid names: {list(RpcName.__members__.values())}")
            
            # 验证配置参数
            try:
                validated[name] = DriveParams.model_validate(config)
            except Exception as e:
                raise ValueError(f"Invalid configuration for RPC '{name}': {e}") from e
        
        return validated
    
    @classmethod
    def get_implementation_config(cls, rpc_name: str) -> DriveParams:
        """
        获取指定RPC实现的验证配置
        
        Args:
            rpc_name: RPC实现名称
            
        Returns:
            DriveParams: 验证后的配置
            
        Raises:
            ValueError: 当RPC名称不存在时
        """
        validated_impls = cls.get_validated_implementations()
        if rpc_name not in validated_impls:
            raise ValueError(f"RPC implementation '{rpc_name}' not found")
        return validated_impls[rpc_name]


class MultipleParse:
    enable: bool = os.environ.get("MULTIPLE_PARSE_ENABLE", "0") == "1"
    wireless_enhance: bool = os.environ.get("MULTIPLE_PARSE_WIRELESS_ENHANCE", "0") == "1"
    image_ocr_time_out: int = os.environ.get("MULTIPLE_PARSE_IMAGE_OCR_TIME_OUT", 5)
    pic_input_ocr_time_out: int = os.environ.get("MULTIPLE_PARSE_PIC_INPUT_OCR_TIME_OUT", 20)

class ConfIPv6:
    is_ipv6:bool = os.environ.get("CONF_IPV6_IS_IPV6", "false") == "true"