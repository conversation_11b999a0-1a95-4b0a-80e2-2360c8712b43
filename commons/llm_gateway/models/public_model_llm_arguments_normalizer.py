#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@文件    :public_model_extend_llm_arguments_factory.py
@说明    :
@时间    :2024/07/26 14:54:04
@作者    :xuxiang
@版本    :1.0
'''
from commons.llm_gateway.models.chat_data import Message
from abc import ABC, abstractmethod
from typing import List, Dict, Type, Union, Any, Optional
from pydantic import BaseModel


class NormalizeParams(BaseModel):
    messages: Optional[List[Message]] = None
    context: Optional[str] = None
    extended_llm_arguments: Optional[Dict[str, Any]] = None

    def dict(self, *args, exclude_none: bool = True, **kwargs) -> Any:
        return super().dict(*args, exclude_none=True, **kwargs)


class PublicModelParamsNormalizer(ABC):
    @abstractmethod
    def normalize_llm_arguments(self, key: str, messages: List[Message] = None) -> NormalizeParams:
        return NormalizeParams()


class MinimaxParamsNormalizer(PublicModelParamsNormalizer):
    _context = "WPSAI人工智能"
    _bot_name = "WPSAI"

    def normalize_llm_arguments(self, key: str, messages: List[Message] = None) -> NormalizeParams:
        for msg in messages:
            if not msg.role:
                msg.role = "user"

            if not msg.name:
                msg.name = msg.role

        if len(messages) != 0 and messages[0].role == "system":
            context = messages[0].content
            messages = messages[1:]
        else:
            context = self._context
        normalized_args = NormalizeParams(**{
            "messages": messages,
            "context": context,
            "extended_llm_arguments": {
                key: {
                    "mask_sensitive_info": False,
                    "bot_setting": [{"bot_name": self._bot_name, "content": context}],
                    "reply_constraints": {"sender_type": "BOT", "sender_name": self._bot_name},
                }
            }
        })
        return normalized_args


parameter_process_categories: Dict[str, Type[PublicModelParamsNormalizer]] = {
    "minimax": MinimaxParamsNormalizer,
    "minimax-zone": MinimaxParamsNormalizer,
}


class ParameterProcessor:
    def __init__(self, provider: str, model: str, extend_llm_arguments: Union[Dict[str, Any], None]):
        self.provider = provider
        self._model = model
        self._extend_llm_arguments = extend_llm_arguments if extend_llm_arguments is not None else {}

    def process_params(self, messages: List[Message]) -> NormalizeParams:
        if self.provider in parameter_process_categories:
            key = f"{self.provider}_{self._model}"
            if key not in self._extend_llm_arguments.keys():
                category_class = parameter_process_categories[self.provider]
                category_instance = category_class()
                value = category_instance.normalize_llm_arguments(key, messages)
                return value
        return NormalizeParams()
