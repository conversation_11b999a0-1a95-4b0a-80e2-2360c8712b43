import base64
import json
import logging

from pydantic import BaseModel
from typing import Optional, Any, Union, List
from enum import Enum
from commons.trace.tracer import ExtraInfoModel
from .sft_model_config import load_sft_model_config
from fastapi import Request
import warnings


class ChatType(str, Enum):
    chat = "chat"
    multimodal = "multimodal"


SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()


class MultiModalType(str, Enum):
    image_url = "image_url"
    image = "image"
    text = "text"


class SftMultiModalImageDetail(str, Enum):
    auto = "auto"
    low = "low"
    high = "high"


class SftMultiModalImageUrl(BaseModel):
    url: str
    detail: Optional[SftMultiModalImageDetail] = None


# 多模态
class MultiModalContent(BaseModel):
    type: MultiModalType
    content: Optional[str] = None


class SftMultiModalText(BaseModel):
    type: MultiModalType
    text: Optional[str] = None


class SftMultiModalImage(BaseModel):
    type: MultiModalType
    image_url: Optional[SftMultiModalImageUrl] = None


class Message(BaseModel):
    # 消息内容
    content: Union[str, List[Union[MultiModalContent, SftMultiModalText, SftMultiModalImage]]] = ""
    # 消息角色（如：user）
    role: Optional[str] = None
    # 消息发言人, 如：小明，多人会话时使用
    name: Optional[str] = None
    tool_calls: Optional[Any] = None


class Usage(BaseModel):
    completion_tokens: int
    prompt_tokens: int
    total_tokens: int


class TraceInfo(ExtraInfoModel):
    usage: Optional[Usage] = None


class AIHubLLMType(str, Enum):
    llm_chat = "llm-chat"  # 聊天
    llm_multimodal = "llm-multimodal"  # 多模态


class PublicLLMConfig(BaseModel):
    # 模型信息
    provider: str
    model: str
    version: str

    # 产品编号，None则走ai_gateway默认配置
    product_name: Optional[str] = None
    intention_code: Optional[str] = None

    # 审核编号，None则走ai_gateway默认配置
    sec_scene: Optional[str] = None
    sec_from: Optional[str] = None

    llm_types: Optional[List[Union[AIHubLLMType, str]]] = None


class SftLLMConfig(BaseModel):
    # 模型信息
    base_model: str
    # lora
    lora_model: Optional[str] = None


class LLMConfig(BaseModel):
    gateway: int = 0
    public: Optional[PublicLLMConfig] = None
    sft: Optional[SftLLMConfig] = None


class AIHubType(str, Enum):
    public = "public"  # 官方模型
    private = "private"  # 用户自定义模型


class AIHubModel(BaseModel):
    """
    AI Hub 信息，从该接口获取https://365.kdocs.cn/office/link?target=http%3A%2F%2Fkbe.kingsoft.net%2Fapis%2Fall.html%23tag%2FAiHub%2Foperation%2Fget_user_model_profile&fileId=320015328846
    """
    info: Optional[dict] = None
    # 模型支持的LLM类型，enum：llm-chat（聊天）、llm-multimodal（多模态）
    llm_types: Optional[List[Union[AIHubLLMType, str]]] = None
    name: Optional[str] = None
    provider: Optional[str] = None
    version: Optional[str] = None
    # 私有化用，ai网关的AI-Gateway-Model-Choice，enum：public（官方）、private（用户自定义）
    type: Union[AIHubType, str, None] = None


class PublicGatewayHeader(BaseModel):
    """
    AI网关请求权益相关的头信息
    """
    ai_gateway_uid: Optional[str] = None
    ai_gateway_company_id: Optional[str] = None
    ai_gateway_product_name: Optional[str] = None
    ai_gateway_intention_code: Optional[str] = None
    ai_gateway_billing_token: Optional[str] = None
    """
    AI Hub 模型配置
    """
    ai_hub_models: Optional[List[AIHubModel]] = None

    # 已过期
    ai_hub_provider: Optional[str] = None
    # 已过期
    ai_hub_model: Optional[str] = None
    # 已过期
    ai_hub_version: Optional[str] = None
    # 已过期
    ai_gateway_model_choice: Optional[str] = None
    """
    AI 网关审核配置
    """
    sec_from: Optional[str] = None
    sec_scene: Optional[str] = None
    sec_answer_flag: int = 0
    sec_extra_text: Optional[List[str]] = None
    """
    最终选定的模型（AI Hub会返回模型列表，这里确定最终llm请求使用的模型）
    """
    final_ai_hub_model: Optional[AIHubModel] = None

    def load_by_req(self, req: Request):
        """
        已过期
        通过fastapi Request加载
        :param req: FastAPI Request 对象
        """
        warnings.warn("load_by_req is deprecated", DeprecationWarning)
        self.ai_gateway_uid = req.headers.get("AI-Gateway-Uid", None)
        self.ai_gateway_company_id = req.headers.get("AI-Gateway-Company-Id", None)
        self.ai_gateway_product_name = req.headers.get("AI-Gateway-Product-Name", None)
        self.ai_gateway_intention_code = req.headers.get("AI-Gateway-Intention-Code", None)
        self.ai_gateway_billing_token = req.headers.get("AI-Gateway-Billing-Token", None)

        self.sec_from = req.headers.get("Sec-From", None)
        self.sec_scene = req.headers.get("Sec-Scene", None)

        sec_extra_text = req.headers.get("Sec-Extra-Text", None)
        if sec_extra_text is not None:
            decoded_bytes = base64.b64decode(sec_extra_text)
            self.sec_extra_text = [decoded_bytes.decode('utf-8')]

        ai_hub_models = req.headers.get("AI-Hub-Models", None)

        if ai_hub_models is not None:
            try:
                res = []
                for m in json.loads(ai_hub_models):
                    res.append(AIHubModel.model_validate(m))
                self.ai_hub_models = res
            except Exception as e:
                logging.error(f"Invalid AI-Hub-Models header: {e}")

        self.ai_hub_provider = req.headers.get("AI-Hub-Provider", None)
        self.ai_hub_model = req.headers.get("AI-Hub-Model", None)
        self.ai_hub_version = req.headers.get("AI-Hub-Version", None)
        self.ai_gateway_model_choice = req.headers.get("AI-Gateway-Model-Choice", None)

    def load_by_req_header(self, req: Request):
        """
        通过fastapi Request加载
        :param req: FastAPI Request 对象
        """
        self.ai_gateway_uid = req.headers.get("AI-Gateway-Uid", None)
        self.ai_gateway_company_id = req.headers.get("AI-Gateway-Company-Id", None)
        self.ai_gateway_product_name = req.headers.get("AI-Gateway-Product-Name", None)
        self.ai_gateway_intention_code = req.headers.get("AI-Gateway-Intention-Code", None)
        self.ai_gateway_billing_token = req.headers.get("AI-Gateway-Billing-Token", None)

        self.sec_from = req.headers.get("Sec-From", None)
        self.sec_scene = req.headers.get("Sec-Scene", None)

        sec_extra_text = req.headers.get("Sec-Extra-Text", None)
        if sec_extra_text is not None:
            decoded_bytes = base64.b64decode(sec_extra_text)
            self.sec_extra_text = [decoded_bytes.decode('utf-8')]

        ai_hub_models = req.headers.get("AI-Hub-Models", None)

        if ai_hub_models is not None:
            try:
                res = []
                for m in json.loads(ai_hub_models):
                    res.append(AIHubModel.model_validate(m))
                self.ai_hub_models = res
            except Exception as e:
                logging.error(f"Invalid AI-Hub-Models header: {e}")

    def set_final_ai_hub_model(self, ai_hub_model: AIHubModel):
        """
        设置最终选定的AI Hub模型
        :param ai_hub_model: AIHubModel 对象
        """
        if ai_hub_model is not None:
            self.final_ai_hub_model = ai_hub_model

    def load_by_llm_config(self, llm_config: LLMConfig, cover_origin: bool = False):
        """
        已过期
        通过LLMConfig加载
        :param llm_config: LLMConfig 对象
        """
        warnings.warn("load_by_req is deprecated", DeprecationWarning)
        if llm_config is not None and llm_config.public is not None:
            if cover_origin:
                self.ai_gateway_product_name = llm_config.public.product_name
                self.ai_gateway_intention_code = llm_config.public.intention_code
                self.sec_from = llm_config.public.sec_from
                self.sec_scene = llm_config.public.sec_scene
                self.ai_hub_provider = llm_config.public.provider
                self.ai_hub_model = llm_config.public.model
                self.ai_hub_version = llm_config.public.version
                self.final_ai_hub_model = AIHubModel(
                    provider=llm_config.public.provider,
                    name=llm_config.public.model,
                    version=llm_config.public.version,
                    llm_types=llm_config.public.llm_types
                )
            else:
                self.ai_gateway_product_name = self.ai_gateway_product_name if self.ai_gateway_product_name is not None else llm_config.public.product_name
                self.ai_gateway_intention_code = self.ai_gateway_intention_code if self.ai_gateway_intention_code is not None else llm_config.public.intention_code
                self.sec_from = self.sec_from if self.sec_from is not None else llm_config.public.sec_from
                self.sec_scene = self.sec_scene if self.sec_scene is not None else llm_config.public.sec_scene
                self.ai_hub_provider = self.ai_hub_provider if self.ai_hub_provider is not None else llm_config.public.provider
                self.ai_hub_model = self.ai_hub_model if self.ai_hub_model is not None else llm_config.public.model
                self.ai_hub_version = self.ai_hub_version if self.ai_hub_version is not None else llm_config.public.version
                self.final_ai_hub_model = self.final_ai_hub_model if self.final_ai_hub_model is not None else AIHubModel(
                    provider=llm_config.public.provider,
                    name=llm_config.public.model,
                    version=llm_config.public.version,
                    llm_types=llm_config.public.llm_types
                )

    def set_sec_data(self, sec_answer_flag: int, sec_extra_text: List[str]):
        self.sec_answer_flag = sec_answer_flag
        self.sec_extra_text = sec_extra_text

    def load_by_local_llm_config(self, llm_config: LLMConfig, cover_origin: bool = False):
        """
        通过LLMConfig加载
        :param llm_config: LLMConfig 对象
        """
        if llm_config is not None and llm_config.public is not None:
            if cover_origin:
                self.ai_gateway_product_name = llm_config.public.product_name
                self.ai_gateway_intention_code = llm_config.public.intention_code
                self.sec_from = llm_config.public.sec_from
                self.sec_scene = llm_config.public.sec_scene
                self.final_ai_hub_model = AIHubModel(
                    provider=llm_config.public.provider,
                    name=llm_config.public.model,
                    version=llm_config.public.version,
                    llm_types=llm_config.public.llm_types
                )
            else:
                self.ai_gateway_product_name = self.ai_gateway_product_name if self.ai_gateway_product_name is not None else llm_config.public.product_name
                self.ai_gateway_intention_code = self.ai_gateway_intention_code if self.ai_gateway_intention_code is not None else llm_config.public.intention_code
                self.sec_from = self.sec_from if self.sec_from is not None else llm_config.public.sec_from
                self.sec_scene = self.sec_scene if self.sec_scene is not None else llm_config.public.sec_scene
                self.final_ai_hub_model = self.final_ai_hub_model if self.final_ai_hub_model is not None else AIHubModel(
                    provider=llm_config.public.provider,
                    name=llm_config.public.model,
                    version=llm_config.public.version,
                    llm_types=llm_config.public.llm_types
                )

    def set_sec_data(self, sec_answer_flag: int, sec_extra_text: List[str]):
        self.sec_answer_flag = sec_answer_flag
        self.sec_extra_text = sec_extra_text
