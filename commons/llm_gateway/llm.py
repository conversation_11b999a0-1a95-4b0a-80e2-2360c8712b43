import asyncio

from commons.llm_gateway.models.public_model_gateway import PublicModelGateway, ChatResponse, SecText
from commons.llm_gateway.models.public_model_gateway import (
    LLMParam,
    ChatParams,
    PublicCode,
    ERROR_AUDIT_ARR,
    ERROR_PRIVILEGE_ARR,
    ERROR_LIMIT_ARR,
    ChatResponse as PublicChatResponse,
)
from commons.llm_gateway.models.sft_model_gateway import SftModelGateway, SftChatParams, SftChatResponse
from commons.llm_gateway.models.private_model_gateway import PrivateModelGateway, PrivateChatParams, \
    PrivateChatResponse, PrivateCode
from commons.llm_gateway.models.private_model_gateway import Platform as PrivatePlatform
from commons.llm_gateway.models.chat_data import Message, ChatType, SftBaseModelType, Usage, PublicGatewayHeader
from commons.tools.utils import Singleton
from typing import Optional, List, Union, Generator, Any, Dict, Tuple, AsyncGenerator
from enum import Enum
from pydantic import BaseModel
from commons.tools.utils import calc_time, async_calc_time


# 整合公网、自部署、私有化等模型的返回状态
class LLMChatStatus(str, Enum):
    OK = "ok"
    FAIL = "fail"
    AUDIT = "audit"
    PRIVILEGE = "privilege"
    LIMIT = "limit"
    JSON_ERROR = "json_error"


class LLMStreamItem(BaseModel):
    status: LLMChatStatus
    text: str
    usage: Optional[Usage] = None


class LLModelRpc(object, metaclass=Singleton):
    class Gateway(int, Enum):
        # 公网AI模型网关
        Public = 0
        # 私有部署的微调模型服务
        Sft = 1
        # 私有化AI模型网关
        Private = 2

    class ModelSelector(BaseModel):
        provider: str = ""
        model: str = ""
        version: str = ""

    class SftBaseModel(BaseModel):
        base_model: str = ""

    class LLMArgs(BaseModel):
        temperature: float = 0.7
        max_tokens: int = 2000
        top_p: Optional[float] = None
        top_k: Optional[int] = None
        stop: Optional[List[str]] = None
        stop_token_ids: Optional[List[int]] = None
        min_p: Optional[float] = None
        context: Optional[str] = None
        extended_llm_arguments: Optional[Dict[str, Any]] = None
        tool_choice: Optional[str] = None
        tools: Optional[List[Dict]] = None
        repetition_penalty: Optional[float] = None
    def __init__(self):
        self._public_model: Optional[PublicModelGateway] = None
        self._sft_model: Optional[SftModelGateway] = None
        self._private_model: Optional[PrivateModelGateway] = None

    async def close(self):
        if self._public_model:
            await self._public_model.close()

    def create_models(self,
                      public_conf: PublicModelGateway.Conf = None,
                      sft_conf: SftModelGateway.Conf = None,
                      private_conf: PrivateModelGateway.Conf = None,
                      pool_max=-1):

        if public_conf:
            self._public_model = PublicModelGateway(public_conf, pool_max=pool_max)
        if sft_conf:
            self._sft_model = SftModelGateway(sft_conf, pool_max=pool_max)
        if private_conf:
            self._private_model = PrivateModelGateway(private_conf, pool_max=pool_max)

    def _create_params(self,
                       stream: bool,
                       gateway: Gateway,
                       messages: List[Message],
                       selector: ModelSelector,
                       args: LLMArgs,
                       prompt: Union[List[int], List[List[int]], str, List[str]] = None,
                       chat_type: ChatType = ChatType.chat,
                       sft_base_model: SftBaseModel = None,
                       **kwargs):
        if gateway == LLModelRpc.Gateway.Public:
            if args is None:
                args = LLModelRpc.LLMArgs(top_p=0.9)

            base_llm_arguments = LLMParam(
                temperature=args.temperature,
                max_tokens=args.max_tokens,
                top_p=args.top_p,
                stop=args.stop)

            if args.extended_llm_arguments:
                provider = selector.provider if selector else self._public_model.get_conf().provider
                model = selector.model if selector else self._public_model.get_conf().model
                extended_llm_arguments = {
                    f"{provider}_{model}": args.extended_llm_arguments
                }
            else:
                extended_llm_arguments = None
            public_gateway_header = kwargs.get("public_gateway_header", None)
            if public_gateway_header is not None:
                if isinstance(public_gateway_header, str):
                    PublicGatewayHeader.model_validate_json(public_gateway_header)
                else:
                    public_gateway_header = PublicGatewayHeader.model_validate(public_gateway_header)
                user_id = public_gateway_header.ai_gateway_uid
                company_id = public_gateway_header.ai_gateway_company_id
                ai_product_name = public_gateway_header.ai_gateway_product_name
                ai_intention_code = public_gateway_header.ai_gateway_intention_code
                ai_token = public_gateway_header.ai_gateway_billing_token
                if public_gateway_header.final_ai_hub_model is not None:
                    model_type = public_gateway_header.final_ai_hub_model.type
                elif public_gateway_header.ai_gateway_model_choice is not None:
                    model_type = public_gateway_header.ai_gateway_model_choice
                else:
                    model_type = None
                sec_text = SecText(scene=public_gateway_header.sec_scene)
                sec_text.from_ = public_gateway_header.sec_from
                sec_text.answer_flag = public_gateway_header.sec_answer_flag
                sec_text.extra_text = public_gateway_header.sec_extra_text
            else:
                # 兼容老版本
                user_id = kwargs.get("user_id", None)
                company_id = kwargs.get("company_id", None)
                ai_product_name = kwargs.get("ai_product_name", None)
                ai_intention_code = kwargs.get("ai_intention_code", None)
                ai_token = kwargs.get("ai_token", None)
                model_type = kwargs.get("model_type", None)
                sec_text = None
                if kwargs.get("sec_text", None):
                    sec_text = SecText(scene=kwargs.get("sec_scene", None))
                    sec_text.from_ = kwargs.get("sec_from", None)
                    sec_text.answer_flag = kwargs.get("sec_answer_flag", 0)
                    sec_text.extra_text = kwargs.get("sec_extra_text", None)
            params = ChatParams(
                stream=stream,
                provider=selector.provider if selector else "",
                model=selector.model if selector else "",
                version=selector.version if selector else "",
                chat_type=chat_type,
                messages=messages,
                context=args.context,
                base_llm_arguments=base_llm_arguments,
                extended_llm_arguments=extended_llm_arguments,
                company_id=company_id,
                ai_token=ai_token,
                ai_product_name=ai_product_name,
                ai_intention_code=ai_intention_code,
                user_id=user_id,
                model_type=model_type,
                sec_text=sec_text,
                tool_choice=args.tool_choice,
                tools=args.tools,
                time_out=kwargs.get("time_out", -1)  # 用于设置请求超时时间
            )
        elif gateway == LLModelRpc.Gateway.Sft:
            if args is None:
                args = LLModelRpc.LLMArgs(stop_token_ids=[151645, 151644])
            else:
                if not args.stop_token_ids:
                    args.stop_token_ids = [151645, 151644]  # [<|im_start|> 、 <|im_end|>]
            params = SftChatParams(
                model=selector.model if selector and selector.model else "",  # lora adapter name
                chat_type=chat_type,
                stream=stream,
                base_model=sft_base_model.base_model if sft_base_model else None,
                max_tokens=args.max_tokens,
                temperature=args.temperature,
                messages=messages,
                prompt=prompt,
                stop=args.stop,
                stop_token_ids=args.stop_token_ids,
                min_p=args.min_p,
                guided_choice=kwargs.get("guided_choice", None),
                guided_regex=kwargs.get("guided_regex", None),
                guided_json=kwargs.get("guided_json", None),
                n=kwargs.get('n', 1) if not stream else 1,
                time_out=kwargs.get("time_out", -1),  # 用于设置请求超时时间
                chat_template_kwargs=kwargs.get("chat_template_kwargs", None)
            )

        elif gateway == LLModelRpc.Gateway.Private:
            params = PrivateChatParams(stream=stream,
                                       max_tokens=args.max_tokens,
                                       temperature=args.temperature,
                                       messages=messages,
                                       stop=args.stop,
                                       top_p=args.top_p,
                                       stop_token_ids=args.stop_token_ids,
                                       private_uid=kwargs.get("private_uid", None),
                                       private_product_name=kwargs.get("private_product_name", None))
        else:
            raise Exception(f"gateway type error: gateway={gateway}")
        return params

    def _select_model_by_gateway(self, gateway: Gateway):
        model = None
        if gateway == LLModelRpc.Gateway.Public:
            model = self._public_model
        elif gateway == LLModelRpc.Gateway.Sft:
            model = self._sft_model
        elif gateway == LLModelRpc.Gateway.Private:
            model = self._private_model
        else:
            raise Exception(f"gateway type error: gateway={gateway}")
        return model

    def chat(self,
             stream: bool,
             gateway: Gateway,
             messages: List[Message],
             selector: ModelSelector = None,
             args: LLMArgs = None,
             chat_type: ChatType = ChatType.chat,
             sft_base_model: SftBaseModel = None,
             enable_trace: bool = True,
             **kwargs
             ) -> Union[
        PublicChatResponse,
        SftChatResponse,
        PrivateChatResponse,
        Generator[ChatResponse, None, None],
        Generator[Union[SftChatResponse, None], None, None],
        Generator[PrivateChatResponse, None, None],
        None
    ]:
        params = self._create_params(
            stream, gateway, messages, selector, args, chat_type=chat_type, sft_base_model=sft_base_model, **kwargs)
        model = self._select_model_by_gateway(gateway)
        trace_name = f"{selector.provider}:{selector.model}:{selector.version}" if selector else ""
        return ( model.chat_stream(params, __trace_name__ = trace_name, enable_trace = enable_trace)
                if stream else model.chat(params, __trace_name__ = trace_name, enable_trace = enable_trace))

    async def async_chat(self,
                         stream: bool,
                         gateway: Gateway,
                         messages: List[Message],
                         selector: ModelSelector = None,
                         args: LLMArgs = None,
                         chat_type: ChatType = ChatType.chat,
                         sft_base_model: SftBaseModel = None,
                         enable_trace = True,
                         **kwargs
                         ) -> Union[
        PublicChatResponse,
        SftChatResponse,
        PrivateChatResponse,
        AsyncGenerator[PublicChatResponse, Any],
        AsyncGenerator[SftChatResponse, Any],
        AsyncGenerator[PrivateChatResponse, Any],
        None,
    ]:
        params = self._create_params(stream, gateway, messages, selector, args, chat_type=chat_type, sft_base_model=sft_base_model ,**kwargs)
        model = self._select_model_by_gateway(gateway)
        trace_name = f"{selector.provider}:{selector.model}:{selector.version}" if selector else ""
        if stream:
            return model.async_chat_stream(params, __trace_name__ = trace_name, enable_trace = enable_trace)
        else:
            return await model.async_chat(params, __trace_name__ = trace_name, enable_trace = enable_trace)

    def _before_chattext(self,
                         gateway: Gateway,
                         query: str,
                         temperature,
                         max_token,
                         adapter: str = "",
                         repetition_penalty: float = 1.0,
                         system_content: str = None,
                         selectors: ModelSelector = None,
                         messages: List[Message] = None,
                         **kwargs
                         ) -> Tuple[List[Message], Optional["LLModelRpc.ModelSelector"], "LLModelRpc.LLMArgs"]:
        args = LLModelRpc.LLMArgs(temperature=temperature, max_tokens=max_token,repetition_penalty=repetition_penalty)
        valid_kwargs = {k: v for k, v in kwargs.items() if k in LLModelRpc.LLMArgs.__fields__}
        for k, v in valid_kwargs.items():
            if k == 'stop' and isinstance(v, str):
                v = [v]
            setattr(args, k, v)

        if messages is None:
            if gateway == LLModelRpc.Gateway.Public:
                messages = []
                if system_content:
                    messages.append(Message(role="system", content=system_content))
                messages.append(Message(role="user", content=query))
            elif gateway == LLModelRpc.Gateway.Sft:
                messages = [Message(role="system", content="You are a helpful assistant."),
                            Message(role="user", content=query)]
                if selectors is None:
                    selectors = LLModelRpc.ModelSelector(model=adapter) if adapter else None
            elif gateway == LLModelRpc.Gateway.Private and \
                    self._private_model.get_conf().platform == PrivatePlatform.minimax:
                messages = [Message(role="USER", content=query)]
            else:
                messages = [Message(role="user", content=query)]
        return messages, selectors, args

    def _after_chattext_stream(self, res, gateway: Gateway):
        for line in res:
            if gateway == LLModelRpc.Gateway.Public:
                if line is None:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                elif line.code == PublicCode.FAILED:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                elif line.code in ERROR_AUDIT_ARR:
                    yield LLMStreamItem(status=LLMChatStatus.AUDIT, text="")
                elif line.code in ERROR_PRIVILEGE_ARR:
                    yield LLMStreamItem(status=LLMChatStatus.PRIVILEGE, text="")
                elif line.code in ERROR_LIMIT_ARR:
                    yield LLMStreamItem(status=LLMChatStatus.LIMIT, text="")
                # 审核拦截时没有 choices，需要暴露审核信息，所以choices 判断需要放置到 audit 验证之后
                elif not line.choices:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                else:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=line.choices[0].text, usage=line.usage)
            elif gateway == LLModelRpc.Gateway.Sft:
                if line is None or line.choices is None:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                elif len(line.choices) == 0 and line.usage is not None:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text="", usage=line.usage)
                else:
                    if line.choices[0].text is not None:
                        yield LLMStreamItem(status=LLMChatStatus.OK, text=line.choices[0].text, usage=line.usage)
                    else:
                        yield LLMStreamItem(status=LLMChatStatus.OK, text=line.choices[0].delta.content, usage=line.usage)
            elif gateway == LLModelRpc.Gateway.Private:
                if line is None or not line.choices:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                elif line.code == PrivateCode.FAILED:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                else:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=line.choices[0].text, usage=line.usage)
            else:
                raise Exception(f"gateway type error: gateway={gateway}")

    async def _async_after_chattext_stream(self, res, gateway: Gateway):
        async for line in res:
            if gateway == LLModelRpc.Gateway.Public:
                if line is None:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                elif line.code == PublicCode.FAILED:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                elif line.code in ERROR_AUDIT_ARR:
                    yield LLMStreamItem(status=LLMChatStatus.AUDIT, text="")
                elif line.code in ERROR_PRIVILEGE_ARR:
                    yield LLMStreamItem(status=LLMChatStatus.PRIVILEGE, text="")
                elif line.code in ERROR_LIMIT_ARR:
                    yield LLMStreamItem(status=LLMChatStatus.LIMIT, text="")
                elif not line.choices:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                else:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=line.choices[0].text, usage=line.usage)
            elif gateway == LLModelRpc.Gateway.Sft:
                if line is None or line.choices is None:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                else:
                    if len(line.choices) == 0:
                        content = ""
                    else:
                        content = line.choices[0].delta.content
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=content, usage=line.usage)
            elif gateway == LLModelRpc.Gateway.Private:
                if line is None or not line.choices:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                elif line.code == PrivateCode.FAILED:
                    yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")
                else:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=line.choices[0].text, usage=line.usage)
            else:
                raise Exception(f"gateway type error: gateway={gateway}")

    def _after_chattext(self, res, gateway: Gateway, return_usage: bool):
        text = ""
        status: LLMChatStatus = LLMChatStatus.OK
        if gateway == LLModelRpc.Gateway.Public:
            if res is None or res.code == PublicCode.FAILED:
                status = LLMChatStatus.FAIL
            elif res.code in ERROR_AUDIT_ARR:
                status = LLMChatStatus.AUDIT
            elif res.code in ERROR_PRIVILEGE_ARR:
                status = LLMChatStatus.PRIVILEGE
            elif res.code in ERROR_LIMIT_ARR:
                status = LLMChatStatus.LIMIT
            elif not res.choices:
                status = LLMChatStatus.FAIL
            else:
                text =  res.choices[0].text
        elif gateway == LLModelRpc.Gateway.Sft:
            if res is None or not res.choices:
                status = LLMChatStatus.FAIL
            else:
                if res.choices[0].text is not None:
                    result = [_.text for _ in res.choices]
                else:
                    result = [_.message.content for _ in res.choices]
                text = result[0] if len(result) == 1 else result
        elif gateway == LLModelRpc.Gateway.Private:
            if res is None or res.code == PrivateCode.FAILED or not res.choices:
                status = LLMChatStatus.FAIL
            else:
                text = res.choices[0].text
        else:
            raise Exception(f"gateway type error: gateway={gateway}")
        if return_usage:
            return status, text, res.usage
        else:
            return status, text

    @calc_time
    def chat_text(self,
                  gateway: Gateway,
                  query: str = "",
                  temperature=0.7,
                  max_token=2000,
                  adapter: str = "",
                  prompt: Union[List[int], List[List[int]], str, List[str], None] = None,
                  selector: ModelSelector = None,
                  messages: List[Message] = None,
                  sft_base_model: SftBaseModel = None,
                  return_usage: bool = False,
                  **kwargs
                  ) -> Union[
                      Tuple[LLMChatStatus, Union[str, List[str]]],
                      Tuple[LLMChatStatus, Union[str, List[str]], Usage]
                            ]:
        """
        kwargs: 放不同llm特定的参数
                public: company_id:str
                        ai_token:str
                        ai_product_name:str
                        ai_intention_code:str
                        user_id:str
                sft: guided_choice:str
                     guided_regex:str
                     guided_json:str
                private: private_uid:str
                         private_product_name:str
        query 会拼接成messages形式  [ {"role": "user", "content": query} ]， 在vllm中进行拼接成chatml格式的输入
        prompt 是补全模式，直接输入进模型，不会进行任何修改
        """
        if prompt is not None:
            if gateway != LLModelRpc.Gateway.Sft:
                return LLMChatStatus.FAIL, "补全只支持私有化模型"
            query = ''
        messages, selectors, args = self._before_chattext(gateway, query, temperature, max_token, adapter,
                                                          system_content=kwargs.get("system_content", None),
                                                          selectors=selector,
                                                          messages=messages, **kwargs)
        res = self.chat(
            False, gateway, messages, selectors, args, prompt=prompt, sft_base_model=sft_base_model, **kwargs)
        return self._after_chattext(res, gateway, return_usage)

    def chat_text_stream(self,
                         gateway: Gateway,
                         query: str = "",
                         temperature=0.7,
                         max_token=2000,
                         adapter: str = "",
                         prompt: Union[List[int], List[List[int]], str, List[str], None] = None,
                         selector: ModelSelector = None,
                         messages: List[Message] = None,
                         sft_base_model: SftBaseModel = None,
                         **kwargs
                         ) -> Generator[LLMStreamItem, None, None]:
        if prompt is not None:
            if gateway != LLModelRpc.Gateway.Sft:
                return self._after_chattext_stream([None], gateway)
            query = ''
        messages, selectors, args = self._before_chattext(gateway, query, temperature, max_token, adapter,
                                                          system_content=kwargs.get("system_content", None),
                                                          selectors=selector,
                                                          messages=messages,
                                                          **kwargs)
        res = self.chat(
            True, gateway, messages, selectors, args, prompt=prompt, sft_base_model=sft_base_model,**kwargs)
        return self._after_chattext_stream(res, gateway)

    @async_calc_time
    async def async_chat_text(self,
                              gateway: Gateway,
                              query: str = "",
                              temperature=0.7,
                              max_token=2000,
                              repetition_penalty=1.0,
                              adapter: str = "",
                              prompt: Union[List[int], List[List[int]], str, List[str], None] = None,
                              selector: ModelSelector = None,
                              messages: List[Message] = None,
                              sft_base_model: SftBaseModel = None,
                              return_usage: bool = False,
                              **kwargs
                              ) -> Union[
                                Tuple[LLMChatStatus, Union[str, List[str]]],
                                Tuple[LLMChatStatus, Union[str, List[str]], Usage]
                                ]:
        if prompt is not None:
            if gateway != LLModelRpc.Gateway.Sft:
                return LLMChatStatus.FAIL, "补全只支持私有化模型"
            query = ''
        messages, selectors, args = self._before_chattext(gateway, query, temperature, max_token, adapter,
                                                          repetition_penalty,
                                                          system_content=kwargs.get("system_content", None),
                                                          selectors=selector,
                                                          messages=messages,
                                                          **kwargs)
        res = await self.async_chat(
            False, gateway, messages, selectors, args, prompt=prompt, sft_base_model=sft_base_model, **kwargs)
        return self._after_chattext(res, gateway, return_usage)

    async def async_chat_text_stream(self,
                                     gateway: Gateway,
                                     query: str = "",
                                     temperature=0.7,
                                     max_token=2000,
                                     adapter: str = "",
                                     prompt: Union[List[int], List[List[int]], str, List[str], None] = None,
                                     selector: ModelSelector = None,
                                     messages: List[Message] = None,
                                     sft_base_model: SftBaseModel = None,
                                     **kwargs
                                     ) -> AsyncGenerator[LLMStreamItem, Any]:
        if prompt is not None:
            if gateway != LLModelRpc.Gateway.Sft:
                return self._async_after_chattext_stream([None], gateway)
            query = ''
        messages, selectors, args = self._before_chattext(gateway, query, temperature, max_token, adapter,
                                                          system_content=kwargs.get("system_content", None),
                                                          selectors=selector,
                                                          messages=messages,
                                                          **kwargs)
        res = await self.async_chat(
            True, gateway, messages, selectors, args, sft_base_model=sft_base_model, **kwargs)
        return self._async_after_chattext_stream(res, gateway)

    @calc_time
    def multimodal(self,
                   gateway: Gateway,
                   messages: List[Message],
                   stream: bool = False,
                   temperature=0.7,
                   max_token=2000,
                   adapter: str = "",
                   selector: ModelSelector = None,
                   sft_base_model: SftBaseModel = None,
                   return_usage: bool = False,
                   **kwargs
                   ) -> Union[Tuple[LLMChatStatus, Union[str, List[str]]], Generator[LLMStreamItem, None, None]]:
        """
        kwargs: 放不同llm特定的参数
                public: company_id:str
                        ai_token:str
                        ai_product_name:str
                        ai_intention_code:str
                        user_id:str
        """
        messages, selectors, args = self._before_chattext(gateway, "", temperature, max_token, adapter,
                                                          system_content=kwargs.get("system_content", None),
                                                          selectors=selector,
                                                          messages=messages,
                                                          **kwargs)
        res = self.chat(stream, gateway, messages, selectors, args, ChatType.multimodal, sft_base_model=sft_base_model, **kwargs)
        if stream:
            return self._after_chattext_stream(res, gateway)
        else:
            return self._after_chattext(res, gateway, return_usage=return_usage)

    @async_calc_time
    async def async_multimodal(self,
                               gateway: Gateway,
                               messages: List[Message],
                               stream: bool = False,
                               temperature=0.7,
                               max_token=2000,
                               adapter: str = "",
                               selector: ModelSelector = None,
                               sft_base_model: SftBaseModel = None,
                               return_usage: bool = False,
                               **kwargs
                               ) -> Union[
        Tuple[LLMChatStatus, Union[str, List[str]]], AsyncGenerator[LLMStreamItem, Any]]:
        messages, selectors, args = self._before_chattext(gateway, "", temperature, max_token, adapter,
                                                          system_content=kwargs.get("system_content", None),
                                                          selectors=selector,
                                                          messages=messages,
                                                          **kwargs)
        res = await self.async_chat(stream, gateway, messages, selectors, args, ChatType.multimodal, sft_base_model=sft_base_model, **kwargs)
        if stream:
            return self._async_after_chattext_stream(res, gateway)
        else:
            return self._after_chattext(res, gateway, return_usage=return_usage)

    def _get_default_tpm_key(self, gateway: Gateway) -> str:
        key = ""
        match gateway:
            case LLModelRpc.Gateway.Public:
                key = f"{self._public_model.get_conf().provider}_{self._public_model.get_conf().model}"
            case LLModelRpc.Gateway.Sft:
                key = ChatType.chat.value
            case LLModelRpc.Gateway.Private:
                key = ""
        return key

    def get_tpm(self, gateway: Gateway, key: str = None) -> int:
        if key is None:
            key = self._get_default_tpm_key(gateway)
        model = self._select_model_by_gateway(gateway)
        return model.get_tpm(key)

    async def async_get_tpm(self, gateway: Gateway, key: str = None):
        if key is None:
            key = self._get_default_tpm_key(gateway)
        model = self._select_model_by_gateway(gateway)
        return await model.async_get_tpm(key)


# 注释掉main方法逻辑以提高测试覆盖率
# if __name__ == "__main__":
#     import os
#     import time
#     from ..logger.logger import init_logger
#     init_logger("", "DEBUG", "err")


#     async def _main():
#         record_token = False
#         _record_token_callback = None
#         _arecord_token_callback = None
#         _get_tpm_callback = None
#         _aget_tpm_callback = None
#         if record_token:
#             from ..db.redis5dao import Redis5Dao
#             await Redis5Dao().init(hosts=["**************:6379"], password="", prefix="llm_",
#                                    cluster=False)

#             def _record_token_callback(key: str, token_count: int):
#                 if Redis5Dao().is_init:
#                     # 使用当前分钟作为键
#                     minute_key = f"{key}_{int(time.time()) // 60}"
#                     # 增加当前分钟的token
#                     Redis5Dao().incrby(minute_key, token_count)
#                     Redis5Dao().expire(minute_key, 60)

#             async def _arecord_token_callback(key: str, token_count: int):
#                 if Redis5Dao().is_init:
#                     # 使用当前分钟作为键
#                     minute_key = f"{key}_{int(time.time()) // 60}"
#                     # 增加当前分钟的token
#                     await Redis5Dao().aincrby(minute_key, token_count)
#                     await Redis5Dao().aexpire(minute_key, 60)

#             def _get_tpm_callback(key: str) -> int:
#                 if Redis5Dao().is_init:
#                     # 获取当前分钟的键
#                     minute_key = f"{key}_{int(time.time()) // 60}"
#                     # 获取当前分钟的令牌数
#                     tpm = Redis5Dao().getint(minute_key)
#                     return tpm
#                 else:
#                     return 0

#             async def _aget_tpm_callback(key: str) -> int:
#                 if Redis5Dao().is_init:
#                     # 获取当前分钟的键
#                     minute_key = f"{key}_{int(time.time()) // 60}"
#                     # 获取当前分钟的令牌数
#                     tpm = await Redis5Dao().agetint(minute_key)
#                     return tpm
#                 else:
#                     return 0

#         test_list = [("ali", "qwen-plus"), ("zhipu", "glm-4"), ("minimax", "abab6.5s-chat")]
#         sec_scene = "aidocs_extract"
#         sec_from = "AI_DRIVE_KNOWLEDGE"

#         pub_conf = PublicModelGateway.Conf(
#             host="http://aigc-gateway-test.ksord.com",
#             token=os.environ["ai_gateway_token"],
#             uid="9047",
#             product_name="wps-kanmail-qa",
#             intention_code="aigctest",
#             provider="ali",
#             model="qwen-plus",
#             version=None,
#             multimodal_provider="ali",
#             multimodal_model="qwen-vl-max-0809",
#             multimodal_version=None,
#             sec_from="AI_DRIVE_KNOWLEDGE",
#             record_token=record_token,
#             record_token_callback=_record_token_callback,
#             async_record_token_callback=_arecord_token_callback,
#             get_tpm_callback=_get_tpm_callback,
#             async_get_tpm_callback=_aget_tpm_callback
#         )
#         sft_conf = SftModelGateway.Conf(host="http://privatization-model-test.kna.wps.cn",
#                                         multimodal_host="http://privatization-model.kna.wps.cn/vl_7b",
#                                         token=os.environ["kna_gateway_token"])
#         private_conf = PrivateModelGateway.Conf(
#             host="http://weboffice-aigatewaypri",
#             platform=PrivatePlatform.minimax,
#             prom_token=False,
#             minimax_model="abab5-chat")
#         LLModelRpc().create_models(pub_conf, sft_conf, private_conf)

    #     query = "写一首五言律诗"
    #     messages = [Message(role="user", content="写一篇100字的风景文")]
    #     # company_id = "*********"
    #     # ai_token = None
    #     # ai_product_name = "saas_knowledgebase_pc"
    #     # ai_intention_code = "saas_knowledgebase_assistedcreation"
    #     # user_id = "*********"

    #     company_id = None
    #     ai_token = None
    #     ai_product_name = None
    #     ai_intention_code = None
    #     user_id = None

    #     print("--------------------------public model--------------------------------")
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Public, query, company_id=company_id, ai_token=ai_token,
    #                                  ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                  user_id=user_id))
    #     print("*********stream***********")
    #     res = LLModelRpc().chat_text_stream(LLModelRpc.Gateway.Public, query, company_id=company_id, ai_token=ai_token,
    #                                         ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                         user_id=user_id)
    #     for t in res:
    #         print(t)

    #     for test in test_list:
    #         provider = test[0]
    #         model = test[1]
    #         print(f"-----------------------{provider}, {model}---------------------")
    #         selector = LLModelRpc.ModelSelector(provider=provider, model=model)
    #         print(LLModelRpc().chat_text(LLModelRpc.Gateway.Public, query, selector=selector))
    #         print(await
    #               LLModelRpc().async_chat_text(LLModelRpc.Gateway.Public, query, selector=selector))

    #     print("********************************")
    #     print("************messages************")
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Public, messages=messages, company_id=company_id,
    #                                  ai_token=ai_token,
    #                                  ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                  user_id=user_id))
    #     print("********************************")

    #     print(f"---------------------------private model----------------------------------------------")
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, query, company_id=company_id, ai_token=ai_token,
    #                                  ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                  user_id=user_id))
    #     print("lora -----------------------------------------------------------")
    #     lora_query = '''
    #  \n你是一个开放领域知识图谱三元组抽取专家，给定一个可能与本活动相关的文本chunk，一个实体类型列表，还有全文摘要.\n-目标一-\n从文本中识别所有这些类型的新实体，并结合全文摘要进行实体消歧和指代统一(**遇到类似\"公司\"等指代词要修改为全称**)  ，尽可能抽取所有实体。\n-步骤-\n1.提取以下信息：\n- 实体名称：抽取实体尽量为名词词性，且描述具体清晰，实体的实体消歧和指代统一后的名称\n- 实体类型：以下类型之一：[Organization/Institution,Person,Position/Role,Policy/Regulation,Event/Activity,Financial Product,Location,Product/Service]\n- 实体描述：实体属性和活动基于原文的全面描述，尤其注意**遇到具体的数值日期信息要记录在内**\n将每个实体格式化为 (\"entity\"<|><实体名称><|><实体类型><|><实体描述>)\n2. 以中文所有实体和关系的单个列表。使用 ## 作为列表分隔符。\n\n-目标二-\n结合全文概括为每一个实体从文本中识别所有和已经抽取的实体相关的关系，形成主谓宾三元组，关系的源实体和目标实体必须是**本chunk已抽取实体**中所出现的。\n\n-步骤-\n1. 识别所有 *所有显著和隐性相关* 的 (源实体, 目标实体) 对，保证实体必须来自全局实体列表中的实体，保证每个实体都能找到对应的关系。\n2. 对于每对相关的实体，提取以下信息：\n- 源实体（source_entity）：源实体的名称，保证实体必须来自全局实体列表\n- 目标实体（target_entity）：目标实体的名称，保证实体必须来自全局实体列表\n- 关系描述（relationship_description）：解释为什么源实体和目标实体彼此相关，注意关系描述要严格和**文本**表述一致，一些模糊的表述结合**全文摘要**补全\n- 关系强度（relationship_strength）：一个数字评分，表示源实体和目标实体之间关系的强度\n将每个关系格式化为 (\"relationship\"<|><源实体><|><目标实体><|><关系描述><|><关系强度>)\n\n完成后，输出 <|COMPLETE|>\n\n实体抽取思维逻辑过程：\n1. 将文本chunk中的数据尽可能全面地抽取实体，所抽取的实体要完全涵盖chunk内所有信息,同时理解chunk内文本的结构信息,比如,一,1,2...之间的层级关系\n2. 结合**全文摘要**背景进一步明确描述，使其描述与文档内容紧密相关。\n3. **严禁将多个词合并作为一个实体，必须将他们分开作为单独实体**\n4. 遇到一些缩写简称或代词，应根据原文以及全局实体列表中在实体描述（entity_description）出现修改为全称\n5. 遇到类似“公司”，“报告”一类泛指名词，应该根据原文以及全局实体列表修改为“XXX公司”，“XXX报告”等在全文内容核心相关的公司，组织，政策，事件相关背景下。\n\n关系抽取思维链过程：\n1. 在文本中识别所有可能的关系对 (源实体, 目标实体)。\n2. 检查识别出的实体对是否都在**本chunk已抽取实体中**。\n3. 关系关键词尽量简练体现关系，比如 患者-疾病 关键词为“患有”，关键词要精炼，尽量为动词，体现因果，主谓，逻辑关系\n\n-输出注意-\n只输出最终结果不做解释，输出必须为中文，注意遇到类似“公司”等含糊词要改为全称，所有关系中的source和target必须和前序实体对应，所有实体都要参与参与关系构建，尽可能多抽取实体和关系\n\n######################\n-实际数据-\n实体类型: Organization/Institution,Person,Position/Role,Policy/Regulation,Event/Activity,Financial Product,Location,Product/Service\n全文摘要：WPS 365技术白皮书详述了该产品的各项特性和技术，包括服务架构、云服务特性及部署架构。白皮书强调WPS 365作为全面的云服务套件，为企业办公文档的存储、编辑、协作和管控提供全面解决方案。文档还介绍了服务架构的具体特性，如云文档系统、搜索技术、WebOffice技术、即时通信技术等，并揭示了其在提供高可用性、高性能和安全性方面的优势。\n文本chunk: 目 录目录1. 序言2. 产品介绍3. 应用架构3.1.服务架构3.2.服务特性介绍3.2.2.云文档系统3.2.3.搜3.2.4.WebOffice 技术3.2.5.即时通信技术3.2.6.安全文档3.2.7.企业管理后台3.2.8.开放生态4. 部署架构4.1.物理架构4.1.1.三数据中心灾备物理架构4.1.2.业务服务容器化部署4.1.3.数据库高可用部署4.2.技术特性介绍4.2.1.可用性4.2.2.高性能4.2.3.安全性5. 总结与展望5.1.总结5.2.展望1 .序言在这个数据驱动的数字时代,企业的成功越来越依赖于信息技术和通信工具的强大能力。有效的协作、实时的通信、安全的数据存储和处理以及灵活的企业管理,这些都已成为现代企业运营的核心。WPS 365 作为一款全面的云服务套件,为企业带来了前所未有的便利和效率,帮助企业在这个日新月异的数字化世界中保持竞争力。本白皮书旨在深入探讨 WPS 365 的各项特性和技术,包括其产品的全面性、服务架构、云服务特性,以及其在企业环境中的部署架构。我们将详细分析 WPS 365 的主要技术组件,包括云文档系统、搜索服务、WebOffice 技术、即时通信技术等,并探讨其在提供高可用、高性能和安全服务方面的优势。我们希望这份白皮书可以帮助您更好地理解 WPS 365 的强大能力,以及它如何帮助您的企业在数字化转型的道路上取得成功。无论您是企业决策者、 IT 专业人员还是对云服务有兴趣的读者,我们都希望您在阅读这份白皮书的过程中能够获得有价值的见解和信息。现在,让我们开始这一探索之旅,一起见证 WPS 365 如何将企业带入全新的数字化未来。2 .产品介绍WPS 365 是金山办公旗下的通用办公套件产品,是全员提效的数字办公全家桶,包含强大的办公软件和企业协作管理能力,完整覆盖企业办公文档的存储、编辑、协作、管控的全过程,可与企业系统集成使用,也可以独立使用。WPS 365 作为面向全员协作的企业文档中心,管控与协作一体,可助力企业完成:● 按组织架构统一存储和管理文件:拥有个人- 团队-企业三层文件归属和组织架构功能。● 全员文件共享和协同编辑能力:安全高效的分享能力,为日常工作统筹提效。● 文档流转权限管控,动态回收能力:存储、传输技术安全,文档流转权限管控,文档水印。● 与业务系统集成协作的能力:WPS 365 通过云端部署,成为企业文档中台,并通过开放平台赋能企业信息系统接入进来,让数据内容在系统和文档中流转与同步。WPS 365 具备丰富的自有生态,可为企业打造全链路的云办公平台:\n
    #  '''
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, lora_query,
    #                                  selector=LLModelRpc.ModelSelector(model="extractor2"),
    #                                  temperature=0.0, top_p=1.0, max_token=4000,
    #                                  sft_base_model=LLModelRpc.SftBaseModel(base_model=SftBaseModelType.qwen2_5_14b)))

    #     print(f"---------------------------private model prompt ----------------------------------------------")
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft,
    #                                  prompt=f"<|im_start|>user\n{query}<|im_end|>\n<|im_start|>assistant\n今天下暴雨，",
    #                                  company_id=company_id,
    #                                  ai_token=ai_token,
    #                                  ai_product_name=ai_product_name,
    #                                  ai_intention_code=ai_intention_code,
    #                                  user_id=user_id))

    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, query, company_id=company_id, ai_token=ai_token,
    #                                  ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                  user_id=user_id))

    #     # prompt用法
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft,
    #                                  prompt=f"<|im_start|>user\n{query}<|im_end|>\n<|im_start|>assistant\n今天下暴雨，",
    #                                  company_id=company_id,
    #                                  ai_token=ai_token,
    #                                  ai_product_name=ai_product_name,
    #                                  ai_intention_code=ai_intention_code,
    #                                  user_id=user_id))

    #     # 约束生成  选择 / 正则 / json
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, "苹果和香蕉哪个好吃", guided_choice=['苹果', '香蕉']))
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, "小明有5个苹果，吃了1个，丢了2个，还有几个？",
    #                                  guided_regex='有\d个香蕉'))
    #     json_schema = {'type': 'object',
    #                    'properties': {'name': {'type': 'string'}, 'age': {'type': 'integer'},
    #                                   'skills': {'type': 'array', 'items': {'type': 'string', 'maxLength': 10},
    #                                              'minItems': 3}, },
    #                    'required': ['name', 'age', 'skills']}
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, "生成一个员工信息", guided_json=json_schema))

    #     # beam search
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, query, company_id=company_id, ai_token=ai_token,
    #                                  ai_product_name=ai_product_name,
    #                                  ai_intention_code=ai_intention_code, user_id=user_id,
    #                                  n=2))
    #     # beam search async
    #     print(await
    #           LLModelRpc().async_chat_text(LLModelRpc.Gateway.Sft, query, company_id=company_id, ai_token=ai_token,
    #                                        ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                        user_id=user_id, n=2))

    #     print("*************stream**************")
    #     res = LLModelRpc().chat_text_stream(LLModelRpc.Gateway.Sft, query, company_id=company_id, ai_token=ai_token,
    #                                         ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                         user_id=user_id)
    #     for t in res:
    #         print(t)

    #     res = LLModelRpc().chat_text_stream(LLModelRpc.Gateway.Sft,
    #                                         prompt=f"<|im_start|>user\n{query}<|im_end|>\n<|im_start|>assistant\n今天下暴雨，",
    #                                         company_id=company_id, ai_token=ai_token, ai_product_name=ai_product_name,
    #                                         ai_intention_code=ai_intention_code, user_id=user_id)
    #     for t in res:
    #         print(t)

    #     for test in test_list:
    #         provider = test[0]
    #         model = test[1]
    #         print(f"-----------------------{provider}, {model}---------------------")
    #         selector = LLModelRpc.ModelSelector(provider=provider, model=model)
    #         messsages = [Message(role="user", content=f"{query}")]
    #         res = LLModelRpc().chat(False, LLModelRpc.Gateway.Public, messsages, selector=selector,
    #                                 company_id=company_id,
    #                                 ai_token=ai_token, ai_product_name=ai_product_name,
    #                                 ai_intention_code=ai_intention_code,
    #                                 user_id=user_id)
    #         print(res.choices[0].text)
    #     print("************messages************")
    #     print(
    #         LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, messages=messages, company_id=company_id, ai_token=ai_token,
    #                                ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                user_id=user_id))
    #     print("********************************")

    #     print(f"--------------------------async-----------------------")
    #     print(await
    #           LLModelRpc().async_chat_text(LLModelRpc.Gateway.Public, query, company_id=company_id, ai_token=ai_token,
    #                                        ai_product_name=ai_product_name, ai_intention_code=ai_intention_code,
    #                                        user_id=user_id))

    #     async def async_chat_stream():
    #         res = await LLModelRpc().async_chat_text_stream(LLModelRpc.Gateway.Public, query, company_id=company_id,
    #                                                         ai_token=ai_token,
    #                                                         ai_product_name=ai_product_name,
    #                                                         ai_intention_code=ai_intention_code, user_id=user_id)
    #         async for t in res:
    #             print(t.text)

    #     await async_chat_stream()

    #     print("Publid default TPM", LLModelRpc().get_tpm(LLModelRpc.Gateway.Public))
    #     for pm in test_list:
    #         print(f"Publid {pm[0]}_{pm[1]} TPM", LLModelRpc().get_tpm(LLModelRpc.Gateway.Public, f"{pm[0]}_{pm[1]}"))

    #     print("多模态 public ####################################################")
    #     from ..llm_gateway.models.chat_data import MultiModalContent, MultiModalType
    #     import requests
    #     image_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/%E6%A0%91%E5%8F%B6.png?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=2090195433&Signature=JvRgCf5H76Jk7oxSW966t5Ack3E%3D"
    #     res = requests.get(image_url)
    #     import base64
    #     image_base64 = base64.b64encode(res.content).decode('utf-8')

    #     messages = [
    #         Message(
    #             content=[
    #                 MultiModalContent(type=MultiModalType.image_url, content=image_url),
    #                 MultiModalContent(type=MultiModalType.image, content=f"data:image/png;base64,{image_base64}"),
    #                 MultiModalContent(type=MultiModalType.text, content="这些图片说的是什么")
    #             ],
    #             role="user"
    #         )
    #     ]
    #     print(LLModelRpc().multimodal(LLModelRpc.Gateway.Public, messages))
    #     for i in LLModelRpc().multimodal(LLModelRpc.Gateway.Public, messages, True):
    #         print(i.text)

    #     print(await LLModelRpc().async_multimodal(LLModelRpc.Gateway.Public, messages))
    #     async for i in await LLModelRpc().async_multimodal(LLModelRpc.Gateway.Public, messages, True):
    #         print(i.text)

    #     print("多模态 sft ####################################################")
    #     from ..llm_gateway.models.chat_data import MultiModalType, \
    #         SftMultiModalText, SftMultiModalImage, SftMultiModalImageUrl
    #     import requests
    #     image_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/%E6%A0%91%E5%8F%B6.png?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=2090195433&Signature=JvRgCf5H76Jk7oxSW966t5Ack3E%3D"
    #     messages = [
    #         Message(
    #             content=[
    #                 SftMultiModalImage(type=MultiModalType.image_url, image_url=SftMultiModalImageUrl(url=image_url)),
    #                 SftMultiModalText(type=MultiModalType.text, text="这些图片说的是什么")
    #             ],
    #             role="user"
    #         )
    #     ]
    #     print(LLModelRpc().multimodal(LLModelRpc.Gateway.Sft, messages))
    #     for i in LLModelRpc().multimodal(LLModelRpc.Gateway.Sft, messages, True):
    #         print(i.text)

    #     print(await LLModelRpc().async_multimodal(LLModelRpc.Gateway.Sft, messages))
    #     async for i in await LLModelRpc().async_multimodal(LLModelRpc.Gateway.Sft, messages, True):
    #         print(i.text)

    #     print("模型切换 sft ####################################################")
    #     query = "写一首五言律诗"
    #     print(LLModelRpc().chat_text(LLModelRpc.Gateway.Sft, query, sft_base_model=LLModelRpc.SftBaseModel(base_model=SftBaseModelType.qwen2_5_14b)))
    #     print(await LLModelRpc().async_chat_text(LLModelRpc.Gateway.Sft, query, sft_base_model=LLModelRpc.SftBaseModel(base_model=SftBaseModelType.qwen2_5_14b)))

    #     messages = [
    #         Message(
    #             content=[
    #                 SftMultiModalImage(type=MultiModalType.image_url, image_url=SftMultiModalImageUrl(url=image_url)),
    #                 SftMultiModalText(type=MultiModalType.text, text="这些图片说的是什么")
    #             ],
    #             role="user"
    #         )
    #     ]
    #     print(LLModelRpc().multimodal(LLModelRpc.Gateway.Sft, messages, sft_base_model=LLModelRpc.SftBaseModel(base_model=SftBaseModelType.qwen2_vl_7b)))
    #     print(await LLModelRpc().async_multimodal(LLModelRpc.Gateway.Sft, messages, sft_base_model=LLModelRpc.SftBaseModel(base_model=SftBaseModelType.qwen2_vl_7b)))

    #     await LLModelRpc().close()
    #     if record_token:
    #         await Redis5Dao().close()


#     asyncio.run(_main())
