# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/4/25 18:40

from commons.tools.utils import Singleton
import time
import json
from commons.auth.auth_rpc import sig_wps2
from pydantic import BaseModel
from enum import Enum
from typing import Optional, Dict
import logging
from commons.logger.logger import request_id_context
import requests
from fastapi import status


class ResultCode(str, Enum):
    OK = "ok"
    FAIL = "error"


class RespModel(BaseModel):
    result: str = ResultCode.OK
    msg: str = ""
    data: Optional[Dict] = None


class AuthCheckData(BaseModel):
    # 是否可用
    ai_available: bool
    # 原因编码，标识AI是否有权益的原因序列
    reason_code: int
    # 是否需要模型降级
    can_replace: bool
    # 用户AI权益token
    token: str
    # 用户AI可用点数
    ai_point: int
    # 用户AI点数总额
    ai_point_total: int
    # AI功能封禁状态码
    limit_code: int
    # 封禁相关文案
    limit_msg: str
    # 封禁限制截止时间，秒级时间戳（永久封禁：0）
    limit_expire: int


class UseAIPrivilege(BaseModel):
    # 流水号ID
    flow_id: str
    # AI功能是否可用
    ai_available: bool
    # 原因编码，AI是否可用的具体编码
    reason_code: int
    # 封禁编码，AI是否封禁的具体编码
    limit_code: int
    # 扣除后剩余量 (开关类型权益返回-1)
    remain_value: int
    # 企业ID，0则代表个人，大于0则是企业
    company_id: int = 0


class AIPrivilegeRpc(object, metaclass=Singleton):
    def __init__(self):
        self._ak = ""
        self._sk = ""
        self._host = ""
        self._product_name = ""
        self._intention_code = ""

    def init(self, ak: str, sk: str, host: str, product_name: str, intention_code: str):
        self._ak = ak
        self._sk = sk
        self._host = host
        self._product_name = product_name
        self._intention_code = intention_code

    def _sig(self, uri: str, body: dict = None) -> dict:
        if body:
            body = json.dumps(body).encode("utf-8")
        date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
        return sig_wps2(uri, body, self._ak, self._sk, date)

    def _call(
            self, method: str, uri: str, data: dict = None, cookies: dict = None, headers: dict = None,
            timeout_second: int = 180
    ) -> RespModel:
        sig_headers = self._sig(uri, data)
        if headers:
            sig_headers.update(headers)
        sig_headers["X-Request-ID"] = request_id_context.get()
        url = f"{self._host}{uri}"
        r = requests.request(method, url, json=data, headers=sig_headers, cookies=cookies, timeout=timeout_second)
        if r.status_code != status.HTTP_200_OK:
            err_text = f"ai_privilege rpc request fail, uri: {uri}, status_code: {r.status_code}, res_text: {r.text}"
            logging.error(err_text)
            raise Exception(err_text)
        resp: RespModel = RespModel.parse_raw(r.text)
        if resp.result != ResultCode.OK:
            err_text = f"ai_privilege rpc request fail, uri: {uri}, status_code: {r.status_code}, res_text: {r.text}"
            logging.error(err_text)
            raise Exception(err_text)
        return resp

    def auth_check(self, user_id: int) -> Optional[AuthCheckData]:
        body = {
            "user_id": user_id,
            "product_name": self._product_name,
            "intention_code": self._intention_code
        }
        uri = "/privilege_auth/v1/auth_check"
        resp = self._call("POST", uri, data=body)
        return AuthCheckData.parse_obj(resp.data)

    def use_ai_privilege(self, user_id: int, count: int = None) -> Optional[UseAIPrivilege]:
        body = {
            "user_id": user_id,
            "product_name": self._product_name,
            "intention_code": self._intention_code
        }
        if count is not None:
            body["count"] = count
        uri = "/privilege_auth/v1/use_ai_privilege"
        resp = self._call("POST", uri, data=body)
        return UseAIPrivilege.parse_obj(resp.data)
