# coding: utf-8
import logging
import json
import contextvars
from pydantic import BaseModel
import sys
import traceback
import re
from datetime import datetime

ip_context = contextvars.ContextVar("ip-context", default="no ip")
type_context = contextvars.ContextVar("type-context", default="no type")
request_id_context = contextvars.ContextVar("Request-Id", default="")
local_id_context = contextvars.ContextVar("Local-Id", default="no Local-Id")
index_context = contextvars.ContextVar("index", default=0)
uri_context = contextvars.ContextVar("uri", default="")
trace_id_context = contextvars.ContextVar("trace", default="")
span_id_context = contextvars.ContextVar("span", default="")

error_monitor_name = ""


class PassableLogContext(BaseModel):
    ip: str
    req_type: str
    request_id: str
    local_id: str
    index: int
    uri: str
    trace_id: str
    span_id: str

    @classmethod
    def dump(cls, ip: str = "", req_type: str = "", request_id: str = "", local_id: str = "", index: int | None = None,
             uri: str = ""):
        return {
            "ip": ip or ip_context.get(),
            "req_type": req_type or type_context.get(),
            "request_id": request_id or request_id_context.get(),
            "local_id": local_id or local_id_context.get(),
            "index": index or index_context.get(),
            "uri": uri or uri_context.get(),
            "trace_id": trace_id_context.get(),
            "span_id": span_id_context.get(),
        }

    def export(self):
        ip_context.set(self.ip)
        type_context.set(self.req_type)
        request_id_context.set(self.request_id)
        local_id_context.set(self.local_id)
        index_context.set(self.index)
        uri_context.set(self.uri)
        trace_id_context.set(self.trace_id)
        span_id_context.set(self.span_id)

    def __enter__(self):
        self.swap()

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.swap()

    def swap(self):
        ip = ip_context.get()
        ip_context.set(self.ip)
        self.ip = ip
        type_ = type_context.get()
        type_context.set(self.req_type)
        self.req_type = type_
        request_id = request_id_context.get()
        request_id_context.set(self.request_id)
        self.request_id = request_id
        local_id = local_id_context.get()
        local_id_context.set(self.local_id)
        self.local_id = local_id
        index = index_context.get()
        index_context.set(self.index)
        self.index = index
        uri = uri_context.get()
        uri_context.set(self.uri)
        self.uri = uri
        trace_id = trace_id_context.get()
        trace_id_context.set(self.trace_id)
        self.trace_id = trace_id
        span_id = span_id_context.get()
        span_id_context.set(self.span_id)
        self.span_id = span_id


class LoggerFilter(logging.Filter):
    def filter(self, record):
        record.ip_context = ip_context.get()
        record.type_context = type_context.get()
        record.request_id = request_id_context.get()
        record.local_id = local_id_context.get()
        record.uri = uri_context.get()
        i = index_context.get()
        record.index = i
        index_context.set(i + 1)
        return True


class ELKLoggerItem(BaseModel):
    message: str
    level: str
    # extra
    request_id: str = ""
    local_id: str = ""
    uri: str = ""
    file_path: str = ""
    log_time: str = ""
    ip: str = ""
    error_trace: str = ""


colors_code = {
    logging.DEBUG: " \033[92m ",
    logging.INFO: " \033[94m ",
    logging.WARNING: " \033[93m ",
    logging.ERROR: " \033[91m ",
    logging.CRITICAL: " \033[1;95m ",
    "reset": " \033[0m "
}

def common_format(record: logging.LogRecord, elk_format: bool = True):
    log_fmt = f"[%(asctime)s] [%(levelname)s] [%(request_id)s] [%(index)s] %(message)s"
    base_formatter = logging.Formatter(log_fmt)
    base_log = base_formatter.format(record)

    if not elk_format:
        return base_log

    dt = datetime.fromtimestamp(record.created)
    log_time = dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    request_id = _get_record_attr(record, "request_id", "")
    local_id = _get_record_attr(record, "local_id", "")
    ip = _get_record_attr(record, "ip_context", "")
    uri = _get_record_attr(record, "uri", "")

    error_trace = ""
    if record.levelno == logging.ERROR:
        # 增加错误监控标识
        base_log = f"[{error_monitor_name}] " + base_log
        # 增加错误堆栈
        if not hasattr(record, "trace_log") or getattr(record, "trace_log", False):
            ex_type, ex_value, ex_traceback = sys.exc_info()
            trace_back = traceback.extract_tb(ex_traceback)
            for trace in trace_back:
                error_trace += f"--------> File : {trace[0]} , Line : {trace[1]}, Func.Name : {trace[2]}, Message : {trace[3]}. "

    additional_fields = {}
    business_fields = _get_record_attr(record, "business_fields", None)

    if business_fields and isinstance(business_fields, dict):
        additional_fields = {
            key: value for key, value in business_fields.items()
            if key not in {"request_id", "local_id", "uri", "file_path", "log_time", "ip", "error_trace"}
        }

    merged_fields = {
        "level": record.levelname,
        "message": base_log,
        "request_id": request_id,
        "local_id": local_id,
        "uri": uri,
        "file_path": f"File：{record.pathname}, line：{record.lineno}, func：{record.funcName}",
        "log_time": log_time,
        "ip": ip,
        "error_trace": error_trace,
        **additional_fields,  # Include dynamic fields
    }

    return json.dumps(merged_fields, ensure_ascii=False)


def _get_record_attr(record, key: str, default_value=None):
    if hasattr(record, key):
        return getattr(record, key)
    return default_value


class BaseFormatter(logging.Formatter):
    def format(self, record):
        if hasattr(record, "elk_format") and not getattr(record, "elk_format", False):
            base_log = common_format(record, False)
            return colors_code[record.levelno] + base_log + colors_code["reset"]
        else:
            return common_format(record, True)


class ColorFormatter(logging.Formatter):
    """
    注意，颜色打印无法被json解析，在sre部署时，ELK进行json解析会异常，故在ELK日志中尽量不打印颜色
    """
    def format(self, record):
        if hasattr(record, "elk_format") and not getattr(record, "elk_format", False):
            base_log = common_format(record, False)
        else:
            base_log = common_format(record, True)

        return colors_code[record.levelno] + base_log + colors_code["reset"]


def init_logger(logger_name, log_level, monitor_name, with_color=False):
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)
    # error日志添加特定标识
    # if monitor_name:
    #     global error_monitor_name
    #     error_monitor_name = monitor_name
    #
    # console_handler = logging.StreamHandler()
    # if with_color:
    #     console_handler.setFormatter(ColorFormatter())
    # else:
    #     console_handler.setFormatter(BaseFormatter())
    # console_handler.setLevel(log_level)
    # console_handler.addFilter(LoggerFilter())
    # # 移除已有的处理器
    # if logger.hasHandlers():
    #     logger.handlers.clear()
    # logger.addHandler(console_handler)
