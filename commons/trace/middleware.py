from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from opentelemetry import trace
from opentelemetry.trace import SpanK<PERSON>, StatusCode, format_trace_id, format_span_id
from opentelemetry.propagate import extract
from commons.logger.logger import (
    trace_id_context,
    span_id_context,
    request_id_context,
    uri_context,
    ip_context,
)
import uuid

class TraceMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, tracer=None):
        super().__init__(app)
        self._tracer = tracer or trace.get_tracer(__name__)

    async def dispatch(self, request: Request, call_next):
        # 提取 B3 上下文
        ctx = extract(request.headers)

        # 获取 path 和 IP
        path = request.url.path
        source_ip = request.client.host
        request_id = request.headers.get("Client-Request-Id") or request.headers.get("X-Request-Id") or str(uuid.uuid4())

        # 开启 span
        with self._tracer.start_as_current_span(
            name=path,
            context=ctx,
            kind=SpanKind.SERVER
        ) as span:
            span_ctx = span.get_span_context()

            # 设置你自定义的 contextvars
            trace_id_context.set(format_trace_id(span_ctx.trace_id))
            span_id_context.set(format_span_id(span_ctx.span_id))
            request_id_context.set(request_id)
            uri_context.set(path)
            ip_context.set(source_ip)

            try:
                response: Response = await call_next(request)
                return response
            except Exception as exc:
                span.set_status(StatusCode.ERROR)
                span.record_exception(exc)
                raise
