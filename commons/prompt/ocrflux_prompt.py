import json
import logging
from typing import Optional

from pydantic import BaseModel

from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.models.chat_data import SftBaseModelType, Message, SftMultiModalImage, SftMultiModalText, \
    SftMultiModalImageUrl, LLMConfig, PublicGatewayHeader,MultiModalType, MultiModalContent
from commons.adapter.gateway_base import AsyncChatTextArgs
from commons.adapter.util import create_gateway_adapter

prompt = "下面是一个文档的一页图像。只需返回该文档的纯文本表示形式，就像你正在自然阅读它一样。 所有表格都应该以 HTML 格式呈现。 将所有标题和标题显示为 H1 标题。 不要产生幻觉。"
prompt_pic = "下面是一张图片。只需返回该图片里的文字信息，以纯文本表示形式，就像你正在自然阅读它一样。 所有表格都应该以 HTML 格式呈现。 将所有标题和标题显示为 H1 标题。 不要产生幻觉。"


class OcrFlux:
    llm_config: LLMConfig = None

    def __init__(self, public_gateway_header: PublicGatewayHeader):
        self.public_gateway_header = public_gateway_header

    def _build_messages(self, text, image_path):
        if self.llm_config.gateway == LLModelRpc.Gateway.Sft:
            return [
                    Message(role="user", content=[
                        SftMultiModalImage(type=MultiModalType.image_url,
                                           image_url=SftMultiModalImageUrl(url=image_path)),
                        SftMultiModalText(type=MultiModalType.text,
                                          text=text)
                        # {"type": "image_url", "image_url": {"url": image_url}},
                        # {"type": "text", "text": prompt}
                ])
            ]
        else:
            return [
                    Message(role="user", content=[
                        MultiModalContent(type=MultiModalType.image_url,
                                          content=image_path),
                        MultiModalContent(type=MultiModalType.text,
                                          content=text)
                ])
            ]

    async def preprocess_ocrflux(self, image_url: str,prompt_str: str,time_out: int=60) -> (str,bool):
        if self.llm_config is None:
            raise Exception("配置未加载")

        messages = self._build_messages(prompt_str, image_url)
        try:
            adapter = create_gateway_adapter(llm_config=self.llm_config, public_gateway_header=self.public_gateway_header)
            output_data = await adapter.async_chat_text(AsyncChatTextArgs(messages=messages,temperature=0.3,max_token=4096,repetition_penalty=1.2,time_out=time_out))
            logging.debug(f"llm output:{output_data}")
            if not output_data or len(output_data) < 2:
                logging.debug("llm output is empty")
                return "",True
            output = json.loads(output_data[1])
            return output.get("natural_text", ""),True
        except Exception as e:
            # 可能会存在一些解析json的问题，直接返回文本。
            logging.error("Error processing LLM response, returning raw text.", e)
            return "",False
