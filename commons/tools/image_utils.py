# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/26 10:29

from PIL import Image
from io import BytesIO
from typing import Union
import logging


def image_compress(
        file_path_or_bytes: Union[bytes, str],
        res_size: int = 200,
        start_quality: int = 50,
        end_quality: int = 10,
        quality_step: int = 10
) -> bytes:
    """
    图片压缩
    :param file_path_or_bytes: 文件路径或者二进制数据
    :param res_size: 压缩后的目标大小
    :param start_quality: 开始压缩质量
    :param end_quality: 结束压缩质量（低于一定质量后图片大小不会有变化，防止死循环，需固定结束质量）
    :param quality_step: 每次减少的质量
    :return: 压缩后的二进制数据
    """
    if isinstance(file_path_or_bytes, str):
        with open(file_path_or_bytes, 'rb') as f:
            img_bytes = f.read()
    else:
        img_bytes = file_path_or_bytes
    bio = BytesIO(img_bytes)
    logging.debug(f"base size: {len(bio.getvalue()) / 1024}KB")
    image = Image.open(bio)
    try:
        if image.mode == 'RGBA':
            # 更安全的方式处理透明通道
            bg = Image.new('RGB', image.size, (255, 255, 255))
            try:
                alpha = image.split()[-1]  # 可能在这里出错
                bg.paste(image, mask=alpha)
            except:
                # 如果获取alpha通道失败，直接粘贴（忽略透明度）
                bg.paste(image)
            image = bg
    except Exception as e:
        print(f"将RGBA转换为RGB失败: {e}")
        raise
    # 转换为 RGB 模式
    image = image.convert('RGB')
    quality = start_quality
    while True:
        img_byte_arr = BytesIO()
        image.save(img_byte_arr, format='JPEG', quality=quality)
        size_kb = len(img_byte_arr.getvalue()) / 1024

        if size_kb <= res_size or quality <= end_quality:
            break

        quality = quality - quality_step
    img_byte_arr.seek(0)
    logging.debug(f"compressed size: {size_kb}KB")
    res_img_bytes = img_byte_arr.getvalue()
    return res_img_bytes

