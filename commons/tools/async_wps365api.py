import aiohttp
import urllib
import json
import logging
from enum import Enum
from pydantic import BaseModel
from typing import Type, Optional, Union, Dict, Tuple
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
import time
import warnings

from commons.tools.utils import Singleton, BaseModelT
from commons.tools.openapi import OpenApi
from commons.tools.utils import norm_http_params
from commons.logger.logger import request_id_context



class TokenAuthException(BaseException):
    pass


class HTTPCODE(int, Enum):
    OK = 0
    FAILED = 1


class ResponseModel(BaseModel):
    code: int = HTTPCODE.FAILED
    msg: str = ""
    more: Optional[object] = None
    data: Optional[Dict] = None


class RespTokenAuthError(BaseModel):
    code: int
    message: str = ""
    data: Optional[str] = None


class AsyncWps365Api(object, metaclass=Singleton):
    class TokenItem(BaseModel):
        token: str
        expires: float

    def __init__(self, pool_max=-1):
        warnings.warn(
            "AsyncWps365Api is deprecated, please use commons.tools.wps365api.Wps365api instead",
            DeprecationWarning,
        )
        self._host = "https://api.wps.cn"
        self._set_origin()
        self._pool_max = -1
        self._openapi: OpenApi = None
        self._tokens: Dict[str, AsyncWps365Api.TokenItem] = {}
        self._woa_custom_ak: str = None

    def init(self,
             host: Optional[str] = None,
             pool_max: int = -1,
             openapi_host: str = None,
             openapi_ak: str = None,
             openapi_sk: str = None,
             woa_custom_ak: str = None):
        if host:
            self._host = host
        if openapi_host is not None:
            self._openapi = OpenApi(openapi_host, openapi_ak, openapi_sk, pool_max)
        self._woa_custom_ak = woa_custom_ak
        self._set_origin()
        self._pool_max = pool_max

    def update_host(self, host: str):
        self._host = host
        self._set_origin()

    def _set_origin(self):
        http_obj = urllib.parse.urlparse(self._host)
        self._origin = f"{http_obj.scheme}://{http_obj.netloc}"

    async def get_token(self, company_id: str) -> str:
        assert self._openapi is not None, \
            "The get_token function in the wps365api requires the initialization of the openapi"
        now = time.time()
        if company_id not in self._tokens or self._tokens[company_id].expires <= now:
            resp = await self._openapi.async_get_token(company_id)
            if resp:
                token_item = AsyncWps365Api.TokenItem(token=f"{resp.token_type} {resp.access_token}",
                                                      expires=now + resp.expires_in * 0.7)
                self._tokens[company_id] = token_item
            else:
                return ""
        return self._tokens[company_id].token

    def pop_token(self, company_id: str):
        if company_id in self._tokens:
            self._tokens.pop(company_id)

    @retry(stop=stop_after_attempt(3),
           wait=wait_fixed(1),
           reraise=True,
           retry=retry_if_exception_type(TokenAuthException))
    async def call(
            self,
            method: str,
            uri: str,
            body: Optional[dict] = None,
            params: Optional[dict] = None,
            cookies: Optional[dict] = None,
            headers: Optional[dict] = None,
            company_id: Optional[str] = None,
            timeout_second: int = 180,
    ) -> Tuple[int, bytes, dict]:
        url = f"{self._host}{uri}"

        new_headers = {
        }
        if self._woa_custom_ak:
            new_headers["open-app-id"] = self._woa_custom_ak
        if company_id is not None:
            new_headers["Authorization"] = await self.get_token(company_id)

        if len(self._origin) > 0:
            new_headers.update({"Origin": self._origin})

        if params:
            params = norm_http_params(params, True)
        if body:
            body = norm_http_params(body, False)
        if headers:
            new_headers.update(headers)

        req_id = request_id_context.get()
        if req_id and "X-Request-Id" not in new_headers:
            new_headers["X-Request-Id"] = req_id

        conn = None
        if self._pool_max > 0:
            conn = aiohttp.TCPConnector(limit=self._pool_max)
        timeout = aiohttp.ClientTimeout(total=timeout_second)
        async with aiohttp.ClientSession(connector=conn) as sess:
            async with sess.request(method, url, json=body, params=params, headers=new_headers,
                                    cookies=cookies, timeout=timeout) as resp:
                code = resp.status
                content = await resp.read()
                if code != 200:
                    text = content.decode('utf-8')
                    logging.error(f"wps 365 rpc request fail, uri: {uri}, status_code: {code}, res_text: {text}")
                    if code == 403:
                        resp_fail = RespTokenAuthError.parse_raw(content)
                        if resp_fail.code == 400006:
                            self.pop_token(company_id)
                            raise TokenAuthException(
                                f"wps365api token auth fail, uri: {uri}, status_code: {code}, res_text: {text}")
                return resp.status, content, resp.headers

    def _serve_http_result(
            self, uri: str, content: bytes, resp_model: Type[BaseModelT] = None) -> Union[BaseModelT, Dict, None]:
        if resp_model:
            resp = ResponseModel.parse_raw(content)
            if resp.code != HTTPCODE.OK:
                logging.error(f"call {uri} failed!, code={resp.code}, msg={resp.msg}")
                return None
            return resp_model.parse_obj(resp.data)
        else:
            jobj = json.loads(content)
            if jobj["code"] != HTTPCODE.OK:
                logging.error(f"call {uri} failed!, code={jobj['code']}, msg={jobj.get('msg')}")
                return None
            return jobj.get("data", {})

    async def get(self,
                  uri: str,
                  wps_sid: str,
                  params: dict = None,
                  resp_model: Type[BaseModelT] = None,
                  headers: Optional[dict] = None,
                  company_id: Optional[str] = None,
                  error: Optional[Dict] = None) -> Union[BaseModelT, Dict, None]:
        try:
            if headers is None:
                headers = {}
            if company_id is not None:
                headers["Authorization"] = await self.get_token(company_id)

            code, content, _ = await self.call(
                "GET", uri, params=params, cookies={"wps_sid": wps_sid}, headers=headers)
            if code != 200:
                if error:
                    try:
                        error["code"] = json.loads(content)["code"]
                    except Exception:
                        pass
                return None
            return self._serve_http_result(uri, content, resp_model)
        except Exception as e:
            logging.error(e)
            return None

    async def post(self,
                   uri: str,
                   wps_sid: str,
                   body: dict = None,
                   resp_model: Type[BaseModelT] = None,
                   headers: Optional[dict] = None,
                   company_id: Optional[str] = None,
                   error: Optional[Dict] = None) -> Union[BaseModelT, Dict, None]:
        try:
            if headers is None:
                headers = {}
            if company_id is not None:
                headers["Authorization"] = await self.get_token(company_id)

            code, content, _ = await self.call(
                "POST", uri, body=body, cookies={"wps_sid": wps_sid},
                headers=headers)
            if code != 200:
                if error:
                    try:
                        error["code"] = json.loads(content)["code"]
                    except Exception:
                        pass
                return None

            return self._serve_http_result(uri, content, resp_model)
        except Exception as e:
            logging.error(e)
            return None


# if __name__ == '__main__':
#     import os
#     import asyncio

#     AsyncWps365Api().init("https://api.wps.cn", 10, "https://openapi.wps.cn", os.environ["openapi_ak"],
#                           os.environ["openapi_sk"], os.environ.get("woa_custom_ak", ""))

#     # beta
#     uid = "*********"
#     company_id = "*********"
#     # prod
#     # uid = ""
#     # company_id = "41000207"

#     res = asyncio.run(AsyncWps365Api().get("/v7/service_principals/current", wps_sid=None, company_id=company_id))
#     print(res)

#     body = {
#         "executor": uid,
#         "title": {
#             "prefix": "11",
#             "subject": "22"
#         }
#     }
#     res = asyncio.run(AsyncWps365Api().post("/v7/todo/tasks", wps_sid=None, body=body, company_id=company_id))
#     print(res)
