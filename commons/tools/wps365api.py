# http://kbe.kingsoft.net/apis/all.html


import json
from enum import Enum
import urllib
import requests
from requests.adapters import HTTPAdapter
from pydantic import BaseModel
from typing import Type, Optional, Union, Dict
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
import time
import aiohttp
from requests_toolbelt import MultipartEncoder

from commons.logger.business_log import logger
from commons.tools.utils import Singleton, BaseModelT
from commons.tools.openapi import OpenApi


class TokenAuthException(BaseException):
    pass


class HTTPCODE(int, Enum):
    OK = 0
    FAILED = 1


class ResponseModel(BaseModel):
    code: int = HTTPCODE.FAILED
    msg: str = ""
    more: Optional[object] = None
    data: Optional[Dict] = None


class RespTokenAuthError(BaseModel):
    code: int
    msg: str = ""
    data: Optional[str] = None


class Wps365api(object, metaclass=Singleton):
    class TokenItem(BaseModel):
        token: str
        expires: float

    def __init__(self):
        self._host = "https://api.wps.cn"
        self._set_origin()
        self._sess: requests.Session = None
        self._async_sess: aiohttp.ClientSession = None
        self._openapi: OpenApi = None
        self._tokens: Dict[str, Wps365api.TokenItem] = {}
        self._woa_custom_ak: str = ""

    def init(self, host: str, pool_max: int = -1, openapi_host: str = None, openapi_ak: str = None,
             openapi_sk: str = None, woa_custom_ak: str = None):
        self._host = host
        if openapi_host is not None:
            self._openapi = OpenApi(openapi_host, openapi_ak, openapi_sk, pool_max)
        self._woa_custom_ak = woa_custom_ak
        self._set_origin()
        async_conn = None
        if pool_max > 0:
            self._sess = requests.Session()
            self._sess.mount(self._host, HTTPAdapter(pool_connections=1, pool_maxsize=pool_max))
            async_conn = aiohttp.TCPConnector(limit=pool_max)
        self._async_sess = aiohttp.ClientSession(connector=async_conn)

    async def close(self):
        if self._sess:
            self._sess.close()
        if self._async_sess:
            await self._async_sess.close()

    def set_host(self, host: str):
        self._host = host
        self._set_origin()

    def _set_origin(self):
        http_obj = urllib.parse.urlparse(self._host)
        self._origin = f"{http_obj.scheme}://{http_obj.netloc}"

    def get_token(self, company_id: str) -> str:
        assert self._openapi is not None, \
            "The get_token function in the wps365api requires the initialization of the openapi"
        now = time.time()
        if company_id not in self._tokens or self._tokens[company_id].expires <= now:
            resp = self._openapi.get_token(company_id)
            if resp:
                token_item = Wps365api.TokenItem(token=f"{resp.token_type} {resp.access_token}",
                                                 expires=now + resp.expires_in * 0.7)
                self._tokens[company_id] = token_item
            else:
                self._tokens[company_id] = Wps365api.TokenItem(token="",
                                                               expires=now + 3600 * 0.7)
        return self._tokens[company_id].token

    async def aget_token(self, company_id: str) -> str:
        assert self._openapi is not None, \
            "The get_token function in the wps365api requires the initialization of the openapi"
        now = time.time()
        if company_id not in self._tokens or self._tokens[company_id].expires <= now:
            resp = await self._openapi.async_get_token(company_id)
            if resp:
                token_item = Wps365api.TokenItem(token=f"{resp.token_type} {resp.access_token}",
                                                 expires=now + resp.expires_in * 0.7)
                self._tokens[company_id] = token_item
            else:
                self._tokens[company_id] = Wps365api.TokenItem(token="",
                                                               expires=now + 3600 * 0.7)
        return self._tokens[company_id].token

    def pop_token(self, company_id: str):
        if company_id in self._tokens:
            self._tokens.pop(company_id)

    def _noraml_json_params(self, d: dict):
        new_d = {}
        for key, value in d.items():
            if value is None:
                continue

            if isinstance(value, bool):
                new_d[key] = "true" if value else "false"
            elif isinstance(value, dict):
                new_d[key] = self._noraml_json_params(value)
            elif isinstance(value, list):
                new_value = []
                for item in value:
                    if isinstance(item, dict):
                        new_value.append(self._noraml_json_params(item))
                    else:
                        new_value.append(item)
                new_d[key] = new_value
            else:
                new_d[key] = value
        return new_d

    @retry(stop=stop_after_attempt(3),
           wait=wait_fixed(1),
           reraise=True,
           retry=retry_if_exception_type(TokenAuthException))
    def call(
            self,
            method: str,
            uri: str,
            body: dict = None,
            form_data: MultipartEncoder = None,
            params: dict = None,
            cookies: dict = None,
            headers: dict = None,
            timeout_second: int = 180,
    ) -> (int, bytes, dict):
        url = f"{self._host}{uri}"

        new_headers = {
            "open-app-id": self._woa_custom_ak
            # "User-Agent": "Kso-apiclient-go/1.0.0"
        }
        if len(self._origin) > 0:
            new_headers.update({"Origin": self._origin})

        if params:
            params = self._noraml_json_params(params)
        # if body:
        #     body = self._noraml_json_params(body)
        if form_data:
            new_headers["Content-Type"] = form_data.content_type
            form_data = form_data.to_string()
        if headers:
            new_headers.update(headers)
        if self._sess:
            r = self._sess.request(
                method, url, json=body, data=form_data, params=params, headers=new_headers, cookies=cookies, timeout=timeout_second)
        else:
            r = requests.request(
                method, url, json=body, data=form_data, headers=new_headers, cookies=cookies, timeout=timeout_second)
        return r.status_code, r.content, r.headers

    @retry(stop=stop_after_attempt(3),
           wait=wait_fixed(1),
           reraise=True,
           retry=retry_if_exception_type(TokenAuthException))
    async def acall(
            self,
            method: str,
            uri: str,
            body: dict = None,
            form_data: MultipartEncoder = None,
            params: dict = None,
            cookies: dict = None,
            headers: dict = None,
            timeout_second: int = 180,
    ) -> (int, bytes, dict):
        url = f"{self._host}{uri}"

        new_headers = {
            "open-app-id": self._woa_custom_ak
            # "User-Agent": "Kso-apiclient-go/1.0.0"
        }
        if len(self._origin) > 0:
            new_headers.update({"Origin": self._origin})

        if params:
            params = self._noraml_json_params(params)
        # if body:
        #     body = self._noraml_json_params(body)
        if form_data:
            new_headers["Content-Type"] = form_data.content_type
            form_data = form_data.to_string()
        if headers:
            new_headers.update(headers)
        async with self._async_sess.request(method, url, json=body, data=form_data, params=params, headers=new_headers,
                                            cookies=cookies, timeout=timeout_second) as r:
            code = r.status
            content = await r.content.read()
            res_headers = r.headers
        return code, content, res_headers

    # 处理kdc异步方法和同步方法
    def _serve_http_result(
            self, uri: str, content: bytes, resp_model: Type[BaseModelT] = None, use_async_func = False) -> Union[BaseModelT, Dict, None]:
        if resp_model:
            resp = ResponseModel.parse_raw(content)
            if resp.code != HTTPCODE.OK:
                logger.error(f"call {uri} failed!, code={resp.code}, msg={resp.msg}")
                return None
            return resp_model.parse_obj(resp.data)
        else:
            jobj = json.loads(content)
            if use_async_func:
                if jobj["result"] != "OK":
                    logger.error(f"call {uri} failed!, result={jobj['result']}")
                    return None
                return jobj
            else:
                if jobj["code"] != HTTPCODE.OK:
                    logger.error(f"call {uri} failed!, code={jobj['code']}, msg={jobj.get('msg')}")
                    return None
                return jobj.get("data", {})

    # 处理kdc异步获取结果的方法
    def _async_serve_http_result(
            self, uri: str, content: bytes, resp_model: Type[BaseModelT] = None) -> Union[BaseModelT, Dict, None]:
        if resp_model:
            resp = ResponseModel.parse_raw(content)
            if resp.code != HTTPCODE.OK:
                logger.error(f"call {uri} failed!, code={resp.code}, msg={resp.msg}")
                return None
            return resp_model.parse_obj(resp.data)
        else:
            jobj = json.loads(content)
            return jobj

    def get(
            self,
            uri: str,
            wps_sid: str = None,
            kso_sid: str = None,
            params: dict = None,
            resp_model: Type[BaseModelT] = None,
            company_id: Optional[str] = None,
    ) -> Union[BaseModelT, Dict, None]:
        try:
            headers = {}
            if company_id is not None:
                headers["Authorization"] = self.get_token(company_id)
            cookies = {}
            if wps_sid:
                cookies["wps_sid"] = wps_sid
            if kso_sid:
                cookies["kso_sid"] = kso_sid
            code, content, _ = self.call("GET", uri, params=params, cookies=cookies, headers=headers)
            if code != 200:
                text = content.decode('utf-8')
                logger.error(f"wps 365 rpc request fail, uri: {uri}, status_code: {code}, res_text: {text}")
                if code == 403:
                    resp_fail = RespTokenAuthError.parse_raw(content)
                    if resp_fail.code == 400006:
                        self.pop_token(company_id)
                        raise TokenAuthException(
                            f"wps365api token auth fail, uri: {uri}, status_code: {code}, res_text: {text}")
                return None
            return self._serve_http_result(uri, content, resp_model)
        except Exception as e:
            logger.error(e)
            return None

    async def aget(
            self,
            uri: str,
            wps_sid: str = None,
            kso_sid: str = None,
            params: dict = None,
            resp_model: Type[BaseModelT] = None,
            company_id: Optional[str] = None,
            use_async_func: bool = False,
    ) -> Union[BaseModelT, Dict, None]:
        try:
            headers = {}
            if company_id is not None:
                headers["Authorization"] = await self.aget_token(company_id)
            cookies = {}
            if wps_sid:
                cookies["wps_sid"] = wps_sid
            if kso_sid:
                cookies["kso_sid"] = kso_sid
            code, content, _ = await self.acall("GET", uri, params=params, cookies=cookies, headers=headers)
            if code != 200:
                text = content.decode('utf-8')
                logger.error(f"wps 365 rpc request fail, uri: {uri}, status_code: {code}, res_text: {text}")
                if code == 403:
                    resp_fail = RespTokenAuthError.parse_raw(content)
                    if resp_fail.code == 400006:
                        self.pop_token(company_id)
                        raise TokenAuthException(
                            f"wps365api token auth fail, uri: {uri}, status_code: {code}, res_text: {text}")
                return None
            if use_async_func:
                return self._async_serve_http_result(uri, content, resp_model)
            else:
                return self._serve_http_result(uri, content, resp_model, use_async_func)
        except Exception as e:
            logger.error(e)
            return None

    def post(
            self,
            uri: str,
            wps_sid: str = None,
            kso_sid: str = None,
            body: dict = None,
            form_data: MultipartEncoder = None,
            resp_model: Type[BaseModelT] = None,
            company_id: Optional[str] = None,
    ) -> Union[BaseModelT, Dict, None]:
        try:
            headers = {}
            if company_id is not None:
                headers["Authorization"] = self.get_token(company_id)
            cookies = {}
            if wps_sid:
                cookies["wps_sid"] = wps_sid
            if kso_sid:
                cookies["kso_sid"] = kso_sid
            code, content, _ = self.call("POST", uri, body=body, form_data=form_data, cookies=cookies, headers=headers)
            if code != 200:
                text = content.decode('utf-8')
                if code == 403:
                    resp_fail = RespTokenAuthError.parse_raw(content)
                    if resp_fail.code == *********:
                        raise ValueError(f"文件损坏或没有权限访问！")
                    if resp_fail.code == 400006:
                        self.pop_token(company_id)
                        raise TokenAuthException(
                            f"wps365api token auth fail, uri: {uri}, status_code: {code}, res_text: {text}")
                logger.error(f"wps 365 rpc request fail, uri: {uri}, status_code: {code}, res_text: {text}")
                return None

            return self._serve_http_result(uri, content, resp_model)
        except Exception as e:
            logger.error(e)
            return None

    async def apost(
            self,
            uri: str,
            wps_sid: str = None,
            kso_sid: str = None,
            body: dict = None,
            form_data: MultipartEncoder = None,
            resp_model: Type[BaseModelT] = None,
            company_id: Optional[str] = None,
            use_async_func: bool = False,
    ) -> Union[BaseModelT, Dict, None]:
        try:
            headers = {}
            if company_id is not None:
                headers["Authorization"] = await self.aget_token(company_id)
            cookies = {}
            if wps_sid:
                cookies["wps_sid"] = wps_sid
            if kso_sid:
                cookies["kso_sid"] = kso_sid
            code, content, _ = await self.acall("POST", uri,  body=body, form_data=form_data, cookies=cookies, headers=headers)
            if code != 200:
                text = content.decode('utf-8')
                logger.error(f"wps 365 rpc request fail, uri: {uri}, status_code: {code}, res_text: {text}")
                if code == 403:
                    resp_fail = RespTokenAuthError.parse_raw(content)
                    if resp_fail.code == *********:
                        raise ValueError(f"文件损坏或没有权限访问！")
                    if resp_fail.code == 400006:
                        self.pop_token(company_id)
                        raise TokenAuthException(
                            f"wps365api token auth fail, uri: {uri}, status_code: {code}, res_text: {text}")
                return None

            return self._serve_http_result(uri, content, resp_model, use_async_func)
        except Exception as e:
            logger.error(e)
            return None


# if __name__ == '__main__':
#     import os
#     import asyncio
#     from commons.logger.logger import init_logger
#     init_logger("", "DEBUG", "err", True)

#     Wps365api().init("https://api.wps.cn", 10, "https://openapi.wps.cn", os.environ["openapi_ak"],
#                      os.environ["openapi_sk"], os.environ.get("woa_custom_ak", ""))
#     # beta
#     uid = "*********"
#     company_id = "*********"
#     # prod
#     uid = "1384382171"
#     company_id = "41000207"

#     res = Wps365api().get("/v7/service_principals/current", wps_sid=None, company_id=company_id)
#     print(res)

#     drive_id = "2265797916"
#     file_id = "clGbblj3vTQN"
#     res = Wps365api().get(f"/v7/drives/{drive_id}/files/{file_id}/content", wps_sid=None, company_id=company_id)
#     print(res)

#     body = {
#         "executor": uid,
#         "title": {
#             "prefix": "11",
#             "subject": "22"
#         }
#     }
#     res = Wps365api().post("/v7/todo/tasks", wps_sid=None, body=body, company_id=company_id)
#     print(res)

#     body = {
#         "format": "plain",
#         "url": "https://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/parse_test.docx?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1741069232&Signature=rFGOJzLmkYWLij2P1Jgveeqn%2Bjc%3D",
#         "filename": "parse_test.docx",
#     }
#     res = Wps365api().post("/v7/longtask/exporter/export_file_content", body=body, company_id=company_id)
#     print(res)

#     async def _async():
#         # beta
#         uid = "*********"
#         company_id = "*********"
#         # prod
#         uid = "1384382171"
#         company_id = "41000207"

#         res = await Wps365api().aget("/v7/service_principals/current", wps_sid=None, company_id=company_id)
#         print(res)

#         drive_id = "2265797916"
#         file_id = "clGbblj3vTQN"
#         res = await Wps365api().aget(f"/v7/drives/{drive_id}/files/{file_id}/content", wps_sid=None, company_id=company_id)
#         print(res)

#         body = {
#             "executor": uid,
#             "title": {
#                 "prefix": "11",
#                 "subject": "22"
#             }
#         }
#         res = await Wps365api().apost("/v7/todo/tasks", wps_sid=None, body=body, company_id=company_id)
#         print(res)

#         await Wps365api().close()

#     loop = asyncio.get_event_loop()
#     loop.run_until_complete(_async())
