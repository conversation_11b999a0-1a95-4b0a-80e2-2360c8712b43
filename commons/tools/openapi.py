# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/5/15 14:26
import aiohttp
import json
import logging
from pydantic import BaseModel
from typing import Type, Union, Dict
from tenacity import retry, stop_after_attempt, wait_fixed
import requests
from requests.adapters import HTTPAdapter

from commons.tools.utils import BaseModelT

class TokenData(BaseModel):
    access_token: str
    expires_in: int
    token_type: str


class OpenApi(object):
    def __init__(self, host: str, ak: str, sk: str, pool_max: int = -1):
        self._host = host
        self._ak = ak
        self._sk = sk
        self._pool_max = pool_max
        self._sess = None
        if pool_max > 0:
            self._sess = requests.Session()
            self._sess.mount(self._host, HTTPAdapter(pool_connections=1, pool_maxsize=pool_max))

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    async def async_call(self,
                         method: str,
                         uri: str,
                         body: dict = None,
                         params: dict = None,
                         cookies: dict = None,
                         headers: dict = None,
                         timeout_second: int = 180) -> (int, bytes, dict):
        url = f"{self._host}{uri}"
        conn = None
        if self._pool_max > 0:
            conn = aiohttp.TCPConnector(limit=self._pool_max)
        timeout = aiohttp.ClientTimeout(total=timeout_second)
        async with aiohttp.ClientSession(connector=conn) as sess:
            async with sess.request(method, url, data=body, params=params, cookies=cookies, headers=headers,
                                    timeout=timeout) as resp:
                content = await resp.read()
                return resp.status, content, resp.headers

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
    def call(self,
             method: str,
             uri: str,
             body: dict = None,
             params: dict = None,
             cookies: dict = None,
             headers: dict = None,
             timeout_second: int = 180) -> (int, bytes, dict):
        url = f"{self._host}{uri}"
        if self._sess:
            resp = self._sess.request(
                method, url, data=body, params=params, headers=headers, cookies=cookies, timeout=timeout_second)
        else:
            resp = requests.request(
                method, url, data=body, params=params, headers=headers, cookies=cookies, timeout=timeout_second)
        return resp.status_code, resp.content, resp.headers

    def _serve_http_result(
            self, content: bytes, resp_model: Type[BaseModelT] = None) -> Union[BaseModelT, Dict]:
        if resp_model:
            return resp_model.parse_raw(content)
        else:
            return json.loads(content)

    async def async_get_token(self, company_id: str) -> Union[TokenData, None]:
        uri = "/oauth2/token"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        if company_id == '0':
            company_ids = ['41000207']
        else:
            company_ids = [company_id, '41000207']

        for cid in company_ids:
            body = {
                "grant_type": "client_credentials",
                "client_id": self._ak,
                "client_secret": self._sk,
                "company_id": cid
            }
            try:
                status_code, content, _ = await self.async_call("POST", uri, body, headers=headers)
                if status_code == 200:
                    return self._serve_http_result(content, resp_model=TokenData)
                else:
                    logging.error(f"Failed with company_id={cid}, status={status_code}: {content.decode('utf-8')}")
            except Exception as e:
                logging.error(f"Request error with company_id={cid}: {str(e)}")

        return None


    def get_token(self, company_id: str) -> Union[TokenData, None]:
        uri = "/oauth2/token"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        if company_id == '0':
            company_ids = ['41000207']
        else:
            company_ids = [company_id, '41000207']

        for cid in company_ids:
            body = {
                "grant_type": "client_credentials",
                "client_id": self._ak,
                "client_secret": self._sk,
                "company_id": cid
            }
            try:
                status_code, content, _ = self.call("POST", uri, body, headers=headers)
                if status_code == 200:
                    return self._serve_http_result(content, resp_model=TokenData)
                else:
                    logging.error(f"Failed with company_id={cid}, status={status_code}: {content.decode('utf-8')}")
            except Exception as e:
                logging.error(f"Request error with company_id={cid}: {str(e)}")

        return None

