import os
import base64
import hashlib
from Crypto.Cipher import AES

from commons.tools.utils import Singleton

rootHead = "kingsoft-header"
keyHead = "kingsoft-key"
NONCE_BYTE_SIZE = 12


class KmsLocalClient(metaclass=Singleton):
    def __init__(self):
        self.rootKey = None
        self.keyId = None

    def init(self, keyId, configPath, secretPath):
        mid = ""
        tail = ""
        with open(configPath, "r", encoding="utf-8") as f:
            mid = f.read()
        with open(secretPath, "r", encoding="utf-8") as f:
            tail = f.read()
        self.rootKey = self.makeKey(rootHead, mid.strip(' \r\n\t'), tail.strip(' \r\n\t'))
        self.keyId = keyId

    def makeKey(self, head, mid, tail):
        srcData = head + mid + tail
        sha1 = hashlib.md5(bytes(srcData, encoding="utf-8"))
        return sha1.hexdigest()

    def getBusinessKey(self):
        key = base64.standard_b64decode(self.keyId)
        root_key = bytes(self.rootKey, encoding="utf-8")
        res = self.decrypt_GCM(key, root_key)
        return res[:32]

    def encrypt_GCM(self, msg, secretKey):
        nonce = os.urandom(12)
        aesCipher = AES.new(secretKey, AES.MODE_GCM, nonce=nonce)
        ciphertext, authTag = aesCipher.encrypt_and_digest(msg)
        return (ciphertext, aesCipher.nonce, authTag)

    def decrypt_GCM(self, ciphertext, secretKey):
        nonce = ciphertext[:12]
        authTag = ciphertext[len(ciphertext) - 16:]
        ciphertext = ciphertext[12:len(ciphertext) - 16]
        aesCipher = AES.new(secretKey, AES.MODE_GCM, nonce)
        plaintext = aesCipher.decrypt_and_verify(ciphertext, authTag)
        return plaintext

    def DoEncrypt(self, plainText):
        key = self.getBusinessKey()
        ciphertext, nonce, tag = self.encrypt_GCM(plainText, key)
        return base64.standard_b64encode(nonce + ciphertext + tag)

    def DoDecrypt(self, plainText):
        key = self.getBusinessKey()
        return self.decrypt_GCM(plainText, key)

    def DecryptBase64(self, text: str):
        s = base64.b64decode(text)
        return str(self.DoDecrypt(s), "utf-8")

