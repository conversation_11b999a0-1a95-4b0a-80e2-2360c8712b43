# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/1/5 16:19

import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import base64

cams_sk_part = "CAMS_SK_PART"

def get_content_hash(content: str) -> str:
    hash_object = hashlib.sha256(content.encode())
    hash_str = hash_object.hexdigest()
    return hash_str


def cbc_encrypt(data: bytes, key: str, iv: str):
    cipher = AES.new(key.encode("utf-8"), AES.MODE_CBC, iv.encode("utf-8"))
    padding = 16 - len(data) % 16
    pad_text = bytes([padding]) * padding
    padded_data = data + pad_text
    ciphertext = cipher.encrypt(padded_data)
    return base64.b64encode(ciphertext).decode("utf-8")


def cbc_decrypt(data: bytes, key: str, iv: str) -> str:
    b_key = key.encode("utf-8")
    b_iv = iv.encode("utf-8")
    block = AES.new(b_key, AES.MODE_CBC, b_iv)
    d_data = block.decrypt(data)
    unpadded_data = unpad(d_data, AES.block_size)
    return unpadded_data.decode("utf-8")


def cams_encrypt(ak: str, source_str: str, sk_part: str = "") -> str:
    if len(sk_part) == 0:
        sk_part = cams_sk_part
    iv = get_content_hash(ak)[:16]
    key = get_content_hash(sk_part)[:16]
    b_sk_salt = source_str.encode("utf-8")
    return cbc_encrypt(b_sk_salt, key, iv)


def cams_decrypt(ak: str, sk_salt: str, sk_part: str = "") -> str:
    if len(sk_part) == 0:
        sk_part = cams_sk_part
    iv = get_content_hash(ak)[:16]
    key = get_content_hash(sk_part)[:16]
    b_sk_salt = base64.standard_b64decode(sk_salt)
    return cbc_decrypt(b_sk_salt, key, iv)

# 二开的minio的sk解密
def ecis_decrypt(cams_sk: str, ecis_esk: str) -> str:
    # 计算 AES 密钥
    h = hashlib.md5()
    h.update(cams_sk.encode())
    akey = h.hexdigest()
    # 从 Base64 解码数据
    en_data_from_base64 = base64.b64decode(ecis_esk)
    # 创建 AES 解密器
    cipher = AES.new(akey.encode(), AES.MODE_CBC, akey.encode()[:AES.block_size])
    # 解密数据
    decrypted_data = cipher.decrypt(en_data_from_base64)
    # 去除填充
    length = len(decrypted_data)
    unpadding = ord(decrypted_data[length - 1:])
    if length - unpadding >= 0:
        decrypted_data = decrypted_data[:(length - unpadding)]
    return decrypted_data.decode()
