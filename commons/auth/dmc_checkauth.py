import json
from functools import lru_cache
from urllib.parse import quote_plus
import logging
from typing import Optional, List
from fastapi import Request

from commons.auth.auth_rpc import AuthRequest, SigVerType, sig_wps2
from commons.tools.utils import Singleton

class DmcCheckAuth(object, metaclass=Singleton):
    def __init__(self):
        self._auth_rpc: Optional[AuthRequest] = None

    def init(self, host, ak, sk):
        self._auth_rpc = AuthRequest(host, ak, sk, SigVerType.wps2)
        code, text = self._auth_rpc.call("POST", f"/acl/v2/init?v=v2")
        if code != 200 or json.loads(text)["result"] != "ok":
            logging.error(text)
            raise Exception("init auth failed!")

    async def authorization(self, request: Request) -> bool:
        uri = request.url.path
        if len(request.url.query) > 0:
            uri += f"?{request.url.query}"
        header = request.headers
        content_type = header.get("Content-Type", "")
        if "multipart/form-data" in content_type:
            # 去除boundary
            content_type = "multipart/form-data"
        date = header.get("Date", "")
        if len(date) == 0:
            return False

        authstr = header.get("Authorization", "")
        if len(authstr) == 0:
            return False

        items = authstr.strip().split(":")
        if len(items) != 3:
            return False

        check_ak = items[1]
        body = await request.body() if "POST" == request.method and "application/json" == content_type else None
        isok, retry = self._check(authstr, uri, body, check_ak, date, content_type)
        if not isok and retry:
            self._get_check_sk_list.cache_clear()
            isok, _ = self._check(authstr, uri, body, check_ak, date, content_type)
        return isok

    def _check(self, authstr, uri, body, ak, date, content_type) -> (bool, bool):
        sk_list = self._get_check_sk_list(ak)
        if not sk_list:
            self._get_check_sk_list.cache_clear()
            return False, False

        for sk in sk_list:
            header = sig_wps2(uri, body, ak, sk, date, content_type)
            if authstr == header["Authorization"]:
                return True, False
        return False, True

    @lru_cache(maxsize=10)
    def _get_check_sk_list(self, ak) -> Optional[List[str]]:
        try:
            _, text = self._auth_rpc.call("GET", f"/acl/v2/app?app_id={quote_plus(ak)}")
            appkeys = json.loads(text)["object"]["app_keys"]
            return [item["app_key"] for item in appkeys]
        except Exception as e:
            logging.error(e)
            return None
