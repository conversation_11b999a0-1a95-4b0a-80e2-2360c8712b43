from fastapi import Request

from commons.auth.auth_rpc import sig_wps2
from commons.tools.utils import Singleton

class PrivateCheckAuth(object, metaclass=Singleton):
    def __init__(self):
        self._aksk_dict = {}

    def init(self, aksk_dict: dict):
        self._aksk_dict = aksk_dict

    async def authorization(self, request: Request) -> bool:
        uri = request.url.path
        header = request.headers
        content_type = header.get("Content-Type", "")
        if "multipart/form-data" in content_type:
            # 去除boundary
            content_type = "multipart/form-data"
        authstr = header.get("Wps-Docs-Authorization", "")
        if len(authstr) == 0:
            authstr = header.get("Authorization", "")
            if len(authstr) == 0:
                return False
        items = authstr.strip().split(":")
        if len(items) != 3:
            return False

        check_ak = items[1]
        check_sk = self._aksk_dict.get(check_ak, "")
        if len(check_sk) == 0:
            return False
        body = await request.body() if "POST" == request.method and "application/json" == content_type else None

        date = header.get("Date", None)
        if date is None:
            return False

        h = sig_wps2(uri, body, check_ak, check_sk, date, content_type)
        return h["Authorization"] == authstr
