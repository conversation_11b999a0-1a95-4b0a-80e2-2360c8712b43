from commons.adapter.public_gateway_adapter import PublicGatewayAdapter
from commons.adapter.sft_gateway_adapter import SftGatewayAdapter
from commons.llm_gateway.models.chat_data import LLMConfig, PublicGatewayHeader
from commons.llm_gateway.llm import LLModelRpc

def create_gateway_adapter(llm_config: LLMConfig, public_gateway_header: PublicGatewayHeader):
    if llm_config.gateway == LLModelRpc.Gateway.Sft:
        return SftGatewayAdapter(llm_config)
    else:
        return PublicGatewayAdapter(llm_config, public_gateway_header)