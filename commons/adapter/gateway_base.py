from abc import ABC, abstractmethod
from typing import Any, Union, List
from commons.llm_gateway.models.chat_data import Message

class MultiModelArgs:
    def __init__(self,
                messages: list[Message] = None,
                stream: bool = False,
                temperature: float = 0.7,
                max_token: int = 2000,
                adapter: str = "",
                return_usage: bool = False,
                **kwargs):

        self.messages=messages
        self.stream=stream
        self.temperature=temperature
        self.max_token=max_token
        self.adapter=adapter
        self.return_usage=return_usage
        self.extra_dict=kwargs

class AsyncChatTextArgs:
    def __init__(self,
                prompt: Union[List[int], List[List[int]], str, List[str], None] = None,
                messages: list[Message] = None,
                query: str = "",
                temperature: float = 0.7,
                max_token: int = 2000,
                repetition_penalty:float = 1.0,
                adapter: str = "",
                return_usage: bool = False,
                **kwargs):

        self.prompt = prompt
        self.messages = messages
        self.query = query
        self.temperature = temperature
        self.max_token = max_token
        self.repetition_penalty = repetition_penalty
        self.adapter = adapter
        self.return_usage = return_usage
        self.extra_dict = kwargs


class GatewayBase(ABC):

    @abstractmethod
    async def async_multimodal(self, model_args: MultiModelArgs):
        pass

    @abstractmethod
    async def async_chat_text(self, model_args: AsyncChatTextArgs):
        pass