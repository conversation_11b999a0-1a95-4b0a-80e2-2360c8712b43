from commons.adapter.gateway_base import GatewayBase, MultiModelArgs, AsyncChatTextArgs
from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.models.chat_data import LLMConfig

class SftGatewayAdapter(GatewayBase):
    def __init__(self, llm_config: LLMConfig):
        self.llm_config = llm_config

    def _check_config(self):
        if self.llm_config is None:
            raise Exception("模型配置未加载")

    async def async_multimodal(self, model_args: MultiModelArgs):
        self._check_config()
        return await LLModelRpc().async_multimodal(
            gateway=LLModelRpc.Gateway.Sft,
            messages=model_args.messages,
            stream=model_args.stream,
            temperature=model_args.temperature,
            max_token=model_args.max_token,
            adapter=model_args.adapter,
            selector=LLModelRpc.ModelSelector(
                model=self.llm_config.sft.lora_model
            ),
            sft_base_model=LLModelRpc.SftBaseModel(
                base_model=self.llm_config.sft.base_model
            ),
            return_usage=model_args.return_usage,
            **model_args.extra_dict
        )

    async def async_chat_text(self, model_args: AsyncChatTextArgs):
        self._check_config()
        return await LLModelRpc().async_chat_text(
            gateway=LLModelRpc.Gateway.Sft,
            query=model_args.prompt,
            temperature=model_args.temperature,
            max_token=model_args.max_token,
            repetition_penalty=model_args.repetition_penalty,
            adapter=model_args.adapter,
            selector=LLModelRpc.ModelSelector(
              model=self.llm_config.sft.lora_model
            ),
            messages=model_args.messages,
            sft_base_model=LLModelRpc.SftBaseModel(
                base_model=self.llm_config.sft.base_model
            ),
            return_usage=model_args.return_usage,
            **model_args.extra_dict
        )