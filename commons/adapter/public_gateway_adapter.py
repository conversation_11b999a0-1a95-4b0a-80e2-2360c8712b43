from commons.adapter.gateway_base import GatewayBase, MultiModelArgs, AsyncChatTextArgs
from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.models.chat_data import LLMConfig, PublicGatewayHeader, AIHubModel

class PublicGatewayAdapter(GatewayBase):
    def __init__(self, llm_config: LLMConfig, public_gateway_header: PublicGatewayHeader):
        self.llm_config = llm_config
        self.public_gateway_header = public_gateway_header
        self.ai_hub_model:AIHubModel

        self._choice_ai_hub_model_by_config()

    def _choice_ai_hub_model_by_config(self):
        for ai_hub_model in self.public_gateway_header.ai_hub_models:
            if ai_hub_model.provider == self.llm_config.provider and \
               ai_hub_model.name == self.llm_config.model and \
               ai_hub_model.version == self.llm_config.version:
                self.ai_hub_model = ai_hub_model
                return

    def _check_config(self):
        if self.llm_config is None:
            raise Exception("模型配置未加载")
        if self.ai_hub_model is None:
            raise Exception("AIHub模型未找到")
        if self.public_gateway_header is None:
            self.public_gateway_header = PublicGatewayHeader()
        self.public_gateway_header.load_by_local_llm_config(llm_config=self.llm_config)

    async def async_multimodal(self, model_args: MultiModelArgs):
        self._check_config()
        return await LLModelRpc().async_multimodal(
            gateway=LLModelRpc.Gateway.Public,
            messages=model_args.messages,
            stream=model_args.stream,
            temperature=model_args.temperature,
            max_token=model_args.max_token,
            adapter=model_args.adapter,
            selector=LLModelRpc.ModelSelector(
                model=self.ai_hub_model.name,
                provider=self.ai_hub_model.provider,
                version=self.ai_hub_model.version,
            ),
            sft_base_model=None,
            return_usage=model_args.return_usage,
            public_gateway_header=self.public_gateway_header,
            **model_args.extra_dict
        )

    async def async_chat_text(self, model_args: AsyncChatTextArgs):
        self._check_config()
        return await LLModelRpc().async_chat_text(
            gateway=LLModelRpc.Gateway.Public,
            query=model_args.prompt,
            temperature=model_args.temperature,
            max_token=model_args.max_token,
            repetition_penalty=model_args.repetition_penalty,
            adapter=model_args.adapter,
            selector=LLModelRpc.ModelSelector(
                model=self.ai_hub_model.name,
                provider=self.ai_hub_model.provider,
                version=self.ai_hub_model.version,
            ),
            messages=model_args.messages,
            sft_base_model=None,
            return_usage=model_args.return_usage,
            public_gateway_header=self.public_gateway_header,
            **model_args.extra_dict
        )