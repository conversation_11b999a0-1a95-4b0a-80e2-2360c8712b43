# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/10 16:19
import contextvars
import json
from typing import Optional

private_context = contextvars.ContextVar("Private-Context", default="")


class PrivateHeadersContext:
    def __init__(self):
        self._headers_context = {}

    def set(self, headers: dict):
        if "User-Agent" in headers:
            self._headers_context["User-Agent"] = headers["User-Agent"]
        if "X-Forwarded-For" in headers:
            self._headers_context["X-Forwarded-For"] = headers["X-Forwarded-For"]
        if "X-Real-IP" in headers:
            self._headers_context["X-Real-IP"] = headers["X-Real-IP"]
        if "Device-ID" in headers:
            self._headers_context["Device-ID"] = headers["Device-ID"]
        if "Device-Name" in headers:
            self._headers_context["Device-Name"] = headers["Device-Name"]
        if "X-App-Name" in headers:
            self._headers_context["X-App-Name"] = headers["X-App-Name"]
        if "X-App-Version" in headers:
            self._headers_context["X-App-Version"] = headers["X-App-Version"]
        if "Device-Platform" in headers:
            self._headers_context["Device-Platform"] = headers["Device-Platform"]

    def get(self) -> dict:
        return self._headers_context


def get_private_context() -> Optional[dict]:
    c = private_context.get()
    if len(c) > 0:
        return json.loads(c)
    else:
        return None


def set_private_context(context: dict):
    private_context.set(json.dumps(context))
