# coding: utf-8
from pydantic import BaseModel
import contextvars
from typing import Union, Dict
from commons.llm_gateway.llm import LLModelRpc
import os


def _get_class_or_method_name(obj: Union[str, type, callable]):
    if isinstance(obj, str):
        name = obj
    elif isinstance(obj, type) or callable(obj):
        name = obj.__name__
    else:
        raise TypeError("参数类型错误, 应输入字符串或class或class.method, 无括号")
    return f"llm_config_{name}"


class LLMConfig(BaseModel):
    gateway: LLModelRpc.Gateway = LLModelRpc.Gateway.Public
    provider: str = ""
    model: str = ""
    version: str = ""
    sft_base_model: str = ""

    def __init__(self,
                 gateway: LLModelRpc.Gateway = LLModelRpc.Gateway.Public,
                 provider: str = "",
                 model: str = "",
                 version: str = "",
                 sft_base_model: str = ""
                 ):
        super().__init__()
        self.gateway = gateway
        self.provider = provider
        self.model = model
        self.version = version
        self.sft_base_model = sft_base_model

    def get_selector(self):
        return LLModelRpc.ModelSelector(provider=self.provider, model=self.model, version=self.version)


class AiContext(BaseModel):
    """
    AI接口上下文
    """
    # 接口基础信息
    ip: str = None
    request_id: str = None
    local_id: str = None
    uri: str = None
    # 日志信息
    log_index: int = 0
    # 用户信息
    wps_sid: str = None
    uid: str = None
    cid: str = None
    # 模型信息
    llm_configs: Dict[str, LLMConfig] = {}
    # ai网关信息
    model_type: str = None
    product_name: str = None
    intention_code: str = None
    billing_token: str = None
    is_multi_modal: bool = False


ai_context = contextvars.ContextVar('ai_context', default=AiContext())


class AiContextManager(object):
    @classmethod
    def set_ai_context(cls, context: AiContext):
        ai_context.set(context)

    @classmethod
    def get_ai_context(cls) -> AiContext:
        return ai_context.get()

    @classmethod
    def set_llm_config(cls, obj: Union[str, type, callable], default_config: LLMConfig):
        name = _get_class_or_method_name(obj)
        llm_configs = cls.get_ai_context().llm_configs
        if name in llm_configs:
            raise ValueError(f"已存在名为{name}的LLM配置, keys: {llm_configs.keys()}")
        env_config = os.environ.get(name, None)
        if env_config is not None:
            config = LLMConfig.parse_raw(env_config)
        else:
            config = default_config
        llm_configs[name] = config

    @classmethod
    def get_llm_config(cls, obj: Union[str, type, callable]) -> Union[LLMConfig, None]:
        name = _get_class_or_method_name(obj)
        return cls.get_ai_context().llm_configs.get(name, None)


# 注释掉main方法逻辑以提高测试覆盖率
# if __name__ == '__main__':
#     # AiContextManager.set_ai_context(AiContext())

#     AiContextManager.set_llm_config("test", LLMConfig(LLModelRpc.Gateway.Public, "zhipu", "glm4", ""))
#     AiContextManager.set_llm_config(AiContextManager, LLMConfig(LLModelRpc.Gateway.Public, "minimax", "adb", ""))

#     print(AiContextManager.get_llm_config("test"))
#     print(AiContextManager.get_llm_config(AiContextManager))