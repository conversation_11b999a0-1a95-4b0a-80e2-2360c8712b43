# coding: utf-8
from fastapi import Request, Response
from commons.context.ai_context import <PERSON><PERSON><PERSON>xt<PERSON><PERSON><PERSON>, <PERSON><PERSON>ontex<PERSON>
from starlette.middleware.base import BaseHTTPMiddleware
import uuid

class AiMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        # 上下文
        request_id = request.headers.get("Client-Request-Id", "")
        if len(request_id) == 0:
            request_id = request.headers.get("X-Request-Id", str(uuid.uuid4()))
        if "route" in request.scope:
            uri = request.scope["route"].path
        else:
            uri = request.url.path
        ai_context: AiContext = AiContext(
            ip=request.client.host,
            request_id=request_id,
            local_id=str(uuid.uuid4()),
            uri=uri,
            wps_sid=request.headers.get("wps_sid", ""),
        )
        AiContextManager.set_ai_context(ai_context)
        response: Response = await call_next(request)

        return response
