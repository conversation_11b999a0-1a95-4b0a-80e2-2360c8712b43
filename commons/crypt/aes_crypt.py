# -*- coding: utf-8 -*-
import os
import struct
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes

key = "MODEL_BUILD_KEY".encode("utf-8")
if os.environ.get("BUILD_KEY", None) is not None:
    key = os.environ["BUILD_KEY"].encode("utf-8")
IV_LEN = 16


def encrypt_buffer(plaintext, key: bytes = key):
    iv = get_random_bytes(IV_LEN)
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_plaintext = pad(plaintext)
    ciphertext = cipher.encrypt(padded_plaintext)
    return iv + ciphertext


def decrypt_buffer(ciphertext, key: bytes = key):
    iv = ciphertext[:IV_LEN]
    ciphertext = ciphertext[IV_LEN:]
    cipher = AES.new(key, AES.MODE_CBC, iv)
    plaintext = cipher.decrypt(ciphertext)
    plaintext = unpad(plaintext)
    return plaintext


def pad(data):
    padding_size = AES.block_size - len(data) % AES.block_size
    return data + bytes([padding_size] * padding_size)


def unpad(data):
    padding_size = data[-1]
    return data[:-padding_size]


UINT16_MAX = 0xFFFF
BLOCK_SIZE = 1024
HEADER_LEN = 12
HEADER_STR = b"kna_decrypt"


class ENCRYPT_HEADER:
    def init(self):
        self.batch = 0


class ENCRYPT_RECORD:
    def init(self):
        self.block_size = 0


def encrypt_file(src: str, dst: str):
    with open(dst, "wb") as outfile, open(src, "rb") as infile:
        HEADER_FORMAT = "I"
        RECORD_FORMAT = "H"
        header = ENCRYPT_HEADER()
        header.batch = 1
        outfile.write(struct.pack(HEADER_FORMAT, header.batch))

        record = ENCRYPT_RECORD()
        encrypted = encrypt_buffer(bytes(HEADER_STR))
        record.block_size = len(encrypted)
        outfile.write(struct.pack(RECORD_FORMAT, record.block_size))
        outfile.write(encrypted)
        while True:
            tmp_buffer = infile.read(BLOCK_SIZE)
            if not tmp_buffer:
                break
            record = ENCRYPT_RECORD()
            encrypted = encrypt_buffer(bytes(tmp_buffer))
            record.block_size = len(encrypted)
            outfile.write(struct.pack(RECORD_FORMAT, record.block_size))
            outfile.write(encrypted)
            header.batch += 1
        outfile.seek(0)
        outfile.write(struct.pack(HEADER_FORMAT, header.batch))


def decrypt_file(src: str, dst: str):
    with open(dst, "wb") as outfile, open(src, "rb") as infile:
        header = ENCRYPT_HEADER()
        HEADER_FORMAT = "I"
        header_data = infile.read(struct.calcsize(HEADER_FORMAT))
        header_values = struct.unpack(HEADER_FORMAT, header_data)
        if len(header_values) != 0:
            header.batch = header_values[0]

        RECORD_FORMAT = "H"
        for i in range(header.batch):
            record_data = infile.read(struct.calcsize(RECORD_FORMAT))
            record_values = struct.unpack(RECORD_FORMAT, record_data)
            if len(record_values) != 0:
                block_size = record_values[0]
                buffer_size = block_size
                assert buffer_size > 0
                tmp_buffer = infile.read(buffer_size)
                decrypted = decrypt_buffer(tmp_buffer)
                if len(decrypted) != 0:
                    # 比较 buffer 和 HEADER_STR
                    if i == 0 and decrypted.startswith(HEADER_STR):
                        continue
                    outfile.write(decrypted)
