import os
import time
import tempfile
import shutil
from commons.tools.utils import WalkDir
from commons.crypt.aes_crypt import decrypt_file, encrypt_file

DECRYPT_FILE_FLAG = ".enc"


def decrypt_model(model_path: str, decrypted_model_path: str = None, model_name: str = None):
    current_time = time.time_ns()
    if not decrypted_model_path:
        decrypted_model_path = f"/{tempfile.gettempdir()}/{current_time}"
    if model_name:
        decrypted_model_path = f"{decrypted_model_path}/{model_name}"

    if not os.path.exists(decrypted_model_path):
        os.makedirs(decrypted_model_path)

    for fname, fdir in WalkDir(model_path)():
        f_path = os.path.join(fdir, fname)
        tfilepath = f_path.replace(model_path, "")
        copy_file = f"{decrypted_model_path}/{tfilepath}"
        os.makedirs(os.path.dirname(copy_file), exist_ok=True)
        if f_path.endswith(DECRYPT_FILE_FLAG):
            copy_file = copy_file[: -len(DECRYPT_FILE_FLAG)]
            decrypt_file(f_path, copy_file)
        else:
            shutil.copy2(f_path, copy_file)
    return decrypted_model_path


def encrypt_model(dir_path, target_dir_path):
    for file_name, fdir in WalkDir(dir_path)():
        file_path = os.path.join(fdir, file_name)
        target_file_path = file_path.replace(dir_path, target_dir_path)
        os.makedirs(os.path.dirname(target_file_path), exist_ok=True)
        encrypted_file_path = target_file_path + DECRYPT_FILE_FLAG
        encrypt_file(file_path, encrypted_file_path)
        print(f"文件 {file_path} 已加密并保存为 {encrypted_file_path}")
