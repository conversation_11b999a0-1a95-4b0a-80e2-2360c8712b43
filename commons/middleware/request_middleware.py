import secrets

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response


class RequestIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 获取现有的 request ID（不区分大小写）
        request_id = request.headers.get("x-request-id")

        # 如果没有则生成一个 128 位（16 字节 = 32 hex 字符）ID
        if not request_id:
            request_id = secrets.token_hex(16)
        request.state.request_id = request_id
        # 调用下一个中间件或视图处理函数
        response: Response = await call_next(request)

        # 添加 x-request-id 到响应头
        response.headers["x-request-id"] = request_id
        return response
