import os
from typing import Any

from concurrent.futures import Thread<PERSON>oolExecutor, as_completed


class MultiThread(object):
    cpu_count = os.cpu_count()
    @classmethod
    def set_cpu_count(cls, cpu_count):
        cls.cpu_count = cpu_count

    @classmethod
    def sync_run(cls, fn: Any, iterator, /, *args, **kwargs):
        result = []
        fetures = []
        with ThreadPoolExecutor(max_workers=cls.cpu_count) as executor:
            for item in iterator:
                fetures.append(executor.submit(fn, item, *args, **kwargs))
            for feture in as_completed(fetures):
                res = feture.result()
                if res:
                    result.append(res)
        return result

# if __name__ == "__main__":
#     def func(a,b):
#         print(a+b)

#     items = [1,2,3,4]
#     MultiThread.sync_run(func, items, 10)

#     class IteratorClass(object):
#         def __init__(self):
#             self.items = [2,4,6,8]

#         def __iter__(self):
#             for item in self.items:
#                 yield item

#     MultiThread.sync_run(func, IteratorClass(), 20)