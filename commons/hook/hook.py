# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/3/30 22:41
import os

def _hooked():
    import multiprocessing
    from multiprocessing.spawn import get_command_line
    original_get_command_line = get_command_line

    def _hooked_get_command_line(**kwds):
        cmd = original_get_command_line(**kwds)
        res_cmd = []
        for c in cmd:
            if c != "-B":
                res_cmd.append(c)
        return res_cmd

    multiprocessing.spawn.get_command_line = _hooked_get_command_line
    from multiprocessing import util
    original_args = util._args_from_interpreter_flags
    def _hooked_args():
        cmd = original_args()
        res_cmd = []
        for c in cmd:
            if c != "-B":
                res_cmd.append(c)
        return res_cmd
    util._args_from_interpreter_flags = _hooked_args
    os.environ["PYTHONDONTWRITEBYTECODE"] = "1"
    return original_get_command_line, original_args

def _hooked_remove(original_get_command_line, original_args):
    import multiprocessing
    multiprocessing.spawn.get_command_line = original_get_command_line
    from multiprocessing import util
    util._args_from_interpreter_flags = original_args
    del os.environ["PYTHONDONTWRITEBYTECODE"]

