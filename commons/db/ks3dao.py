# coding:utf-8
# @Author:   <PERSON><PERSON><PERSON><PERSON><PERSON>
# @date:     2018/9/11 16:04
import logging

from ks3.connection import Connection
from ks3.key import Key
from ks3.prefix import Prefix

from commons.tools.utils import Singleton


class KS3Tool(object, metaclass=Singleton):
    def __init__(self):
        self.bucket = None
        self.conn: Connection = None

    def init(self, host, ak, sk, bucket):
        self.bucket = bucket
        self.conn = Connection(ak, sk, host=host, is_secure=False)

    def upload_from_bytes(self, ks3path: str, data: bytes) -> bool:

        try:
            b = self.conn.get_bucket(self.bucket)
            k: Key = b.new_key(ks3path)
            ret = k.set_contents_from_string(data, policy="private")
            if ret and ret.status == 200:
                return True
            else:
                logging.error(ret)
        except Exception as e:
            logging.error(e)
        return False

    async def async_upload_from_bytes(self, ks3path: str, data: bytes) -> bool:

        try:
            b = self.conn.get_bucket(self.bucket)
            k: Key = b.new_key(ks3path)
            from concurrent.futures import ThreadPoolExecutor
            import asyncio
            loop = asyncio.get_running_loop()
            with ThreadPoolExecutor() as pool:
                ret = await loop.run_in_executor(pool, lambda: k.set_contents_from_string(data, policy="private"))
            if ret and ret.status == 200:
                return True
            else:
                logging.error(ret)
        except Exception as e:
            logging.error(e)
        return False

    def upload_from_text(self, ks3path: str, text: str) -> bool:
        return self.upload_from_bytes(ks3path, text.encode("utf-8"))

    def upload_from_file(self, ks3path: str, fpath: str, replace=True) -> bool:
        try:
            b = self.conn.get_bucket(self.bucket)
            k: Key = b.new_key(ks3path)
            ret = k.set_contents_from_filename(fpath, replace=replace, policy="private")
            if ret and ret.status == 200:
                return True
            else:
                logging.error(ret)
        except Exception as e:
            logging.error(e)
        return False

    def download_to_text(self, ks3path: str) -> (bool, str):
        k = None
        try:
            b = self.conn.get_bucket(self.bucket)
            k = b.get_key(ks3path.strip("/"))
            text = k.get_contents_as_string()
            return True, text.decode("utf-8", errors="ignore")
        except Exception as e:
            logging.error(ks3path)
            logging.exception(e)
            return False, ""
        finally:
            if k:
                k.close()

    def download_to_file(self, ks3path: str, fpath: str) -> bool:
        k = None
        try:
            b = self.conn.get_bucket(self.bucket)
            k = b.get_key(ks3path.strip("/"))
            k.get_contents_to_filename(fpath)
            return True
        except Exception as e:
            logging.exception(e)
            return False
        finally:
            if k:
                k.close()

    def generate_url(self, ks3path: str, timeout: int) -> str:
        try:
            return self.conn.generate_url(timeout, "GET", self.bucket, ks3path)
        except Exception as e:
            logging.error(e)
            return ""

    async def async_generate_url(self, ks3path: str, timeout: int) -> str:
        try:
            from concurrent.futures import ThreadPoolExecutor
            import asyncio
            loop = asyncio.get_running_loop()
            with ThreadPoolExecutor() as pool:
                return await loop.run_in_executor(pool,
                                                  lambda: self.conn.generate_url(timeout, "GET", self.bucket, ks3path))
        except Exception as e:
            logging.error(e)
            return ""

    def list_root(self):
        try:
            b = self.conn.get_bucket(self.bucket)
            keys = b.list(delimiter="/")
            return keys
        except Exception as e:
            logging.exception(e)
            return []

    def lookup_root(self):
        from ks3.key import Key
        from ks3.prefix import Prefix
        keys = self.list_root()
        for key in keys:
            if isinstance(key, Key):
                logging.debug("file:%s" % key.name)
            elif isinstance(key, Prefix):
                logging.debug("dir:%s" % key.name)

    def list(self, path: str):
        try:
            b = self.conn.get_bucket(self.bucket)
            keys = b.list(prefix=path, delimiter="/")
            return keys
        except Exception as e:
            logging.exception(e)
            return []

    def list_dir(self, path: str):
        try:
            b = self.conn.get_bucket(self.bucket)
            keys = b.list(prefix=path)
            directory = []
            for k in keys:
                if isinstance(k, Prefix):
                    directory.append(k.name)
            return directory
        except Exception as e:
            logging.exception(e)
            return []

    def list_file(self, path: str):
        try:
            b = self.conn.get_bucket(self.bucket)
            keys = b.list(prefix=path)
            files = []
            for k in keys:
                if isinstance(k, Key):
                    files.append(k.name)
            return files
        except Exception as e:
            logging.exception(e)
            return []

    def delete_by_key(self, key_name: str) -> bool:
        try:
            b = self.conn.get_bucket(self.bucket)
            k = b.get_key(key_name)
            if k is None:
                return False
            b.delete_key(key_name=key_name)
            return True
        except Exception as e:
            logging.error(f"delete_by_key error: {e}")
            return False

# if __name__=="__main__":
# from conf import ConfKs3
# import os
# KS3Tool().init(ConfKs3.host, ConfKs3.ak, ConfKs3.sk, ConfKs3.bucket)
# prefix = "anti_spam_download/"
# max_version = 0
# key_name = ""
# for k in KS3Tool().list(prefix):
#     f = k.name[len(prefix):]
#     if len(f) > 0:
#         v = f.replace(".zip", "")
#         if v.isnumeric() and int(v) > max_version:
#             max_version = v
#             key_name = k.name
# res = KS3Tool().generate_url(key_name, 10)
# print(res)
