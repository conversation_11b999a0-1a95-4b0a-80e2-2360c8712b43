from commons.logger.business_log import logger
from commons.tools.utils import Singleton
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.admin import NewTopic, AIOKafkaAdminClient, NewPartitions
from aiokafka.admin.config_resource import ConfigResource, ConfigResourceType
from kafka import KafkaProducer
import json
from typing import List, Optional, Dict



class KafkaProducerDao(object, metaclass=Singleton):
    def __init__(self):
        self.aproducer: Optional[AIOKafkaProducer] = None
        self.producer: Optional[KafkaProducer] = None
        self.level_topic_dict: Dict[str, str] = {}

    async def init(self, level_topic_dict: Dict[str, str], bootstrap_servers: List[str]):
        """
        Kafka 生产者类，用于向指定主题发送消息。

        :param bootstrap_servers: Kafka服务器地址列表 ["localhost:9092"]
        :param topic: 目标主题名称
        """
        self.level_topic_dict = level_topic_dict
        self.aproducer = AIOKafkaProducer(
            bootstrap_servers=bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            request_timeout_ms=30000,
            retry_backoff_ms=1000,
        )
        await self.aproducer.start()
        self.producer = KafkaProducer(
            bootstrap_servers=bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            request_timeout_ms=30000,
            retry_backoff_ms=1000,
        )

    async def close(self):
        """
        关闭 Kafka 生产者连接
        """
        await self.aproducer.stop()
        self.producer.close()

    async def asend_message(self, level: str, value: dict) -> bool:
        """
        发送消息到Kafka
        :param topic: 目标主题名称
        :param value: 消息内容，字典格式
        """
        try:
            if level not in self.level_topic_dict:
                logger.error(f"KafkaProducerDao.asend_message level {level} not in level_topic_dict")
                return False
            if self.aproducer._closed:
                await self.aproducer.start()
            await self.aproducer.send_and_wait(self.level_topic_dict[level], value=value)
            return True
        except Exception as e:
            logger.error(f"KafkaProducerDao.asend_message 消息发送失败: {e}")
            return False

    def send_message(self, level: str, value: dict) -> bool:
        """
        发送消息到Kafka
        :param topic: 目标主题名称
        :param value: 消息内容，字典格式
        """
        try:
            if level not in self.level_topic_dict:
                logger.error(f"KafkaProducerDao.send_message level {level} not in level_topic_dict")
                return False
            future = self.producer.send(self.level_topic_dict[level], value=value)
            future.get(timeout=10)
            return True
        except Exception as e:
            logger.error(f"KafkaProducerDao.send_message 消息发送失败: {e}")
            return False


class KafkaConsumerDao(object):
    def __init__(self, bootstrap_servers: List[str], topics: List[str], group_id: str, auto_offset_reset:str="latest"):
        self.bootstrap_servers = bootstrap_servers
        self.topics = topics
        self.group_id = group_id
        self.auto_offset_reset = auto_offset_reset

    async def get_consumer(self):
        """
        获取消费者实例，根据业务需要进行消费
        """
        consumer = AIOKafkaConsumer(
            *self.topics,
            bootstrap_servers=self.bootstrap_servers,
            group_id=self.group_id,
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            enable_auto_commit=False,
            auto_offset_reset=self.auto_offset_reset,
            max_poll_interval_ms=600000, # 两次拉取msg的最大时间间隔，超过这个时间，kafka认为消费者挂了，会把分区分配给其他消费者，根据消息处理耗时调整，10分钟
            max_poll_records=1, # 一次拉取msg的最大数量
            session_timeout_ms=60000, # 消费者与broker的会话超时时间，超过这个时间，broker认为消费者挂了，会把分区分配给其他消费者，根据网络稳定性调整，1分钟
        )
        await consumer.start()
        return consumer

    async def test_consume_messages(self):
        """
        简单的消费消息，调试用
        :return: 返回消息
        """
        consumer = await self.get_consumer()
        try:
            async for message in consumer:
                yield message
                # 消费完成后提交
                await consumer.commit()
        except Exception as e:
            logger.error(f"消息消费失败: {e}")
        finally:
            await consumer.stop()


class KafkaAdminClient(object, metaclass=Singleton):
    def __init__(self):
        self.client: Optional[AIOKafkaAdminClient] = None

    async def init(self, bootstrap_servers: List[str]):
        """
        Kafka 生产者类，用于向指定主题发送消息。

        :param bootstrap_servers: Kafka服务器地址列表 ["localhost:9092"]
        :param topic: 目标主题名称
        """
        self.client = AIOKafkaAdminClient(
            bootstrap_servers=bootstrap_servers,
        )
        await self.client.start()

    async def close(self):
        await self.client.close()

    async def create_topic(
            self,
            topic_name: str,
            partitions: int,
            replication: int = 1,
            topic_config=None
    ) -> bool:
        try:
            topic_desc = await self.client.describe_topics([topic_name])
            if len(topic_desc[0]["partitions"]) > 0:
                logger.info(f"KafkaAdminClient.create_topic 主题 {topic_name} 已经存在，更新配置...")
                if topic_config is not None:
                    if not await self.update_topic_config(topic_name, topic_config):
                        return False
                if not await self.update_topic_partitions(topic_name, partitions):
                    return False
                return True
            if topic_config is None:
                topic_config = {"retention.ms": "2592000000"} # 30天过期
            await self.client.create_topics([
                NewTopic(
                    name=topic_name,
                    num_partitions=partitions,
                    replication_factor=replication,
                    topic_configs=topic_config,
                )
            ])
            logger.info(f"KafkaAdminClient.create_topic 创建主题 {topic_name} 成功, partitions={partitions}, replication={replication}, topic_config={topic_config}")
            return True
        except Exception as e:
            logger.error(f"KafkaAdminClient.create_topic 创建主题失败: {e}")
            return False

    async def update_topic_config(
            self,
            topic_name: str,
            topic_config: dict) -> bool:
        try:
            topic_desc = await self.client.describe_topics([topic_name])
            if len(topic_desc[0]["partitions"]) == 0:
                logger.error(f"KafkaAdminClient.update_topic_config 主题 {topic_name} 不存在")
                return False
            await self.client.alter_configs([
                ConfigResource(ConfigResourceType.TOPIC, name=topic_name, configs=topic_config)
            ])
            logger.info(f"KafkaAdminClient.update_topic_config 更新主题 {topic_name} 配置成功: topic_config={topic_config}")
            return True
        except Exception as e:
            logger.error(f"KafkaAdminClient.update_topic_config 更新主题失败: {e}")
            return False

    async def update_topic_partitions(
            self,
            topic_name: str,
            partitions: int,
    ) -> bool:
        """
        kafka分区数量只能增加不能减少, partitions指定总的分区数，大于原有分区数则增加分区，小于原有分区数则不变
        """
        try:
            topic_desc = await self.client.describe_topics([topic_name])
            if len(topic_desc[0]["partitions"]) == 0:
                logger.error(f"KafkaAdminClient.update_topic_partitions 主题 {topic_name} 不存在")
                return False
            partitions_num = len(topic_desc[0]["partitions"])
            logger.info(f"KafkaAdminClient.update_topic_partitions 主题 {topic_name} 当前分区数: {partitions_num}")
            if partitions_num >= partitions:
                logger.info(f"KafkaAdminClient.update_topic_partitions 主题 {topic_name} 分区数已经满足")
                return True
            await self.client.create_partitions({topic_name: NewPartitions(total_count=partitions)})
            logger.info(f"KafkaAdminClient.update_topic_partitions 更新主题 {topic_name} 分区成功: partitions={partitions}")
            return True
        except Exception as e:
            logger.error(f"KafkaAdminClient.update_topic_partitions 更新主题分区副本失败: {e}")
            return False

    async def del_topic(self, topic_name: str) -> bool:
        try:
            await self.client.delete_topics([topic_name])
            logger.info(f"KafkaAdminClient.del_topic 删除主题 {topic_name} 成功")
            return True
        except Exception as e:
            logger.error(f"KafkaAdminClient.del_topic 删除主题失败: {e}")
            return False

    def get_client(self):
        return self.client
