CREATE TABLE IF NOT EXISTS `parse_record` (
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `token` varchar(40) DEFAULT '' COLLATE utf8mb4_general_ci NOT NULL COMMENT '解析token',
  `record_json` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '解析记录, json格式文本',
  `heartbeat` int(5) DEFAULT '0' NOT NULL COMMENT '心跳次数',
  `env` VARCHAR(20) DEFAULT '' COLLATE utf8mb4_general_ci NOT NULL COMMENT '环境',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uk_parse_record_token` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='解析记录表';
