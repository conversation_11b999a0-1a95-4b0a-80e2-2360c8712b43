aiofiles==23.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.8.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
atomicwrites==1.4.1
attrs==24.3.0
beautifulsoup4==4.12.3
bigtree==0.23.0
Brotli==1.1.0
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
crcmod==1.7
cuda-python==12.6.2.post1
datasets==3.2.0
datashaper==0.0.49
dill==0.3.8
diskcache==5.6.3
dnspython==2.7.0
docx2python==3.5.0
fastapi==0.115.6
filelock==3.16.1
fire==0.7.0
flatbuffers==24.12.23
fonttools==4.55.3
frozenlist==1.5.0
fsspec==2024.9.0
gevent==24.11.1
geventhttpclient==2.3.3
greenlet==3.1.1
grpcio==1.67.1
h11==0.14.0
huggingface-hub==0.27.1
humanfriendly==10.0
idna==3.10
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1

ks3sdk==1.11.0
lxml==5.3.0
minio==7.2.14
more-itertools==10.5.0
mpmath==1.3.0
multidict==6.1.0
multiprocess==0.70.16
numpy==1.26.4
onnxruntime==1.20.1
opencv-python==*********
opencv-python-headless==*********
packaging==24.2
pandas==2.2.3
paragraphs==1.0.1
pdf2docx==0.5.8
pillow==11.1.0
pluggy==1.5.0
prometheus_client==0.21.1
propcache==0.2.1
protobuf==5.29.2
py==1.11.0
pyarrow==15.0.2
pyclipper==1.3.0.post6
pycparser==2.22
pycryptodome==3.20.0
pydantic==2.10.4
pydantic_core==2.27.2
pymongo==4.10.1
PyMuPDF==1.25.1
pymupdf4llm==0.0.17
pyperclip==1.9.0
pyreadline3==3.5.4
pysbd==0.3.4
pytest==8.3.3
pytest-cov==6.0.0
pytest-asyncio==0.24.0
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-rapidjson==1.20
pytz==2024.2
PyYAML==6.0.2
rapidocr-onnxruntime==1.4.3
redis==5.2.1
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rpds-py==0.22.3
safetensors==0.5.1
shapely==2.0.6
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
starlette==0.41.3
sympy==1.13.3
tabulate==0.9.0
tenacity==9.0.0
termcolor==2.5.0
tokenizers==0.21.0
tqdm==4.67.1
transformers==4.50.0
tritonclient==2.53.0
typing_extensions==4.12.2
tzdata==2024.2
urllib3==2.3.0
uvicorn==0.34.0
xxhash==3.5.0
yarl==1.18.3
zope.event==5.0
zope.interface==7.2
psutil==6.1.1
matplotlib==3.10.0
scipy==1.15.1
scikit-image==0.25.0
py-cpuinfo==9.0.0
python-multipart==0.0.6
apscheduler==3.11.0
aiokafka==0.12.0
kafka-python==2.1.4
opentelemetry-api==1.31.1
opentelemetry-exporter-jaeger==1.21.0
opentelemetry-exporter-jaeger-proto-grpc==1.21.0
opentelemetry-exporter-jaeger-thrift==1.21.0
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
sqlalchemy==2.0.40
pymysql==1.1.1
cairosvg==2.7.1
wand==0.6.13
defusedxml~=0.7.1
pip~=25.0.1
wheel~=0.45.1
setuptools~=78.1.0
typing_extensions~=4.12.2
cssselect~=1.3.0
zipp~=3.21.0
exceptiongroup~=1.3.0
async-timeout~=5.0.1
thrift~=0.21.0
Deprecated~=1.2.18
pydantic_core~=2.27.2
imageio~=2.37.0
tifffile~=2025.5.10
lazy_loader~=0.4
networkx~=3.4.2
tzlocal~=5.3.1
cssselect2~=0.8.0
tinycss2~=1.4.0
cairocffi~=1.7.1
webencodings~=0.5.1
contourpy~=1.3.2
pyparsing~=3.2.3
wrapt~=1.17.2
cycler~=0.12.1
kiwisolver~=1.4.8
importlib_metadata~=8.6.1
prometheus_client~=0.21.1
scikit-learn==1.4.0
jsonpickle==2.2.0
opentelemetry-propagator-b3==1.31.1
opentelemetry-exporter-otlp==1.31.1