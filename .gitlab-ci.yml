stages:
  - echoexport
  - build

logVar:
  stage: echoexport
  #image: golang:1.18.7   node 16.13.0
  image: hub-mirror.wps.cn/pripub/buildtool:1.0.0
  tags:
    - cams
  only:
    - triggers
  script:
    - export

compileProject:
  stage: build
  #image: golang:1.18.7   node 16.13.0
  image: hub-mirror.wps.cn/pripub/buildtool:1.0.0
  tags:
    - cams
  before_script:
    - |
      if [[ "$buildType" != "zip" ]]; then
        echo "ca build"
        logFile=${cid}_${version}_${platform}_$[$(date +%s%N)/1000000]_log.txt
        touch $logFile
        (bash ${scriptPath} ${cid} ${version} > $logFile 2>&1) && cat $logFile|| false=1
      else
        echo "zip build"
      fi
  only:
    - triggers
  script:
    - 'buildtool callback --logFile=`pwd`/$logFile'
    - 'echo "清理目录:"$CI_PROJECT_DIR && rm -rf $CI_PROJECT_DIR'