# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/4/25 15:30

from enum import Enum


class HTTPCODE(int, Enum):
    ERROR = -1
    OK = 0
    ERROR_PARAMS = 100
    ERROR_SQL = 101
    ERROR_AUDIT = 103
    ERROR_DATA = 104
    ERROR_LLM = 105
    ERROR_NOLOGIN = 106
    ERROR_STREAM = 107
    ERROR_PRIVILEGE = 108
    ERROR_PAGESIZE_LIMIT = 109

    # 解析异常  12XX
    ERROR_File_Format = 1201
    ERROR_KAFKA_MSG_SEND = 1202
